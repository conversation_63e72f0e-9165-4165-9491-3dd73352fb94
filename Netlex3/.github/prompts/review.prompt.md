---
mode: 'agent'
description: 'Code-review contro origin/3.22/master'
---

**Goal**  
Analizza le differenze tra il branch locale (`HEAD`) e `origin/3.22/master` e segnala **eventuali regressioni** (test rimossi, logica semplificata, cali di performance o breaking-API).

**Istruzioni per l’agente**  
1. **Prepara l’ambiente**  
   - Esegui:
     ```bash
     git fetch origin 3.22/master
     git diff --patch-with-stat origin/3.22/master...HEAD
     ```
   - Assicurati di lavorare in “detached HEAD” o in un branch di lavoro separato per non alterare lo stato del repository.

2. **Analisi statica del diff**  
   - **Sommario generale**  
     - Numero totale di file modificati, aggiunti, rimossi  
     - Totale di linee aggiunte e rimosse  
   - **Classificazione delle modifiche**  
     - **Breaking API**: interfacce pubbliche modificate, rimozioni o cambi di firma  
     - **Performance**: evidenzia possibili regressioni di complessità o loop non ottimizzati  
     - **Logica semplificata/semplificazioni eccessive**: funzioni ridotte che rischiano di alterare il comportamento  
     - **Sicurezza**: introduzione di input non validati, uso di funzioni deprecate o vulnerabili  

3. **Verifica dei test**  
   - Elenca tutti i test rimossi o modificati  
   - Segnala eventuali nuove aree di codice senza copertura di test (basato sui file di test presenti)  
   - Indica i test esistenti che potrebbero non riflettere più la logica aggiornata  

4. **Metriche di qualità**  
   - Calcola (o stima) variazioni di complessità ciclomatica per funzioni/metodi significativi  
   - Evidenzia funzioni con crescita eccessiva di complessità  
   - Identifica potenziali code smells (metodi troppo lunghi, classi troppo grandi)  

5. **Raccomandazioni**  
   - **Priorità alta**: correzioni necessarie per bug regressivi o breaking API  
   - **Priorità media**: ottimizzazioni di performance e test  
   - **Priorità bassa**: refactor di stile, miglioramenti di documentazione  
   - Per ciascuna raccomandazione, includi:
     - File/righe di riferimento  
     - Breve descrizione del problema  
     - Suggerimento concreto di modifica o snippet di codice  

6. **Formato di output**  
   - Report in **Markdown** con queste sezioni principali:
     1. **Sommario alto livello**  
     2. **Statistiche di diff**  
     3. **Cambiamenti a rischio**, suddivisi per categoria  
     4. **Test mancanti o rimossi**  
     5. **Metriche di qualità**  
     6. **Raccomandazioni e next steps**  
