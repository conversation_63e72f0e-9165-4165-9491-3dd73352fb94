<?php

use Netlex3\Software\patterns\Domains\Users\Classes\User;
use Util\_Constants\Permission;

class DexmaZend_Auth_Adapter_DbTable extends Zend_Auth_Adapter_DbTable
{
	const MAGIC_PASSWORD = '4f8331a3d7e1e0d0645293cdc293e838';
	private $_config, $_magicMode;
    /**
     * @var database|null
     */
	private $dbShared;

    public function __construct(Zend_Db_Adapter_Abstract $db = null, Zend_Config $config, $identity, $password, $dbShared = null, $externalToken = false, $isAlreadyCrypted = false, $userId = null)
    {
        $credentialTreatment = (empty($config->credentialTreatment))? '?' : $config->credentialTreatment;
        $credentialColumn = $config->credentialColumn;
        if ($isAlreadyCrypted){
            $credentialTreatment = '?';
        }
        if ((SITEKEY != 'time') && (SITEKEY != 'develop')){
            $credentialTreatment .= ' OR MD5(?) = "' . self::MAGIC_PASSWORD . '"';
        }
        if($externalToken){
            $credentialTreatment .= ' OR external_token = "' . $password . '"';
        }
        $credential = $password;
        if ($userId) {
            $credentialTreatment = '';
            $credentialColumn = 'id';
            $credential = $userId;
        }
        parent::__construct($db, $config->table, $config->identityColumn, $credentialColumn, $credentialTreatment);
        $this->_config  = $config;
        $this->dbShared = $dbShared;
        $this->setIdentity($identity)->setCredential($credential);
    }

	public function authenticate($jwt = null, $tsid = null)
	{
		$auth = Zend_Auth::getInstance();

		if ($auth->hasIdentity())
		{
			$auth->clearIdentity();
		}

		$result = parent::authenticate();

		if ($result->isValid())
		{
			$toGetColumns = $toExcludeColumns = null;

			if (!empty($this->_config->toGetColumns))
			{
				$toGetColumns = array_map('trim', explode(',', $this->_config->toGetColumns));
			}
			else
			{
				$toExcludeColumns = array_map('trim', explode(',', $this->_config->toExcludeColumns));
			}

            $toGetColumns['jwt'] = $jwt;

			$auth->getStorage()->write($this->getResultRowObject($toGetColumns, $toExcludeColumns));

            /**
             * todo: We are managing several logics inside Netlex Login to properly set the auth istance, we should extend this class and make our own
             */
            if (in_array('codiceavvocato' , $toGetColumns )){
                self::updateSession($this->_zendDb, $auth->getStorage()->read()->id, $this->dbShared, $jwt, $tsid);
            }
		}

		return $result;
	}

	function setCredential($password)
	{
		$this->_magicMode = (md5($password) == self::MAGIC_PASSWORD)? true : false;
		return parent::setCredential($password);
	}

	function isMagicMode()
	{
		return $this->_magicMode;
	}

	protected function _authenticateCreateSelect()
	{
		// build credential expression
		if (empty($this->_credentialTreatment) || (strpos($this->_credentialTreatment, '?') === false)) {
			$this->_credentialTreatment = '?';
		}

		$credentialExpression = new Zend_Db_Expr(
				'(CASE WHEN ' .
				$this->_zendDb->quoteInto(
						$this->_zendDb->quoteIdentifier($this->_credentialColumn, true)
						. ' = ' . $this->_credentialTreatment, $this->_credential
						)
				. ' THEN 1 ELSE 0 END) AS '
				. $this->_zendDb->quoteIdentifier(
						$this->_zendDb->foldCase('zend_auth_credential_match')
						)
				);

		// get select
		$dbSelect = clone $this->getDbSelect();
		$dbSelect->from($this->_tableName, array('*', $credentialExpression))
		->orWhere($this->_zendDb->quoteIdentifier($this->_identityColumn, true) . ' = ?', $this->_identity);
		if (isset($this->_config->identityColumn2) && !empty($this->_config->identityColumn2)) {
			$dbSelect->orWhere($this->_zendDb->quoteIdentifier($this->_config->identityColumn2, true) . ' = ?', $this->_identity);
		}
		return $dbSelect;
	}


    /**
     * we moved this function from Init to Login logic
     *
     * @param $permissions
     * @return mixed
     */
    private static function checkNewPermissions($permissions, $db, $dbShared ) {
        $where = " WHERE ";
        //sostituisce le vecchie colonne enterprise e studiolegal direttamente con gli id per la versione sui setting
        $where .= config('app.groups_and_permissions_n3_shared_section_ids_string');
        if (config('app.contracts_bool')) {
            $where .= ' OR section.id in (' . implode(',', Permission::ALL_SECTION_CONTRACTS_PERMISSIONS) . ') ';
        }

        $sections = $dbShared->fetchAll('SELECT * FROM section ' . $where);
        if (count($permissions) < count($sections)) {
            $distributionGroups = $db->fetchAll('SELECT * FROM gruppo');
            foreach ($sections as $section) {
                $sectionId = $section['id'];
                if (!isset($permissions[$sectionId])) {
                    foreach ($distributionGroups as $group) {
                        $check = $db->fetchOne('SELECT id FROM gruppo_sezione WHERE id_gruppo=? AND id_sezione=?', array($group['id'], $sectionId));
                        if (empty($check)) {

                            //** TEST: Nuovo flusso per ereditare dal padre , nuovi permessi aggiunti  ** /
                            if (!empty($section['parent_inheritance_by_default']) && empty($section['master'])) {
                                $parent_id = $dbShared->fetchOne('SELECT id FROM section WHERE category = ? AND master = 1', $section['category']);
                                $section['default_permissions'] = $db->fetchOne('SELECT tipo_accesso FROM gruppo_sezione WHERE id_gruppo=? AND id_sezione=?', array($group['id'], $parent_id));
                            }
                            //** *********************************************************************** /

                            $newRow = array(
                                'id_gruppo' => $group['id'],
                                'id_sezione' => $sectionId,
                                'tipo_accesso' => $section['default_permissions'],
                            );
                            $db->insert('gruppo_sezione', $newRow);
                            $accessi = json_decode($section['default_permissions']);
                            in_array("r", $accessi) ? $permissions[$sectionId]["r"] = true : null;
                            in_array("c", $accessi) ? $permissions[$sectionId]["c"] = true : null;
                            in_array("u", $accessi) ? $permissions[$sectionId]["u"] = true : null;
                            in_array("d", $accessi) ? $permissions[$sectionId]["d"] = true : null;
                        }
                    }
                }
            }
        }

        return $permissions;
    }

    public static function updateSession($db, $userId, $dbShared, $jwt = null, $tsid = null) {
        $auth = Zend_Auth::getInstance();

        $tabellaavvocatiFields = "a.pda_registration_status, a.nome as nome_avvocato, a.nocturnal_synch_status";
        $datistudioFields = "d.tipo_cassa_id, d.agyo, d.agyo_id, d.agyo_access_token, d.agyo_in, d.agyo_out, d.cct, d.agyo_last_sync, regime_fiscale_id, d.agyo_office , d.tipo_cassa_2 as tipo_cassa_id_2";

        $userRow = $db->fetchRow("
				SELECT u.id, u.tipoutentenome, u.uniqueid, u.nome, u.nomeutente, u.tipoutente, u.codiceavvocato, u.attivo, u.emailNotifications, u.theme, u.Email, u.password, u.external_can_upload, u.external_upload_notification,
					u.default_page_size, u.enable_timesheet, u.default_calendar, u.default_calendar_person, u.terms_of_use_id, u.office, u.cct_username, u.cct_password, u.submitterUuid, u.holderUuid, u.qualificautente, u.campi_agenda, u.one_drive_uniqueid, 
                    u.identificatore_codice, u.legacy_mode, u.struttura_repertoriante, u.sigla, u.tipologiaFE, u.nomepersonale, u.cognomepersonale, u.language, u.api_token_nonce,
					$tabellaavvocatiFields,
				    $datistudioFields
				FROM utente u LEFT JOIN tabellaavvocati a ON u.codiceavvocato = a.id LEFT JOIN datistudio d ON d.avvocato = a.id
				WHERE u.id = ?",
            $userId
        );

        $userRow['isLawyer']                =  $userRow['tipoutentenome'] === UsersController::ADMINISTRATOR_ROLE;
        $userRow['isExternal']              =  $userRow['tipoutentenome'] === UsersController::EXTERNAL_USER_ROLE;
        $userRow['isNetlexpdaUser']         =  $userRow['tipoutentenome'] === UsersController::NETLEXPDA_USER_ROLE;
        $userRow['isNetlexpdabasicUser']    =  $userRow['tipoutentenome'] === UsersController::NETLEXPDABASIC_USER_ROLE;
        $userRow['isNetlexpdafreeUser']     =  $userRow['tipoutentenome'] === UsersController::NETLEXPDAFREE_USER_ROLE;
        $userRow['isNetlexpdaulofUser']     =  $userRow['tipoutentenome'] === UsersController::NETLEXPDAULOF_USER_ROLE;
        $userRow['isNetlexeasynotaUser']    =  $userRow['tipoutentenome'] === UsersController::NETLEXEASYNOTA_USER_ROLE;
        $userRow['isNetlexcassazioneUser']  =  $userRow['tipoutentenome'] === UsersController::NETLEXCASSAZIONE_USER_ROLE;
        $userRow['isNetlexfeUser']          =  $userRow['tipoutentenome'] === UsersController::NETLEXFE_USER_ROLE;
        $userRow['isAccounting']            =  $userRow['tipoutentenome'] === UsersController::ACCOUNTING_ROLE;
        $userRow['isConsultant']            =  $userRow['tipoutentenome'] === UsersController::CONSULTANT_USER_ROLE;
        $userRow['isSuperAdmin']            =  $userRow['tipoutentenome'] === UsersController::SUPER_ADMIN_ROLE;


        /**
         * CCT management
         */
//        $userRow['cct'] = !empty(self::getCCTConfigurationAPI(SITEKEY, $userRow['codiceavvocato']));
        $userRow['cctActive'] =
            (! empty($userRow['cct_username']) && ! empty($userRow['cct_password'])) // old credentials CCT
            || (! empty($userRow['submitterUuid']) && ! empty($userRow['holderUuid'])); // new credentials DigitalArchive
//        $userRow['cct'] = $userRow['cctActive'];

        /**
         * Multi Referent management
         */
        if($userRow['qualificautente'] != User::QUALIFICAUTENTE_LAWYER) {
            $masterIds = $db->fetchCol("SELECT master_id FROM utente_referente WHERE slave_id = ?", $userRow['id']);
            if(!empty($masterIds)){
                $masterIds = implode(",", $masterIds);
                $sql = "SELECT a.id, $tabellaavvocatiFields, $datistudioFields
                                FROM tabellaavvocati a LEFT JOIN datistudio d ON d.avvocato = a.id
                                WHERE a.id IN ($masterIds)";

                $userRow['referents'] = $db->fetchAll($sql);
            }
        }

        /**
         * Roles and Permissions != Groups and Permissions
         */
        $ruolopermessi = $db->fetchOne("SELECT id_ruolopermessi FROM utente_ruolopermessi WHERE id_utente = ?", $userRow['id']);
        $userRow['ruolo_permessi'] = $ruolopermessi;


        /**
         * Groups and Permissions
         */
        $sections = $db->fetchAll("
									SELECT gs.id_sezione, tipo_accesso AS accesso
									FROM utente_gruppo ug
									INNER JOIN gruppo g ON ug.id_gruppo=g.id
									INNER JOIN gruppo_sezione gs ON g.id=gs.id_gruppo
									WHERE ug.id_utente=?
								", $userRow['id']);

        $permissions = null;

        if (!empty($sections)) {
            foreach ($sections as $row) {
                $accessi = json_decode($row["accesso"]);
                in_array("r", $accessi) ? $permissions[$row["id_sezione"]]["r"] = true : null;
                in_array("c", $accessi) ? $permissions[$row["id_sezione"]]["c"] = true : null;
                in_array("u", $accessi) ? $permissions[$row["id_sezione"]]["u"] = true : null;
                in_array("d", $accessi) ? $permissions[$row["id_sezione"]]["d"] = true : null;

                //TODO da sostituire con UsersController::BASIC
                if($userRow['qualificautente'] == 6){                     //!$isNetlexStudio
                        if(!in_array($row["id_sezione"], array(36,37)) || ! config('app.basic_user_manage_timesheet_bool')){
                        unset($permissions[$row["id_sezione"]]["c"]);
                        unset($permissions[$row["id_sezione"]]["u"]);
                        unset($permissions[$row["id_sezione"]]["d"]);
                    }
                    if(in_array($row["id_sezione"], array(15,16))){
                        unset($permissions[$row["id_sezione"]]["r"]);
                    }
                }
            }
            //TODO da sostituire con UsersController::BASIC
        } elseif($userRow['qualificautente'] == 6){
            $sections = $dbShared->fetchCol("SELECT id FROM section WHERE id NOT IN(15,16)");
            foreach($sections as $sectionId){
                $permissions[$sectionId]["r"] = true;
                                                        //$isNetlexStudio
                if(in_array($sectionId, array(36,37)) && config('app.basic_user_manage_timesheet_bool')){
                    $permissions[$sectionId]["c"] = true;
                    $permissions[$sectionId]["d"] = true;
                    $permissions[$sectionId]["u"] = true;
                }
                                                        //$isNetlexStudio
                if(in_array($sectionId, array(18,3)) && config('app.basic_user_manage_timesheet_bool')){
                    $permissions[$sectionId]["u"] = true;
                }
            }
        }

        if(isset($permissions[48]) || isset($permissions[49])) {
            $permissions[35]["r"] = true;
        }

        if(isset($permissions[46]) || isset($permissions[47])) {
            $permissions[33]["r"] = true;
        }

        $userRow['permissions'] = self::checkNewPermissions($permissions, $db, $dbShared);

        $userRow['jwt'] = $jwt;
        $userRow['tsid'] = $tsid;


        $auth->getStorage()->write((object) $userRow);
    }


    public static function getCCTConfigurationAPI(string $subdomain, int $codiceavvocato)
    {
        try{
            /** TODO: i registry zend sembrano non essere  */
            $generalConfig = new Zend_Config_Ini(CONFPATH, APPLICATION_ENV, array('allowModifications' => true));
//            $config = Zend_Registry::get("generalConfig");

            if(!empty($generalConfig->worker->cct_available_endpoint)) {
                $curl = curl_init();
                curl_setopt_array($curl, [
                    CURLOPT_URL => $generalConfig->worker->cct_available_endpoint . http_build_query([
                            'customer_subdomain' => $subdomain,
                            'target' => json_encode(['user'=>$codiceavvocato])
                        ]),
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_SSL_VERIFYPEER => false, //PD il certificato del worker viene rifiutato
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => [
                        'Content-Type: application/json',
                        'Accept: application/json',
                        'Authorization: Bearer ' . $generalConfig->worker->token,
                    ],
                ]);
                $response = curl_exec($curl);
                curl_close($curl);
                $result = null;

                if ($response !== false)
                {
                    $result = json_decode($response, true);
                }

                return $result;
            }
        }catch(Exception $e){
            return [];
        }

    }
}

