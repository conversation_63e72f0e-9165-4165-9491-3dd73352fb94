<?php
class DexmaZend_Acl extends Zend_Acl
{
    public function __construct($config, $request = null)
    {
        if (!is_array($config)) {
            if ($config instanceof Zend_Config) {
                $config = $config->toArray();
            } else {
                throw new Zend_Acl_Exception('$config must be in an array or a Zend_Config object');
            }
        }

        $options = $config['options'];
        $fastMode = (null !== $request  && array_key_exists('fastMode', $options) && true == $options['fastMode']);

        foreach ($config['roles'] as $role => $parents) {
            if (!$this->has($role)) {
                if (!empty($parents)) {
                    $parents = array_map('trim', explode(',', $parents));
                    $this->addRole($role, $parents);
                } else {
                    $this->addRole($role);
                }
            }
        }

        foreach ($config['resources'] as $permission => $modules) {
            foreach ($modules as $module => $controllers) {
                if ($fastMode && $request->getModuleName() !== $module) {
                    continue;
                }

                foreach ($controllers as $controller => $actions) {
                    if ($fastMode && $request->getControllerName() !== $controller) {
                        continue;
                    }

                    $resource = strtolower("$module-$controller");

                    if (!$this->has($resource)) {
                        $this->addResource($resource);
                    }

                    foreach ($actions as $action => $role) {
                        $action = strtolower($action);

                        if ('all' === $action) {
                            $action = null;
                        }

                        if ($action !== null && $fastMode && $request->getActionName() !== $action) {
                            continue;
                        }

                        if ('allow' === strtolower($permission)) {
                            $this->allow($role, $resource, $action);
                        } else {
                            $this->deny($role, $resource, $action);
                        }
                    }
                }
            }
        }
    }
}
