<?php

require_once('Utils/DbStorageAdapter.php');
require_once('Utils/ValidityCheckerSingleton.php');
require_once('Utils/DisconectService.php');

class DexmaZend_Controller_Plugin_SimpleGridMysqlFirewall extends Zend_Controller_Plugin_Abstract
{
	/** @var array A blacklist array containing params to pars, type checks, possible values */
	private $blacklist = [  'page'=>['int'=>[]],
		'pageSize'=>['int'=>[]],
		'sortColumn'=>['word'=>[]],
		'sortOrder'=>['word'=>['asc','desc', 'ASC', 'DESC']],
	];

	/** @var array request array */
	private $params;

	/** @var ValidityCheckerSingleton|null  */
	private $singleton;

    /** @var int */
    private $maxAttempts = 7;


	/**
	 * DexmaZend_Controller_Plugin_SimpleGridMysqlFirewall constructor.
	 */
	public function __construct(DexmaZend_Db_Adapter_Mysql $database)
	{
		$this->params = $_REQUEST;

		/** Singleton dependency Injection */
		$this->singleton = ValidityCheckerSingleton::getInstance($database, $this->maxAttempts);

		/** TODO: Do we need whitelisting some actions?  */
	}


	/**
	 * Cycle through the params and check if they are valid
	 *
	 * @param Zend_Controller_Request_Abstract $request
	 */
	public function preDispatch(Zend_Controller_Request_Abstract $request): void
	{
		foreach ($this->params as $key => $val) {
			if (!empty($this->blacklist[$key]) && !empty(($val))) {
				$type = $this->blacklist[$key];
				$this->singleton->check($type, $val);
			}
		}

		if ( $this->singleton->getMistakes())
		{
            $invokable = new DisconectService();
			try {
                $this->singleton->incrementAttempts();

				if ($this->singleton->isLocked()){
					$invokable($this, true, true);
				}
			} catch (Exception $e) {
                $invokable($this, true);
			}
            $invokable($this);
		}

	}

}





