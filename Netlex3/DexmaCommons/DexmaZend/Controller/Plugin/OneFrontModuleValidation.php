<?php

declare(strict_types=1);

class DexmaZend_Controller_Plugin_OneFrontModuleValidation extends Zend_Controller_Plugin_Abstract
{
    public function preDispatch(Zend_Controller_Request_Abstract $request)
    {
        $auth = Zend_Auth::getInstance();
        if ($auth->hasIdentity() && isRequestFromOneFront() && ! config('modules.onefront_enabled')) {
            http_response_code(401);
            echo json_encode(array(
                'error' => 'unauthorized',
                'error_description' => 'Modulo OneFront disabilitato',
            ));
            exit;
        }

        return;
    }
}
