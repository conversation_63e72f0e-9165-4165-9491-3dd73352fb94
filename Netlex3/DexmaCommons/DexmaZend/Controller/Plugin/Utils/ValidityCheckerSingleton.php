<?php

class ValidityCheckerSingleton
{
    /** @var DbStorageAdapter an adpater layer to save data */
    private $storageAdapter;

    /** @var string Defines string type for is numeric checks */
    private const INT_TYPE = 'int';

    /** @var string Defines string type for is single word string checks */
    private const WORD_TYPE = 'word';

    /** @var int Counter to store attempts */
    private $mistakes = 0;

    /** @var int Max number of attempts */
    private $maxAttempts;

    /** @var int Number of current attempts */
    private $attempts;

    /** @var int Timeout in minutes per attempt */
    private $minPerAttempt;

    /** @var int Max length for a single word */
    private $maxWordLen = 30;

    /** @var DisconectService  */
    private $disconectService;

    /**
     *  Singleton istance
     *
     * @param null $storage
     * @param int|null $maxAttempts
     * @param int|null $minPerAttempt
     * @return ValidityCheckerSingleton|null
     */
    public static function getInstance($storage = null, int $maxAttempts = 0, int $minPerAttempt = 1): ?ValidityCheckerSingleton
    {
        static $instance = null;

        if (null === $instance) {
            $instance = new ValidityCheckerSingleton($storage, $maxAttempts, $minPerAttempt);
        }

        return $instance;
    }

    /**
     * ValidityCheckerSingleton constructor usable by instance itself only.
     * @param $storage
     * @param int $maxAttempts
     * @param int $minPerAttempt
     */
    protected function __construct($storage, int $maxAttempts = 0, int $minPerAttempt = 1)
    {
        if ($storage instanceof DexmaZend_Db_Adapter_Mysql) {
            $this->storageAdapter = new DbStorageAdapter($storage, 'utente', 'login_attempts');
        }

        $this->maxAttempts = $maxAttempts;

        $this->minPerAttempt = $minPerAttempt;

    }

    /**
     * Check if user is allowed to perform action SQL injection
     *
     * @param array $type
     * @param string|int $val
     * @return bool
     */
    public function check(array $type, $val): bool
    {
        switch (key($type)) {

            case self::INT_TYPE:
                if (!is_numeric($val) ||
                    (!empty($type[self::INT_TYPE]) && !in_array($val, $type[self::INT_TYPE], true)))
                {
                    $this->mistakes++;
                    return false;
                }

                break;

            case self::WORD_TYPE:
                if ((!is_string($val) || (strpos($val, ' ') !== false) || (strlen($val) > $this->maxWordLen)) ||
                    (!empty($type[self::WORD_TYPE]) && !in_array($val, $type[self::WORD_TYPE], true)))
                {
                    $this->mistakes++;
                    return false;
                }

                break;

            default:

                break;

        }

        return true;
    }

    /**
     * Check if too many attempts are made locking the account login
     * comparing a response with an expected value
     *
     * @param string $response
     * @param string $expected
     * @return void
     */
    public function wrongAttemptsCheck(string $response, string $expected): void
    {
        $attempts = $this->getAttempts();

        if ($response != $expected) {
            $this->storageAdapter->store($attempts+1);
        }
    }

    /**
     * Increment attempts counter
     *
     * @return void
     */
    public function incrementAttempts(): void
    {
        $attempts = $this->getAttempts();
        $newAttempts = $attempts + 1;
        
        $this->storageAdapter->store($newAttempts);

        $this->setAttempts($newAttempts);
    }

    /**
     * Return if attempts have passed the threshold
     *
     * @return bool
     */
    public function isLocked(): bool
    {
        $attempts = $this->getAttempts();

        return $attempts >= $this->maxAttempts;
    }

    /**
     * Retrieve attempts counter
     *
     * @return int
     */
    public function getMistakes(): int
    {
        return $this->mistakes;
    }


    /**
     * Store attempts counter
     *
     * @return void
     * @throws Exception
     */
    public function triggerThrottle(): void
    {
        if (($exceedAttempts = $this->mistakes - $this->maxAttempts) > 0)
        {
            $minutes_to_add = $exceedAttempts * $this->minPerAttempt;
            $time = (new DateTime())->add(new DateInterval('PT' . $minutes_to_add . 'M'));

            $this->storageAdapter->store($time->format('Y-m-d H:i:s'));
        }

    }

    /**
     * @param int $maxAttempts
     */
    public function setMaxAttempts(int $maxAttempts): void
    {
        $this->maxAttempts = $maxAttempts;
    }


    /**
     * Getter for number of attempts, if not set it will be retrieved from storage
     *
     * @return int
     */
    private function getAttempts(): int
    {
        if (!$this->attempts)
        {
            $this->attempts = $this->storageAdapter->getAttempts();
        }

        return $this->attempts;
    }

    private function setAttempts(int $attempts): void
    {
        $this->attempts = $attempts;
    }

}