<?php

use Netlex3\Software\patterns\Domains\Login\Services\LoginService;
use Netlex3\Software\patterns\Domains\Login\Services\LoginServiceInterface;

class DisconectService {
    /** @var LoginServiceInterface */
    private $loginService;
    
    public function __construct()
    {
        $this->loginService = LoginService::getInstance();
    }
    /**
     * Returns a 403 and error => Forbidden response or disconect user
     */
    public function __invoke(&$flow, bool $forceLogout = false, bool $sendTicket = false): void
    {
        try {
            if ($forceLogout) {
                throw new Zend_Session_Exception('Forced logout');
            }

            $flow->getResponse()->setHttpResponseCode(403);
            $flow->getResponse()->setHeader('Content-Type', 'application/json');
            $flow->getResponse()->setBody(json_encode(['error'=>'Forbidden']));
            $flow->getResponse()->sendResponse();
            exit;
        } catch (Exception $e) {
            if ($sendTicket) {
                $this->loginService->insertTicket(Zend_Auth::getInstance()->getIdentity()->nome);
            }
            /** Clear session and kill connection */
            Zend_Auth::getInstance()->clearIdentity();
            Zend_Session::destroy();
            exit;
        }
    }
}