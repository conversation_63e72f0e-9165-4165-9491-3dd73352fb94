<?php

require_once('DbStorageAdapter.php');


class DbStorageAdapter
{
    /** @var DexmaZend_Db_Adapter_Mysql  */
    private $storage;
    /** @var string  */
    private $table;
    /** @var string  */
    private $column;
    /** @var mixed|null  */
    private $user;

    public function __construct(DexmaZend_Db_Adapter_Mysql $storage, string $table, string $name)
    {
        $this->storage = $storage;
        $this->table = $table ?? 'utente';
        $this->column = $name ?? 'throttle';
        $this->user = Zend_Auth::getInstance()->getIdentity();
    }

    /**
     * @return DexmaZend_Db_Adapter_Mysql
     */
    public function getStorage(): DexmaZend_Db_Adapter_Mysql
    {
        return $this->storage;
    }

    /**
     * Store throttle data or expected column inside the storage
     *
     * @param $data
     * @return bool
     */
    public function store($data): bool
    {
        try{
            return $this->storage->update($this->table, [$this->column => $data], ['id = ?' => $this->user->id]);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get number of attempts from storage
     *
     * @return int
     */
    public function getAttempts(): int
    {
        return $this->storage->fetchOne("SELECT {$this->column} FROM {$this->table} WHERE id = ?",  $this->user->id);
    }

}