<?php

declare(strict_types=1);

// phpcs:disable
class DexmaZend_Controller_Plugin_MobileAppEnabled extends Zend_Controller_Plugin_Abstract
// phpcs:enable
{
    /** @var Zend_Config */
    private $config;

    public function __construct()
    {
        $this->config = Zend_Registry::get('generalConfig');
    }

    public function preDispatch(Zend_Controller_Request_Abstract $request): void
    {
        if (! $request instanceof Zend_Controller_Request_Http) {
            return;
        }

        if (! isRequestFromTslegalApp()) {
            return;
        }

        if (! config('app.mobile_app_enabled_bool')) {
            http_response_code(503);
            echo json_encode([
                'error' => 'not enabled',
                'error_description' => 'app is not  enabled',
                'app_error_title' => 'App non abilitata',
                'app_error_description' => 'Non hai il modulo App abilitato per la tua distribuzione',
            ]);
            die;
        }
    }
}
