<?php
class DexmaZend_Controller_Plugin_AntiBruteforce extends Zend_Controller_Plugin_Abstract {
    /**
     * @var array $blacklist
     */
    private $blacklist = array();

    /** @var ValidityCheckerSingleton|null  */
    private $singleton;

    /** TODO: this should be aligned with the login threshold */
    private $maxAttempts = 7;


    public function __construct(DexmaZend_Db_Adapter_Mysql $database)
    {
        /** TODO: maybe it could help introducing a regex like case */
        $this->blacklist[] = (object) array('controller' => 'index',
                                            'action' => 'backuprequest',
                                            'expected' => '1'
                                            );
        /** @var ValidityCheckerSingleton we get the singleton already initialised by other plugins in case */
        $this->singleton = ValidityCheckerSingleton::getInstance($database, $this->maxAttempts);

    }

    /**
     * @param Zend_Controller_Request_Abstract $request
     * @throws Exception
     */
    public function preDispatch(Zend_Controller_Request_Abstract $request): void
    {

        if ($request->getMethod() === 'POST') {

            $obj = $this->getBlacklistedObj($request);

            /** Let's set the maxattempts limit for the current Operation */
            $this->singleton->setMaxAttempts($this->maxAttempts);

            /**
             * If the controller is contained in the blacklist, check if max attempts has been reached
             */
            if (count($obj) && $this->singleton->isLocked()){
                (new DisconectService())($this, true);
            }
        }
    }

    /**
     * @param Zend_Controller_Request_Abstract $request
     */
    public function postDispatch(Zend_Controller_Request_Abstract $request): void
    {
        if ($request->getMethod() === 'POST') {

            $obj = $this->getBlacklistedObj($request);

            if (count($obj)) {
                /** If obj is blacklisted we should check if response is the expected one */
                $this->singleton->wrongAttemptsCheck($this->getResponse()->getBody(), $obj[0]->expected);
            }
        }
    }


    /**
     * Tells us if the controller and action are contained in the blacklist
     *
     * @param Zend_Controller_Request_Abstract $request
     * @return array
     */
    private function getBlacklistedObj(Zend_Controller_Request_Abstract $request): array
    {
        $controllerName = $request->getControllerName();
        $currentAction = $request->getActionName();

        return array_filter($this->blacklist, static function($obj) use ($controllerName, $currentAction) {
            if ($controllerName === $obj->controller && $currentAction === $obj->action){
                return $obj;
            }
        });
    }
}


