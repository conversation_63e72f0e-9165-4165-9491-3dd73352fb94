<?php

declare(strict_types=1);

// phpcs:disable
class DexmaZend_Controller_Plugin_ConfigVersionControlMiddleware extends Zend_Controller_Plugin_Abstract
{
    // phpcs:enable

    public function preDispatch(Zend_Controller_Request_Abstract $request): void
    {
        if (! isRequestFromN4() || haveConfigUpdated()) {
            return;
        }

        $viewRenderer = $this->getViewRender();
        $configs = config();
        $viewRenderer->view->configs = $configs;
    }

    public function postDispatch(Zend_Controller_Request_Abstract $request): void
    {
        $response = $this->getResponse();

        // Add config to response only if request comes from TS Legal App and config has been updated
        if (! isRequestFromTslegalApp() || haveConfigUpdated()) {
            return;
        }

        $config = config();

        // filtrare solo quelle che hanno un valore booleano oppure la chiave è uuid, così da rimuovere info sensibili per il MAPT
        $config['app'] = array_filter($config['app'], static function ($value, $key) {
            return is_bool($value) || $key === 'uuid';
        }, ARRAY_FILTER_USE_BOTH);

        $body = json_decode($response->getBody(), true);

        $mergedBody = array_merge($body, ['configs' => $config]);

        $response->setHeader('Content-Type', 'application/json');
        $response->setBody(json_encode($mergedBody));
    }

    protected function getViewRender(): Zend_Controller_Action_Helper_Abstract
    {
        try {
            $viewRenderer = Zend_Controller_Action_HelperBroker::getExistingHelper('viewRenderer');
        } catch (Throwable $e) {
            $viewRenderer = Zend_Controller_Action_HelperBroker::getStaticHelper('viewRenderer');
        }

        return $viewRenderer;
    }
}
