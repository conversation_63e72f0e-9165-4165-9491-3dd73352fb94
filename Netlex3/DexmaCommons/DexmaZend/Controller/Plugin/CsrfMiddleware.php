<?php
class DexmaZend_Controller_Plugin_CsrfMiddleware extends Zend_Controller_Plugin_Abstract {
    /**
     * @var array $whiteList
     */
    private $whiteList = array();


    function __construct()
    {
        /**
         * Whitelisted controllers and actions
         * - If a controller is present AND it doesn't specify any action => all its actions are whitelisted
         * - If a controller is present AND it does specify some actions  => such specific actions are whitelisted
         */
        $this->whiteList[] = (object) array('controller' => 'api');
        $this->whiteList[] = (object) array('controller' => 'api-v2');
        $this->whiteList[] = (object) array('controller' => 'oauth');
        $this->whiteList[] = (object) array('controller' => 'apisynchronizer');
        $this->whiteList[] = (object) array('controller' => 'one-front');
        $this->whiteList[] = (object) array('controller' => 'polisweb');
        $this->whiteList[] = (object) array('controller' => 'archivepolisweb');
        $this->whiteList[] = (object) array('controller' => 'mailbox');
        $this->whiteList[] = (object) array('controller' => 'archivebudgetliquidazione');
		$this->whiteList[] = (object) array('controller' => 'notifications');
        $this->whiteList[] = (object) array('controller' => 'saml');

        $this->whiteList[] = (object) array(
                'controller' => 'antirecregister',
                'actions' => array(
                    'create-register',
                    'download-register'
                )
            );

        $this->whiteList[] = (object) array(
                'controller' => 'anagrafiche',
                'actions' => array(
                    'prepareaddtofileAction'
                )
            );
        $this->whiteList[] = (object) array(
                'controller' => 'archive',
                'actions' => array(
                    'save',
                    'checkduplicated'
                )
            );
        $this->whiteList[] = (object) array(
                'controller' => 'emailaccounts',
                'actions' => array(
                    'save'
                )
            );
        $this->whiteList[] = (object) array(
                'controller' => 'fatture',
                'actions' => array(
                    'sendpin',
                    'confirmpin'
                )
            );
        $this->whiteList[] = (object) array(
                'controller' => 'paymentreminder',
                'actions' => array(
                    'generate-lettera-pdf',
                    'send-by-email'
                )
            );
        $this->whiteList[] = (object) array(
            'controller' => 'archivebudgetliquidazione',
            'actions' => array(
                'getbudget',
                'getextrabudget',
                'getliquidazione',
                'xmlreader'
            )
        );

         $this->whiteList[] = (object) array(
            'controller' => 'archivecreditrecovery',
            'actions' => array(
                'generate-diffida-pdf'
            )
        );



        $this->whiteList[] = (object) array(
            'controller' => 'documents',
            'actions' => array(
                'savefolder'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'instructors',
            'actions' => array(
                'remotesave'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'agendadeadlines',
            'actions' => array(
                'save'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'archiveagenda',
            'actions' => array(
                'save'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'digitalsignature',
            'actions'    => array(
                'signature-events'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'archivenotifications',
            'actions'    => array(
                'saverelata'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'archivedetails',
            'actions'    => array(
                'submit-modify-item',
                'create-item'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'Archivepayments',
            'actions'    => array(
                'list',
                'insert'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'archiveinfocamere',
            'actions'    => array(
                'saveAction'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'archiveinfocamere',
            'actions'    => array(
                'webservicecall'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'utilities',
            'actions'    => array(
                'saveresultsscorporo'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'messages',
            'actions'    => array(
                'getrowdata',
                'sendmessage'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'filestypes',
            'actions'    => array(
                'remotesave'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'archive-profits-distribution',
            'actions'    => array(
                'get-users-for-basket'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'pda',
            'actions'    => array(
                'verify-user-data'
            )
        );

        $this->whiteList[] = (object) array(
            'controller' => 'deadlines',
            'actions'    => array('getrowdata')
        );

    }

    /**
     * @param Zend_Controller_Request_Abstract $request
     * @throws Exception
     */
    public function preDispatch(Zend_Controller_Request_Abstract $request) {
        if (isRequestFromOneFront()) {
            return;
        }

        if ($request->getMethod() === 'POST') {
            $controllerName = $request->getControllerName();
            $actionName = $request->getActionName();
            $currentControllerAction = null;

            /*
             * Check whitelisted items that are eligible to bypass CSRF check
             */
            foreach ($this->whiteList as $item) {
                if ($controllerName == $item->controller) {
                    /**
                     * Se l’oggetto NON ha la proprietà `actions`
                     *     ⇒ tutte le azioni sono whitelisted
                     * Se la proprietà esiste MA è vuota
                     *     ⇒ idem, tutte whitelisted
                     * Se invece contiene un array non vuoto
                     *     ⇒ verifichiamo che l’azione corrente sia in elenco
                     */
                    if (!property_exists($item, 'actions') || empty($item->actions)) {
                        $currentControllerAction = $item;
                        break;
                    }

                    if (in_array($actionName, $item->actions)) {
                        $currentControllerAction = $item;
                        break;
                    }
                }
            }


            if(is_null($currentControllerAction)) {
                $security_flag = false;
                /*
                 * Ajax request
                 */
                if ($request->isXmlHttpRequest() && (empty($request->getHeader('CSRF-TOKEN')) || $request->getHeader('CSRF-TOKEN') != $_SESSION['CSRF-TOKEN'])) {
                    $security_flag = true;
                }
                /*
                 * Form request
                 */
                if (!$request->isXmlHttpRequest() && (empty($_POST['CSRF-TOKEN']) || $_POST['CSRF-TOKEN'] != $_SESSION['CSRF-TOKEN'])) {
                    $security_flag = true;
                }

                if ($security_flag) {
                    throw new Exception('CSRF token mismatch calling ' . $request->getRequestUri() . ' url.');
                } else {
                    /*
                     * Remove CSRF token from post params once it's been validated
                     */
                    if (isset($_POST['CSRF-TOKEN'])) {
                        unset($_POST['CSRF-TOKEN']);
                    }
                }
            }
        }
   }
}

