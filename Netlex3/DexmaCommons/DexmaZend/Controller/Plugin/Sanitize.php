<?php class DexmaZend_Controller_Plugin_Sanitize extends Zend_Controller_Plugin_Abstract
{
    //private $_config;

    /**
     * DEXMA PATH
     */
    private $dexmaPath;
    private $whiteList;
    /**
     * DexmaZend_Controller_Plugin_Sanitize constructor.
     */
    function __construct($dexmaPath = '/home/<USER>/DexmaCommons')
    {
        /*if (!is_array($config))
        {
            if ($config instanceof Zend_Config)
            {
                $config = $config->toArray();
            }
            else
            {
                throw new Zend_Acl_Exception('$config must be in an array or a Zend_Config object');
            }
        }
        $this->_config = $config;*/
        $this->dexmaPath = $dexmaPath;

        $this->whiteList = array();
//				$this->whiteList[] = (object) array('controller' => '', 'action' => '');	// helk indica controller ed action (empty)
        $this->whiteList[] = (object) array('controller' => 'custom', 'action' => 'open-pdf-base64');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'save-deadline');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'save-file');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'save-events');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'save-master-detail');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'end-sync');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'clear-running-request');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'proxy-pda');
        $this->whiteList[] = (object) array('controller' => 'polisweb', 'action' => 'searchoffices');
        $this->whiteList[] = (object) array('controller' => 'deadlines', 'action' => 'save');
//        $this->whiteList[] = (object) array('controller' => 'deadlines', 'action' => 'itemlist');
        $this->whiteList[] = (object) array('controller' => 'archivepolisweb', 'action' => 'save-document');
//        $this->whiteList[] = (object) array('controller' => 'archivepolisweb', 'action' => 'documentslist');
        $this->whiteList[] = (object) array('controller' => 'archivefatturecomplex', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archivefatturecomplex', 'action' => 'savemovement');
        $this->whiteList[] = (object) array('controller' => 'archivefatturecomplex', 'action' => 'updatefe');
        $this->whiteList[] = (object) array('controller' => 'archivefatturecomplex', 'action' => 'senddocument');
        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'save');
//        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'checkduplicated');
        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'searchoffices');
        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'search-oggetto-pratica');
        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'savedinamic');
        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'getavailabletags');
        $this->whiteList[] = (object) array('controller' => 'archive', 'action' => 'exportcsv');
        $this->whiteList[] = (object) array('controller' => 'archivepdapayment', 'action' => 'save-receipt');
        $this->whiteList[] = (object) array('controller' => 'archivepdapayment', 'action' => 'update');
        $this->whiteList[] = (object) array('controller' => 'archivepdapayment', 'action' => 'save-Crs');
        $this->whiteList[] = (object) array('controller' => 'archivepdapayment', 'action' => 'save');
//        $this->whiteList[] = (object) array('controller' => 'onedrive', 'action' => 'get-token');
//        $this->whiteList[] = (object) array('controller' => 'onedrive', 'action' => ''); 	// su helk indica action (empty)
        $this->whiteList[] = (object) array('controller' => 'archivepct', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archivepct', 'action' => 'save-done-step');
        $this->whiteList[] = (object) array('controller' => 'archivepct', 'action' => 'getrolenotificationnote');
        $this->whiteList[] = (object) array('controller' => 'archivepct', 'action' => 'summary');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'upload-document');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'send-error-mail');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'fascicolo-sicid');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'fascicolo-siecic');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'verify-user-data');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'fascicolo-sigp');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'salva-ricerca-ajax');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'archive-files');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'registration');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'save-anag');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'cass-ricorsi');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'archives');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'professionals');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'judgments');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'deadlines');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'index');
        $this->whiteList[] = (object) array('controller' => 'pda', 'action' => 'public-adm');
//        $this->whiteList[] = (object) array('controller' => 'js', 'action' => 'ver783');	// su helk indica ver783. Va parametrizzata l'action?
        $this->whiteList[] = (object) array('controller' => 'agenda', 'action' => 'search-attivita');
        $this->whiteList[] = (object) array('controller' => 'agenda', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archivedeadlines', 'action' => 'save');
//        $this->whiteList[] = (object) array('controller' => 'archivedeadlines', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'login', 'action' => 'passwordreset');
        $this->whiteList[] = (object) array('controller' => 'login', 'action' => 'login');
        $this->whiteList[] = (object) array('controller' => 'login', 'action' => 'password-setup');
        $this->whiteList[] = (object) array('controller' => 'archiveagenda', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'anagrafiche', 'action' => 'fiscal-code');
        $this->whiteList[] = (object) array('controller' => 'anagrafiche', 'action' => 'update');
//        $this->whiteList[] = (object) array('controller' => 'anagrafiche', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'anagrafiche', 'action' => 'checkdata');
        $this->whiteList[] = (object) array('controller' => 'anagrafiche', 'action' => 'insert-anagrafiche-impegni');
        $this->whiteList[] = (object) array('controller' => 'anagrafiche', 'action' => 'aggiornatags');
        $this->whiteList[] = (object) array('controller' => 'anagrafiche', 'action' => '');	// su helk indica action (empty)
        $this->whiteList[] = (object) array('controller' => 'archivenotifications', 'action' => 'buildrelata');
        $this->whiteList[] = (object) array('controller' => 'archivenotifications', 'action' => 'saverelata');
        $this->whiteList[] = (object) array('controller' => 'archivenotifications', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'fatturecomplex', 'action' => 'savemovement');
        $this->whiteList[] = (object) array('controller' => 'fatturecomplex', 'action' => 'updatefe');
        $this->whiteList[] = (object) array('controller' => 'fatturecomplex', 'action' => 'jsonlogger');
        $this->whiteList[] = (object) array('controller' => 'fatturecomplex', 'action' => 'senddocument');
        $this->whiteList[] = (object) array('controller' => 'fatturecomplex', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archivemessages', 'action' => 'sendmessage');
        $this->whiteList[] = (object) array('controller' => 'helpdesk', 'action' => 'update');
        $this->whiteList[] = (object) array('controller' => 'saml', 'action' => 'microsoft-ad');
        $this->whiteList[] = (object) array('controller' => 'microsoft-ad', 'action' => 'onlinesave');
//        $this->whiteList[] = (object) array('controller' => 'microsoft-ad', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'microsoft-ad', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'archivetimesheet');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'timesheet');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'archive');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'mailbox');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'archivepct');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'fatturepa');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'archivenotifications');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'fatture');
        $this->whiteList[] = (object) array('controller' => 'default', 'action' => 'archivenir');
        $this->whiteList[] = (object) array('controller' => 'documents', 'action' => 'savefolder');
        $this->whiteList[] = (object) array('controller' => 'documents', 'action' => 'onlinesave');
        $this->whiteList[] = (object) array('controller' => 'archivedocuments', 'action' => 'onlinesave');
        $this->whiteList[] = (object) array('controller' => 'documents', 'action' => 'getfile');
//        $this->whiteList[] = (object) array('controller' => 'documents', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'archivemailbox', 'action' => 'save-data');
        $this->whiteList[] = (object) array('controller' => 'archivemailbox', 'action' => 'get-attachment');
        $this->whiteList[] = (object) array('controller' => 'archivemailbox', 'action' => 'detail');
        $this->whiteList[] = (object) array('controller' => 'archivemailbox', 'action' => 'delete');
        $this->whiteList[] = (object) array('controller' => 'error', 'action' => 'error-managed');
        $this->whiteList[] = (object) array('controller' => 'mailbox', 'action' => 'send-email');
        $this->whiteList[] = (object) array('controller' => 'mailbox', 'action' => 'get-available-emails');
        $this->whiteList[] = (object) array('controller' => 'mailbox', 'action' => 'get-available-archive-emails');
        $this->whiteList[] = (object) array('controller' => 'mailbox', 'action' => 'getmessages');
        $this->whiteList[] = (object) array('controller' => 'mailbox', 'action' => 'openmessage');
        $this->whiteList[] = (object) array('controller' => 'getmessages', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archiveanagrafiche', 'action' => 'aggiungiresponsabile');
        $this->whiteList[] = (object) array('controller' => 'archiveanagrafiche', 'action' => 'addutentepratica');
        $this->whiteList[] = (object) array('controller' => 'remotesign', 'action' => 'sign');
        $this->whiteList[] = (object) array('controller' => 'users', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'nocturnalpolisweb', 'action' => 'add-office');
//        $this->whiteList[] = (object) array('controller' => 'feesitems', 'action' => 'itemlist');
        $this->whiteList[] = (object) array('controller' => 'cities', 'action' => 'checkdata');
        $this->whiteList[] = (object) array('controller' => 'cities', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'peopleprefees', 'action' => 'savemovement');
        $this->whiteList[] = (object) array('controller' => 'peopleprefees', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'messages', 'action' => 'sendmessage');
        $this->whiteList[] = (object) array('controller' => 'authorities', 'action' => 'setpreferita');
        $this->whiteList[] = (object) array('controller' => 'authorities', 'action' => 'remotesave');
//        $this->whiteList[] = (object) array('controller' => 'authorities', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'authorities', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'api-v2', 'action' => 'emails-storage-raw');
        $this->whiteList[] = (object) array('controller' => 'api-v2', 'action' => 'document');
        $this->whiteList[] = (object) array('controller' => 'cerved', 'action' => 'search-results');
//        $this->whiteList[] = (object) array('controller' => 'archivedetails', 'action' => 'itemlist');
        $this->whiteList[] = (object) array('controller' => 'archivedetails', 'action' => 'create-item');
        $this->whiteList[] = (object) array('controller' => 'archivedetails', 'action' => 'submit-modify-item');
        $this->whiteList[] = (object) array('controller' => 'peoplecenter', 'action' => 'getaddresskey');
        $this->whiteList[] = (object) array('controller' => 'paymentreminder', 'action' => 'generate-lettera-pdf');
        $this->whiteList[] = (object) array('controller' => 'certificates', 'action' => 'get-subscription-file');
        $this->whiteList[] = (object) array('controller' => 'certificates', 'action' => 'send-subscription');
        $this->whiteList[] = (object) array('controller' => 'agendadeadlines', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'clauses', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archiveexpensereportcomplex', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archiveexpensereportcomplex', 'action' => 'savemovement');
        $this->whiteList[] = (object) array('controller' => 'macro', 'action' => 'savedetails');
        $this->whiteList[] = (object) array('controller' => 'macro', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archiveriskmanagement', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'apisynchronizer', 'action' => 'document-report');
        $this->whiteList[] = (object) array('controller' => 'archivecreditrecovery', 'action' => 'export-csv');
        $this->whiteList[] = (object) array('controller' => 'archivecreditrecovery', 'action' => 'generate-diffida-pdf');
        $this->whiteList[] = (object) array('controller' => 'fatture', 'action' => 'saveNew');
        $this->whiteList[] = (object) array('controller' => 'fatture', 'action' => 'confirmpin');
        $this->whiteList[] = (object) array('controller' => 'fatture', 'action' => 'sendpin');
//        $this->whiteList[] = (object) array('controller' => 'fatture', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'objects', 'action' => 'checkdata');
        $this->whiteList[] = (object) array('controller' => 'objects', 'action' => 'checkdata');
//        $this->whiteList[] = (object) array('controller' => 'objects', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'objects', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'officesettings', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'letterheads', 'action' => 'preview');
        $this->whiteList[] = (object) array('controller' => 'letterheads', 'action' => 'update');
        $this->whiteList[] = (object) array('controller' => 'search', 'action' => '');
        $this->whiteList[] = (object) array('controller' => 'search', 'action' => 'search');
        $this->whiteList[] = (object) array('controller' => 'proceduralroles', 'action' => 'remotesave');
        $this->whiteList[] = (object) array('controller' => 'emailaccounts', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'peoplefees', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'api', 'action' => 'upload-document');
        $this->whiteList[] = (object) array('controller' => 'instructors', 'action' => 'remotesave');
        $this->whiteList[] = (object) array('controller' => 'instructors', 'action' => 'remotesave');
        $this->whiteList[] = (object) array('controller' => 'archiveppt', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'campidinamici', 'action' => 'salvacampo');
        $this->whiteList[] = (object) array('controller' => 'payments', 'action' => 'payments');
        $this->whiteList[] = (object) array('controller' => 'payments', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archiveperformances', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'archiveinfocamere', 'action' => 'webservicecall');
        $this->whiteList[] = (object) array('controller' => 'template', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'crimes', 'action' => 'remotesave');
        $this->whiteList[] = (object) array('controller' => 'categorie', 'action' => 'modificacategoria');
//        $this->whiteList[] = (object) array('controller' => 'archiveframeaddhearings', 'action' => 'list');
        $this->whiteList[] = (object) array('controller' => 'agyo', 'action' => 'contract2');
        $this->whiteList[] = (object) array('controller' => 'archivefatture', 'action' => 'saveNew');
        $this->whiteList[] = (object) array('controller' => 'calendar', 'action' => 'getevents');
        $this->whiteList[] = (object) array('controller' => 'additionalnotes', 'action' => 'update');
        $this->whiteList[] = (object) array('controller' => 'timesheet', 'action' => 'timer');
        $this->whiteList[] = (object) array('controller' => 'filestypes', 'action' => 'remotesave');
        $this->whiteList[] = (object) array('controller' => 'querybuilder', 'action' => 'checkjoinablefields');
        $this->whiteList[] = (object) array('controller' => 'querybuilder', 'action' => 'getquery');
        $this->whiteList[] = (object) array('controller' => 'querybuilder', 'action' => 'executequery');
        $this->whiteList[] = (object) array('controller' => 'querybuilder', 'action' => 'storequery');
        $this->whiteList[] = (object) array('controller' => 'workflows', 'action' => 'save');
        $this->whiteList[] = (object) array('controller' => 'feespa', 'action' => 'agyoview');
        $this->whiteList[] = (object) array('controller' => 'feesitems', 'action' => 'create-item');
    }

    /**
     * @param Zend_Controller_Request_Abstract $request
     */


    private function nestedLowercase($value) {
        if (is_array($value)) {
            return array_map('nestedLowercase', $value);
        }
        return strtolower($value);
    }

    function preDispatch(Zend_Controller_Request_Abstract $request)
    {
        $params = $_REQUEST;
        $method = $request->getMethod();
        $actionName = $request->getActionName();
        $controllername = $request->getControllerName();
        $currentControllerAction = null;
        $whiteParams = null;
        $logHelk = false;

        foreach($this->whiteList as $obj) {
            if ($controllername == $obj->controller && $actionName == $obj->action ){
                if ( empty($obj->ignore) ){
                    $currentControllerAction = $obj;
                    break;
                }else{
                    $whiteParams = $obj->ignore;
                }

            }
        }

        if(is_null($currentControllerAction)){
            /**
             * todo: for Production make path relative
             */

            require_once  $this->dexmaPath.'/htmlpurifier-4.13.0/library/HTMLPurifier.auto.php';
            //require_once  '<Proj_Netlex>/DexmaCommons/htmlpurifier-4.13.0/library/HTMLPurifier.auto.php';
            //require_once  '<Proj_Netlex>/dexmaBeta/DexmaCommons/htmlpurifier-4.13.0/library/HTMLPurifier.auto.php';

            $unwanted_word = array(
                '"'=> '&quot', "'"=>'&#39' ,'<' =>'&lt', '>'=> '&gt',
                'Á'=>'A', 'Â'=>'A', 'Ã'=>'A', 'Ä'=>'A', 'Å'=>'A','Æ'=>'A',
                'Ç'=>'C', 'Ê'=>'E', 'Ë'=>'E','Î'=>'I', 'Ï'=>'I', 'Ð'=>'D',
                'Ñ'=>'N', 'Ô'=>'O', 'Õ'=>'O', 'Ö'=>'O', 'Ø'=>'O', 'Û'=>'U',
                'Ü'=>'U', 'ß'=>'B', 'â'=>'a', 'ã'=>'a', 'ä'=>'a', 'å'=>'a',
                'æ'=>'ae','ç'=>'c', 'ê'=>'e', 'ë'=>'e', 'î'=>'i', 'ï'=>'i',
                'ñ'=>'n', 'ô'=>'o', 'õ'=>'o', 'ö'=>'o', 'ø'=>'o', 'û'=>'u',
                'ü'=>'U', 'ÿ'=>'y', 'Ā'=>'A', 'ā'=>'a', 'Ă'=>'A', 'ă'=>'a',
                'Ą'=>'A', 'ą'=>'a', 'Ĉ'=>'C', 'ĉ'=>'c', 'Ċ'=>'c', 'ċ'=>'c',
                'Č'=>'C', 'č'=>'c', 'Ď'=>'D', 'ď'=>'d', 'Đ'=>'d', 'đ'=>'d',
                'Ē'=>'E', 'ē'=>'e', 'Ĕ'=>'E', 'ĕ'=>'e', 'Ė'=>'E', 'ė'=>'e',
                'Ę'=>'E', 'ę'=>'e', 'Ě'=>'E', 'ě'=>'e', 'Ĝ'=>'G', 'ĝ'=>'G',
                'Ğ'=>'G', 'ğ'=>'g', 'Ġ'=>'G', 'ġ'=>'g', 'Ģ'=>'G', 'Ĥ'=>'H',
                'ĥ'=>'h', 'Ħ'=>'H', 'ħ'=>'h', 'Ĩ'=>'I', 'ĩ'=>'i', 'Ī'=>'I',
                'ī'=>'i', 'Ĭ'=>'I', 'ĭ'=>'i', 'Į'=>'I', 'į'=>'i', 'İ'=>'I',
                'ķ'=>'k', 'Ĺ'=>'L', 'ĺ'=>'l', 'Ļ'=>'L', 'ļ'=>'l', 'Ľ'=>'L',
                'ľ'=>'l', 'Ŀ'=>'l', 'ŀ'=>'l', 'Ł'=>'l', 'ł'=>'l', 'Ņ'=>'N',
                'ņ'=>'n', 'Ň'=>'N', 'ň'=>'n', 'ŉ'=>'n', 'Ō'=>'O', 'ō'=>'o',
                'Ŏ'=>'o', 'ŏ'=>'o', 'Ő'=>'O', 'ő'=>'o', 'Œ'=>'OE', 'œ'=>'oe',
                'Ŕ'=>'R', 'ŕ'=>'r', 'Ŗ'=>'R', 'ŗ'=>'r', 'Ř'=>'R', 'ř'=>'r',
                'Ś'=>'S', 'ś'=>'s', 'Ŝ'=>'S', 'ŝ'=>'s', 'Ş'=>'S', 'ş'=>'s',
                'Š'=>'S', 'š'=>'s', 'Ţ'=>'T', 'ţ'=>'t', 'Ť'=>'T', 'ť'=>'t',
                'Ŧ'=>'T', 'ŧ'=>'t', 'Ũ'=>'U', 'ũ'=>'u', 'Ū'=>'U', 'ū'=>'u',
                'Ŭ'=>'U', 'ŭ'=>'u', 'Ů'=>'U', 'ů'=>'u', 'Ű'=>'U', 'ű'=>'u',
                'Ų'=>'U', 'ų'=>'u', 'Ŵ'=>'W', 'ŵ'=>'w', 'Ŷ'=>'Y', 'ŷ'=>'y',
                'Ÿ'=>'Y', 'Ź'=>'Z', 'ź'=>'z', 'Ż'=>'Z', 'ż'=>'z', 'Ž'=>'Z',
                'ž'=>'z', 'ſ'=>'f', 'ƒ'=>'F', 'Ơ'=>'O', 'ơ'=>'o', 'Ư'=>'U',
                'ư'=>'ur', 'Ǎ'=>'A', 'ǎ'=>'a', 'Ǐ'=>'I', 'ǐ'=>'i', 'Ǒ'=>'O',
                'ǒ'=>'o', 'Ǔ'=>'U', 'ǔ'=>'u', 'Ǖ'=>'U', 'ǖ'=>'u', 'Ǘ'=>'U',
                'ǘ'=>'u', 'Ǚ'=>'U', 'ǚ'=>'u', 'Ǜ'=>'U', 'ǜ'=>'u', 'Ǻ'=>'A',
                'ǻ'=>'a', 'Ǽ'=>'ae', 'ǽ'=>'ae', 'Ǿ'=>'O', 'ǿ'=>'o', '`'=>'',
                'ı'=>'l', 'Ĳ'=>'IJ', 'ĳ'=>'ij', 'Ĵ'=>'J', 'ĵ'=>'J', 'Ķ'=>'K',
            );
            $unwanted_array = array(
                'onMediaError' => '', 'onRepeat' => '', 'FSCommand'=>'',
                'onAbort'=>'', 'onActivate' => '', 'onAfter' => '', 'execCommand' => '',
                'onBefore' => '', 'onBeforeUpdate' => '', 'onBegin' => '', 'onBlur' => '',
                'onBounce' => '', 'onCellChange' => '', 'onChange' => '', 'onClick' => '',
                'onContextMenu' => '', 'onControlSelect' => '', 'onCopy' => '', 'onCut' => '',
                'onData' => '', 'onDeactivate' => '', 'onDrag' => '', 'onDrop' => '',
                'onEnd' => '', 'onError' => '', 'onFilterChange' => '', 'onFinish' => '',
                'onFocus' => '', 'onFocusIn' => '', 'onHashChange' => '', 'onHelp' => '',
                'onInput' => '','onKey' => '','onLayoutComplete' => '','onLoad' => '',
                'onLoseCapture' => '', 'releaseCapture' => '', 'onMediaComplete' => '',
                'onMessage' => '', 'onMouse' => '', 'onMove' => '', 'onOffline' => '',
                'onOnline' => '', 'onOutOfSync' => '', 'onPaste' => '','onPause' => '',
                'onPropertyChange' => '', 'onReadyStateChange' => '', 'onRedo' => '',
                'onReset' => '', 'onResize' => '', 'onResume' => '', 'onReverse' => '',
                'onRow' => '', 'onScroll' => '', 'scrollBy' => '', 'onSeek' => '',
                'onSelect' => '', 'onStart' => '', 'onStop' => '', 'onStorage' => '',
                'onSyncRestored' => '', 'onSubmit' => '', 'onTimeError' => '', 'onTrackChange' => '',
                'onUndo' => '', 'onUnload' => '', 'seekSegmentTime' => '',
            );
            $unwanted_lower = array_change_key_case($unwanted_array, CASE_LOWER);


            $config = HTMLPurifier_Config::createDefault();

            /**
             * strict html purifier: you can adjust it
             */
//                $config->set('URI.Base', 'https://'.SITEKEY); // set the base URL (overrides a <base element in the HTML head?)
            $config->set('URI.MakeAbsolute', true); // make all URLs absolute using the base URL set above
            $config->set('AutoFormat.RemoveEmpty', true); // remove empty elements
            $config->set('HTML.Doctype', 'XHTML 1.0 Strict'); // valid XML output (?)
            $config->set('HTML.AllowedElements', array('p', 'div', 'a', 'br', 'table', 'thead', 'tbody', 'tr', 'th', 'td', 'ul', 'ol', 'li', 'b', 'i'));
            $config->set('HTML.AllowedAttributes', array('a.href')); // remove all attributes except a.href
            $config->set('CSS.AllowedProperties', array()); // remove all CSS


            $purifier = new HTMLPurifier($config);



            foreach ( $params as $key => &$val /** per non agire sui params mettere $val */ ) {

								$prev_val = $val;

                if ($key === 'annotazionipratica') {
                    continue;
                }

                if (!empty($whiteParams) && in_array($key,$whiteParams) ){
                    continue;
                }

                if (is_array($val)) {

                	$this->traverse_arr($val);

                }

                if (!is_array($val)) {

                		$val       =       strtr( $val, $unwanted_word );
                    $val       =       strtr( $val, $unwanted_lower);
                    $val       =       strtr( $val, $unwanted_array);

                    /**
                     * let's no break json dude
                     */
                    if (!$this->isJson($val))
                    {
                        $val       =       strtr( $val, $unwanted_word );
                        $val       =       strtr( $val, $unwanted_array);
                        $val       =       strtr( $val, $unwanted_lower);
                        $val       =       preg_replace('/-/', "-", $val);
                    }

                    $val       =       preg_replace('/java/', "j a v a", $val);
                    $val       =       preg_replace('/JaVa/', "j a v a", $val);
                    $val       =       strip_tags($val);
                    $val       =       $purifier->purify($val);


                }

                // Check sanitize
                //if ( !$logHelk && $this->check_sanitized_data( $prev_val, $val ) ) $logHelk = true;

            }

            // Log controller and action to add in whitelist
//            if ( $logHelk ) jsonlogger('info', [ 'text' => 'sanitized form' ]);

            //SANITIZE
            $_REQUEST = $params;
            switch($method){
                case 'POST':
                    $_POST = $params;
                    break;
                case 'PUT':
                    $_PUT = $params;
                    break;
                case 'DELETE':
                    $_DELETE = $params;
                case 'GET':
                default:
                    $_GET = $params;
                    break;
            }
        }

        $request->setDispatched(true);
    }

//    function check_sanitized_data( $prev_val, $new_val )
//		{
//
//			$result = false;
//
//			if (
//				( !is_array( $prev_val ) && $prev_val != $new_val ) ||
//				( is_array( $prev_val ) && !$this->arrays_are_equal( $prev_val, $new_val ) ) ) {
//
//				$result = true;
//
//			}
//
//			return $result;
//
//		}

	private function arrays_are_equal($array1, $array2)
	{
		array_multisort($array1);
		array_multisort($array2);
		return ( serialize($array1) === serialize($array2) );
	}

    /**
     * @param $string
     * @return bool
     */
    private function isJson($string) {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    protected function traverse_arr(&$array):void{
        $unwanted_word = array(
            '"'=> '&quot', "'"=>'&#39' ,'<' =>'&lt', '>'=> '&gt',
            'Á'=>'A', 'Â'=>'A', 'Ã'=>'A', 'Ä'=>'A', 'Å'=>'A','Æ'=>'A',
            'Ç'=>'C', 'Ê'=>'E', 'Ë'=>'E','Î'=>'I', 'Ï'=>'I', 'Ð'=>'D',
            'Ñ'=>'N', 'Ô'=>'O', 'Õ'=>'O', 'Ö'=>'O', 'Ø'=>'O', 'Û'=>'U',
            'Ü'=>'U', 'ß'=>'B', 'â'=>'a', 'ã'=>'a', 'ä'=>'a', 'å'=>'a',
            'æ'=>'ae','ç'=>'c', 'ê'=>'e', 'ë'=>'e', 'î'=>'i', 'ï'=>'i',
            'ñ'=>'n', 'ô'=>'o', 'õ'=>'o', 'ö'=>'o', 'ø'=>'o', 'û'=>'u',
            'ü'=>'U', 'ÿ'=>'y', 'Ā'=>'A', 'ā'=>'a', 'Ă'=>'A', 'ă'=>'a',
            'Ą'=>'A', 'ą'=>'a', 'Ĉ'=>'C', 'ĉ'=>'c', 'Ċ'=>'c', 'ċ'=>'c',
            'Č'=>'C', 'č'=>'c', 'Ď'=>'D', 'ď'=>'d', 'Đ'=>'d', 'đ'=>'d',
            'Ē'=>'E', 'ē'=>'e', 'Ĕ'=>'E', 'ĕ'=>'e', 'Ė'=>'E', 'ė'=>'e',
            'Ę'=>'E', 'ę'=>'e', 'Ě'=>'E', 'ě'=>'e', 'Ĝ'=>'G', 'ĝ'=>'G',
            'Ğ'=>'G', 'ğ'=>'g', 'Ġ'=>'G', 'ġ'=>'g', 'Ģ'=>'G', 'Ĥ'=>'H',
            'ĥ'=>'h', 'Ħ'=>'H', 'ħ'=>'h', 'Ĩ'=>'I', 'ĩ'=>'i', 'Ī'=>'I',
            'ī'=>'i', 'Ĭ'=>'I', 'ĭ'=>'i', 'Į'=>'I', 'į'=>'i', 'İ'=>'I',
            'ķ'=>'k', 'Ĺ'=>'L', 'ĺ'=>'l', 'Ļ'=>'L', 'ļ'=>'l', 'Ľ'=>'L',
            'ľ'=>'l', 'Ŀ'=>'l', 'ŀ'=>'l', 'Ł'=>'l', 'ł'=>'l', 'Ņ'=>'N',
            'ņ'=>'n', 'Ň'=>'N', 'ň'=>'n', 'ŉ'=>'n', 'Ō'=>'O', 'ō'=>'o',
            'Ŏ'=>'o', 'ŏ'=>'o', 'Ő'=>'O', 'ő'=>'o', 'Œ'=>'OE', 'œ'=>'oe',
            'Ŕ'=>'R', 'ŕ'=>'r', 'Ŗ'=>'R', 'ŗ'=>'r', 'Ř'=>'R', 'ř'=>'r',
            'Ś'=>'S', 'ś'=>'s', 'Ŝ'=>'S', 'ŝ'=>'s', 'Ş'=>'S', 'ş'=>'s',
            'Š'=>'S', 'š'=>'s', 'Ţ'=>'T', 'ţ'=>'t', 'Ť'=>'T', 'ť'=>'t',
            'Ŧ'=>'T', 'ŧ'=>'t', 'Ũ'=>'U', 'ũ'=>'u', 'Ū'=>'U', 'ū'=>'u',
            'Ŭ'=>'U', 'ŭ'=>'u', 'Ů'=>'U', 'ů'=>'u', 'Ű'=>'U', 'ű'=>'u',
            'Ų'=>'U', 'ų'=>'u', 'Ŵ'=>'W', 'ŵ'=>'w', 'Ŷ'=>'Y', 'ŷ'=>'y',
            'Ÿ'=>'Y', 'Ź'=>'Z', 'ź'=>'z', 'Ż'=>'Z', 'ż'=>'z', 'Ž'=>'Z',
            'ž'=>'z', 'ſ'=>'f', 'ƒ'=>'F', 'Ơ'=>'O', 'ơ'=>'o', 'Ư'=>'U',
            'ư'=>'ur', 'Ǎ'=>'A', 'ǎ'=>'a', 'Ǐ'=>'I', 'ǐ'=>'i', 'Ǒ'=>'O',
            'ǒ'=>'o', 'Ǔ'=>'U', 'ǔ'=>'u', 'Ǖ'=>'U', 'ǖ'=>'u', 'Ǘ'=>'U',
            'ǘ'=>'u', 'Ǚ'=>'U', 'ǚ'=>'u', 'Ǜ'=>'U', 'ǜ'=>'u', 'Ǻ'=>'A',
            'ǻ'=>'a', 'Ǽ'=>'ae', 'ǽ'=>'ae', 'Ǿ'=>'O', 'ǿ'=>'o', '`'=>'',
            'ı'=>'l', 'Ĳ'=>'IJ', 'ĳ'=>'ij', 'Ĵ'=>'J', 'ĵ'=>'J', 'Ķ'=>'K',
        );
        $unwanted_array = array(
            'onMediaError' => '', 'onRepeat' => '', 'FSCommand'=>'',
            'onAbort'=>'', 'onActivate' => '', 'onAfter' => '', 'execCommand' => '',
            'onBefore' => '', 'onBeforeUpdate' => '', 'onBegin' => '', 'onBlur' => '',
            'onBounce' => '', 'onCellChange' => '', 'onChange' => '', 'onClick' => '',
            'onContextMenu' => '', 'onControlSelect' => '', 'onCopy' => '', 'onCut' => '',
            'onData' => '', 'onDeactivate' => '', 'onDrag' => '', 'onDrop' => '',
            'onEnd' => '', 'onError' => '', 'onFilterChange' => '', 'onFinish' => '',
            'onFocus' => '', 'onFocusIn' => '', 'onHashChange' => '', 'onHelp' => '',
            'onInput' => '','onKey' => '','onLayoutComplete' => '','onLoad' => '',
            'onLoseCapture' => '', 'releaseCapture' => '', 'onMediaComplete' => '',
            'onMessage' => '', 'onMouse' => '', 'onMove' => '', 'onOffline' => '',
            'onOnline' => '', 'onOutOfSync' => '', 'onPaste' => '','onPause' => '',
            'onPropertyChange' => '', 'onReadyStateChange' => '', 'onRedo' => '',
            'onReset' => '', 'onResize' => '', 'onResume' => '', 'onReverse' => '',
            'onRow' => '', 'onScroll' => '', 'scrollBy' => '', 'onSeek' => '',
            'onSelect' => '', 'onStart' => '', 'onStop' => '', 'onStorage' => '',
            'onSyncRestored' => '', 'onSubmit' => '', 'onTimeError' => '', 'onTrackChange' => '',
            'onUndo' => '', 'onUnload' => '',  'seekSegmentTime' => '',
        );
        $unwanted_lower = array_change_key_case($unwanted_array, CASE_LOWER);

        foreach($array as $key => $value)
        {

            if ($key === 'annotazionipratica') {
                continue;
            }

            if (is_array($array[$key])) { 
                $this->traverse_arr($array[$key]);
            }    
            else {
                $array[$key]        =       strtr( $array[$key], $unwanted_word );       
                $array[$key]        =       strtr( $array[$key], $unwanted_lower);
                $array[$key]        =       strtr( $array[$key], $unwanted_array);
                $array[$key]        =       strip_tags($array[$key]);
                $array[$key] = preg_replace('/\bjava\b/i', "j a v a", $array[$key]);
                $array[$key] = preg_replace('/\bhref\b/i', "h r e f", $array[$key]);
                $array[$key] = preg_replace('/\bsrc\b/i', "s r c", $array[$key]);
                $array[$key] = preg_replace('/\burl\b/i', "u r l", $array[$key]);
                $array[$key] = preg_replace('/\bimg\b/i', "i m g", $array[$key]);
                $array[$key] = preg_replace('/\bstyle\b/i', "s t y l e", $array[$key]);

                $array[$key]        =       $array[$key];
            }

        }
        // return;
    }
}
