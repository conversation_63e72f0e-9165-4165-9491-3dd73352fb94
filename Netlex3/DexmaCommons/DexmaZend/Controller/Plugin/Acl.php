<?php class DexmaZend_Controller_Plugin_Acl extends Zend_Controller_Plugin_Abstract
{
	private $_config;

	function __construct($config)
	{
		if (!is_array($config))
		{
			if ($config instanceof Zend_Config)
			{
				$config = $config->toArray();
			}
			else
			{
				throw new Zend_Acl_Exception('$config must be in an array or a Zend_Config object');
			}
		}

		$this->_config = $config;
	}

	function preDispatch(Zend_Controller_Request_Abstract $request)
	{
		$auth = Zend_Auth::getInstance();
		$acl = new DexmaZend_Acl($this->_config, $request);
		$options = $this->_config['options'];
		$role = trim($options['defaultRole']);

		if ($auth->hasIdentity())
		{
			$user = $auth->getIdentity();
			$roleColumn = trim($options['roleColumn']);
			$role = $user->$roleColumn;
		}

		$resource = $request->getModuleName() . '-' . $request->getControllerName();
		$privilege = $request->getActionName();

		try {
			if (!$acl->isAllowed($role, $resource, $privilege))
			{
				if (isRequestFromOnefrontIframe()) {
					$request
						->setControllerName('one-front')
						->setActionName('index')
						->setParam('onefrontPath', '/logout')
						->setDispatched(true);

					return;
				}

				$redirect = $options['redirect'];
				$request->setModuleName($this->normalize($redirect['module']))
						->setControllerName($this->normalize($redirect['controller']))
						->setActionName($this->normalize($redirect['action']))
						->setDispatched(true);
			}
		}
		catch (Zend_Acl_Exception $e) {
			if (empty($options['exceptionUrl'])) {
				throw $e;
			}

			$request->setRequestUri($options['exceptionUrl'])->setDispatched(true);
		}
	}

	private function normalize($val)
	{
		return strtolower(trim($val));
	}
}