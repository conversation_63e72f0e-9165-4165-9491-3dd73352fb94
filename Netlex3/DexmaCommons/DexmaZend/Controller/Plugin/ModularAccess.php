<?php 

class DexmaZend_Controller_Plugin_ModularAccess extends Zend_Controller_Plugin_Abstract {

    /**
     *
     */
    const REGISTRY_CACHE_KEY = 'Cache';

    /**
     * @const string
     */
    const CACHE_MODULES_KEY = 'availableModules';

    /**
     * @const string
     */
    const CACHE_ACCOUNT_KEY = 'account';

    /**
     * @const int
     */
    const GRUPPI_PERMESSI = 1;

    /**
     * @var int|null
     */
    private $_version;
    /**
     * @var array|null
     */
    private $_modules;
    /**
     * @var string|null
     */
    private $_customerUid;

    /**
     * @var database istance
     */
    private $_dbProvisioning;

    private $defaultCachePath = DIRECTORY_SEPARATOR . 'home' . DIRECTORY_SEPARATOR . 'netlex3cache' . DIRECTORY_SEPARATOR;

    /**
     * @var Zend_Cache_Core
     */
    private $cache;

    /**
     * @var bool
     */
    private $_cacheless = FALSE;

    private $subdomain;
    private $domain;
    private $port;

    private $netlex4_subdomain;
    private $netlex4_domain;
    private $netlex4_port;

    function __construct($db, $dbProvisioning, $dbShared, $configAcl, $generalConfig, $netlex3, $netlex4)
    {
 
        $this->subdomain = $netlex3->subdomain;
        $this->domain = $netlex3->domain;
        $this->port = $netlex3->port;

        $this->netlex4_subdomain = $netlex4->subdomain;
        $this->netlex4_domain = $netlex4->domain;
        $this->netlex4_port = $netlex4->port;

        $this->_dbProvisioning = $dbProvisioning;
//        $cachePath = $generalConfig->netlex3middlewarePath;
        $cachePath = $generalConfig->netlex3cachePath ?? $this->defaultCachePath ;

        try{

            if ( !$this->_cacheless ) {

                $this->cache = self::cacheLoader($this->subdomain, $cachePath);
                $this->_modules = $this->cache->load(self::CACHE_MODULES_KEY)['modules'];
                $this->_version = $this->cache->load(self::CACHE_MODULES_KEY)['version'];
                $this->_customerUid = $this->cache->load(self::CACHE_MODULES_KEY)['uniqueid'];
                $this->_account = $this->cache->load(self::CACHE_ACCOUNT_KEY);

            }

        } catch(Exception $e){
            /**
             * todo: manage Zend_Cache_Exception unable to load cache
             */
            $this->_modules     = NULL;
            $this->_version     = NULL;
            $this->_customerUid = NULL;
            $this->_account     = NULL;
            $this->_cacheless   = TRUE;
        }

        if (!is_array($this->_modules) || empty($this->_version) || empty($this->_customerUid)) {
            $customer_data                  = $dbProvisioning->fetchRow('SELECT subscription_type as version , modules, uniqueid FROM customers WHERE subdomain_name = ?', $this->subdomain);
            $this->_version                 = $customer_data['version'];
            $this->_modules                 = json_decode($customer_data['modules'],1);
            $this->_customerUid             = $customer_data['uniqueid'];
            /**
             * In case we are missing modules -> bug ? -> getting default version modules by default
             */
            if (empty($this->_modules)){
                $this->_modules             = $dbShared->fetchCol('SELECT id_module FROM module_version WHERE id_version = ?', $this->_version);
            }

            /**
             * In case modules has Groups And Permissions enable we should manage it
             */
            if (in_array(self::GRUPPI_PERMESSI,$this->_modules)){
                /**
                 * todo: add it inside the class attributes
                 */
            }

            /**
             * _cacheless == False  then we are going to store retrieved data inside Cache
             */
            if (!$this->_cacheless){
                $this->cache->save(array(
                    'version'   => $this->_version,
                    'modules'   => $this->_modules,
                    'uniqueid'  => $this->_customerUid
                ),
                    self::CACHE_MODULES_KEY);
            }
        }

        /**
         * Load account data
         */
        if ( empty($this->_account) ) {

            $this->_account = new Provisioning\Account($this->_dbProvisioning, $this->_customerUid);

            /**
             * _cacheless == False  then we are going to store retrieved data inside Cache
             */
            if ( !$this->_cacheless ) $this->cache->save( $this->_account,self::CACHE_ACCOUNT_KEY );

        }


    }

    /**
     * @param $subdomain ; the SITEKEY to hash and retrieve the desired cache
     * @param $cachePath ; a default config to get the Cache base path
     * @return Zend_Cache_Core
     * @throws Zend_Cache_Exception
     */
    private static function cacheLoader($subdomain, $cachePath, $customerCache = false){

        $cacheDir = empty($customerCache)? ($cachePath . md5($subdomain)) : $cachePath  ;

        if (!is_dir($cacheDir)) {
            $oldmask = umask(0);
            mkdir($cacheDir, 0755, true);
            umask($oldmask);
        }

        $cache = Zend_Cache::factory(
            "Core",
            "File",
            array(
                "lifetime" => 3600 * 24, //cache is cleaned once a day
                "automatic_serialization" => true
            ),
            array("cache_dir" => $cacheDir)
        );
        Zend_Db_Table_Abstract::setDefaultMetadataCache($cache);
        Zend_Registry::set(self::REGISTRY_CACHE_KEY, $cache);

        return $cache;
    }

    /**
     * @return false|mixed
     * @throws Exception
     */
    private function getIdentity(){
        $auth = Zend_Auth::getInstance();
        if ($auth->hasIdentity()) {
            $loggedUser = $auth->getIdentity();
        }else{
            /**
             * todo: throw new Exception('Missing Identity from $auth->hasIdentity()');
             */
        }
        return $loggedUser ?? false ;
    }


    /**
     * @param Zend_Controller_Request_Abstract $request
     */
    function preDispatch(Zend_Controller_Request_Abstract $request)
    {
        if ($user = self::getIdentity()){

            /**
             * Actually is possible to set Legacy Mode through User istance
             */
            !$user->legacy_mode?: $this->setLegacyMode($request);

            /**
             * missing User or Not Active
             */
            if (!$user->id || 2 == $user->attivo) {
                if (!$request->isXmlHttpRequest()) {
                    if ('logout' !== $request->getActionName()) {

                        /**
                         * redirect to Logout then back to login EXIT from preDispatch, User is Missing or Not Active Anymore
                         */
                        $redirector = Zend_Controller_Action_HelperBroker::getStaticHelper('redirector');
                        $redirector->gotoUrl('/login/logout')->redirectAndExit();
                    }
                } else {
                    Zend_Auth::getInstance()->clearIdentity();
                    Zend_Session::destroy();
                    exit;
                }
            }

            /**
             * Accounting on Expired Copy can Access? Otherwise logout if toleranceIsExpired()|!customerExists()|isDeleted()|isDisabled()
             */
            elseif(!$user->isAccounting && 'logout' !== $request->getActionName() &&
                ($this->_account->toleranceIsExpired()
                        || !$this->_account->customerExists()
                        || $this->_account->isDeleted()
                        || $this->_account->isDisabled()
                ))
            {
                $redirector = Zend_Controller_Action_HelperBroker::getStaticHelper('redirector');
                $redirector->gotoUrl('/login/logout')->redirectAndExit();
            }


        }else{

            /**
             * todo: we should manage a missing identity? let's check behaviour from automated scripts
             */

        }

        $request->setParam('_middleware', $this->_modules);

        /** Legacy-Header allows us understand if are in Legacy Mode inside Laravel requests */
        if ($this->isLegacyMode($request))
        {
            $viewRenderer = $this->getViewRender();
            $viewRenderer->setNeverRender(TRUE);
        }

    }

    /**
     * @param Zend_Controller_Request_Abstract $request
     */
    function postDispatch(Zend_Controller_Request_Abstract $request)
    {
        $viewRenderer       = $this->getViewRender();
        $view               = $viewRenderer->view;
        $view->_middleware  = $request->getParam('_middleware');

        // pagina e get
        if ($this->isLegacyMode($request, $header_only = true))
        {
            /** In case we are missing an Identity Authentication is required */
            if (!self::getIdentity())
            {
                $this->getResponse()->setHttpResponseCode(401);

                echo json_encode(array(
                    'Auth_required'=>$request->getHttpHost()
                ));
            }
            /** this can be helpful inside netlex $request->isXmlHttpRequest() */
            /** We are managing AJAX calls and Template rendering requests */
            elseif (empty($request->getHeader('AjaxCall'))){
                $smartyVar = $view->getEngine()->getTemplateVars();
                $smartyVar['rendering_required'] = TRUE;
                echo json_encode($smartyVar);
            }

        }else
        {
            // First authentication
            if (self::getIdentity() && $this->isLegacyMode($request)) {

                $cookie = $request->getCookie('PHPSESSID');
                // ** todo: the first time we get here with an auth session in a test environment we can redirect to Laravel/anagrafiche or something like that * /

                $currentCookieParams = session_get_cookie_params();
                
                $rootDomain = "$this->subdomain.$this->domain";

                session_set_cookie_params(
                    $currentCookieParams["lifetime"],
                    $currentCookieParams["path"],
                    $rootDomain,
                    $currentCookieParams["secure"],
                    $currentCookieParams["httponly"]
                );

                session_name('mysessionname');
                session_start();

                setcookie('EXCHANGE_LARAVEL', $cookie, time() + 3600, '/', $rootDomain);

                // when netlex3 login successfully it redirect to
                // http://netlex3.test/ any times so will be changed into /calendar

                $url = sprintf(
                    "%s://%s.%s:%d%s",
                    $_SERVER['REQUEST_SCHEME'],
                    $this->netlex4_subdomain,
                    $this->netlex4_domain,
                    $this->netlex4_port,
                    "/anagrafiche"
                );

                header("Location: $url");
                exit;
            }

            /** Old logic for netlex */
            if (!self::getIdentity() || empty($viewRenderer->view->getEngine()->template_objects)) {

                $viewRenderer->setNoRender();

            }
        }

    }


    /**
     * @return Zend_Controller_Action_Helper_Abstract
     *
     * This function return an existing viewRenderer if exist or just a new one
     */
    protected function getViewRender()
    {
        try{
            $viewRenderer       = Zend_Controller_Action_HelperBroker::getExistingHelper('viewRenderer');
        }catch( Exception $e){
            $viewRenderer       = Zend_Controller_Action_HelperBroker::getStaticHelper('viewRenderer');
        }
        return $viewRenderer;
    }

    /**
     * @param Zend_Controller_Request_Abstract $request
     * @param bool $header_only | boolean it takes in consideration only header and not request param
     * @return bool
     *
     * This function return if $request is made by Laravel externally
     * with Legacy-Header or if User legacy_mode is enabled
     */
    protected function isLegacyMode(Zend_Controller_Request_Abstract $request, bool $header_only = false): bool
    {
        return !empty($request->getHeader('Legacy-Header')) || (!empty($request->getParam('Legacy-Mode')) && !$header_only);
    }


    /**
     * @param $request | Is a pointer of current request
     *
     * This function set request param to enable Legacy mode
     */
    protected function setLegacyMode(&$request): void
    {
        $request->setParam('Legacy-Mode',true);
    }

    
}