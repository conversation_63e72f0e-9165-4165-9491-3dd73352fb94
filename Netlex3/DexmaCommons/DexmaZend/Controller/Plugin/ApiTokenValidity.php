<?php

declare(strict_types=1);

use Netlex3\Software\patterns\Domains\Oauth\Services\OauthService;
use Netlex3\Software\patterns\Repositories\UserRepository;

class DexmaZend_Controller_Plugin_ApiTokenValidity extends Zend_Controller_Plugin_Abstract
{
    /**
     * Il controllo sull'utente nel token ha rotto la retrocompatibilità delle api-v2 per atmspa
     * Il cliente ha ricevuto una documentazione sulle v2 (~2023) che non prevedeva l'utilizzo del token specifico per utente
     * TODO: Necessario refactoring api per una soluzione sostenibile
     */
    const EXCLUDED_CUSTOMERS_FROM_USER_CHECK = ['atmspa', 'testenv19', 'a2a', 'a2atest', 'testenv18'];

    const ENABLED_CONTROLLERS_FOR_TOKEN_CHECK = ['api-v2', 'lexi'];
    const ENABLED_USERS_CONTROLLERS_FOR_TOKEN_CHECK = ['api-v2', 'lexi'];

    /** @var OauthService */
    private $oauthService;

    public function __construct() {
        $this->oauthService = OauthService::getInstance();
    }

    public function preDispatch(Zend_Controller_Request_Abstract $request)
    {
        if (in_array($request->getControllerName(), self::ENABLED_CONTROLLERS_FOR_TOKEN_CHECK)) {
            if ($request instanceof Zend_Controller_Request_Http) {
                $authorizationHeader = $request->getHeader('Authorization');

                if (! $authorizationHeader) {
                    http_response_code(401);
                    echo json_encode(array(
                        'error' => 'unauthorized',
                        'error_description' => 'token not found',
                        'app_error_title' => 'Non autorizzato',
                        'app_error_description' => 'Prova ad accedere di nuovo.',
                    ));
                    die();
                }

                if ($authorizationHeader) {
                    $bearerToken = null;

                    if (preg_match('/Bearer\s(\S+)/', $authorizationHeader, $matches)) {
                        $bearerToken = $matches[1];
                    }

                    // Store raw token
                    $request->setParam('raw-access-token', $bearerToken);
        
                    // Verifica self-signed token
                    $accessToken = $this->oauthService->verifySelfSignedToken($bearerToken);

                    // Store token decodificato
                    $request->setParam('access-token', $accessToken);

                    $controller = $request->getControllerName();

                    if (in_array($controller, self::ENABLED_USERS_CONTROLLERS_FOR_TOKEN_CHECK)) {
                        $id = intval($accessToken->user_id);

                        if (
                            ! $id
                            && in_array(SITEKEY, self::EXCLUDED_CUSTOMERS_FROM_USER_CHECK)
                            && $controller === 'api-v2'
                        ) {
                            return;
                        }

                        $user = UserRepository::getInstance()->findUserById($id);

                        if (!$user) {
                            // Errore json
                            http_response_code(401);
                            echo json_encode(array(
                                'error' => 'unauthorized',
                                'error_description' => 'user not found',
                                'app_error_title' => 'Non autorizzato',
                                'app_error_description' => 'Prova ad accedere di nuovo.',
                            ));

                            exit;
                        }

                        if (isRequestFromTslegalApp() && ! haveConfigUpdated()) {
                            reloadConfig();
                            setConfigUser($user);
                        }


                        $request->setParam('user', $user);
                    }
                }
            }
        }
    }
}