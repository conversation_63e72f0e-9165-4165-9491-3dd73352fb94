<?php 

class DexmaZend_Controller_Plugin_CsvSanitize extends Zend_Controller_Plugin_Abstract
{
    /**
     * DEXMA PATH
     */
    private $dexmaPath;
    private $csvImportActionsList;

    /**
     * DexmaZend_Controller_Plugin_CsvSanitize constructor.
     */
    function __construct($dexmaPath = '/home/<USER>/DexmaCommons')
    {
        $this->dexmaPath = $dexmaPath;

        // Lista delle action che importano file CSV e necessitano controllo del contenuto
        $this->csvImportActionsList = array();
        $this->csvImportActionsList[] = (object) array('controller' => 'tools', 'action' => 'upload-csv');
        $this->csvImportActionsList[] = (object) array('controller' => 'anagrafiche', 'action' => 'importfromcsv');
        $this->csvImportActionsList[] = (object) array('controller' => 'import', 'action' => 'execute');
        $this->csvImportActionsList[] = (object) array('controller' => 'archivecreditrecovery', 'action' => 'upload-csv');
        $this->csvImportActionsList[] = (object) array('controller' => 'library', 'action' => 'upload');
    }

    /**
     * Sanitizza il contenuto di un file CSV rimuovendo contenuti pericolosi
     * @param string $filePath Percorso del file
     * @return string Contenuto sanitizzato del file
     */
    private function sanitizeCsvContent($filePath)
    {
        // Leggo tutto il contenuto del file
        $content = file_get_contents($filePath);
        
        if ($content === false) {
            throw new Exception('Impossibile leggere il contenuto del file CSV per la sanitizzazione');
        }

        // Array di caratteri pericolosi dal file Sanitize.php
        $unwanted_word = array(
            '"'=> '&quot', "'"=>'&#39' ,'<' =>'&lt', '>'=> '&gt',
            'Á'=>'A', 'Â'=>'A', 'Ã'=>'A', 'Ä'=>'A', 'Å'=>'A','Æ'=>'A',
            'Ç'=>'C', 'Ê'=>'E', 'Ë'=>'E','Î'=>'I', 'Ï'=>'I', 'Ð'=>'D',
            'Ñ'=>'N', 'Ô'=>'O', 'Õ'=>'O', 'Ö'=>'O', 'Ø'=>'O', 'Û'=>'U',
            'Ü'=>'U', 'ß'=>'B', 'â'=>'a', 'ã'=>'a', 'ä'=>'a', 'å'=>'a',
            'æ'=>'ae','ç'=>'c', 'ê'=>'e', 'ë'=>'e', 'î'=>'i', 'ï'=>'i',
            'ñ'=>'n', 'ô'=>'o', 'õ'=>'o', 'ö'=>'o', 'ø'=>'o', 'û'=>'u',
            'ü'=>'U', 'ÿ'=>'y', 'Ā'=>'A', 'ā'=>'a', 'Ă'=>'A', 'ă'=>'a',
            'Ą'=>'A', 'ą'=>'a', 'Ĉ'=>'C', 'ĉ'=>'c', 'Ċ'=>'c', 'ċ'=>'c',
            'Č'=>'C', 'č'=>'c', 'Ď'=>'D', 'ď'=>'d', 'Đ'=>'d', 'đ'=>'d',
            'Ē'=>'E', 'ē'=>'e', 'Ĕ'=>'E', 'ĕ'=>'e', 'Ė'=>'E', 'ė'=>'e',
            'Ę'=>'E', 'ę'=>'e', 'Ě'=>'E', 'ě'=>'e', 'Ĝ'=>'G', 'ĝ'=>'G',
            'Ğ'=>'G', 'ğ'=>'g', 'Ġ'=>'G', 'ġ'=>'g', 'Ģ'=>'G', 'Ĥ'=>'H',
            'ĥ'=>'h', 'Ħ'=>'H', 'ħ'=>'h', 'Ĩ'=>'I', 'ĩ'=>'i', 'Ī'=>'I',
            'ī'=>'i', 'Ĭ'=>'I', 'ĭ'=>'i', 'Į'=>'I', 'į'=>'i', 'İ'=>'I',
            'ķ'=>'k', 'Ĺ'=>'L', 'ĺ'=>'l', 'Ļ'=>'L', 'ļ'=>'l', 'Ľ'=>'L',
            'ľ'=>'l', 'Ŀ'=>'l', 'ŀ'=>'l', 'Ł'=>'l', 'ł'=>'l', 'Ņ'=>'N',
            'ņ'=>'n', 'Ň'=>'N', 'ň'=>'n', 'ŉ'=>'n', 'Ō'=>'O', 'ō'=>'o',
            'Ŏ'=>'o', 'ŏ'=>'o', 'Ő'=>'O', 'ő'=>'o', 'Œ'=>'OE', 'œ'=>'oe',
            'Ŕ'=>'R', 'ŕ'=>'r', 'Ŗ'=>'R', 'ŗ'=>'r', 'Ř'=>'R', 'ř'=>'r',
            'Ś'=>'S', 'ś'=>'s', 'Ŝ'=>'S', 'ŝ'=>'s', 'Ş'=>'S', 'ş'=>'s',
            'Š'=>'S', 'š'=>'s', 'Ţ'=>'T', 'ţ'=>'t', 'Ť'=>'T', 'ť'=>'t',
            'Ŧ'=>'T', 'ŧ'=>'t', 'Ũ'=>'U', 'ũ'=>'u', 'Ū'=>'U', 'ū'=>'u',
            'Ŭ'=>'U', 'ŭ'=>'u', 'Ů'=>'U', 'ů'=>'u', 'Ű'=>'U', 'ű'=>'u',
            'Ų'=>'U', 'ų'=>'u', 'Ŵ'=>'W', 'ŵ'=>'w', 'Ŷ'=>'Y', 'ŷ'=>'y',
            'Ÿ'=>'Y', 'Ź'=>'Z', 'ź'=>'z', 'Ż'=>'Z', 'ż'=>'z', 'Ž'=>'Z',
            'ž'=>'z', 'ſ'=>'f', 'ƒ'=>'F', 'Ơ'=>'O', 'ơ'=>'o', 'Ư'=>'U',
            'ư'=>'ur', 'Ǎ'=>'A', 'ǎ'=>'a', 'Ǐ'=>'I', 'ǐ'=>'i', 'Ǒ'=>'O',
            'ǒ'=>'o', 'Ǔ'=>'U', 'ǔ'=>'u', 'Ǖ'=>'U', 'ǖ'=>'u', 'Ǘ'=>'U',
            'ǘ'=>'u', 'Ǚ'=>'U', 'ǚ'=>'u', 'Ǜ'=>'U', 'ǜ'=>'u', 'Ǻ'=>'A',
            'ǻ'=>'a', 'Ǽ'=>'ae', 'ǽ'=>'ae', 'Ǿ'=>'O', 'ǿ'=>'o', '`'=>'',
            'ı'=>'l', 'Ĳ'=>'IJ', 'ĳ'=>'ij', 'Ĵ'=>'J', 'ĵ'=>'J', 'Ķ'=>'K',
        );

        // Array di eventi JavaScript pericolosi dal file Sanitize.php
        $unwanted_array = array(
            'onMediaError' => '', 'onRepeat' => '', 'FSCommand'=>'',
            'onAbort'=>'', 'onActivate' => '', 'onAfter' => '', 'execCommand' => '',
            'onBefore' => '', 'onBeforeUpdate' => '', 'onBegin' => '', 'onBlur' => '',
            'onBounce' => '', 'onCellChange' => '', 'onChange' => '', 'onClick' => '',
            'onContextMenu' => '', 'onControlSelect' => '', 'onCopy' => '', 'onCut' => '',
            'onData' => '', 'onDeactivate' => '', 'onDrag' => '', 'onDrop' => '',
            'onEnd' => '', 'onError' => '', 'onFilterChange' => '', 'onFinish' => '',
            'onFocus' => '', 'onFocusIn' => '', 'onHashChange' => '', 'onHelp' => '',
            'onInput' => '','onKey' => '','onLayoutComplete' => '','onLoad' => '',
            'onLoseCapture' => '', 'releaseCapture' => '', 'onMediaComplete' => '',
            'onMessage' => '', 'onMouse' => '', 'onMove' => '', 'onOffline' => '',
            'onOnline' => '', 'onOutOfSync' => '', 'onPaste' => '','onPause' => '',
            'onPropertyChange' => '', 'onReadyStateChange' => '', 'onRedo' => '',
            'onReset' => '', 'onResize' => '', 'onResume' => '', 'onReverse' => '',
            'onRow' => '', 'onScroll' => '', 'scrollBy' => '', 'onSeek' => '',
            'onSelect' => '', 'onStart' => '', 'onStop' => '', 'onStorage' => '',
            'onSyncRestored' => '', 'onSubmit' => '', 'onTimeError' => '', 'onTrackChange' => '',
            'onUndo' => '', 'onUnload' => '', 'seekSegmentTime' => '',
        );

        $unwanted_lower = array_change_key_case($unwanted_array, CASE_LOWER);

        // Inizializzazione HTMLPurifier con configurazione strict
        require_once $this->dexmaPath.'/htmlpurifier-4.13.0/library/HTMLPurifier.auto.php';
        
        $config = HTMLPurifier_Config::createDefault();
        $config->set('URI.MakeAbsolute', true);
        $config->set('AutoFormat.RemoveEmpty', true);
        $config->set('HTML.Doctype', 'XHTML 1.0 Strict');
        $config->set('HTML.AllowedElements', array('p', 'div', 'a', 'br', 'table', 'thead', 'tbody', 'tr', 'th', 'td', 'ul', 'ol', 'li', 'b', 'i'));
        $config->set('HTML.AllowedAttributes', array('a.href'));
        $config->set('CSS.AllowedProperties', array());

        $purifier = new HTMLPurifier($config);

        // Controllo se il contenuto è JSON per evitare di romperlo
        if (!$this->isJson($content)) {
            // Applico la sanitizzazione
            $content = strtr($content, $unwanted_word);
            $content = strtr($content, $unwanted_lower);
            $content = strtr($content, $unwanted_array);

            // Rimuovo pattern pericolosi come java, href, src, etc.
            $content = preg_replace('/\bjava\b/i', "", $content);
            $content = preg_replace('/\bhref\b/i', "", $content);
            $content = preg_replace('/\bsrc\b/i', "", $content);
            $content = preg_replace('/\burl\b/i', "u r l", $content);
            $content = preg_replace('/\bimg\b/i', "", $content);
            $content = preg_replace('/\bstyle\b/i', "s t y l e", $content);
            $content = preg_replace('/\bscript\b/i', "", $content);
            $content = preg_replace('/\biframe\b/i', "", $content);
            $content = preg_replace('/\bobject\b/i', "o b j e c t", $content);
            $content = preg_replace('/\bembed\b/i', "", $content);

            // Rimuovo tag HTML pericolosi e uso HTMLPurifier
            $content = strip_tags($content);
            $content = $purifier->purify($content);
        }

        return $content;
    }

    /**
     * Controlla se una stringa è un JSON valido
     * @param string $string
     * @return bool
     */
    private function isJson($string) {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Verifica se il file è un CSV
     * @param string $filename
     * @return bool
     */
    private function isCsvFile($filename)
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return $extension === 'csv';
    }

    /**
     * Controlla se l'action corrente è nella lista di quelle che importano CSV
     * @param string $controller
     * @param string $action
     * @return bool
     */
    private function shouldCheckCsvFiles($controller, $action)
    {
        foreach($this->csvImportActionsList as $obj) {
            if ($controller == $obj->controller && $action == $obj->action) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param Zend_Controller_Request_Abstract $request
     */
    function preDispatch(Zend_Controller_Request_Abstract $request)
    {
        $actionName = $request->getActionName();
        $controllerName = $request->getControllerName();

        // Controllo se l'action corrente deve verificare i file CSV caricati
        if (!$this->shouldCheckCsvFiles($controllerName, $actionName)) {
            $request->setDispatched(true);
            return;
        }

        // Controllo se ci sono file caricati
        if (empty($_FILES)) {
            $request->setDispatched(true);
            return;
        }

        try {
            // Itero attraverso tutti i file caricati
            foreach ($_FILES as $fileFieldName => $fileData) {
                
                // Gestisco sia singoli file che array di file
                if (is_array($fileData['tmp_name'])) {
                    // Array di file
                    for ($i = 0; $i < count($fileData['tmp_name']); $i++) {
                        if (isset($fileData['tmp_name'][$i]) && is_uploaded_file($fileData['tmp_name'][$i])) {
                            $filename = $fileData['name'][$i];
                            $tmpPath = $fileData['tmp_name'][$i];
                            
                            if ($this->isCsvFile($filename)) {
                                // Log dell'inizio sanitizzazione
                                error_log("CsvSanitize: Inizio sanitizzazione file: " . $filename);
                                
                                $sanitizedContent = $this->sanitizeCsvContent($tmpPath);
                                file_put_contents($tmpPath, $sanitizedContent);
                                
                                error_log("CsvSanitize: CSV sanitizzato con successo: " . $filename);
                            }
                        }
                    }
                } else {
                    // Singolo file
                    if (isset($fileData['tmp_name']) && is_uploaded_file($fileData['tmp_name'])) {
                        $filename = $fileData['name'];
                        $tmpPath = $fileData['tmp_name'];
                        
                        if ($this->isCsvFile($filename)) {
                            // Log dell'inizio sanitizzazione
                            error_log("CsvSanitize: Inizio sanitizzazione CSV: " . $filename);
                            
                            $sanitizedContent = $this->sanitizeCsvContent($tmpPath);
                            file_put_contents($tmpPath, $sanitizedContent);
                            
                            error_log("CsvSanitize: CSV sanitizzato con successo: " . $filename);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log("CsvSanitize: Errore durante la sanitizzazione CSV: " . $e->getMessage());
            
            // In caso di errore, blocco l'esecuzione per sicurezza
            throw new Exception('Errore durante la sanitizzazione del file CSV caricato. Upload non consentito per motivi di sicurezza.');
        }

        $request->setDispatched(true);
    }
} 