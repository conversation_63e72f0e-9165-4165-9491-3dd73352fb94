<?php

class DexmaZend_Controller_Plugin_FileUploadMiddleware extends Zend_Controller_Plugin_Abstract
{
    /**
     * @var string
     */
    private $logFile;

    /**
     * @var string[]
     */
    private $allowedExtensions = [
        // Documenti
        'doc', 'docx', 'odt', 'pdf', 'rtf', 'txt', 'xls', 'xlsx', 'ods',

        // Immagini
        'jpg', 'jpeg', 'png', 'gif', 'tif', 'tiff', 'bmp', 'svg',

        // Audio
        'mp3', 'wav', 'm4a', 'aac',

        // Video
        'mp4', 'avi', 'mov', 'mkv', 'flv',

        // Archivi
        'zip', 'rar', '7z', 'tar', 'gz',

        // Firma Digitale e Certificati
        'p7m', 'p12', 'crt', 'cer',

        // <PERSON>ri
        'ics', 'eml', 'msg', 'vcf'
    ];

    /**
     * @var string[]
     */
    private $allowedMimeTypes = [
        // Documenti
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.oasis.opendocument.text', 'application/pdf', 'application/rtf', 'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.oasis.opendocument.spreadsheet',

        // Immagini
        'image/jpeg', 'image/png', 'image/gif', 'image/tiff', 'image/bmp', 'image/svg+xml',

        // Audio
        'audio/mpeg', 'audio/wav', 'audio/x-m4a', 'audio/aac',

        // Video
        'video/mp4', 'video/x-msvideo', 'video/quicktime', 'video/x-matroska', 'video/x-flv',

        // Archivi
        'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/x-tar', 'application/gzip',

        // Firma Digitale e Certificati
        'application/pkcs7-mime', 'application/x-pkcs12', 'application/x-x509-ca-cert', 'application/pkix-cert',

        // Altri
        'text/calendar', 'message/rfc822', 'application/vnd.ms-outlook', 'text/vcard',

        // Aggiunte basate sulle estensioni fornite
        'audio/mp3', 'audio/vnd.wav', 'video/avi', 'video/mov',

        'application/x-pem-file', 'application/pkix-crl',

        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel.sheet.macroEnabled.12',
        
        'message/rfc822', 'application/vnd.ms-outlook'
    ];

    /**
     * @param string $logFile
     */
    public function __construct(string $logFile) {
        $this->logFile = $logFile;
    }

    public function preDispatch(Zend_Controller_Request_Abstract $request) {

        $blockedExtensions = [];
        $blockedMimeTypes = [];

        if ($request->getMethod() === 'POST') {

            $uploadedFiles = $_FILES;

            foreach ($uploadedFiles as $key => $file) {
                $filePaths = is_array($file['tmp_name']) ? $file['tmp_name'] : [$file['tmp_name']];
                $fileNames = is_array($file['name']) ? $file['name'] : [$file['name']];

                $number_of_files = count($filePaths);

                for ($i = 0; $i < $number_of_files; $i++) {
                    $filePath = $filePaths[$i];
                    $fileName = $fileNames[$i];

                    if (! $filePath || ! $fileName) {
                        continue;
                    }

                    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);

                    // Controlla l'estensione
                    if (!in_array($fileExtension, $this->allowedExtensions) && !in_array($fileExtension, $blockedExtensions)) {
                        if ($this->canWriteToLogFile()) {
                            file_put_contents($this->logFile, "Blocked Extension: " . $fileExtension . "\n", FILE_APPEND);
                        }
                        $blockedExtensions[] = $fileExtension;
                        // throw new Exception("File extension not allowed.");
                    }

                    $mimeType = mime_content_type($filePath);

                    // Controlla il tipo MIME
                    if (!in_array($mimeType, $this->allowedMimeTypes) && !in_array($mimeType, $blockedMimeTypes)) {
                        if ($this->canWriteToLogFile()) {
                            file_put_contents($this->logFile, "Blocked MIME Type: " . $mimeType . "\n", FILE_APPEND);
                        }
                        $blockedMimeTypes[] = $mimeType;
                        // throw new Exception("MIME type not allowed.");
                    }

                    // Controllo contenuto EICAR (questo è un esempio specifico per il WAPT)
                    if (strpos(file_get_contents($filePath), 'EICAR-STANDARD-ANTIVIRUS-TEST-FILE') !== false) {
                        if ($this->canWriteToLogFile()) {
                            file_put_contents($this->logFile, "Malicious content detected in file: " . $file['name'] . "\n", FILE_APPEND);
                        }
                        // throw new Exception("Malicious content detected.");
                    }
                }
            }
        }
    }

    /**
     * To avoid too big files, max 10mb allowed
     *
     * @return bool
     */
    private function canWriteToLogFile() {
        $maxSize = 10 * 1024 * 1024; // 10 MB
        $currentSize = filesize($this->logFile);

        return $currentSize <= $maxSize;
    }
    
}
