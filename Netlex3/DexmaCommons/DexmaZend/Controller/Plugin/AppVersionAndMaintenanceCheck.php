<?php

declare(strict_types=1);

// phpcs:disable
class DexmaZend_Controller_Plugin_AppVersionAndMaintenanceCheck extends Zend_Controller_Plugin_Abstract
// phpcs:enable
{
    /** @var Zend_Config */
    private $config;

    public function __construct()
    {
        $this->config = Zend_Registry::get('generalConfig');
    }

    public function preDispatch(Zend_Controller_Request_Abstract $request): void
    {
        if (! $request instanceof Zend_Controller_Request_Http) {
            return;
        }

        if (! isRequestFromTslegalApp()) {
            return;
        }

        // Check if app is in maintenance mode
        if ($this->config->mobileApp->maintenance_mode) {
            http_response_code(503);
            echo json_encode([
                'error' => 'maintenance',
                'error_description' => 'app is in maintenance mode',
                'app_error_title' => 'Manutenzione',
                'app_error_description' => 'L\'app è in manutenzione, tornerà disponibile a breve',
            ]);
            die;
        }

        $appVersion = getAppVersionFromTslegalAppHeader();
        $deviceType = getDeviceTypeFromTslegalAppHeader();
        $isAndroid = $deviceType === 'android';

        // Check if app version is supported
        $minAppVersion = $isAndroid ? $this->config->mobileApp->min_android_version : $this->config->mobileApp->min_ios_version;
        if ($appVersion < $minAppVersion) {
            http_response_code(426);
            echo json_encode([
                'error' => 'update',
                'error_description' => 'need to update the app',
                'app_error_title' => 'Aggiorna l\'app',
                'app_error_description' => 'E\' necessario aggiornare l\'app per continuare ad utilizzarla',
            ]);
            die;
        }
    }
}
