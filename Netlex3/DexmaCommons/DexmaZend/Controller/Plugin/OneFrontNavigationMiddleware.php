<?php

declare(strict_types=1);

use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use Netlex3\Software\patterns\Domains\Users\Services\UsersIamService;
use Netlex3\Software\patterns\Domains\Users\Services\UsersService;
use Util\_Constants\OnefrontConstants;

class DexmaZend_Controller_Plugin_OneFrontNavigationMiddleware extends Zend_Controller_Plugin_Abstract
{
    const WHITELIST = [
        'prints/save',
        'cities/save',
    ];

    const DOWNLOAD_ACTIONS = [
        'create-register',
        'generate-antirec',
        'generate-diffida-pdf',
        'buildrelata',
        'generate-lettera-pdf',
        'senddocument',
        'advancedtools',
        'advancedtoolsnew',
        'terminiPDF',
        'gettemplate',
        'onlineprint',
        'get-jnlp',
        'getfile',
        'get-subscription-file',
    ];

    /** @var UsersService */
    private $usersService;

    /** @var Zend_Db_Adapter_Abstract */
    private $dbShared;

    /** @var Zend_Db_Adapter_Abstract */
    private $db;

    private $generalConfig;

    /** @var UsersIamService */
    private $userIamService;

    private $applogger;

    public function __construct() {
        $this->db = Zend_Registry::get('db');
        $dbProvisioning = Zend_Registry::get('dbProvisioning');
        $this->applogger = Zend_Registry::get('applogger');
        $this->dbShared = Zend_Registry::get('dbShared');
        $this->generalConfig = Zend_Registry::get('generalConfig');

        $zend_infrastructure = new ZendInfrastructure( $this->db, $dbProvisioning, $this->dbShared, $this->applogger, null , SITEKEY, $this->generalConfig);
        $this->usersService = UsersService::getInstance($zend_infrastructure);
        $this->userIamService = UsersIamService::getInstance($zend_infrastructure);
    }

    public function preDispatch(Zend_Controller_Request_Abstract $request)
    {
        $controller = $request->getControllerName();
        $action = $request->getActionName();
        $params = $request->getParams();
        $queryString = getCleanQueryStringFromOneFront($request->getRequestUri());
        $currentPath = $controller . '/' . $action . $queryString;

        if (! isRequestFromOnefrontIframe() || in_array($controller . '/' . $action, self::WHITELIST)) {
            return;
        }

        // Se la richiesta non è di tipo GET o l'azione inizia con 'print', non fare nulla
        if (
            ! $request->isGet()
            || strpos($action, 'print') === 0
            || strpos($action, 'export') === 0
            || strpos($action, 'download') === 0
            || in_array($action, self::DOWNLOAD_ACTIONS)
        ) {
            return;
        }

        /**
         * If the SESSION_TOKEN is present and the PHPSESSID cookie is not set or is different from the SESSION_TOKEN,
         * this is used to set the PHPSESSID cookie with the SESSION_TOKEN value at first interaction with one-front embedding.
         */
        $auth = Zend_Auth::getInstance();
        if (! $auth->hasIdentity()) {
            try {
                if ($params['SESSION_TOKEN'] === $this->generalConfig->onefront->testToken) {
                    $userId = (int) $this->generalConfig->onefront->testUserId;
                } else {
                    $jwt = $this->getRequest()->getParam('SESSION_TOKEN');
                    $userIam = $this->userIamService->me($jwt);
                    $userId = $userIam->get('customerUserId');
                    if (! $userId) {
                        throw new Exception('JWT check - User not found');
                    }
                }
                $user = $this->usersService->findUserById($userId);
                $configAcl = Zend_Registry::get('configAcl');
                $authAdapter = new DexmaZend_Auth_Adapter_DbTable($this->db, $configAcl->login, $user['nome'], null, $this->dbShared, false, true, $userId);
                $authAdapter->authenticate();

                $request
                    ->setControllerName('one-front')
                    ->setActionName('index')
                    ->setParam('redirectLegacyPath', $currentPath . (empty($queryString) ? '?' : '&') . 'session_started=true')
                    ->setDispatched(true);

                return;
            } catch (Exception $e) {
                $this->applogger->info('Error in me: ' . $e->getMessage());
            }
        }

        $realTimeLegacyValue = ! empty($_COOKIE['IS_LEGACY']) ? $_COOKIE['IS_LEGACY'] : null;
        if ($request->getParam('IS_LEGACY') && (! isset($realTimeLegacyValue) || $realTimeLegacyValue !== $request->getParam('IS_LEGACY'))) {
            setcookie('IS_LEGACY', $request->getParam('IS_LEGACY'), 0, '/');
            $realTimeLegacyValue = $request->getParam('IS_LEGACY');
        }

        if (isset($realTimeLegacyValue) && $realTimeLegacyValue === 'false') {
            if (in_array($controller . '/' . $action, array_keys(OnefrontConstants::PATHS_TO_REDIRECT_TO_ONEFRONT))) {
                $onefrontPathInfo = OnefrontConstants::PATHS_TO_REDIRECT_TO_ONEFRONT[$controller . '/' . $action];

                $request
                    ->setControllerName('one-front')
                    ->setActionName('index')
                    ->setParams($params)
                    ->setParam('isReplaceable', $onefrontPathInfo['legacy'])
                    ->setParam('onefrontPath', $onefrontPathInfo['path'])
                    ->setDispatched(true);
            }
        }

        // questo è necesario per quei casi tipo il collega documento perché ha una navigazione ibrida su redirect salvato in sessione
        // quindi si fa redirect specificando un url che non esiste su zend ma esiste su onefront e dobbiamo bypassare i controlli
        if (in_array($controller . '/' . $action, OnefrontConstants::URLS_TO_BYPASS_ACL)) {
            // prendi il path preciso che si sta visitando
            $realPath = $_SERVER['REQUEST_URI'];
            $request
                ->setControllerName('one-front')
                ->setActionName('index')
                ->setParams($params)
                ->setParam('isReplaceable', true)
                ->setParam('onefrontPath', $realPath)
                ->setDispatched(true);
        }

        if ($request->getParam('UNIVERSAL')) {
            return;
        }

        if (isset($params['iframe']) && $params['iframe']) {
            return;
        }

        $request
            ->setControllerName('one-front')
            ->setActionName('index')
            ->setParams($params)
            ->setParam('isReplaceable', true)
            ->setParam('onefrontPath', "/legacy/" . $currentPath)
            ->setDispatched(true);
    }

    public function postDispatch(Zend_Controller_Request_Abstract $request)
    {
        $controller = $request->getControllerName();
        $action = $request->getActionName();

        if (! isRequestFromOnefrontIframe() || in_array($controller . '/' . $action, self::WHITELIST)) {
            return;
        }

        // Se la richiesta non è di tipo GET o l'azione inizia con 'print', non fare nulla
        if (
            ! $request->isGet()
            || strpos($action, 'print') === 0
            || strpos($action, 'export') === 0
            || strpos($action, 'download') === 0
            || in_array($action, self::DOWNLOAD_ACTIONS)
        ) {
            return;
        }

        // Verifica se la sessione è stata avviata
        if (session_id() === '') {
            // La sessione non è stata avviata, avviala
            Zend_Session::start();
        }

        // Disabilita l'invio automatico dei cookie di sessione
        ini_set('session.use_cookies', '0');

        // Ottieni i parametri del cookie di sessione
        $cookieParams = session_get_cookie_params();

        // Costruisci l'header Set-Cookie manualmente
        $sessionName = session_name();
        $sessionId = session_id();

        $cookieString = urlencode($sessionName) . '=' . urlencode($sessionId);

        // Aggiungi gli attributi al cookie
        if ($cookieParams['lifetime'] > 0) {
            $cookieString .= '; Expires=' . gmdate('D, d-M-Y H:i:s T', time() + $cookieParams['lifetime']) . ' GMT';
        }
        $cookieString .= '; Path=' . $cookieParams['path'];
        if (!empty($cookieParams['domain'])) {
            $cookieString .= '; Domain=' . $cookieParams['domain'];
        }
        $cookieString .= '; Secure';
        $cookieString .= '; HttpOnly';
        $cookieString .= '; SameSite=None';

        // Imposta l'header Set-Cookie
        $response = $this->getResponse();
        $response->setHeader('Set-Cookie', $cookieString, true);
    }
}
