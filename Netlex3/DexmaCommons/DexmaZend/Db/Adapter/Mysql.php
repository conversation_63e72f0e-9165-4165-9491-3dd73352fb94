<?php

class DexmaZend_Db_Adapter_Mysql extends Zend_Db_Adapter_Pdo_Mysql
{
	public $write = false;
	public $writeHost = false;

	public function setConnection( PDOInterface $connection )
	{
		$this->_connection = $connection;
	}

	public function setWriteHost( $host, $writeHost = false )
	{
		$this->_config[ 'hostW' ] = $host;
		$this->writeHost = $writeHost;
	}

	public function setWriteDb()
	{
		if ( !$this->write && $this->writeHost ) {
			$this->_config[ 'host' ] = $this->_config[ 'hostW' ];
			$this->_connection = null;
			$this->write = true;
		}
	}

	public function update( $table, array $bind, $where = '', $xss_protection = FALSE )
	{
		$this->setWriteDb();
		if ( !empty( $bind ) && $xss_protection ) {
			$bind = $this->XSS_clenear( $bind, $xss_protection );
		}
		return parent::update( $table, $bind, $where );
	}

	public function delete( $table, $where = '' )
	{
		$this->setWriteDb();
		return parent::delete( $table, $where );
	}

	public function insert( $table, array $bind, $xss_protection = FALSE )
	{
		$this->setWriteDb();
		if ( !empty( $bind ) && $xss_protection ) {
			$bind = $this->XSS_clenear( $bind, $xss_protection );
		}
		return parent::insert( $table, $bind );
	}

	public function query( $sql, $bind = array(), $xss_protection = FALSE )
	{
		$this->setWriteDb();
		if ( !empty( $bind ) ) {
			$bind = (array) $bind;
			if ( $xss_protection ) {
				$bind = $this->XSS_clenear( $bind, $xss_protection );
			}
		}
		return parent::query( $sql, $bind );
	}

	public function beginTransaction()
	{
		$this->setWriteDb();
		return parent::beginTransaction();
	}


	public function save( $table, $data, $xss_protection = FALSE )
	{
		try {
			if ( !empty( $data[ 'uniqueid' ] ) ) {
				$uniqueid = $data[ 'uniqueid' ];
				$where[ 'uniqueid = ?' ] = $uniqueid;
				unset( $data[ 'uniqueid' ] );
				if ( $this->update( $table, $data, $where, $xss_protection ) >= 0 ) {
					return $uniqueid;
				}
			} else {
				$data[ 'uniqueid' ] = $this->getUid();
				if ( $this->insert( $table, $data, $xss_protection ) == 1 ) {
					return $data[ 'uniqueid' ];
				}
			}
		} catch ( Zend_Db_Adapter_Exception $e ) {
			throw $e;
		}
	}

	public function getUid()
	{
		$uid = base_convert( microtime( true ), 10, 16 ) . '-' . uniqid( rand() );
		return strtoupper( $uid );
	}

	protected function XSS_clenear( $params, $level )
	{
		foreach ( $params as $key => $param ) {
			// $param = htmlentities($param,2,"UTF-8");
			if ( $level > 0 ) {  // rimozione tags html
				$params[ $key ] = strip_tags( $param );
			}
			if ( $level > 1 ) { // rimozione apostrofo
				$params[ $key ] = preg_replace( "/\"/", "", $param );
			}
		}
		return $params;
	}

	/**
	 * @param string $table The table to insert data into
	 * @param array $col Columns array of string
	 * @param array $values Values array to insert
	 */
	public function multiple_insert( string $table, array $cols, array $vals ) : int
	{

		try {

			if ( !isset( $table ) || !isset( $cols ) || !isset( $vals ) ) throw new Zend_Db_Adapter_Exception( "Invalid params" );

			if ( empty( $cols ) || empty( $vals ) ) return 0;

			$table = $this->quoteIdentifier( $table );

			array_walk( $cols, function ( &$item ) {
				$item = $this->quoteIdentifier( $item );
			} );

			array_walk_recursive( $vals, function ( &$item ) {
                if($item !== null) {
                    $item = addslashes($item);
                }
			} );

			$insertRow	= [];
			array_walk( $vals, function ( $item ) use ( &$insertRow ) {

                $insertString = "(";

                foreach ($item as $key => $record){
                        $insertString .= $record !== null ? "'".$record."'" : 'null';
                        if($key < sizeof($item)-1){
                            $insertString.= ",";
                        }
                }

                $insertString .= ")";

                $insertRow[] = $insertString;

			} );

			$sql = "INSERT INTO " . $table . " (" . implode( ",", $cols ) . ") VALUES " . implode( ",", $insertRow );

			$stmt = $this->query( $sql );
			$result = $stmt->rowCount();

			return $result;

		} catch ( Exception $e ) {

			throw new Zend_Db_Adapter_Exception( $e->getMessage() );

		}

	}

}
