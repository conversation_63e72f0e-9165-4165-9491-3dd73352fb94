<?php
class DexmaZend_View_Smarty extends Zend_View_Abstract
{
	protected $_engine;

	public function __construct($applicationPath)
	{
		require_once(DEXMA_COMMONS . '/Smarty3/Smarty.class.php');
		$this->_engine = new Smarty();
		$this->_engine->error_reporting = E_ALL & ~E_NOTICE;
		$this->_engine->template_dir = $applicationPath . '/Software/templates';
		$this->_engine->compile_dir = $applicationPath . '/Software/data/tmp/templates_c';
		#$this->_engine->plugins_dir = array($applicationPath . '/Software/include/Templater/plugins','plugins');

		$this->_engine->registerPlugin('function', 'dexma_asset', array($this, 'dexmaAsset'));
	}

	public function dexmaAsset($params, $smarty)
	{
		$url = isset($params['url'])? trim($params['url']) : NULL;
		if ($url) {
			$url = trim($params['url']);
			$version = isset($params['version'])? trim($params['version']) : NULL;
			if ($version) {
				if ('.css' === substr($url, -4)) {
					$url = substr($url, 0, -4) . '.' . $version . '.css';
				} elseif ('.js' === substr($url, -3)) {
					$url = substr($url, 0, -3) . '.' . $version . '.js';
				}
			}
			return $url;
		}
	}

	public function getEngine() {
		return $this->_engine;
	}

	public function __set($key, $val) {
		$this->_engine->assign($key, $val);
	}

	public function __get($key) {
		return $this->_engine->get_template_vars($key);
	}

	public function __isset($key) {
		return $this->_engine->get_template_vars($key) !== null;
	}

	public function __unset($key) {
		$this->_engine->clear_assign($key);
	}

	public function assign($spec, $value = null) {
		if (is_array($spec)) {
			$this->_engine->assign($spec);
		}
		else $this->_engine->assign($spec, $value);
	}

	public function clearVars() {
		$this->_engine->clear_all_assign();
	}

	public function render($name) {
		return $this->_engine->fetch(strtolower($name));
	}

	public function _run() {}
}