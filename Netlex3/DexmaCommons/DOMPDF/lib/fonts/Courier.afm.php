<?php return array (
  'codeToName' => 
  array (
    32 => 'space',
    160 => 'space',
    33 => 'exclam',
    34 => 'quotedbl',
    35 => 'numbersign',
    36 => 'dollar',
    37 => 'percent',
    38 => 'ampersand',
    146 => 'quoteright',
    40 => 'parenleft',
    41 => 'parenright',
    42 => 'asterisk',
    43 => 'plus',
    44 => 'comma',
    45 => 'hyphen',
    173 => 'hyphen',
    46 => 'period',
    47 => 'slash',
    48 => 'zero',
    49 => 'one',
    50 => 'two',
    51 => 'three',
    52 => 'four',
    53 => 'five',
    54 => 'six',
    55 => 'seven',
    56 => 'eight',
    57 => 'nine',
    58 => 'colon',
    59 => 'semicolon',
    60 => 'less',
    61 => 'equal',
    62 => 'greater',
    63 => 'question',
    64 => 'at',
    65 => 'A',
    66 => 'B',
    67 => 'C',
    68 => 'D',
    69 => 'E',
    70 => 'F',
    71 => 'G',
    72 => 'H',
    73 => 'I',
    74 => 'J',
    75 => 'K',
    76 => 'L',
    77 => 'M',
    78 => 'N',
    79 => 'O',
    80 => 'P',
    81 => 'Q',
    82 => 'R',
    83 => 'S',
    84 => 'T',
    85 => 'U',
    86 => 'V',
    87 => 'W',
    88 => 'X',
    89 => 'Y',
    90 => 'Z',
    91 => 'bracketleft',
    92 => 'backslash',
    93 => 'bracketright',
    94 => 'asciicircum',
    95 => 'underscore',
    145 => 'quoteleft',
    97 => 'a',
    98 => 'b',
    99 => 'c',
    100 => 'd',
    101 => 'e',
    102 => 'f',
    103 => 'g',
    104 => 'h',
    105 => 'i',
    106 => 'j',
    107 => 'k',
    108 => 'l',
    109 => 'm',
    110 => 'n',
    111 => 'o',
    112 => 'p',
    113 => 'q',
    114 => 'r',
    115 => 's',
    116 => 't',
    117 => 'u',
    118 => 'v',
    119 => 'w',
    120 => 'x',
    121 => 'y',
    122 => 'z',
    123 => 'braceleft',
    124 => 'bar',
    125 => 'braceright',
    126 => 'asciitilde',
    161 => 'exclamdown',
    162 => 'cent',
    163 => 'sterling',
    165 => 'yen',
    131 => 'florin',
    167 => 'section',
    164 => 'currency',
    39 => 'quotesingle',
    147 => 'quotedblleft',
    170 => 'ordfeminine',
    139 => 'guilsinglleft',
    155 => 'guilsinglright',
    150 => 'endash',
    134 => 'dagger',
    135 => 'daggerdbl',
    183 => 'periodcentered',
    182 => 'paragraph',
    149 => 'bullet',
    130 => 'quotesinglbase',
    132 => 'quotedblbase',
    148 => 'quotedblright',
    187 => 'guillemotright',
    133 => 'ellipsis',
    137 => 'perthousand',
    191 => 'questiondown',
    96 => 'grave',
    180 => 'acute',
    136 => 'circumflex',
    152 => 'tilde',
    175 => 'macron',
    168 => 'dieresis',
    184 => 'cedilla',
    151 => 'emdash',
    198 => 'AE',
    216 => 'Oslash',
    140 => 'OE',
    186 => 'ordmasculine',
    230 => 'ae',
    248 => 'oslash',
    156 => 'oe',
    223 => 'germandbls',
    207 => 'Idieresis',
    233 => 'eacute',
    159 => 'Ydieresis',
    247 => 'divide',
    221 => 'Yacute',
    194 => 'Acircumflex',
    225 => 'aacute',
    219 => 'Ucircumflex',
    253 => 'yacute',
    234 => 'ecircumflex',
    220 => 'Udieresis',
    218 => 'Uacute',
    203 => 'Edieresis',
    169 => 'copyright',
    229 => 'aring',
    224 => 'agrave',
    227 => 'atilde',
    154 => 'scaron',
    237 => 'iacute',
    251 => 'ucircumflex',
    226 => 'acircumflex',
    231 => 'ccedilla',
    222 => 'Thorn',
    179 => 'threesuperior',
    210 => 'Ograve',
    192 => 'Agrave',
    215 => 'multiply',
    250 => 'uacute',
    255 => 'ydieresis',
    238 => 'icircumflex',
    202 => 'Ecircumflex',
    228 => 'adieresis',
    235 => 'edieresis',
    205 => 'Iacute',
    177 => 'plusminus',
    166 => 'brokenbar',
    174 => 'registered',
    200 => 'Egrave',
    142 => 'Zcaron',
    208 => 'Eth',
    199 => 'Ccedilla',
    193 => 'Aacute',
    196 => 'Adieresis',
    232 => 'egrave',
    211 => 'Oacute',
    243 => 'oacute',
    239 => 'idieresis',
    212 => 'Ocircumflex',
    217 => 'Ugrave',
    254 => 'thorn',
    178 => 'twosuperior',
    214 => 'Odieresis',
    181 => 'mu',
    236 => 'igrave',
    190 => 'threequarters',
    153 => 'trademark',
    204 => 'Igrave',
    189 => 'onehalf',
    244 => 'ocircumflex',
    241 => 'ntilde',
    201 => 'Eacute',
    188 => 'onequarter',
    138 => 'Scaron',
    176 => 'degree',
    242 => 'ograve',
    249 => 'ugrave',
    209 => 'Ntilde',
    245 => 'otilde',
    195 => 'Atilde',
    197 => 'Aring',
    213 => 'Otilde',
    206 => 'Icircumflex',
    172 => 'logicalnot',
    246 => 'odieresis',
    252 => 'udieresis',
    240 => 'eth',
    158 => 'zcaron',
    185 => 'onesuperior',
    128 => 'Euro',
  ),
  'isUnicode' => false,
  'FontName' => 'Courier',
  'FullName' => 'Courier',
  'FamilyName' => 'Courier',
  'Weight' => 'Medium',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'true',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-23',
    1 => '-250',
    2 => '715',
    3 => '805',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '003.000',
  'EncodingScheme' => 'WinAnsiEncoding',
  'CapHeight' => '562',
  'XHeight' => '426',
  'Ascender' => '629',
  'Descender' => '-157',
  'StdHW' => '51',
  'StdVW' => '51',
  'StartCharMetrics' => '317',
  'C' => 
  array (
    32 => 600.0,
    160 => 600.0,
    33 => 600.0,
    34 => 600.0,
    35 => 600.0,
    36 => 600.0,
    37 => 600.0,
    38 => 600.0,
    146 => 600.0,
    40 => 600.0,
    41 => 600.0,
    42 => 600.0,
    43 => 600.0,
    44 => 600.0,
    45 => 600.0,
    173 => 600.0,
    46 => 600.0,
    47 => 600.0,
    48 => 600.0,
    49 => 600.0,
    50 => 600.0,
    51 => 600.0,
    52 => 600.0,
    53 => 600.0,
    54 => 600.0,
    55 => 600.0,
    56 => 600.0,
    57 => 600.0,
    58 => 600.0,
    59 => 600.0,
    60 => 600.0,
    61 => 600.0,
    62 => 600.0,
    63 => 600.0,
    64 => 600.0,
    65 => 600.0,
    66 => 600.0,
    67 => 600.0,
    68 => 600.0,
    69 => 600.0,
    70 => 600.0,
    71 => 600.0,
    72 => 600.0,
    73 => 600.0,
    74 => 600.0,
    75 => 600.0,
    76 => 600.0,
    77 => 600.0,
    78 => 600.0,
    79 => 600.0,
    80 => 600.0,
    81 => 600.0,
    82 => 600.0,
    83 => 600.0,
    84 => 600.0,
    85 => 600.0,
    86 => 600.0,
    87 => 600.0,
    88 => 600.0,
    89 => 600.0,
    90 => 600.0,
    91 => 600.0,
    92 => 600.0,
    93 => 600.0,
    94 => 600.0,
    95 => 600.0,
    145 => 600.0,
    97 => 600.0,
    98 => 600.0,
    99 => 600.0,
    100 => 600.0,
    101 => 600.0,
    102 => 600.0,
    103 => 600.0,
    104 => 600.0,
    105 => 600.0,
    106 => 600.0,
    107 => 600.0,
    108 => 600.0,
    109 => 600.0,
    110 => 600.0,
    111 => 600.0,
    112 => 600.0,
    113 => 600.0,
    114 => 600.0,
    115 => 600.0,
    116 => 600.0,
    117 => 600.0,
    118 => 600.0,
    119 => 600.0,
    120 => 600.0,
    121 => 600.0,
    122 => 600.0,
    123 => 600.0,
    124 => 600.0,
    125 => 600.0,
    126 => 600.0,
    161 => 600.0,
    162 => 600.0,
    163 => 600.0,
    'fraction' => 600.0,
    165 => 600.0,
    131 => 600.0,
    167 => 600.0,
    164 => 600.0,
    39 => 600.0,
    147 => 600.0,
    170 => 600.0,
    139 => 600.0,
    155 => 600.0,
    'fi' => 600.0,
    'fl' => 600.0,
    150 => 600.0,
    134 => 600.0,
    135 => 600.0,
    183 => 600.0,
    182 => 600.0,
    149 => 600.0,
    130 => 600.0,
    132 => 600.0,
    148 => 600.0,
    187 => 600.0,
    133 => 600.0,
    137 => 600.0,
    191 => 600.0,
    96 => 600.0,
    180 => 600.0,
    136 => 600.0,
    152 => 600.0,
    175 => 600.0,
    'breve' => 600.0,
    'dotaccent' => 600.0,
    168 => 600.0,
    'ring' => 600.0,
    184 => 600.0,
    'hungarumlaut' => 600.0,
    'ogonek' => 600.0,
    'caron' => 600.0,
    151 => 600.0,
    198 => 600.0,
    'Lslash' => 600.0,
    216 => 600.0,
    140 => 600.0,
    186 => 600.0,
    230 => 600.0,
    'dotlessi' => 600.0,
    'lslash' => 600.0,
    248 => 600.0,
    156 => 600.0,
    223 => 600.0,
    207 => 600.0,
    233 => 600.0,
    'abreve' => 600.0,
    'uhungarumlaut' => 600.0,
    'ecaron' => 600.0,
    159 => 600.0,
    247 => 600.0,
    221 => 600.0,
    194 => 600.0,
    225 => 600.0,
    219 => 600.0,
    253 => 600.0,
    'scommaaccent' => 600.0,
    234 => 600.0,
    'Uring' => 600.0,
    220 => 600.0,
    'aogonek' => 600.0,
    218 => 600.0,
    'uogonek' => 600.0,
    203 => 600.0,
    'Dcroat' => 600.0,
    'commaaccent' => 600.0,
    169 => 600.0,
    'Emacron' => 600.0,
    'ccaron' => 600.0,
    229 => 600.0,
    'Ncommaaccent' => 600.0,
    'lacute' => 600.0,
    224 => 600.0,
    'Tcommaaccent' => 600.0,
    'Cacute' => 600.0,
    227 => 600.0,
    'Edotaccent' => 600.0,
    154 => 600.0,
    'scedilla' => 600.0,
    237 => 600.0,
    'lozenge' => 600.0,
    'Rcaron' => 600.0,
    'Gcommaaccent' => 600.0,
    251 => 600.0,
    226 => 600.0,
    'Amacron' => 600.0,
    'rcaron' => 600.0,
    231 => 600.0,
    'Zdotaccent' => 600.0,
    222 => 600.0,
    'Omacron' => 600.0,
    'Racute' => 600.0,
    'Sacute' => 600.0,
    'dcaron' => 600.0,
    'Umacron' => 600.0,
    'uring' => 600.0,
    179 => 600.0,
    210 => 600.0,
    192 => 600.0,
    'Abreve' => 600.0,
    215 => 600.0,
    250 => 600.0,
    'Tcaron' => 600.0,
    'partialdiff' => 600.0,
    255 => 600.0,
    'Nacute' => 600.0,
    238 => 600.0,
    202 => 600.0,
    228 => 600.0,
    235 => 600.0,
    'cacute' => 600.0,
    'nacute' => 600.0,
    'umacron' => 600.0,
    'Ncaron' => 600.0,
    205 => 600.0,
    177 => 600.0,
    166 => 600.0,
    174 => 600.0,
    'Gbreve' => 600.0,
    'Idotaccent' => 600.0,
    'summation' => 600.0,
    200 => 600.0,
    'racute' => 600.0,
    'omacron' => 600.0,
    'Zacute' => 600.0,
    142 => 600.0,
    'greaterequal' => 600.0,
    208 => 600.0,
    199 => 600.0,
    'lcommaaccent' => 600.0,
    'tcaron' => 600.0,
    'eogonek' => 600.0,
    'Uogonek' => 600.0,
    193 => 600.0,
    196 => 600.0,
    232 => 600.0,
    'zacute' => 600.0,
    'iogonek' => 600.0,
    211 => 600.0,
    243 => 600.0,
    'amacron' => 600.0,
    'sacute' => 600.0,
    239 => 600.0,
    212 => 600.0,
    217 => 600.0,
    'Delta' => 600.0,
    254 => 600.0,
    178 => 600.0,
    214 => 600.0,
    181 => 600.0,
    236 => 600.0,
    'ohungarumlaut' => 600.0,
    'Eogonek' => 600.0,
    'dcroat' => 600.0,
    190 => 600.0,
    'Scedilla' => 600.0,
    'lcaron' => 600.0,
    'Kcommaaccent' => 600.0,
    'Lacute' => 600.0,
    153 => 600.0,
    'edotaccent' => 600.0,
    204 => 600.0,
    'Imacron' => 600.0,
    'Lcaron' => 600.0,
    189 => 600.0,
    'lessequal' => 600.0,
    244 => 600.0,
    241 => 600.0,
    'Uhungarumlaut' => 600.0,
    201 => 600.0,
    'emacron' => 600.0,
    'gbreve' => 600.0,
    188 => 600.0,
    138 => 600.0,
    'Scommaaccent' => 600.0,
    'Ohungarumlaut' => 600.0,
    176 => 600.0,
    242 => 600.0,
    'Ccaron' => 600.0,
    249 => 600.0,
    'radical' => 600.0,
    'Dcaron' => 600.0,
    'rcommaaccent' => 600.0,
    209 => 600.0,
    245 => 600.0,
    'Rcommaaccent' => 600.0,
    'Lcommaaccent' => 600.0,
    195 => 600.0,
    'Aogonek' => 600.0,
    197 => 600.0,
    213 => 600.0,
    'zdotaccent' => 600.0,
    'Ecaron' => 600.0,
    'Iogonek' => 600.0,
    'kcommaaccent' => 600.0,
    'minus' => 600.0,
    206 => 600.0,
    'ncaron' => 600.0,
    'tcommaaccent' => 600.0,
    172 => 600.0,
    246 => 600.0,
    252 => 600.0,
    'notequal' => 600.0,
    'gcommaaccent' => 600.0,
    240 => 600.0,
    158 => 600.0,
    'ncommaaccent' => 600.0,
    185 => 600.0,
    'imacron' => 600.0,
    128 => 600.0,
  ),
  'CIDtoGID_Compressed' => true,
  'CIDtoGID' => 'eJwDAAAAAAE=',
  '_version_' => 5,
);