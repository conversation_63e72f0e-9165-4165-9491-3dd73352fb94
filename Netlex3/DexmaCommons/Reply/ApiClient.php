<?php
namespace Reply;

class ApiClient implements ApiInterface
{
	private $apiUrl;
	private $certificatePath;
	private $ch = FALSE;
	private $logPath;
	private $merchantID;
	private $testMode;
	private $verbose;

	private $lastParams;
	private $lastResponse;
	private $lastResultIsOk = FALSE;

	public function __construct($merchantID, $apiUrl, $certificatePath = NULL, $logPath = NULL, $verbose = FALSE, $testMode = FALSE)
	{
		$merchantID = trim($merchantID);
		$apiUrl = filter_var(trim($apiUrl), FILTER_VALIDATE_URL, FILTER_FLAG_PATH_REQUIRED);

		if (!$merchantID || !$apiUrl) {
			throw new \Exception('Missing settings during initialization');
		} elseif (!function_exists('curl_version')) {
			throw new \Exception('cURL is required');
		}

		if ($certificatePath) {
			if (!is_readable($certificatePath)) {
				throw new \Exception('The certificate path is not readable');
			}
			$this->certificatePath = $certificatePath;
		}

		if ($logPath) {
			if (!is_writeable($logPath)) {
				throw new \Exception('The log path is not writeable');
			}
			$this->logPath = $logPath;
		}

		$this->MerchantID = $merchantID;
		$this->apiUrl = $apiUrl;
		$this->testMode = (bool) $testMode;
		$this->verbose = (bool) $verbose;
	}

	public function checkMSISDN($OperatorID, $MSISDN)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<XML
<?xml version="1.0" encoding="ISO-8859-1"?>
<Response>
	<ResultCode>1000</ResultCode>
	<MSISDN>$MSISDN</MSISDN>
	<OperatorID>$OperatorID</OperatorID>
	<Profile>Corporate</Profile>
	<Type>Postpagato</Type>
</Response>
XML;
			$xml = new \SimpleXMLElement($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'MerchantID' => $this->MerchantID,
				'OperatorID' => $OperatorID,
				'MSISDN' => $MSISDN,
			);
			$this->lastParams = $params;

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'MPayCheckMSISDN',
				CURLOPT_POSTFIELDS => http_build_query($params),
			));

			$xml = $this->execute($ch);
		}

		$result = new \stdClass();

		if ($xml) {
			$result->ResultCode = (string) $xml->ResultCode;
			$result->MSISDN = (string) $xml->MSISDN;
			$result->OperatorID = (string) $xml->OperatorID;
			$result->Profile = (string) $xml->Profile;
			$result->Type = (string) $xml->Type;

			$this->setLastResultOk($result->ResultCode);
		}

		return $result;
	}

	public function lastResultIsOk()
	{
		return $this->lastResultIsOk;
	}

	public function getLastParams()
	{
		return $this->lastParams;
	}

	public function getLastInfo()
	{
		if (is_resource($this->ch)) {
			return curl_getinfo($this->ch);
		}
	}

	public function getLastResponse()
	{
		return $this->lastResponse;
	}

	private function setLastResultOk($resultCode)
	{
		$this->lastResultIsOk = '1000' == $resultCode;
	}

	private function getConnection()
	{
		if (FALSE !== $this->ch) {
			return $this->ch;
		}

		$ch = curl_init();
		$opt = array(
			CURLOPT_FAILONERROR => TRUE,
			CURLOPT_FOLLOWLOCATION => TRUE,
			CURLOPT_HEADER => FALSE,
			CURLOPT_POST => TRUE,
			CURLOPT_RETURNTRANSFER => TRUE,
			CURLOPT_SSL_VERIFYHOST => 2,
			CURLOPT_SSL_VERIFYPEER => TRUE,
			CURLOPT_VERBOSE => $this->verbose,
		);

		if ($this->certificatePath) {
			$opt[CURLOPT_CAINFO] = $this->certificatePath;
			$opt[CURLOPT_CAPATH] = dirname($this->certificatePath);
		}

		if ($this->logPath) {
			$opt[CURLOPT_STDERR] = fopen($this->logPath, 'a+');
		}

		curl_setopt_array($ch, $opt);
		$this->ch = $ch;

		return $this->ch;
	}

	private function execute(&$ch)
	{
		$this->lastResultIsOk = FALSE;
		$response = trim(curl_exec($ch));
		if ($curlError = curl_error($ch)) {
			throw new \Exception($curlError);
		}
		$this->lastResponse = $response;

		try {
			return new \SimpleXMLElement($response);
		} catch (Exception $e) {
			error_log($e->getMessage());
		}

		return NULL;
	}

	public function __destruct()
	{
		if (FALSE !== $this->ch) {
			curl_close($this->ch);
		}
	}
}