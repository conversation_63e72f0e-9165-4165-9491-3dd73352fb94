<?php
namespace Reply\Integration;

class ApiClient implements ApiInterface
{
	private $apiUrl;
	private $ch = FALSE;
	private $logPath;
	private $idVendor;
	private $testMode;
	private $verbose;

	private $lastParams;
	private $lastResponse;
	private $lastResultIsOk = FALSE;

	public function __construct($idVendor, $apiUrl, $logPath = NULL, $verbose = FALSE, $testMode = FALSE)
	{
		$idVendor = trim($idVendor);
		$apiUrl = filter_var(trim($apiUrl), FILTER_VALIDATE_URL, FILTER_FLAG_PATH_REQUIRED);

		if (!$idVendor || !$apiUrl) {
			throw new \Exception('Missing settings during initialization');
		} elseif (!function_exists('curl_version')) {
			throw new \Exception('cURL is required');
		} elseif (!function_exists('json_decode')) {
			throw new \Exception('JSON is required');
		}

		if ($logPath) {
			if (!is_writeable($logPath)) {
				throw new \Exception('The log path is not writeable');
			}
			$this->logPath = $logPath;
		}

		$this->idVendor = $idVendor;
		$this->apiUrl = $apiUrl;
		$this->testMode = (bool) $testMode;
		$this->verbose = (bool) $verbose;
	}

	public function activateSubscription($msisdn, $sku)
	{
		if ($this->testMode) {
			$result = json_decode(json_encode(array(
				'result' => array(
					'resultCode' => '0',
					'resultDescription' => 'Success',
					'orderId' => microtime(TRUE),
				),
			)));
		} else {
			$ch = $this->getConnection();
			$params = array(
				'msisdn' => $msisdn,
				'sku' => $sku,
				'idVendor' => $this->idVendor,
			);
			$this->lastParams = $params;
			$JSON = json_encode(array('jsonInput' => array_map('utf8_encode', $params)));

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'activateSubscription',
				CURLOPT_POSTFIELDS => $JSON,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Content-Length: ' . strlen($JSON)
				),
			));

			$result = $this->execute($ch);
		}
		$this->setLastResultOk(isset($result->result, $result->result->resultCode)? $result->result->resultCode : NULL);

		return $result;
	}

	public function deactivateSubscription($orderId)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<JSON
{
    "result": {
        "resultCode": "0",
        "resultDescription": "Success"
    }
}
JSON;
			$result = json_decode($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'orderId' => $orderId,
			);
			$this->lastParams = $params;
			$JSON = json_encode(array('jsonInput' => array_map('utf8_encode', $params)));

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'deactivateSubscription',
				CURLOPT_POSTFIELDS => $JSON,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Content-Length: ' . strlen($JSON)
				),
			));

			$result = $this->execute($ch);
		}
		$this->setLastResultOk(isset($result->result, $result->result->resultCode)? $result->result->resultCode : NULL);

		return $result;
	}

	public function lastResultIsOk()
	{
		return $this->lastResultIsOk;
	}

	public function getLastParams()
	{
		return $this->lastParams;
	}

	public function getLastInfo()
	{
		if (is_resource($this->ch)) {
			return curl_getinfo($this->ch);
		}
	}

	public function getLastResponse()
	{
		return $this->lastResponse;
	}

	private function setLastResultOk($resultCode)
	{
		$this->lastResultIsOk = '0' == $resultCode;
	}

	private function getConnection()
	{
		if (FALSE !== $this->ch) {
			return $this->ch;
		}

		$ch = curl_init();
		$opt = array(
			CURLOPT_FAILONERROR => TRUE,
			CURLOPT_FOLLOWLOCATION => TRUE,
			CURLOPT_HEADER => FALSE,
			CURLOPT_POST => TRUE,
			CURLOPT_RETURNTRANSFER => TRUE,
			CURLOPT_SSL_VERIFYHOST => 2,
			CURLOPT_SSL_VERIFYPEER => TRUE,
			CURLOPT_VERBOSE => $this->verbose,
		);

		if ($this->logPath) {
			$opt[CURLOPT_STDERR] = fopen($this->logPath, 'a+');
		}

		curl_setopt_array($ch, $opt);
		$this->ch = $ch;

		return $this->ch;
	}

	private function execute(&$ch)
	{
		$this->lastResultIsOk = FALSE;
		$response = trim(curl_exec($ch));
		if ($curlError = curl_error($ch)) {
			throw new \Exception($curlError);
		}
		$this->lastResponse = $response;

		return json_decode($response);
	}

	public function __destruct()
	{
		if (FALSE !== $this->ch) {
			curl_close($this->ch);
		}
	}
}