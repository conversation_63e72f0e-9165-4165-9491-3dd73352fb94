<?php

namespace  <PERSON><PERSON><PERSON>ommons\OneDriveManager;

use DateInterval;
use DateTime;
use Zend_Config_Ini;
use Zend_Log;
use Zend_Log_Filter_Priority;
use Zend_Log_Writer_Stream;

class OneDriveHandler
{
    //TODO sostituisce la costante CustomControllerAction::$DOCUMENTS
    public const  BASE_DOCUMENT_PATH = 'documents';
    public const  BASE_DOCUMENT_CONTRACT_PATH = 'contracts';

    private $config;
    private $db;
    private $loggedUser;

    /**
     * @var string Default uri if not set in config
     */
    private $default_redirect = "https://amazon.netlex.cloud/onedrive";

    private $subdomain;
    protected $applogger;
    protected $netlexSettings;

    function save($db, $table, $data, $xss_protection = FALSE)
    {
        try
        {
            if (!empty($data['uniqueid']))
            {
                $uniqueid = $data['uniqueid'];
                $where['uniqueid = ?'] = $uniqueid;
                unset($data['uniqueid']);
                if($db->update($table, $data, $where, $xss_protection) >= 0)
                {
                    return $uniqueid;
                }
            }
            else
            {
                $data['uniqueid'] = getUid();
                if($db->insert($table, $data, $xss_protection) == 1)
                {
                    return $data['uniqueid'];
                }
            }
        }
        catch (Zend_Db_Adapter_Exception $e)
        {
            throw $e;
        }
    }


    public function __construct($db, $config, $subdomain, $loggedUser){
        $this->db = $db;
        $this->loggedUser = $loggedUser;

        if(empty($subdomain)){
            $subdomain = SITEKEY;
        }
        $this->subdomain = $subdomain;

        if(empty($config)){
            $config = new Zend_Config_Ini(SITE_ROOT . '/conf/config.ini', APPLICATION_ENV, array('allowModifications' => true));
            $config->merge(new Zend_Config_Ini(SITE_ROOT . '/conf/config_secrets.ini', APPLICATION_ENV));
        }
        $this->config = $config;

        $applogger = new Zend_Log(new Zend_Log_Writer_Stream(SITE_ROOT .  "/Software/data/logs/oneDrive.log"));
        $filter = new Zend_Log_Filter_Priority(6);
        $applogger->addFilter($filter);
        $this->applogger = $applogger;
        $this->netlexSettings = $this->db->fetchRow("SELECT * FROM settings WHERE id = 1");
    }

    public function call($data=array()){
        $content_type = "Content-Type: application/x-www-form-urlencoded";
        $curlHeader = array($content_type);
        if(isset($data['header'])){
            $curlHeader = $data['header'];
        }

        if(isset($data['bearer_token'])){
            array_push($curlHeader, $data['bearer_token']);
        }
        $url = $data['url'];
        $request_type = $data['request_type'];

        $receivedHeaders = [];

        $curlParams = array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $request_type,
            CURLOPT_HTTPHEADER => $curlHeader
        );

        if(in_array($request_type, ["POST","PUT", "PATCH"]) && isset($data['postParams'])){
            $curlParams[CURLOPT_POSTFIELDS] = $data['postParams'];
        }
        if(isset($data['follow_location'])){
            $curlParams[CURLOPT_FOLLOWLOCATION] = $data['follow_location'];
        }
        if(isset($data['is_copy']) && $data['is_copy']) {
            // Se chiamata per copia aggiungi parsing degli header per ottenere Location
            $curlParams[CURLOPT_HEADERFUNCTION] = function($curl, $header) use (&$receivedHeaders) {
                $len = strlen($header);
                $header = explode(':', $header, 2);

                // Ignora headers non validi
                if (count($header) < 2) {
                    return $len;
                }

                $receivedHeaders[strtolower(trim($header[0]))][] = trim($header[1]);

                return $len;
            };
        }
        $curl = curl_init();
        curl_setopt_array($curl, $curlParams);
        $response = curl_exec($curl);
        $info = curl_getinfo($curl);
        curl_close($curl);

        //Aggiungo il redirect_url in caso di download del file
        if($info['redirect_url']){
            $response = json_decode($response);
            $response['download_url'] = $info['redirect_url'];
            $response = json_encode($response);
        }
        //per il logout restituisco solo http_code
        if(isset($data['is_logout'])){
            $response = $info['http_code'];
        } else if(isset($data['is_copy']) && $data['is_copy']) {
            // Se copia estrapolo drive-id dall'header "Location" (url monitoraggio stato avanzamento copia)

            if(!empty($receivedHeaders) && isset($receivedHeaders['location'])) {
                $response = $receivedHeaders['location'][0];
            }
        }

//        if(!isset($data['follow_location']) && !isset($data['is_logout'])){
//            $this->applogger->info(print_r($this->subdomain . ": " . $response,1));
//        }

        //DELETE, GET per il download e POST per copia documento restituiscono response vuota, casi limite
        if(empty($response) && !in_array($info['http_code'], [202, 204, 302])) {
            $this->applogger->info("*** OneDrive Error, empty response for " . $this->subdomain . ", user_id: " . (!empty($this->loggedUser) ? $this->loggedUser->id : '') . "***");
        } else if(isset($response->error)){
            $this->applogger->info("*** OneDrive Error ***\nsubdomain: " . $this->subdomain . "\nuser_id: " . (!empty($this->loggedUser) ? $this->loggedUser->id : '') . "\nError: " . $response->error . "\nError Description: " . $response->error_description . "\n*********");
        }

        return $response;
    }

    public function checkOneDriveToken(){
        $tokenData = $this->db->fetchRow("SELECT one_drive_access_token, one_drive_refresh_token, one_drive_access_token_date FROM settings");
        $result = $tokenData['one_drive_access_token'];
        if(!empty($tokenData['one_drive_refresh_token'])){
            $config = $this->config;
            $expire_date = DateTime::createFromFormat('Y-m-d H:i:s', $tokenData['one_drive_access_token_date']);
            $redirect_uri = $config->oneDrive->redirect_uri ?? $this->default_redirect;
            if(new DateTime() > $expire_date){
                $postParams = "client_id=" . $config->oneDrive->client_id .
                    "&redirect_uri=" . $redirect_uri .
                    "&client_secret=" . $config->oneDrive->client_secret .
                    "&refresh_token=" . $tokenData['one_drive_refresh_token'] .
                    "&grant_type=" . "refresh_token";
                $data = array(
                    "url" => "https://login.microsoftonline.com/common/oauth2/v2.0/token",
                    "postParams" => $postParams,
                    "request_type" => "POST"
                );
                $response = json_decode($this->call($data));

                if(isset($response->error)){
                    $this->applogger->info("*** OneDrive Error ***\nsubdomain: " . $this->subdomain . "\nuser_id: " . (!empty($this->loggedUser) ? $this->loggedUser->id : '') . "\nError: " . $response->error . "\nError Description: " . $response->error_description . "\n*********");
                } elseif(isset($response->access_token)) {
                    $expiration_date = new Datetime();
                    $expiration_date->add(new DateInterval("PT1H"));
                    $uId = "SELECT uniqueid
						FROM settings";
                    $uId = $this->db->fetchOne($uId);
                    $data = array(
                        "one_drive_access_token" => $response->access_token,
                        "one_drive_refresh_token" => $response->refresh_token,
                        "one_drive_access_token_date" => $expiration_date->format("Y-m-d H:i:s")
                    );
                    if (!empty($uId)) {
                        $data["uniqueid"] = $uId;
                    }
                    $uniqueid = $this->save($this->db,"settings", $data);
                    if (!empty($uniqueid)) {
                        $result = $response->access_token;
                        //Zend_Registry::get("Cache")->remove("netlex_settings");
                    }
                } else {
                    $this->applogger->info(print_r("*** OneDrive Error *** \n" . $response . "\n*******", 1));
                }
            }
        }
        return $result;
    }

    public function filenamePurifier($filename) {
        $filename = str_replace(" ", "_", $filename);
        return $filename = preg_replace("/[^a-zA-Z0-9\_\-\.]/i", '', $filename);
    }

    /**
     * Aggiorna l'id OneDrive della sottocartella nel db
     *
     * @param string $subfolderName nome della sottocartella
     * @param string $subfolderUid id OneDrive attribuito alla sottocartella
     * @param string $fileId id della pratica in cui è contenuta la sottocartella in Netlex
     */
    public function updateFolderId($subfolderName, $subfolderUid, $fileId)
    {
        $document_folder_data = $this->db->fetchRow("SELECT uniqueid, one_drive_uniqueid
                                                                    FROM document_folders 
                                                                    WHERE name = ? 
                                                                    AND file_id = ?", array($subfolderName, $fileId));
        if (empty($document_folder_data['one_drive_uniqueid'])) {
            $this->db->update('document_folders',
                array('one_drive_uniqueid' => $subfolderUid),
                array('uniqueid = ?' => $document_folder_data['uniqueid']));
        }
    }

    public function createSub($path, $parent){
        $accessToken = $this->checkOneDriveToken();

        $folder = explode("/", $path);

        $postParams = array(
            "name" => end($folder),
            "folder" => array(),
            "@microsoft.graph.conflictBehavior" => "replace"
        );

        $createFolderData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/" . $parent . "/children",
            "request_type" => "POST",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            ),
            "postParams" => json_encode($postParams, JSON_FORCE_OBJECT)
        );
        $response = json_decode($this->call($createFolderData));
        return $response;
    }

    public function upload($upload_handler, $fileUid, $fileId, $fileName, $fileSize, $filePath, $direct = null, $folderPath = null, $contractId = null){

        $postParams = json_encode(array("item" => array("@microsoft.graph.conflictBehavior" => "rename")));

        if (! $contractId) {
            $uploadFolder = "/" . $this->subdomain;

            if (! empty($fileUid)) {
                $uploadFolder .= "/" . $this->subdomain . "_" . $fileId;
                if (empty($folderPath)) {
                    $uploadFolder .= "/";
                }
            } else {
                $uploadFolder .= "/public/";
            }

            if (! empty($folderPath)) {
                $uploadFolder .= $folderPath;
            }

            $uploadFolder .= $fileName;


        } else {
            $uploadFolder = $this->getDocumentsFilePath(
                $fileName,
                null,
                $contractId
            );
        }

        $accessToken = $this->checkOneDriveToken();

        if(!empty($fileId)){
            $folderId = $this->createFolder($fileId);
            if (isset($folderId->id)) {
                $this->db->update("archivio", array("one_drive_folder_id" => $folderId->id), array("uniqueid = ?" => $fileUid));
            }
        }

        $driveRootUrl = $this->getUrlDriveRoot($uploadFolder);
        $driveRootUrl .= ':/createUploadSession';

        $uploadSessionData = array(
            "url" => $driveRootUrl,
            "postParams" => $postParams,
            "request_type" => "POST",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );
        $response = json_decode($this->call($uploadSessionData));
        //inizio upload file
        $uploadFileData = array(
            "url" => $response->uploadUrl,
            "request_type" => "PUT",
            "header" => array(
                "Content-Length: " . $fileSize,
                "Content-Range: " . "bytes 0-" . ($fileSize - 1) . "/" . $fileSize
            ),
            "postParams" => !$direct ? file_get_contents($filePath) : $direct
        );


        $response = json_decode($this->call($uploadFileData));
        if(!empty($upload_handler)){
            if (method_exists($upload_handler, 'getDocUniqueid'))
            {
                $doc_uniqueid = $upload_handler->getDocUniqueid();
            }
            else{
                $doc_uniqueid = $upload_handler->getUniqueid();
            }
            $this->db->update("documento",
                array("one_drive_uniqueid" => $response->id, "one_drive_url" => $response->webUrl, "one_drive_new_upload" => 1),
                array("uniqueid = ?" => $doc_uniqueid)
            );
        }
        //condivisione con utenti
        if(empty($fileUid)){
            $this->oneDriveInviteUsersToFileFolder($response->parentReference->id);
        } else {
            $fileData = $this->db->fetchRow("SELECT one_drive_folder_id, riservata FROM archivio WHERE uniqueid = ?", $fileUid);

            if(empty($fileData['one_drive_folder_id'])){
                //non esiste ancora la cartella della pratica in onedrive

                if (!$folderPath) {
                    // primo upload senza sottocartelle

                    $fileFolderOnedriveId = $response->parentReference->id;

                } else {
                    // primo upload con sottocartelle

                    $pathToFolder = SITEKEY . "/" . SITEKEY . "_" . $fileId;
                    $fileFolderOnedriveInfo = $this->getFileByName($pathToFolder);
                    $fileFolderOnedriveId = $fileFolderOnedriveInfo->id;

                    if (isset($response->parentReference->id) && isset($fileFolderOnedriveId) && $response->parentReference->id != $fileFolderOnedriveId) {
                        $this->setOnedriveIdForSubfolders($fileFolderOnedriveId, $response->parentReference->id, $fileId);
                    }

                }

                $this->db->update("archivio",
                    array("one_drive_folder_id" => $fileFolderOnedriveId),
                    array("uniqueid = ?" => $fileUid)
                );
                $this->spreadOneDrivePermissions($fileId, $fileFolderOnedriveId, $fileData['riservata'], $accessToken);

            } else {
                // gestire situazione esistente con aggiunta file in sottocartella aggiuntiva

                if (isset($response->parentReference->id) && $response->parentReference->id != $fileData['one_drive_folder_id']) {
                    $this->setOnedriveIdForSubfolders($fileData['one_drive_folder_id'], $response->parentReference->id, $fileId);
                }
                $this->spreadOneDrivePermissions($fileId, $fileData['one_drive_folder_id'], $fileData['riservata'], $accessToken);

            }
        }
        return $response;
    }

    /**
     * Condivide con gli utenti la cartella OneDrive (fuori dalla pratica).
     *
     * @param string $parentId	driveId della cartella (parentReference->id del documento caricato)
     */
    public function oneDriveInviteUsersToFileFolder(string $parentId) {

        // $this->applogger->info("TEST 3570");
        $whereCondition = ' WHERE (u.tipoutente = 1 OR u.tipoutente = 0 ) AND u.attivo = 1 AND u.one_drive_email IS NOT NULL AND (u.one_drive_uniqueid IS NULL OR u.one_drive_uniqueid != ?)';
        $data = array($this->netlexSettings['one_drive_master_id']);

        $utentiInvitati = $this->db->fetchAll(
            "SELECT DISTINCT u.one_drive_email as email FROM utente u
			$whereCondition",
            $data
        );

        if(count($utentiInvitati) > 0) {
            $postParams = array(
                "requireSignIn" => true,
                "sendInvitation" => false,
                "roles" => array('write'),
                "recipients" => $utentiInvitati,
                "message" => "Here's the file that we're collaborating on."
            );

            $accessToken = $this->checkOneDriveToken();
            $shareFileData = array(
                "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$parentId/invite",
                "request_type" => "POST",
                "header" => array(
                    "Content-Type: application/json",
                    "Authorization: Bearer " . $accessToken
                ),
                "postParams" => json_encode($postParams)
            );

            $inviteResponse = json_decode($this->call($shareFileData));

            if (isset($inviteResponse->value)) {
                foreach($inviteResponse->value as $value) {
                    $oneDriveEmail = $value->invitation->email;
                    $permissionId = $value->id;
                    $oneDriveUserUid = $value->grantedTo->user->id;

                    $oneDriveUser = $this->db->fetchRow("SELECT u.id, u.one_drive_uniqueid FROM utente u WHERE u.one_drive_email = ?", $oneDriveEmail);
                    if(empty($oneDriveUser['one_drive_uniqueid'])){
                        $this->db->update("utente",
                            array("one_drive_uniqueid" => $oneDriveUserUid),
                            array("one_drive_email = ?" => $oneDriveEmail)
                        );
                    }
                }
            }
        }
    }

    /*
		i permessi di OneDrive non possono essere propagati a utenti che hanno già i permessi.
		Dopo aver eseguito una qualunque operazione (Add, Delete, Edit), vengono fatti i seguenti controlli:
			1) La pratica è riservata (dopo l'operazione)
				1.a) propagazione dei permessi agli utenti con riserva che NON li hanno già
			2) La pratica non è riservata (dopo l'operazione)
				2.a) propagazione dei permessi a tutti i soggetti della pratica che NON li hanno già
	*/
    public function spreadOneDrivePermissions($fileId, $oneDriveFolderId, $isReserved, $accessToken, $force=0){

        if(empty($accessToken)){
            $accessToken = $this->checkOneDriveToken();
        }

        $table = $isReserved == 'true' ? 'utentipraticariservata' : 'utente_pratica';
        $sql = "SELECT u.person_id FROM $table u WHERE u.file_id = ?";
        $data[] = $fileId;
        if($force == 0){
            $sql .= " AND NOT EXISTS (SELECT * FROM onedrive_permessi op WHERE op.file_id = ? AND op.user_id = u.person_id)";
            $data[] = $fileId;
        }

        $users = $this->db->fetchCol($sql, $data);

        if (!$isReserved && empty($users)) {
            $users = $this->db->fetchCol('
				SELECT u.id
				FROM utente u
				WHERE one_drive_email IS NOT NULL
			');
        }

//		$this->applogger->info(print_r("UTENTI A CUI PROPAGARE:",1));
//		$this->applogger->info(print_r($users,1));
        if ( !empty( $users ) ) {
            $users = implode( ",", $users );
            $sql = "SELECT u.one_drive_email as email
					FROM utente u
					WHERE (u.tipoutente = 1 OR u.tipoutente = 0) AND u.attivo = 1 AND u.one_drive_email IS NOT NULL
					AND (u.one_drive_uniqueid IS NULL OR u.one_drive_uniqueid != ?) AND u.id IN ($users)";

            $utentiOneDrive = $this->db->fetchAll( $sql, array( $this->netlexSettings[ 'one_drive_master_id' ] ) );

//			$this->applogger->info(print_r("UTENTI ONE DRIVE:",1));
//			$this->applogger->info(print_r($utentiOneDrive,1));

            $postParams = array(
                "requireSignIn" => true,
                "sendInvitation" => false,
                "roles" => array( 'write' ),
                "recipients" => $utentiOneDrive,
                "message" => "Here's the file that we're collaborating on."
            );
            $shareFileData = array(
                "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$oneDriveFolderId/invite",
                "request_type" => "POST",
                "header" => array(
                    "Content-Type: application/json",
                    "Authorization: Bearer " . $accessToken
                ),
                "postParams" => json_encode( $postParams )
            );
            $response = json_decode( $this->call( $shareFileData ) );
            if ( !empty( $response->value ) ) {
                foreach ( $response->value as $value ) {
                    $oneDriveEmail = isset($value->invitation->email) && !empty($value->invitation->email) ? $value->invitation->email : NULL ;
                    $permissionId = $value->id;
                    $oneDriveUserUid = $value->grantedTo->user->id;

                    $oneDriveUser = $oneDriveEmail ? $this->db->fetchRow( "SELECT u.id, u.one_drive_uniqueid FROM utente u WHERE u.one_drive_email = ?", $oneDriveEmail ) : NULL;
                    if ( empty( $oneDriveUser[ 'one_drive_uniqueid' ] ) ) {
                        $this->db->update( "utente",
                            array( "one_drive_uniqueid" => $oneDriveUserUid ),
                            array( "one_drive_email = ?" => $oneDriveEmail )
                        );
                    }

                    if ( !empty( $oneDriveUser[ 'id' ] ) && !empty( $fileId ) ) {

                        $permissionExists = $this->db->fetchOne( "SELECT id FROM onedrive_permessi WHERE user_id = ? AND file_id = ? AND permission_id IS NOT NULL", array( $oneDriveUser[ 'id' ], $fileId ) );
                        if ( empty( $permissionExists ) ) {
                            $permissionData = array(
                                "user_id" => $oneDriveUser[ 'id' ],
                                "file_id" => $fileId,
                                "permission_id" => $permissionId,
                                "created_at" => date( "Y-m-d H:i:s" )
                            );
                            $this->db->insert( "onedrive_permessi", $permissionData );
                        }
                    }

                }

            }
        }



        //caso limite, aggiungo la riserva DOPO aver già propagato: vanno cancellati i permessi degli utenti non nella riserva
        if($isReserved == 'true' && $force==0){
//			$this->applogger->info("RIMOZIONE CONDIVISIONE");
//			$usersToRemove = $this->db->fetchCol("
//				SELECT u.person_id
//				FROM utente_pratica u
//				WHERE ( u.file_id = ? AND NOT EXISTS (SELECT * FROM utentipraticariservata upr WHERE upr.person_id = u.person_id AND upr.file_id = ?) )
//			", array($fileId, $fileId));

            $usersToRemove = $this->db->fetchCol("
				SELECT u.id
				FROM utente u
				WHERE one_drive_email IS NOT NULL AND NOT EXISTS (SELECT * FROM utentipraticariservata upr WHERE upr.person_id = u.id AND upr.file_id = ?)
			", array($fileId) );

//			$this->applogger->info("usersToRemove: ".print_r($usersToRemove,1));

            if(!empty($usersToRemove)){

                $usersToRemove = implode(",", $usersToRemove);

                $permissionToRemove = $this->db->fetchAll(
                    "SELECT op.id, op.permission_id FROM onedrive_permessi op WHERE op.file_id = ? AND op.user_id IN ($usersToRemove)",
                    array($fileId));

//				$this->applogger->info("usersToRemove(implode): ".print_r($usersToRemove,1). "fileid:".$fileId);
//				$this->applogger->info("permissionToRemove: ".print_r($permissionToRemove,1));

                if(!empty($permissionToRemove)){
                    foreach($permissionToRemove as $permission){
                        $permissionUid = $permission['permission_id'];
                        $permissionData = array(
                            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$oneDriveFolderId/permissions/$permissionUid",
                            "request_type" => "DELETE",
                            "header" => array(
                                "Content-Type: application/json",
                                "Authorization: Bearer " . $accessToken
                            )
                        );
                        $response = json_decode($this->call($permissionData));
                        $this->db->delete("onedrive_permessi", array("id = ?" => $permission['id']));
                    }
                }
            }
        }
    }

    public function getFileMetadata($uniqueid){
        $accessToken = $this->checkOneDriveToken();
        $getItemData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid",
            "request_type" => "GET",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );
        $response = json_decode($this->call($getItemData));
        return $response;
    }

    public function checkMonitorCopy($monitorUrl, &$maxRetry = 10){
        $getItemData = array(
            "url" => $monitorUrl,
            "request_type" => "GET"
        );

        $response = json_decode($this->call($getItemData));

        if(isset($response->status) && $response->status === "inProgress"){
            $maxRetry--;
            if($maxRetry > 0){
                sleep(1);
                $response = $this->checkMonitorCopy($monitorUrl, $maxRetry);
            }
        }

        return $response;
    }

    public function getVersionList($uniqueid){
        $accessToken = $this->checkOneDriveToken();
        $getItemVersionsData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid/versions",
            "request_type" => "GET",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );
        $response = json_decode($this->call($getItemVersionsData));
        return $response;
    }

    public function delete($uniqueid){
        $response = 500;
        $accessToken = $this->checkOneDriveToken();
        $response = $this->getFile($uniqueid);
        //Delete dell'oggetto solo se esiste ancora su OneDrive
        if(!isset($response->error)){
            $deleteData = array(
                "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid",
                "request_type" => "DELETE",
                "header" => array(
                    "Content-Type: application/json",
                    "Authorization: Bearer " . $accessToken
                )
            );
            $response = json_decode($this->call($deleteData));
            if(isset($response->error)){
                throw new Exception("One Drive Error");
            }
        }
        return $response;
    }

    public function download($uniqueid){
        $accessToken = $this->checkOneDriveToken();
        $downloadData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid/content",
            "request_type" => "GET",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );
        $response = json_decode($this->call($downloadData));
        if(isset($response->error)){
            throw new \Exception('download - il json ritorna un errore');
        }
        return $response;
    }

    public function copy($uniqueid, $postParams){
        $accessToken = $this->checkOneDriveToken();
        $copyFileData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid/copy",
            "request_type" => "POST",
            "postParams"=> json_encode((object)$postParams),
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            ),
            "is_copy" => true
        );

        $response = $this->call($copyFileData);
        return $response;
    }

    public function getChildren($folderId){
        $accessToken = $this->checkOneDriveToken();
        $endpoint = "https://graph.microsoft.com/v1.0/me/drive/items/$folderId/children";
        $totalResponse = [];
        do{
            $childrenData = array(
                "url" => $endpoint,
                "request_type" => "GET",
                "header" => array(
                    "Content-Type: application/json",
                    "Authorization: Bearer " . $accessToken
                )
            );
            $response = json_decode($this->call($childrenData),1);
            $totalResponse = array_merge($totalResponse,$response['value']);
            $endpoint = (isset($response['@odata.nextLink'])) ? $response['@odata.nextLink'] : NULL ;
        }while($endpoint);
        $response['value'] = $totalResponse;
        return $response;
    }

    public function getVersion($uniqueid, $versionId, $getDownloadUrl = false){
        $accessToken = $this->checkOneDriveToken();
        $url = "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid/versions/$versionId";
        if($getDownloadUrl) $url .= "/content";
        $versionData = array(
            "url" => $url,
            "request_type" => "GET",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );

        $response = json_decode($this->call($versionData),1);
        return $response;
    }

    public function restoreVersion($uniqueid, $versionId){
        $accessToken = $this->checkOneDriveToken();
        $versionData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid/versions/$versionId/restoreVersion",
            "request_type" => "POST",
            "header" => array(
                "Content-Type: application/json",
                "Content-Length: 0",
                "Authorization: Bearer " . $accessToken
            )
        );

        $response = json_decode($this->call($versionData),1);
        return $response;
    }

    public function invite($folderId, $postParams){
        $accessToken = $this->checkOneDriveToken();
        $permissionData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$folderId/invite",
            "request_type" => "POST",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            ),
            "postParams" => json_encode($postParams)
        );
        $response = json_decode($this->call($permissionData));
        return $response;
    }

    public function removePermessions($folderId, $permissionId){
        $accessToken = $this->checkOneDriveToken();
        $permissionData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$folderId/permissions/$permissionId",
            "request_type" => "DELETE",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );
        $response = json_decode($this->call($permissionData));
        return $response;
    }

    public function getFile($uniqueid){
        $accessToken = $this->checkOneDriveToken();
        $getItemData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid",
            "request_type" => "GET",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );
        $response = json_decode($this->call($getItemData));
        return $response;
    }

    /**
     *
     * Dal documento in upload risale il folder tree fino alla cartella pratica,
     * settando gli id Onedrive alle sottocartelle della pratica sul db
     *
     * @param $fileFolderOnedriveId
     * @param $documentParentOnedriveId
     * @param $fileId
     * @return void
     */
    public function setOnedriveIdForSubfolders($fileFolderOnedriveId, $documentParentOnedriveId, $fileId){
        $folderId = $documentParentOnedriveId;

        while ($folderId != $fileFolderOnedriveId) {
            $parentInfo = $this->getFile($folderId);
            if (!empty($parentInfo) && isset($parentInfo->name) && isset($parentInfo->id)
                && isset($parentInfo->parentReference) && isset($parentInfo->parentReference->id)) {
                $this->updateFolderId($parentInfo->name, $parentInfo->id, $fileId);
                $folderId = $parentInfo->parentReference->id;
            } else {
                break;
            }
        }
    }

    public function getFileByName($filePath){
        $accessToken = $this->checkOneDriveToken();

        $driveRootUrl = $this->getUrlDriveRoot($filePath);

        $getItemData = array(
            "url" => $driveRootUrl,
            "request_type" => "GET",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            )
        );
        $response = json_decode($this->call($getItemData));
        return $response;
    }

    public function doesFileExist($filePath){
        $response = $this->getFileByName($filePath);
        return !isset($response->error);
    }

    public function createFolder($fileId) {
        $accessToken = $this->checkOneDriveToken();
        $postParams = array(
            "name" => $this->subdomain . "_" . $fileId,
            "folder" => array(),
            "@microsoft.graph.conflictBehavior" => "replace"
        );

        $subdomainFolder = $this->getFileByName($this->subdomain);

        if (! isset($subdomainFolder->id)) {
            return $subdomainFolder;
        }

        $subdomainFolder = $subdomainFolder->id;
        // $subdomainFolder = "root/" . $this->subdomain;

        $createFolderData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$subdomainFolder/children",
            "request_type" => "POST",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            ),
            "postParams" => json_encode($postParams, JSON_FORCE_OBJECT)
        );
        $response = json_decode($this->call($createFolderData));
        return $response;
    }

    public function move($folderId, $uniqueid){
        $accessToken = $this->checkOneDriveToken();
        $patchParams = array(
            "parentReference" => array(
                "id" => $folderId
            )
        );
        $moveData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid",
            "request_type" => "PATCH",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            ),
            "postParams" => json_encode($patchParams)
        );
        $response = json_decode($this->call($moveData));
        return $response;
    }

    public function rename($filename, $uniqueid){
        $accessToken = $this->checkOneDriveToken();
        $patchParams = array("name" => $filename);
        $renameData = array(
            "url" => "https://graph.microsoft.com/v1.0/me/drive/items/$uniqueid",
            "request_type" => "PATCH",
            "header" => array(
                "Content-Type: application/json",
                "Authorization: Bearer " . $accessToken
            ),
            "postParams" => json_encode($patchParams)
        );

        $response = json_decode($this->call($renameData));
        return $response;
    }

    public function getDocumentsFilePath(?string $nomefile, ?int $archivioId = null, ?int $contractId = null): ?string
    {
        $filePath = '/' . SITEKEY . '/';

        if ($archivioId) {
            $filePath .= SITEKEY . '_' . $archivioId . '/' . $nomefile;
        }

        if ($contractId) {
            $filePath .= self::BASE_DOCUMENT_CONTRACT_PATH . '/' . $contractId . '/' . $nomefile;
        }

        return $filePath;
    }

    public function getUrlDriveRoot(string $filePath): string
    {
        $url = 'https://graph.microsoft.com/v1.0/me/drive/root:/';
        $filePathResult = ltrim($filePath, '/');

        return $url . $filePathResult;
    }
}