const tariffarioPersonalizzato = 20;
const voceLibera = -1;

function contractOpenMethodInsert(uId, fileUniqueid, callback) {
    $('#contractDate').datepicker('setDate', '0');
    $('#contractLimitschek').prop('checked', true);
    manageRanges(uId, fileUniqueid, callback, {
        difficulty: $('form#contractForm select[name="difficolta"]').val(),
        multiAssisted: $('form#contractForm input[name="num_assistiti"]').val(),
    });
}

function loadContractAuthorities(authorities) {
    var $contractAuthorities = $('#contractAuthorities').empty().prop('disabled', true);
    if (authorities.length) {
        var $fragment = $(document.createDocumentFragment());
        for (var i = 0; i < authorities.length; i++) {
            var opt = authorities[i];
            $fragment.append($('<option/>').val(opt.id).text(opt.nome));
        }
        $contractAuthorities.append($fragment).prop('disabled', false).val(authorities[0].id);
    }
}

function manageRanges(uId, fileUniqueid, callback, customParams = {}) {
    callback = (typeof callback == 'function') ? callback : function () {
    };
    var select = $('#contractRange').empty();
    var contractVersion = $('#contractVersion option:selected').val();
    var authorityId = $('#contractAuthorities').val();
    var customRate = 0;
    if (contractVersion == tariffarioPersonalizzato) {
        var customRate = $('#customRates option:selected').val();
    }

    var params = {
        uId: uId,
        authorityId: authorityId,
        fileUniqueid: fileUniqueid,
        customRate: customRate,
        year: $('#contractVersion option:selected').val()
    };
    $.getJSON('/contract/getranges', params, function (data) {
        if (data) {
            var ranges = data.ranges;
            if (ranges.length === 0) {
                $('#contractRangeContainer').addClass('hide');
            } else {
                $('#contractRangeContainer').removeClass('hide');
                for (var i = 0, optionText = '', id = 0; i < ranges.length; i++) {
                    if (ranges[i].value) {
                        var min = number_format(ranges[i].min, 2, ',', '.');
                        var max = number_format(ranges[i].max, 2, ',', '.');
                        optionText = min + ' - ' + max;
                        if (ranges[i].nome != '' && ranges[i].nome != undefined) {
                            optionText = ranges[i].nome + ' (' + optionText + ')';
                        }
                        id = ranges[i].value;
                    } else if (ranges[i].nome) {
                        optionText = ranges[i].nome;
                        id = ranges[i].id;
                    } else {
                        id = ranges[i].Field;

                        optionText = id.replace('fascia_', '').split('_');

                        if (optionText.length == 2) {
                            var firstVal = parseInt(optionText[0]);
                            optionText[1] = /^\d+$/.test(optionText[1])
                                ? number_format(optionText[1], 2, ',', '.')
                                : optionText[1];

                            if ((Number.isNaN && !Number.isNaN(firstVal))
                                || (!Number.isNaN && firstVal == firstVal)) {
                                optionText[0] = parseInt(optionText[0]) + 0.01;
                                optionText[0] = number_format(optionText[0], 2, ',', '.');
                                optionText = optionText.join(' - ');
                            } else {
                                optionText = optionText.join(' ');
                            }
                        }
                    }

                    $('<option/>').val(id).text(optionText).appendTo(select);
                }

                if (data.fascia) {
                    select.val(data.fascia);
                    if (!select.val()) {
                        select.val(select.find('option:first').val());
                    }
                }
            }

            managePhases(uId, callback, customParams);
        }
    });
}

function getRanges(value) {
    var values = value.split('-');
    return {min: values[0], max: values[1], param: values[2] || null};
}

function isPercentual(event) {
    var charCode = (event.which) ? event.which : event.keyCode;
    if (!event.shiftKey && (charCode == 173 || charCode == 109)) {//hyphen
        return true;
    } else {
        return isKeyboardNumber(event, false);
    }
}

function managePhases(uId, callback, customParams = {}) {
    callback = (typeof callback == 'function') ? callback : function () {
    };

    var contractVersion = $('#contractVersion option:selected').val();
    var customRate = 0;
    var range = $('#contractRange').val();

    if (contractVersion == tariffarioPersonalizzato) {
        customRate = $('#customRates option:selected').val();
    }

    const params = {
        uId: uId,
        authorityId: $('#contractAuthorities').val(),
        columnName: $('#contractRange').val(),
        customRate: customRate,
        range: range || '',
        year: $('#contractVersion option:selected').val(),
        fileUniqueId: $('#fileUniqueid').val(),
        ...customParams
    }
    
    $.getJSON('/contract/getphases', params, function(data){
        const warningAlert = document.getElementById('warningAlert');
        if (warningAlert && data.warning) {
            warningAlert.innerHTML = data.warning;
            warningAlert.classList.remove('hide');
        }

        data.values = data.values.map(function(item){
            item.value = item.value.replace(/,/g, '.')
            return item;
        })
        callback(data)
    });
}

function renderPhases(data, callback) {
    var $phases = $('#phases').empty();
    var currency = "€";
    if ($('#currency').length && $('#currency').val() != '') {
        currency = $('#currency').val();
    }
    if (data) {
        var html = '';
        var phases = data.phases;
        var values = data.values;
        var total = 0.00;

        if (data.contract) {
            $('#contractLimitschek').prop('checked', data.contract.applica_limiti == 1);
        }

        if (data.percentualPhases == 0) {
            $('#contractLimitsLabel').text('Applica limiti da decreto');

            for (var i = 0; i < phases.length; i++) {
                var ranges = getRanges(values[i].value);
                var currentVal = ranges.param || ranges.min;
                var isDisabled = ' disabled';
                var isMuted = ' muted';

                if (data.contract) {
                    currentVal = data.contract['fase' + (i + 1)];
                }

                if (currentVal != null) {
                    var currentFloatVal = parseFloat(currentVal);
                    total += currentFloatVal;
                    currentVal = currentFloatVal.toFixed(2);
                    isDisabled = isMuted = '';
                }

                html += '<div id="fase' + (i + 1) + 'Container" class="control-group' + isMuted + '">';
                html += '<label class="control-label" for="fase' + (i + 1) + '">' + _(phases[i].descrizione) + ' </label>';
                html += '<div class="controls">';
                html += '<input class="input-mini phase toSumPartial" type="text" id="fase' + (i + 1) + '" name="fase' + (i + 1) + '" value="' + currentVal + '" onkeydown="return isKeyboardNumber(event, true)" onchange="validateRange($(this), ' + ranges.min + ', ' + ranges.max + ')"' + isDisabled + ' />';
                html += '<span class="help-inline">(Min ' + currency + ' ' + number_format(ranges.min.toString(), 2, ',', '.') + ' - Max ' + currency + ' ' + number_format(ranges.max.toString(), 2, ',', '.') + ')</span>';
                html += getToggleCheckbox(i + 1, currentVal);
                html += '</div>';
                html += '</div>';
            }
        } else {
            $('#contractLimitsLabel').text('Applica limiti percentuali da decreto');

            for (var i = 0; i < phases.length; i++) {
                var currentVal = 0;
                var isDisabled = ' disabled';
                var isMuted = ' muted';

                if (data.contract) {
                    currentVal = data.contract['fase' + (i + 1) + '_perc'];
                }

                if (currentVal != null) {
                    total += parseFloat(values[i].value);
                    isDisabled = isMuted = '';
                }

                html += '<div id="fase' + (i + 1) + 'Container" class="control-group' + isMuted + '">';
                html += '<label class="control-label" for="fase' + (i + 1) + '_perc">' + phases[i].descrizione + ' </label>';
                html += '<div class="controls">';
                html += '<input class="input-mini toSumPartial" type="text" id="fase' + (i + 1) + '" name="fase' + (i + 1) + '" readonly="readonly" value="' + parseFloat(values[i].value).toFixed(2) + '"' + isDisabled + ' />';
                html += '<div class="input-prepend input-append contractMarginLeft">';
                html += '<button id="substractPerc' + (i + 1) + '" title="Diminuisci percentuale" onclick="addSubtractPerc(\'-\', \'fase' + (i + 1) + '\', ' + values[i].value + ', ' + phases[i].minorazione + ')" type="button" class="btn addSubstractArrow"' + isDisabled + '><i class="icon-chevron-left"></i></button>';
                html += '<input class="input-xmini phase" id="fase' + (i + 1) + '_perc" name="fase' + (i + 1) + '_perc" onchange="validatePercent($(this), ' + values[i].value + ', ' + phases[i].minorazione + ', ' + phases[i].maggiorazione + ')" onkeydown="return isPercentual(event)" type="text" value="' + currentVal + '"' + isDisabled + ' /><span class="add-on">%</span>';
                html += '<button id="addPerc' + (i + 1) + '" onclick="addSubtractPerc(\'+\', \'fase' + (i + 1) + '\', ' + values[i].value + ', ' + phases[i].maggiorazione + ')" title="Aumenta percentuale" type="button" class="btn addSubstractArrow"' + isDisabled + '><i class="icon-chevron-right"></i></button>';
                html += getToggleCheckbox(i + 1, currentVal);
                html += '</div>';
                html += '</div>';
                html += '</div>';
            }
        }

        $('#total').text(currency + ' ' + number_format(total.toString(), 2, ',', '.'));
        $phases.html(html);
        changeLimits();
        displayDialog('contractDIV');
        typeof callback === 'function' && callback();
    }
}

function getToggleCheckbox(i, val) {
    return [
        '<input id="phaseToggler' + i + '" class="phaseToggler" type="checkbox" onclick="togglePhase(' + i + ')"' + (val != null ? ' checked' : '') + '>',
        '<label for="phaseToggler' + i + '" class="checkbox inline togglerLabel">'+ gettext('Includi fase') +'</label>'
    ].join('');
}

function togglePhase(i) {
    if (!$('#phaseToggler' + i).prop('checked')) {
        $('#fase' + i).prop('disabled', true);
        $('#fase' + i + 'Container').addClass('muted');
        $('#fase' + i + '_perc').prop('disabled', true);
        $('#substractPerc' + i).prop('disabled', true);
        $('#addPerc' + i).prop('disabled', true);
    } else {
        $('#fase' + i).prop('disabled', false);
        $('#fase' + i + 'Container').removeClass('muted');
        $('#fase' + i + '_perc').prop('disabled', false);
        $('#substractPerc' + i).prop('disabled', false);
        $('#addPerc' + i).prop('disabled', false);
    }

    refreshTotal();
}

function changeLimits() {
    $('#phases input.phase').each(function () {
        $(this).change();
    });

    refreshTotal();
}

function validateRange(field, lowerLimit, upperLimit) {
    var limitsON = $('#contractLimitschek').prop('checked');
    var fieldValue = parseFloat(field.val());

    if ((Number.isNaN && Number.isNaN(fieldValue))
        || (!Number.isNaN && fieldValue != fieldValue)
        || fieldValue < 0) {
        fieldValue = 0;
        field.val(0);
    }

    if (limitsON) {
        var lowerLimitFL = parseFloat(lowerLimit);
        var upperLimitFL = parseFloat(upperLimit);
        if (fieldValue < lowerLimitFL) {
            field.val(lowerLimitFL.toFixed(2));
        } else if (fieldValue > upperLimitFL) {
            field.val(upperLimitFL.toFixed(2));
        }
    }

    refreshTotal();
}

function validatePercent(fieldPerc, startValue, lowerLimit, upperLimit) {
    var limitsON = $('#contractLimitschek').prop('checked');
    var fieldPercValue = parseInt(fieldPerc.val());

    if ((Number.isNaN && Number.isNaN(fieldPercValue))
        || (!Number.isNaN && fieldPercValue != fieldPercValue)) {
        fieldPercValue = 0;
        fieldPerc.val(0);
    }

    if (fieldPercValue < -100) {
        fieldPerc.val(-100);
    }

    if (limitsON) {
        if (fieldPercValue < -parseInt(lowerLimit)) {
            fieldPerc.val(-lowerLimit);
        } else if (fieldPercValue > parseInt(upperLimit)) {
            fieldPerc.val(upperLimit);
        }
    }

    var fieldName = fieldPerc.attr('id').split('_')[0];

    calculatePhaseValue($('#' + fieldName), startValue, fieldPerc.val());
}

function addSubtractPerc(operator, fieldName, startValue, limit) {
    var phaseField = $('#' + fieldName);

    if (phaseField.val() > 0 || operator != '-') {
        var percField = $('#' + fieldName + '_perc');
        var percValue = parseInt(percField.val());

        var limitsON = $('#contractLimitschek').prop('checked');

        if (operator == '-') {
            if (limitsON) {
                if (percValue > -limit) {
                    percValue--;
                }
            } else {
                percValue--;
            }
        } else {
            if (limitsON) {
                if (percValue < limit) {
                    percValue++;
                }
            } else {
                percValue++;
            }
        }

        percField.val(percValue);

        calculatePhaseValue(phaseField, startValue, percValue);
    }
}

function calculatePhaseValue(phaseField, startValue, percValue) {
    var newValue = startValue * (percValue / 100);
    newValue = (startValue + newValue).toFixed(2);
    if (newValue < 0) {
        newValue = 0;
    }

    phaseField.val(newValue);
    refreshTotal();
}

function refreshTotal() {
    var $contractVersion = $('#contractVersion');
    var $contractValue = $('#contractValue');
    var currency = '€';
    if ($('#currency').length && $('#currency').val() != '') {
        currency = $('#currency').val();
    }

    var total = 0.00;
    if ($contractVersion.val() != voceLibera) {
        $('#phases input.toSumPartial').each(function () {
            var $this = $(this);
            if (!$this.prop('disabled')) {
                total += parseFloat($this.val());
            }
        });
    } else {
        total = parseFloat($contractValue.val());
    }

    $('#contractTotal').val(total);
    $('#total').text(currency + ' ' + number_format(total.toString(), 2, ',', '.'));
}

function contractCloseMethod() {
    $('#contractTotal').val(0);
    $('#contractAuthorities option:first').prop('selected', true);
    $('#contractRange').empty();
    $('#phases').empty();
}

function saveContractData() {
    if (
        $('#contractVersion').val() == voceLibera
        && (
            !checkField('contractDescription', 'Descrizione obbligatoria')
            || !checkField('contractValue', 'Valore obbligatorio')
        )
    ) {
        return;
    }

    $('#contractForm').attr('action', '/' + contractMainPath + '/save').submit();
}

function deleteContract(uniqueId) {
    if (confirm('Eliminare definitivamente il contratto?')) {
        location.href = '/' + contractMainPath + '/delete?fileUniqueid=' + $('#fileUniqueid').val() + '&uniqueid=' + uniqueId;
    }
}

$(function () {


    $('#contractVersion').on('change', function() {
        if(parseFloat($(this).val()) >= 2022){
            $('#diffDiv').show();
            $('#assistitiDiv').show();
        } else {
            $('#diffDiv').hide();
            $('#assistitiDiv').hide();
        }
    });

    $('form#contractForm select[name="difficolta"]').on('change', function() {
        managePhases(null, renderPhases, {
			difficulty: $(this).val(),
            multiAssisted: $('form#contractForm input[name="num_assistiti"]').val(),
		});
    });

    $('form#contractForm input[name="num_assistiti"]').on('change', function() {
		managePhases(null, renderPhases, {
            difficulty: $('form#contractForm select[name="difficolta"]').val(),
			multiAssisted: $(this).val(),
		});
	});

    var $contractVersion = $('#contractVersion');
    var $contractAuthorities = $('#contractAuthorities');
    var $contractValue = $('#contractValue'); // Mostrato per tipo = "Voce libera"

    $contractVersion.on('change', function () {
        if ($contractVersion.val() != voceLibera) {
            $('#contractValueContainer').addClass('hide');
            $('#contractDescriptionContainer').addClass('hide');

            if ($contractVersion.val() != tariffarioPersonalizzato) {
                $('#customRatesDiv').addClass('hide');
            } else {
                $('#customRatesDiv').removeClass('hide');
            }

            $('#contractAuthority').removeClass('hide');
            $('#contractRangeContainer').removeClass('hide');
            $('#contractLimits').removeClass('hide');
            $('#totalContainer').removeClass('hide');
            $('#phases').removeClass('hide');
            $('#contractValueContainer').addClass('hide');

            var $customRate = $('#customRates option:selected').val();

            $('#contractRange').attr('disabled' , true)
            $('#phases').attr('disabled' , true)
            $('#archive-contract-update #difficolta').attr('disabled' , true)
            $('#num_assistiti').attr('disabled' , true)
            $('#contractAuthorities').attr('disabled' , true)

            $.getJSON('/contract/getauthorities', {
                version: $contractVersion.val(),
                customRate: $customRate
            }, function (json) {
                $('#contractRange').attr('disabled' , false)
                $('#phases').attr('disabled' , false)
                $('#archive-contract-update #difficolta').attr('disabled' , false)
                $('#num_assistiti').attr('disabled' , false)
                $('#contractAuthorities').attr('disabled' , false)

                if (json && json.authorities) {
                    loadContractAuthorities(json.authorities);
                }
                $contractAuthorities.change();
            });
        } else {
            $('#customRatesDiv').addClass('hide');
            $('#contractAuthority').addClass('hide');
            $('#contractRangeContainer').addClass('hide');
            $('#contractLimits').addClass('hide');
            $('#totalContainer').addClass('hide');
            $('#phases').addClass('hide');

            $('#contractValueContainer').removeClass('hide');
            $('#contractDescriptionContainer').removeClass('hide');
        }
    });

    $contractValue.on('change', function () {
        refreshTotal();
    });


    $('#contractLimitschek').change(changeLimits);

});