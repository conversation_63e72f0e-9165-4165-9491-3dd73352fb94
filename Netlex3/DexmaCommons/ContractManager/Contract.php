<?php

use Util\_Constants\Contract;

class ContractManager_Contract
{
	const MAX_NUMBER_OF_PHASES = 6;
	const DEFAULT_VERSION = 1;
	const TARIFFARIO_PERSONALIZZATO = 20;

	private $dbRatetable,
			$authority,
			$range,
			$phases,
			$rangePrefix = 'fascia_';

	public function __construct($dbRatetable)
	{
		$this->dbRatetable = $dbRatetable;
	}

	public function getAuthoritiesSelect($version = self::DEFAULT_VERSION)
	{
		if ($version != self::TARIFFARIO_PERSONALIZZATO) {
            if((int)$version >= 2022){
                $result = $this->dbRatetable->fetchAll('SELECT id, nome FROM autorita WHERE versione >= ? ORDER BY nome', $version);
            }else{
				$version = ((int)$version === 2018) ? 2 : $version;
			    $result = $this->dbRatetable->fetchAll('SELECT id, nome FROM autorita WHERE versione = ? ORDER BY nome', $version);
            }
        } else {
			$idTariffario = $_REQUEST['customRate'];
			$netlexDb = SITEKEY;
			$result= $this->dbRatetable->fetchAll("
					SELECT ta.id, ta.nome
					FROM autorita ta INNER JOIN $netlexDb.tariffariofasce tf ON ta.id=tf.id_autorita
					WHERE tf.id_tariffario=?
					GROUP BY tf.id_autorita
					", $idTariffario);
		}
		
		return $result;
	}

	public function getRangesSelect($authorityId) {
        $versione = $this->getAuthorityVersione($authorityId);
        $tableName = $this->getAuthorityTableName($authorityId);
        if($versione == 2) { //FASCIA 2018
            return ($this->getRangesSelect2018($authorityId, $tableName));
        }else {   //FASCIA 2022
            return ($this->getRangesSelectAfter2022($authorityId, 2022));
        }
    }

    public function getRangesSelect2018($authorityId, $tableName) {
        $show = "SHOW COLUMNS FROM " . $tableName ." LIKE ?";
        $columns = $this->dbRatetable->fetchAll($show, $this->rangePrefix . '%');
        if($authorityId == 22 || $authorityId == 48 || $authorityId == 75){
            if($authorityId == 22 || $authorityId == 48) {
                $select = "SELECT fascia as id, nome FROM nomi_fasce WHERE autorita = 22 AND fascia < 13 ORDER BY fascia";
            } else {
                $select = "SELECT fascia as id, nome FROM nomi_fasce WHERE autorita = 22 ORDER BY fascia";
            }
            $names = $this->dbRatetable->fetchAll($select);
            $names_n = [];
            foreach ($names as $key => $name) {
                $name['id'] = "fascia_" . $name['id'];
                $names_n[] = $name;
            }
            //echo print_r($names_n, 1);
            return $names_n;
        }
        return $columns;
    }

    public function getRangesSelectAfter2022($authorityId, $year): array
    {
        if($authorityId == Contract::PENAL_2014 ||
            $authorityId == Contract::PENAL_2018 ||
            $authorityId == Contract::PENAL_GE_2022)
        {
            $select = "SELECT fascia as id, nome FROM nomi_fasce WHERE autorita = 22";
            $names = $this->dbRatetable->fetchAll($select);
            $names_n = [];
            foreach ($names as $key => $name) {
                $name['id'] = "fascia_" . $name['id'];
                $names_n[] = $name;
            }
            //echo print_r($names_n, 1);
            return $names_n;
        }

        $ranges = [];
        $tableName = $this->getAuthorityTableName($authorityId);
        $query = "SELECT fasce_max_valori->'$.values' as list , indeterminati FROM " . $tableName . " WHERE anno = ?";
        $result = $this->dbRatetable->fetchRow($query, $year);
        $range_max_values = json_decode($result['list'], false);

        foreach ($range_max_values as $idx=> $item) {
            $min = 0;
            if ($idx !== 0) {
                $min = $range_max_values[$idx - 1];
            }
            $max = $range_max_values[$idx];

            $ranges[$idx]['Field'] = sprintf("fascia_%d_%d", $min, $max);
        }

        if((int)$result['indeterminati'] === 1){
            $ranges[count($range_max_values)]['Field'] = 'fascia_indeterminato_basso';
            $ranges[count($range_max_values)+1]['Field'] = 'fascia_indeterminato_alto';
        }

        return $ranges;
    }

	public function getNomeFascia($authorityId, $fascia)
	{
		return $this->dbRatetable->fetchOne('SELECT nome FROM nomi_fasce WHERE autorita = ? AND fascia = ?', array($authorityId, $fascia));
	}

	public function getAuthorityTableName($authorityId)
	{
		return $this->dbRatetable->fetchOne('SELECT nome_tabella FROM autorita WHERE id = ?', $authorityId);
	}

    public function getAuthorityVersione($authorityId)
    {
        return $this->dbRatetable->fetchOne('SELECT versione FROM autorita WHERE id = ?', $authorityId);
    }

	public function getValues($tableName, $columnName, $whereCondition = NULL)
	{
		$select =  "SELECT $columnName AS value
					FROM $tableName";
		if (!empty($whereCondition)) {
			$select .= " WHERE " . $whereCondition;
		}
		return $this->dbRatetable->fetchAll($select);
	}

    public function getValuesAfter2022($tableName, $year){
        return $this->dbRatetable->fetchRow("SELECT * FROM ".$tableName." WHERE anno = ?", $year);
    }

	public function countPercentualPhases($tableName)
	{
		$select = "SHOW COLUMNS FROM " . $tableName . " where field like 'maggiorazione%' or field like 'minorazione%'";
		return count($this->dbRatetable->fetchAll($select));

		/*
		$select =  "SELECT COUNT(COLUMN_NAME)
					FROM INFORMATION_SCHEMA.COLUMNS
					WHERE TABLE_NAME = ?
					AND (COLUMN_NAME LIKE 'maggiorazione%' OR COLUMN_NAME LIKE 'minorazione%')";
		*/
		//return $this->dbRatetable->fetchOne($select, array($tableName));
	}

	public function getPhases($tableName, $columnName)
	{
		$selectPhases = 'SELECT descrizione';

		if ($this->countPercentualPhases($tableName) > 0)
		{
			if (($tableName == 'tribunale' && $columnName == 'fascia_indeterminabile')
					|| ($tableName == 'giudice_pace' && $columnName != 'fascia_0_5000'))
			{
				$selectPhases .= ', maggiorazione_secondaria AS maggiorazione, minorazione_secondaria AS minorazione';
			}
			else
			{
				$selectPhases .= ', maggiorazione, minorazione';
			}
		}

		$selectPhases .= " FROM $tableName";

		return $this->dbRatetable->fetchAll($selectPhases);
	}

	public function getAuthoritiesDescriptions($authority, $yearVersion = null)
	{
        if (isset($yearVersion) && $yearVersion >= 2022) {
            $tableValues = $this->dbRatetable->fetchRow("SELECT * FROM " . $authority . " WHERE anno = 2022");
            $table       = json_decode($tableValues['tabella'], true);

            $result = array_map(function ($item) {
                return ['descrizione' => $item];
            }, array_keys($table['Fasi']));

            array_values((array)sort($result));

            return array_map(function($item){
                return preg_replace('/\d_/', '', $item);
            },$result);
        }

        $selectPhases = "SELECT descrizione
                     FROM $authority
                     ORDER BY id";

        return $this->dbRatetable->fetchAll($selectPhases);
    }

	public function buildPhasesList($phasesDescriptions, $contract)
	{
		$contractPhases = '\line\line';
		$i = 1;
		$char = ord('a');
		foreach ($phasesDescriptions as $phase)
		{
			if (isset($contract["fase{$i}"]) && $contract["fase{$i}"] != 0)
			{
				$contract["fase{$i}"] = $this->getNumberInEuroFormat($contract["fase{$i}"]);
				$contractPhases .= ' ' . chr($char++) . ")\t Euro " . $contract["fase{$i}"] . ' per la ' . $phase['descrizione'] . '\line';
			}
			$i++;
		}
		$contractPhases .= '\line';

		return $contractPhases;
	}

	public function buildNoPhasesList($contract) {
		return '\line\line' . " a)\t Euro " . $contract["totale"] . ' per la ' . $contract['descrizione'] . '\line\line';
	}

	private function getNumberInEuroFormat($numeroDaConvertire)
	{
		return number_format($numeroDaConvertire, 2, ',' , '.');
	}

	public function getRtfText($listaclienti, $avvocato, $cointestatari, $listacontroparti, $oggetto, $valore, $totale, $fasi, $coeffSpese,
							$partitaiva, $polizza = '', $citta, $data)
	{
		$templateFile = DEXMA_COMMONS . '/ContractManager/contract_template.rtf';

		if ($contractTemplate = file_get_contents($templateFile))
		{
			$contractTemplate = str_replace('%NOMICLIENTI%', $listaclienti, $contractTemplate);
			$contractTemplate = str_replace('%NOMEAVVOCATO%', $avvocato, $contractTemplate);
			$contractTemplate = str_replace('%COINTESTATARI%', $cointestatari, $contractTemplate);
			$contractTemplate = str_replace('%CONTROPARTI%', $listacontroparti, $contractTemplate);
			$contractTemplate = str_replace('%OGGETTOPRATICA%', $oggetto, $contractTemplate);
			$contractTemplate = str_replace('%VALOREPRATICA%', $valore, $contractTemplate);
			$contractTemplate = str_replace('%TOTALE%', $totale, $contractTemplate);
			$contractTemplate = str_replace('%FASI%', $fasi, $contractTemplate);
			$contractTemplate = str_replace('%SPESEGENERALIPERC%', $coeffSpese, $contractTemplate);
			$contractTemplate = str_replace('%PARTITAIVA%', $partitaiva, $contractTemplate);

			if (!empty($polizza))
			{
				$polizza = '{\par Il professionista indica di seguito i dati della propria polizza assicurativa:\line ' . $polizza . '\par}';
			}
			$contractTemplate = str_replace('%POLIZZA%', $polizza, $contractTemplate);

			$contractTemplate = str_replace('%CITTA%', $citta, $contractTemplate);
			$contractTemplate = str_replace('%DATA%', $data, $contractTemplate);

			return $contractTemplate;
		}
	}

	public function sendRtf($contractTemplate)
	{
		if (function_exists('mb_strlen'))
		{
			$size = mb_strlen($contractTemplate, '8bit');
		}
		else
		{
			$size = strlen($contractTemplate);
		}

		header('Content-Description: File Transfer');
		header('Content-Type: application/octet-stream');
		header('Content-Disposition: attachment; filename=Contratto.rtf');
		header('Content-Transfer-Encoding: binary');
		header('Expires: 0');
		header('Cache-Control: must-revalidate');
		header('Pragma: public');
		header('Content-Length: ' . $size);
		ob_clean();
		flush();
		echo $contractTemplate;
	}
}
