<?php

namespace <PERSON><PERSON><PERSON>om<PERSON>\Wizards\Interfaces;

use DexmaCom<PERSON>\Wizards\Classes\Components\WizardComponent;
use DexmaCom<PERSON>\Wizards\Classes\Rules\WizardRule;
use DexmaCommons\Wizards\Classes\Wizard;

interface WizardInterface
{

    /**
     * @param WizardComponent[]|null $components
     * @return Wizard
     */
    public function setComponents(?array $components): Wizard;

    /**
     * @param WizardRule[]|null $rules
     * @return Wizard
     */
    public function setRules(?array $rules): Wizard;


    /**
     * @param mixed $id
     * @return Wizard
     */
    public function setId($id): Wizard;

    /**
     * @return WizardComponent[]|null
     */
    public function getComponents(): ?array;


    /**
     * @return WizardRule[]|null
     */
    public function getRules(): ?array;

}