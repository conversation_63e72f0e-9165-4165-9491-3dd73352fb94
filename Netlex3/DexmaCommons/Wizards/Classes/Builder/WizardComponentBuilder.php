<?php

namespace <PERSON>ma<PERSON>om<PERSON>\Wizards\Classes\Builder;


use DexmaCom<PERSON>\Wizards\Classes\Components\RadioWizardComponent;
use DexmaCommons\Wizards\Classes\Components\SelectWizardComponent;
use DexmaCommons\Wizards\Classes\Components\WizardComponent;
use DexmaCommons\Wizards\Constants\Components;

class WizardComponentBuilder
{
    private $data;
    /**
     * @var mixed|null
     */
    private $callable;

    /**
     * @param array $data
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build(){

        if($this->data['type'] == Components::SELECT){
            return new SelectWizardComponent($this->data);
        }

        if($this->data['type'] == Components::RADIO){
            return new RadioWizardComponent($this->data);
        }

        return new WizardComponent($this->data);
    }
}