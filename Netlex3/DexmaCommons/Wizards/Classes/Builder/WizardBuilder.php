<?php

namespace <PERSON>ma<PERSON>om<PERSON>\Wizards\Classes\Builder;

use Dexma<PERSON>om<PERSON>\Wizards\Classes\Wizard;

class WizardBuilder
{
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build($buildChild = true): Wizard
    {

        $components = [];
        $rules = [];

        $wizard = (new Wizard($this->data['title']))
            ->setId($this->data['id'])
            ->setCreatedAt($this->data['created_at'])
            ->setUpdatedAt($this->data['updated_at'])
            ->setCreatedBy($this->data['created_by'])
            ->setUpdatedBy($this->data['updated_by']);


        if (!$buildChild) {
            return $wizard;
        }

        foreach (json_decode($this->data['components'], true) as $component) {
            $components[] = (new WizardComponentBuilder($component))->build();
        }

        foreach (json_decode($this->data['rules'], true) as $rule) {
            $rules[] = (new WizardRuleBuilder($rule))->build();
        }

        return $wizard
            ->setRules($rules)
            ->setComponents($components)
            ->setDynamicFileds(json_decode($this->data['dynamic_fields'], true));

    }
}