<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Wizards\Classes;


use DexmaCom<PERSON>\Wizards\Classes\Components\WizardComponent;
use <PERSON>maCom<PERSON>\Wizards\Classes\Rules\WizardRule;
use DexmaCom<PERSON>\Wizards\Interfaces\WizardInterface;

class Wizard implements WizardInterface
{

    protected $id;
    protected $title;

    protected $updated_by;
    protected $created_by;

    protected $created_at;
    protected $updated_at;

    /** @var WizardComponent[] */
    protected $components;

    /** @var WizardRule[] */
    protected $rules;
    /**
     * @var array
     */
    private $dynamic_fileds;

    protected $regex;

    public function __construct($title){
        $this->components = null;
        $this->rules = null;
        $this->dynamic_fileds = null;
        $this->title = $title;
    }

    /**
     * @return mixed
     */

    /**
     * @param WizardComponent[]|null $components
     * @return Wizard
     */
    public function setComponents(?array $components): Wizard
    {
        $this->components = $components;
        return $this;
    }

    /**
     * @param WizardRule[]|null $rules
     * @return Wizard
     */
    public function setRules(?array $rules): Wizard
    {
        $this->rules = $rules;
        return $this;
    }

    /**
     * @param mixed $id
     * @return Wizard
     */
    public function setId($id): Wizard
    {
        $this->id = $id ?: null;
        return $this;
    }

    /**
     * @return WizardComponent[]|null
     */
    public function getComponents(): ?array
    {
        return $this->components;
    }

    /**
     * @return WizardRule[]|null
     */
    public function getRules(): ?array
    {
        return $this->rules;
    }

    /**
     * @return mixed
     */
    public function getUpdatedBy()
    {
        return $this->updated_by;
    }

    /**
     * @return mixed
     */
    public function getCreatedBy()
    {
        return $this->created_by;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @return mixed
     */
    public function getTitle()
    {
        return $this->title;
    }

    public function toArray($otherInfo = false): array{
        $wizardArray =  [
           'id' => $this->id,
           'title' => $this->title,
           'updated_by' => $this->updated_by,
           'created_by' => $this->created_by,
           'created_at' => $this->created_at,
           'updated_at' => $this->updated_at,
           'dynamic_fields' => $this->dynamic_fileds
        ];

        if($otherInfo){

            foreach ($this->components as $component){
                $wizardArray['components'][] = $component->toArray();
            }

            foreach ($this->rules as $rule){
                $wizardArray['rules'][] = $rule->toArray();
            }


        }

        return $wizardArray;
    }

    /**
     * @param mixed $updated_by
     * @return Wizard
     */
    public function setUpdatedBy($updated_by)
    {
        $this->updated_by = $updated_by;
        return $this;
    }

    /**
     * @param mixed $created_by
     * @return Wizard
     */
    public function setCreatedBy($created_by)
    {
        $this->created_by = $created_by;
        return $this;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
        return $this;
    }

    /**
     * @param mixed $updated_at
     * @return Wizard
     */
    public function setUpdatedAt($updated_at)
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    public function setDynamicFileds(?array $dynamic_fileds): Wizard
    {
        $this->dynamic_fileds = $dynamic_fileds;
        return $this;
    }

    public function getDynamicFileds(): ?array
    {
        return $this->dynamic_fileds;
    }

    public function setRegex($regex): Wizard
    {
        $this->regex = $regex;
        return $this;
    }

    public function getRegex()
    {
        return $this->regex;
    }
}