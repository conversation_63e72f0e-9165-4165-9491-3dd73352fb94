<?php

namespace DexmaCommons\Wizards\Classes\Components;

class WizardComponent
{
    protected $id;
    protected $type;
    protected $label;
    protected $property;
    protected $enable;
    protected $required;
    protected $editable;
    protected $defaultValue;
    protected $values;
    protected $regex;

    public function __construct($data)
    {
        foreach ($data as $key => $value) {
            if (property_exists(self::class, $key)) {
                $this->{$key} = $value;
            }
        }
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }

    /**
     * @param mixed $values
     * @return WizardComponent
     */
    public function setValues($values)
    {
        $this->values = $values;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getValues()
    {
        return $this->values;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $label
     * @return WizardComponent
     */
    public function setLabel($label)
    {
        $this->label = $label;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getLabel()
    {
        return $this->label;
    }

    public function unsetCallable() {}

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @return mixed
     */
    public function getEnable()
    {
        return $this->enable;
    }

    /**
     * @return mixed
     */
    public function getRequired()
    {
        return $this->required;
    }

    /**
     * @return mixed
     */
    public function getEditable()
    {
        return $this->editable;
    }

    /**
     * @return mixed
     */
    public function getProperty()
    {
        return $this->property;
    }

    /**
     * @param mixed $property
     * @return WizardComponent
     */
    public function setProperty($property)
    {
        $this->property = $property;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getDefaultValue()
    {
        return $this->defaultValue;
    }

    public function setRegex($regex) {
        $this->regex = $regex;
        return $this;
    }
    

    public function getRegex()
    {
        return $this->regex;
    }

    public function getRegexGroups()
    {
        if (!$this->regex) {
            return [];
        }
    
        preg_match_all("/\(\?P<(\w+)>/", $this->regex, $groupMatches);
        $groupNames = $groupMatches[1] ?? [];
    
        $separators = ['/', '-'];
        $matches = [];
        foreach ($separators as $sep) {
            if (strpos($this->regex, $sep) !== false) {
                $matches[] = $sep;
            }
        }
    
        $result = [];
        $separatorIndex = 0;
        foreach ($groupNames as $group) {
            $separator = $matches[$separatorIndex] ?? null;
            $result[$group] = $separator;
            $separatorIndex++;
        }
    
        return $result;
    }
    
}
