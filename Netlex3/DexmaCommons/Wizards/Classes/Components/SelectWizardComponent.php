<?php

namespace Dexma<PERSON>om<PERSON>\Wizards\Classes\Components;

class Select<PERSON><PERSON>rdComponent extends WizardComponent
{

    private $callable;

    public function __construct($data)
    {
        parent::__construct($data);
    }

    /**
     * @return mixed
     */
    public function getCallable()
    {
        return $this->callable;
    }

    /**
     * @param mixed $callable
     * @return SelectWizardComponent
     */
    public function setCallable($callable)
    {
        $this->callable = $callable;
        return $this;
    }

    public function toArray(): array
    {
        return parent::toArray();
    }


    /**
     * @return void
     */
    public function unsetCallable()
    {
        unset($this->callable);
    }
}