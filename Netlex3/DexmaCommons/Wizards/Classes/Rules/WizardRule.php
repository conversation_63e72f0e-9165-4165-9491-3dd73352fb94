<?php

namespace DexmaCom<PERSON>\Wizards\Classes\Rules;

class WizardRule
{

    private $type;

    private $rule;
    private $relation;

    private $ruleCallable;
    private $relationCallable;

    public function __construct($data)
    {
        foreach ($data as $key => $value){
            $this->{$key} = $value;
        }
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @return void
     */
    public function unsetCallable()
    {
        unset($this->relationCallable);
        unset($this->ruleCallable);
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }
}