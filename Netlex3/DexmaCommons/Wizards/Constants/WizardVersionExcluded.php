<?php

namespace <PERSON><PERSON><PERSON>om<PERSON>\Wizards\Constants;

use Util\_Constants\VersionConstants;

class WizardVersionExcluded
{
    public const EXCLUDE_COMPONENTS = [
        VersionConstants::STANDARD => [14],
        VersionConstants::PLUS => [14],
        VersionConstants::PDA => [14],
        VersionConstants::PDA_BASIC => [14],
        VersionConstants::PDA_FREE => [14],
        VersionConstants::PDA_ULOF => [14],
        VersionConstants::EASYNOTA => [14],
        VersionConstants::GOLD => [14],
        VersionConstants::CLOUD => [14],
        VersionConstants::CLOUD_PDA => [14],
        VersionConstants::CLOUD_PDA_PROMO => [14],
        VersionConstants::CLOUD_PROMO => [14],
        VersionConstants::PDUA => [14],
        VersionConstants::BASIC => [14],
        VersionConstants::CASS => [14],
        VersionConstants::FE => [14],
        VersionConstants::STUDIO_LEGAL => [14],
        VersionConstants::NETLEX_PDL => [14],
        VersionConstants::ST_LEGAL_STD => [14],
        VersionConstants::ST_LEGAL_ADV => [14],
        VersionConstants::AEC => [14],
        VersionConstants::CLM => [14]
    ];
}