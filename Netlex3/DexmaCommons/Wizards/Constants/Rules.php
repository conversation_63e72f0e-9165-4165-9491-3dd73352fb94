<?php

namespace DexmaCommons\Wizards\Constants;

class Rules
{
    const CONFIG = [
        'users' => [
            'callable' => [
                'relation' => [
                    'class' => \Netlex3\Software\patterns\Domains\UserRelation\Services\UserRelationService::class,
                    'function' => 'getAll'
                ],
                'role' => [
                    'class' => null,
                    'function' => null
                ]
            ]
        ],
        'subjects' => [
            'callable' => [
                'relation' => [
                    'class' => \Netlex3\Software\patterns\Domains\SubjectRelation\Services\SubjectRelationService::class,
                    'function' => 'getAll'
                ],
                'role' => [
                    'class' => \Netlex3\Software\patterns\Domains\SubjectRole\Services\SubjectRoleService::class,
                    'function' => 'getAll'
                ]
            ]
        ]
    ];
}