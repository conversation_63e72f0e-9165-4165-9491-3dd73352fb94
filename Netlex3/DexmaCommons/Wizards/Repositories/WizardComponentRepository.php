<?php

namespace <PERSON><PERSON><PERSON>om<PERSON>\Wizards\Repositories;


use DexmaCom<PERSON>\Wizards\Classes\Builder\WizardComponentBuilder;
use DexmaCom<PERSON>\Wizards\Classes\Components\WizardComponent;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;


class WizardComponentRepository
{
    const TABLE = 'wizard_component';

    /**
     * @var InfrastructureInterface
     */
    private $infrastucture;

    /**
     * @var \DexmaZend_Db_Adapter_Mysql|null
     */
    private $dbShared;

    public function __construct(InfrastructureInterface $infrastucture)
    {
        $this->infrastucture = $infrastucture;
        $this->dbShared = $this->infrastucture
            ->getSharedBucket();
    }


    public function delete($id){
        $this->dbShared
            ->delete(self::TABLE, ['id = ?' => $id]);
    }

    /**
     * @param $id
     * @return WizardComponent
     */
    public function getComponentById($id)
    {

        $stmt = $this->dbShared->select()
            ->from(self::TABLE)
            ->where('id = ?', $id);

        $data = $this->dbShared->fetchRow($stmt);

        $component = (new WizardComponentBuilder($data))->build();

        if (method_exists($component, 'setCallable')) {
            $component->setCallable($data['callable']) ;
        }

        return $component;
    }

    /**
     * @param $type
     * @return array
     */
    public function getAllWizardComponent(): array
    {
        $components = [];
        $stmt = $this->dbShared->select()
            ->from(self::TABLE);

        $rows = $this->dbShared->fetchAll($stmt);

        foreach($rows as $row){
            $component = (new WizardComponentBuilder($row))
                ->build();

            if(isset($row['callable'])){
                $component->setCallable($row['callable']);
            }

            $components[] = $component;
        }

        return $components ?: [];
    }

}