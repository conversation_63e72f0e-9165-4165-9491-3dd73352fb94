<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Wizards\Repositories;

use <PERSON>ma<PERSON>om<PERSON>\Wizards\Classes\Builder\WizardBuilder;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;

class WizardRepository
{
    const TABLE = 'wizard';

    /**
     * @var InfrastructureInterface
     */
    private $infrastucture;

    /**
     * @var \DexmaZend_Db_Adapter_Mysql|null
     */
    private $db;

    public function __construct(InfrastructureInterface $infrastucture)
    {
        $this->infrastucture = $infrastucture;
        $this->db = $this->infrastucture
            ->getCustomerBucket();
    }


    public function delete($id)
    {
        $this->db
            ->delete(self::TABLE, ['id = ?' => $id]);
    }


    public function findById($id)
    {
        $stmt = $this->db->select()
            ->from(self::TABLE)
            ->where('id = ?', $id);

        $data = $this->db->fetchRow($stmt);

        return (new WizardBuilder($data))->build();
    }

    public function findAll()
    {
        $wizards = [];

        $stmt = $this->db->select()
            ->from(self::TABLE);

        $rows = $this->db->fetchAll($stmt);

        foreach ($rows as $row){
            $wizards[] = (new WizardBuilder($row))->build(false);
        }

        return $wizards;
    }


    public function create($userId, $data)
    {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = null;
        $data['created_by'] = $userId;
        $data['updated_by'] = null;


        $this->db
            ->insert(self::TABLE, $data);
    }

    public function update($userId, $id, array $data){

        $data['updated_at'] = date('Y-m-d H:i:s');
        $data['updated_by'] = $userId;

        $this->db
            ->update(self::TABLE, $data, ['id = ?' => $id]);
    }

    public function findAllPaginateOrdered($page, $limit,$sortColumn = 'id', $sortOrder = 'ASC')
    {
        $wizards = [];

        $stmt = $this->db->select()
            ->from(self::TABLE)
            ->limitPage($page, $limit)
            ->order(sprintf("%s %s",$sortColumn,$sortOrder));

        $rows = $this->db->fetchAll($stmt);

        foreach ($rows as $row){
            $wizards[] = (new WizardBuilder($row))->build(false);
        }

        return $wizards;
    }

    public function count()
    {
        $stmt = $this->db->select()
            ->from(self::TABLE, ['count' => 'COUNT(id)']);

        return $this->db->fetchOne($stmt);
    }

}