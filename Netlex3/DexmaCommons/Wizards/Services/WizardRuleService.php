<?php

namespace DexmaCom<PERSON>\Wizards\Services;

use DexmaCommons\Wizards\Constants\Rules;
use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;

class WizardRuleService
{
    /**
     * @var WizardRuleService
     */
    private static $instance;

    /**
     * @var InfrastructureInterface
     */
    private $infrastructure;

    private function __construct(InfrastructureInterface $infrastructure)
    {
        $this->infrastructure = $infrastructure;
    }

    public static function getInstance(ZendInfrastructure $infrastructure): WizardRuleService
    {
        if (self::$instance === null) {
            self::$instance = new self($infrastructure);
        }

        return self::$instance;
    }

    public function getRules(){

        $rules = [];

        foreach (Rules::CONFIG as $key => $ruleConfig){
            foreach ($ruleConfig['callable'] as $callableKey => $rule){
                if(isset($rule['class'])) {
                    $rules[$key][$callableKey] = $this->transformItem((object)$rule);
                }
            }
        }

        return $rules;
    }

    private function transformItem(\stdClass $rule) : \stdClass {
        $serviceClass = $rule->class;
        $instanceService = forward_static_call([$serviceClass, 'getInstance'], $this->infrastructure);
        $rule->values = call_user_func([$instanceService, $rule->function]);

        foreach($rule->values as &$item){
            if(!empty($item['nome'])){
                $item['nome'] = _($item['nome']);
            }elseif (!empty($item['name'])){
                $item['name'] = _($item['name']);
            }
        }

        unset($rule->class);
        unset($rule->function);

        return $rule;
    }
}