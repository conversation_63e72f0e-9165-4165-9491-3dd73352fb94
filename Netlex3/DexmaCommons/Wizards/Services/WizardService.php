<?php

namespace <PERSON>ma<PERSON>om<PERSON>\Wizards\Services;



use DexmaCommons\Wizards\Constants\Rules;
use DexmaCommons\Wizards\Repositories\WizardRepository;
use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;

class WizardService
{
    /**
     * @var WizardService
     */
    private static $instance;

    /**
     * @var WizardRepository
     */
    private $repository;
    /**
     * @var InfrastructureInterface
     */
    private $infrastructure;

    private function __construct(InfrastructureInterface $infrastructure)
    {
        $this->repository = new WizardRepository($infrastructure);
        $this->infrastructure = $infrastructure;
    }

    public static function getInstance(ZendInfrastructure $infrastructure): WizardService
    {
        if (self::$instance === null) {
            self::$instance = new self($infrastructure);
        }

        return self::$instance;
    }

    public function create($userId, $params){

        $this->repository->create($userId, $params);
    }

    public function findById($id)
    {
        try {
            $wizardComponentService = WizardComponentService::getInstance($this->infrastructure);
            $wizardRuleService =WizardRuleService::getInstance($this->infrastructure);

            $wizard = $this->repository->findById($id);

            foreach ($wizard->getComponents() as &$component) {
                $wizardComponent = $wizardComponentService->getWizardComponentById($component->getId());
            
                $component->setValues($wizardComponent->getValues())
                    ->setLabel($wizardComponent->getLabel())
                    ->setRegex($wizardComponent->getRegex())
                    ->unsetCallable();
            }
            

            $typeOfRules = $wizardRuleService->getRules();
            foreach ($wizard->getRules() as &$rule){
                $wizardRule = $typeOfRules[$rule->getType()];

//                $rule->setRelationValues($wizardRule['relation']->values ?? null)
//                    ->setRulesValues($wizardRule['rule']->values ?? null)
                    $rule->unsetCallable();
            }

            return $wizard;

        } catch (\Exception $e) {
            return [];
        }
    }

    public function findAll()
    {
        try {
            return $this->repository->findAll();
        } catch (\Exception $e) {
            return [];
        }
    }

    public function findAllPaginateOrdered($page, $limit, $sortColumn, $sortOrder)
    {
        try {
            return $this->repository->findAllPaginateOrdered($page, $limit, $sortColumn, $sortOrder);
        } catch (\Exception $e) {
            return [];
        }
    }

    public function count()
    {
        try {
            return $this->repository->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    public function delete($id)
    {
        try {
            $this->repository->delete($id);
        } catch (\Exception $e) {
            return false;
        }
    }

    public function update($userId, $id, array $params)
    {
        try {
            $this->repository->update($userId, $id, $params);
        } catch (\Exception $e) {
            return false;
        }
    }

}