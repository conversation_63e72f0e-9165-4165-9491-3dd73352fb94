<?php

namespace <PERSON>ma<PERSON>om<PERSON>\Wizards\Services;


use DexmaCommons\Wizards\Classes\Components\SelectWizardComponent;
use DexmaCom<PERSON>\Wizards\Classes\Components\WizardComponent;
use DexmaCommons\Wizards\Constants\Components;
use <PERSON>maCom<PERSON>\Wizards\Constants\WizardVersionExcluded;
use <PERSON>ma<PERSON>om<PERSON>\Wizards\Repositories\WizardComponentRepository;
use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;
use Netlex3\Software\patterns\Interfaces\SingletoneInterface;

class WizardComponentService
{

    /**
     * @var WizardComponentService
     */
    private static $instance;
    /**
     * @var InfrastructureInterface
     */
    private $infrastructure;

    private function __construct(InfrastructureInterface $infrastructure)
    {
        $this->repository = new WizardComponentRepository($infrastructure);
        $this->infrastructure = $infrastructure;
    }

    public static function getInstance(ZendInfrastructure $infrastructure): WizardComponentService
    {
        if (self::$instance === null) {
            self::$instance = new self($infrastructure);
        }

        return self::$instance;
    }

    /**
     * @param $id
     */
    public function getWizardComponentById($id)
    {
        try {
            $component =  $this->repository->getComponentById($id);

            if(method_exists($component, 'getCallable')){
                $component->setValues($this->trasformItem($component));
                $component->unsetCallable();
            }

            return $component;

        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * @param $type
     * @return array
     */
    public function getAllWizardComponent(int $version): array
    {
        try {
            $components =  $this->repository->getAllWizardComponent();

            /** @var  $component WizardComponent */
            foreach ($components as $key => $component){

                if(
                    isset(WizardVersionExcluded::EXCLUDE_COMPONENTS[$version]) &&
                    in_array((int)$component->getId(), WizardVersionExcluded::EXCLUDE_COMPONENTS[$version])
                ){
                    unset($components[$key]);
                    continue;
                }

                /** @var  $component SelectWizardComponent */
                if(method_exists($component, 'getCallable') ){
                    $component->setValues($this->trasformItem($component));
                    $component->unsetCallable();
                }
            }

            return $components;

        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * @param $component
     * @return mixed
     */
    private function trasformItem($component): ?array
    {
        $callable = json_decode($component->getCallable(), true);
        $serviceClass = $callable['class'];
        $instanceService = forward_static_call([$serviceClass, 'getInstance'], $this->infrastructure);
        $param_1 = $callable['arguments']['param_1'] ?? false;
        $list = call_user_func([$instanceService, $callable['function']], $param_1);
        
        foreach($list as &$item){
            if(!empty($item['nome'])){
                $item['nome'] = _($item['nome']);
            }elseif (!empty($item['name'])){
                $item['name'] = _($item['name']);
            }
        }
        return $list;
    }

}