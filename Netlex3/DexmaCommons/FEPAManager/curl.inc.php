<?php

function NEWSESS()
{
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_TIMEOUT, 20);

	return $ch;
}

function GET($ch, $url, $headers=false)
{
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_POST, 0);
	curl_setopt($ch,CURLOPT_FOLLOWLOCATION,true);
	if($headers)
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	$x = curl_exec($ch);
	return $x;
}

function POST($ch, $url, $body, $headers=false)
{
	if($url)
		curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch,CURLOPT_FOLLOWLOCATION,true);
	if($headers)
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
 	curl_setopt($ch, CURLOPT_POSTFIELDS    ,$body);
 	
	$x = curl_exec($ch);
	return $x;
}

?>