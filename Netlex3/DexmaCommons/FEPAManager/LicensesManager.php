<?php

$logname = 'sendLicensesToBBS';

set_include_path(get_include_path() . PATH_SEPARATOR . '/home/<USER>/DexmaCommons/FEPAManager');
require_once('Phpseclib/Crypt/Hash.php');
require_once('Phpseclib/Net/SSH2.php');
require_once('Phpseclib/Crypt/RSA.php');
require_once('Phpseclib/Math/BigInteger.php');

require_once('curl.inc.php');
require_once('xml.php');

class LicensesManager
{

	private $applogger = null;

	//const KFEPA_HOST = 'http://licenzetest.teamsystem.com/ws_licenze/ws_coupon.asmx';
	const KFEPA_HOST = 'https://lycenze.teamsystem.com/WS_Licenze/WS_Coupon.asmx';
	const KUTENTE = 'web_netlex';
	//const KPASSWORD = '12345678';
	const KPASSWORD = '7rX&LJsSf;wv';
	const NETLEX1GB = "WCNLX1GB001";
	const NETLEX30GB = "WCNLX30GB001";
	const NETLEXSTD_ATT = "WANLXSTD001";
	const NETLEXPLU_ATT = "WANLXPLU001";
	const NETLEXPDA_ATT = "NETLEX-PDA";
	const NETLEXPDAB_ATT = "NETLEX-PDA_BASIC";
	const LEGAL_ST_ATT = "LEGAL_ST_ATT";
	const LEGAL_ST_AVV_AGG = "LEGAL_ST_AVV_AGG";
	const LEGAL_ST_SK = "LEGAL_ST_SK";
	const LEGAL_ST_SK3 = "LEGAL_ST_SK3";
	const NETLEXPLUS_ACC_AVV = "WCNLXACAPLU001";
	const NETLEXBASE_ACC_AVV = "WCNLXACASTD001";
	const NETLEXPLUS_ACC_USER = "WCNLXACUPLU001";
	const NETLEXBASE_ACC_USER = "WCNLXACUSTD001";
	const MULTI_INTESTARIO = "WCNLXAMI001";
	const ANTIRICICLAGGIO = "WCNLXANT001";
	const FATT_ELETTRONICA = "WCNLXFEA001";
	const NIR = "WCNLXNIR001";
	const NETLEXPDA_ABB = "NETLEX-PDA-ABB";
	const NETLEXPDAB_ABB = "NETLEX-PDA_BASIC-ABB";
	const STUDI_SETTORE = "NETLEX-STUDISETTORE";
	const REDATTORE = "NETLEX-REDATTORE";
	const TRAINING = "NETLEX-TRAINING";
	const RICARICA = "WCNLXRICARICA";
	const FATT_250 = "LEG-NL-B2B250";
	const FATT_500 = "LEG-NL-B2B500";
	const FATT_UP_200 = "LEG-NL-B2BUPG200";
	const RECUPERO_CREDITI = "NETLEX-14036";
	const RECUPERO_CREDITI_NEW = "LEGAL_ST_REC";
	const SMART_MAILER = "NETLEX-14035";
	const EDITOR_TESTO = "NETLEX-14037";
	const ENTERPRISE_5 = "LEGAL_ENT_5";
	const ENTERPRISE_10 = "LEGAL_ENT_10";
	const ENTERPRISE_20 = "LEGAL_ENT_20";
	const ENTERPRISE_50 = "LEGAL_ENT_50";
	const ENTERPRISE_80 = "LEGAL_ENT_80";
	const ENTERPRISE_100 = "LEGAL_ENT_100";
	const ENTERPRISE_UP_100 = "LEGAL_ENT_PGT";
	const ENTERPRISE_UP_AGG = "LEGAL_ENT_UT_AGG";
	const ONEDRIVE = "NETLEX-ONEDRIVE";
	const EASYNOTA_CAN = "NETLEX-NIR-CAN";
	const ULOF_CAN = "NETLEX-ULOF-CAN";
	const GOLD_ATT = "NETLEX-GOLD-ATT";
	const GOLD_CAN = "NETLEX-GOLD-CAN";
	const CCT = "NETLEX-CCT";
	const NETLEX_PDA = "NETLEX-PDA";
	const NETLEX_INCLOUD = "NETLEX-INCLOUD";
	const NETLEX_INCLOUD_AVV = "NETLEX-INCLOUD-AVV";
	const NETLEX_INCLOUD_SGR = "NETLEX-INCLOUD-SGR";
	const NETLEX_PDA_SGR = "NETLEX-PDA-SGR";
	const NETLEX_INCLOUD_50 = "NETLEX-INCLOUD-50";
	const CLASSIFICATORE = "NETLEX_CAM_DIN";
	const LEGAL_ST_LOG = "LEGAL_ST_LOG";
	const LEGAL_ST_WORKF = "LEGAL_ST_WORKF";
	const LEGAL_ENT_WORKF = "LEGAL_ENT_WORKF";
	const NETLEX_CASSAZIONE = "NETLEX-CASSAZIONE";
	const LS_SL_SK3 = "LEGAL_ST_14079";
	const LS_SL_SK = "LEGAL_ST_14080";
	const LS_SL_AVV_AGG = "LEGAL_ST_14081";
	const EASY_CLOUD_AVV = "LEGAL_ST_14082";
	const EASY_CLOUD_AVV_2 = "LEGAL_ST_14083";
	const EASY_CLOUD_AVV_AGG = "LEGAL_ST_14084";
	const NETLEX_PDUA = "NETLEX-PDUA";
	const NETLEX_PDUA_SGR = "NETLEX-PDUA-SGR";
	const NETLEX_UPGRADE_DEPOSITI = "NETLEX-UPGD-DEP";
	const LEGAL_ST_AVV_BASIC = "LEGAL_ST_AVV_BASIC";
	const LEGAL_ENT_AVV_BASIC = "LEGAL_ENT_AVV_BASIC";
	const NETLEX_BASIC = "NETLEX-PDA_FREE";

	const KRSA_KEY = '<RSAKeyValue><Modulus>k7D5CCd3OA5BycvMpKRBFpC3j+tokY0ly9AhOeXp4qNQuk9g2JjbvFqpGET/JqVkXULX4ivnlvDV0a3OQnMBwiibd83lOajvY+tN6M72thAhr/FI31wPQ1SVPfl6Q9C3KMds4WasDKYnbOGOUR0FFaQ6bHCLKfgzzYObieJApA0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>';


	public function __construct($applogger = null)
	{
		$this->applogger = $applogger;
	}

	private function log($msg)
	{

		if ( $this->applogger ) {
			$this->applogger->info($msg);
		}

	}

	public function assignCodes($order, $log=NULL, $contract = NULL){

		$order_json = json_decode($order['order_json']);
		$previous_state = json_decode($order['previous_state']);

		$codes = [];

		if(isset($order_json->activationPrice) && $order_json->activationPrice > 0){
			//PDA
			if($order_json->subscriptionType == 33){
				$codes[self::NETLEXPDA_ATT] = array('price' => $order_json->activationPrice);
			//STANDARD
			} else if($order_json->subscriptionType == 1){
				$codes[self::NETLEXSTD_ATT] = array('price' => $order_json->activationPrice);
			//GOLD
			} else if($order_json->subscriptionType == 36){
				$codes[self::GOLD_ATT] = array('price' => $order_json->activationPrice);
			//PLUS
			} else if($order_json->subscriptionType == 2){
				$codes[self::NETLEXPLU_ATT] = array('price' => $order_json->activationPrice);
			//PDA BASIC
			} else if($order_json->subscriptionType == 55){
				$codes[self::NETLEXPDAB_ATT] = array('price' => $order_json->activationPrice);
			//STUDIO LEGAL
			} else if($order_json->subscriptionType == 66){
				$codes[self::LEGAL_ST_ATT] = array('price' => $order_json->activationPrice);
			}
		}
		
		// --- Canone PDA ---
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->isLicenseExtension && $order_json->subscriptionType == 33)
			$codes[self::NETLEXPDA_ABB] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice);
		// --- Canone PDA BASIC ---
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->isLicenseExtension && $order_json->subscriptionType == 55)
			$codes[self::NETLEXPDAB_ABB] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice);
		// --- Canone PDA Ulof ---
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->isLicenseExtension && $order_json->subscriptionType == 34)
			$codes[self::ULOF_CAN] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice);
		// --- Canone Easynota
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->isLicenseExtension && $order_json->subscriptionType == 35)
			$codes[self::EASYNOTA_CAN] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice);
		// --- Canone Netlex in cloud PDA
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && isset($order_json->remainingDaysLawyersDeficit) && ($order_json->isLicenseExtension || $previous_state->subscription_type != 38) && $order_json->subscriptionType == 38)
			$codes[self::NETLEX_PDA] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice + $order_json->remainingDaysLawyersDeficit);
		// --- Canone Netlex in cloud PDA (Promo Black Week)
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->isLicenseExtension && $order_json->subscriptionType == 39)
			$codes[self::NETLEXPDAB_ATT] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice);
		// --- Canone Netlex in cloud (Promo Black Week)
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->isLicenseExtension && $order_json->subscriptionType == 40)
			$codes[self::GOLD_ATT] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice);
		// --- Canone Netlex in cloud BASIC
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->isLicenseExtension && $order_json->subscriptionType == 42)
			$codes[self::NETLEX_BASIC] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice);
		
		// ---Netlex spazio 1 GB aggiuntivo---
		if(isset($order_json->diskSpaceToAdd) && isset($order_json->remainingDaysDiskSpacePrice) && isset($order_json->newYearsDiskSpacePrice) && $order_json->diskSpaceToAdd > 0){
			if($order_json->subscriptionType != 37 && $order_json->subscriptionType != 38 && $order_json->subscriptionType != 41){
				$codes[self::NETLEX1GB] = array(
					'price' => $order_json->remainingDaysDiskSpacePrice + $order_json->newYearsDiskSpacePrice,
					'qty' => $order_json->diskSpaceToAdd
				);
			} else {
				$codes[self::NETLEX_INCLOUD_50] = array(
					'price' => $order_json->remainingDaysDiskSpacePrice + $order_json->newYearsDiskSpacePrice,
					'qty' => $order_json->diskSpaceToAdd / 50
				);
			}
		}
		
		//////////////////////////// AGGIUNTE CODICI BBS CONTRATTO

		// ---- Firma Remota (aggiunta)----
		if(!empty($order_json->certificatesToAdd) && !empty($order_json->remainingDaysCertificatesPrice) && empty($order_json->newYearsCertificatesPrice)){
			$price_certificates = $order_json->remainingDaysCertificatesPrice;
			
			$codes['WCNLXFRAGG'] = array('price' => $price_certificates, 'qty' => $order_json->certificatesToAdd);		
		}
			
			
		// ---- Firma Remota (rinnovo)----
		if(!empty($order_json->newCertificates) && !empty($order_json->newYearsCertificatesPrice)){
			$price_certificates = $order_json->newYearsCertificatesPrice;
			
			$codes['WCNLXFRAGG'] = array('price' => $price_certificates, 'qty' => ($order_json->newCertificates));		
		}
			
			
		// ---- CCT ----
		if(!empty($order_json->cctPackets)){
			$totalSpace = 0;
			$totalPrice = 0;

			foreach ($order_json->cctPackets as $pacchetto){					
				// --- Solo rinnovo ---
				if ( !empty($pacchetto->space)){
					$totalSpace += $pacchetto->space / 5;
					
					if (!empty($pacchetto->price)){
						$totalPrice += $pacchetto->price;
					}
				}
			} 
			if(!empty($totalPrice)){
				$codes[self::CCT] = array('price' => $totalPrice, 'qty' => $totalSpace );
			}
		}



		////////////////////////////




		// ---Netlex spazio 30 GB aggiuntivo---

		// if(isset($order_json->diskSpaceToAdd) && isset($order_json->remainingDaysDiskSpacePrice) && isset($order_json->newYearsDiskSpacePrice) && $order_json->diskSpaceToAdd == 30){
		// 	$codes[self::NETLEX30GB] = array('price' => $order_json->remainingDaysDiskSpacePrice + $order_json->newYearsDiskSpacePrice);
		// }

		// ---Netlex plus accesso avvocato---
		if(isset($order_json->newLawyersToAdd) && isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->newLawyersToAdd > 0 && $order_json->subscriptionType == 2 && (!isset($order_json->activationPrice) || $order_json->activationPrice == 0)){
			$codes[self::NETLEXPLUS_ACC_AVV] = array(
				'price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice,
				'qty' => $order_json->newLawyersToAdd
			);
		}

		// ---Netlex standard accesso avvocato---
		elseif(isset($order_json->newLawyersToAdd) && isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->newLawyersToAdd > 0 && $order_json->subscriptionType == 1 && (!isset($order_json->activationPrice) || $order_json->activationPrice == 0)){
			$codes[self::NETLEXBASE_ACC_AVV] = array(
				'price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice,
				'qty' => $order_json->newLawyersToAdd
			);
		}

		// ---Netlex gold accesso avvocato---
		elseif(isset($order_json->newLawyersToAdd) && isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->newLawyersToAdd > 0 && $order_json->subscriptionType == 36 && (!isset($order_json->activationPrice) || $order_json->activationPrice == 0)){
			$codes[self::GOLD_CAN] = array(
				'price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice,
				'qty' => $order_json->newLawyersToAdd
			);
		}

		// ---Netlex in cloud accesso avvocato---
		elseif(isset($order_json->newLawyersToAdd) && isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->newLawyersToAdd > 0 && $order_json->subscriptionType == 37 && (!isset($order_json->activationPrice) || $order_json->activationPrice == 0) &&
			(!isset($order_json->cloudLawyers) || empty($order_json->cloudLawyers))){
			$codes[self::NETLEX_INCLOUD_AVV] = array(
				'price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice,
				'qty' => $order_json->newLawyersToAdd
			);
		}

		// ---Netlex in cloud PDUA accesso avvocato---
		elseif(isset($order_json->newLawyersToAdd) && isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->newLawyersToAdd > 0 && $order_json->subscriptionType == 41 && (!isset($order_json->activationPrice) || $order_json->activationPrice == 0) &&
			(!isset($order_json->pduaLawyers) || empty($order_json->pduaLawyers))){
			$codes[self::NETLEX_INCLOUD_AVV] = array(
				'price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice,
				'qty' => $order_json->newLawyersToAdd
			);
		}

		// ---Netlex in cloud PDA accesso avvocato---
		elseif(isset($order_json->newLawyersToAdd) && isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->newLawyersToAdd > 0 && $order_json->subscriptionType == 38 && (!isset($order_json->activationPrice) || $order_json->activationPrice == 0)){
			$codes[self::NETLEX_PDA] = array(
				'price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice,
				'qty' => $order_json->newLawyersToAdd
			);
		}
		
		// ---Netlex plus accesso utente---
		if(isset($order_json->newUsersToAdd) && isset($order_json->remainingDaysUsersPrice) && isset($order_json->newYearsUsersPrice) && 
			$order_json->newUsersToAdd > 0 && $order_json->subscriptionType == 2 && 
			(!isset($order_json->activationPrice) || ($order_json->activationPrice == 0 ))){
			$codes[self::NETLEXPLUS_ACC_USER] = array(
				'price' => $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice,
				'qty' => $order_json->newUsersToAdd
			);
		}

		// ---Netlex standard accesso utente---
		elseif(isset($order_json->newUsersToAdd) && isset($order_json->remainingDaysUsersPrice) && isset($order_json->newYearsUsersPrice) && 
			$order_json->newUsersToAdd > 0 && $order_json->subscriptionType == 1 && 
			(!isset($order_json->activationPrice) || ($order_json->activationPrice == 0 ))){
			$codes[self::NETLEXBASE_ACC_USER] = array(
				'price' => $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice,
				'qty' => $order_json->newUsersToAdd
			);
		}

		// ---Netlex in cloud accesso utente---
		elseif(isset($order_json->newUsersToAdd) && isset($order_json->remainingDaysUsersPrice) && isset($order_json->newYearsUsersPrice) && 
			($order_json->newUsersToAdd > 0 || ($order_json->newUsersToAdd == 0 && $order_json->cloudKitPrice > 0 && $order_json->newYearsUsersPrice > 0)) && $order_json->subscriptionType == 37 && 
			(!isset($order_json->activationPrice) || ($order_json->activationPrice == 0)  )){
				if ($order_json->newUsersToAdd > 0){
					$codes[self::NETLEX_INCLOUD_SGR] = array(
						'price' => $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice,
						'qty' => $order_json->newUsersToAdd
					);
				}elseif($order_json->newUsersToAdd == 0 && $order_json->cloudKitPrice > 0 && $order_json->newYearsUsersPrice > 0){
					$codes[self::NETLEX_INCLOUD_SGR] = array(
						'price' => $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice,
						'qty' => $order_json->newNumberOfUsers,
						'passaggio' => 1
					);
				}

		}

		// ---Netlex in cloud accesso utente---
		elseif(isset($order_json->newUsersToAdd) && isset($order_json->remainingDaysUsersPrice) && isset($order_json->newYearsUsersPrice) && 
			($order_json->newUsersToAdd > 0 || ($order_json->newUsersToAdd == 0 && $order_json->pduaKitPrice > 0 && $order_json->newYearsUsersPrice > 0)) && $order_json->subscriptionType == 41 && 
			(!isset($order_json->activationPrice) || ($order_json->activationPrice == 0)  )){
				if ($order_json->newUsersToAdd > 0){
					$codes[self::NETLEX_PDUA_SGR] = array(
						'price' => $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice,
						'qty' => $order_json->newUsersToAdd
					);
				}elseif($order_json->newUsersToAdd == 0 && $order_json->pduaKitPrice > 0 && $order_json->newYearsUsersPrice > 0){
					$codes[self::NETLEX_PDUA_SGR] = array(
						'price' => $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice,
						'qty' => $order_json->newNumberOfUsers,
						'passaggio' => 1
					);
				}

		}

		// ---Netlex in cloud pda accesso utente---
		elseif(isset($order_json->newUsersToAdd) && isset($order_json->remainingDaysUsersPrice) && isset($order_json->newYearsUsersPrice) && 
			$order_json->newUsersToAdd > 0 && $order_json->subscriptionType == 38 && 
			(!isset($order_json->activationPrice) || ($order_json->activationPrice == 0 ))){
			$codes[self::NETLEX_PDA_SGR] = array(
				'price' => $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice,
				'qty' => $order_json->newUsersToAdd
			);
		}

		// ---Studio Legal utente aggiuntivo---
		if(isset($order_json->newUsersToAdd) && isset($order_json->newYearsUsersPrice) && $order_json->newYearsUsersPrice > 0 && $order_json->newUsersToAdd > 0 && $order_json->subscriptionType == 66){
			$userCode = self::LEGAL_ST_AVV_AGG;
			if(isset($order_json->subType) && $order_json->subType == 1){
				$userCode = self::LS_SL_AVV_AGG;
			}
			$codes[$userCode] = array(
				'price' => $order_json->newYearsUsersPrice,
				'qty' => $order_json->newUsersToAdd
			);
		}

		// ---Utente basic aggiuntivo---
		if(isset($order_json->newBasicUsersToAdd) && isset($order_json->remainingDaysBasicUsersPrice) && $order_json->newBasicUsersToAdd > 0){
			$userCode = self::LEGAL_ST_AVV_BASIC;
			if($order_json->subscriptionType == 77){
				$userCode = self::LEGAL_ENT_AVV_BASIC;
			}
			$codes[$userCode] = array(
				'price' => $order_json->remainingDaysBasicUsersPrice,
				'qty' => $order_json->newBasicUsersToAdd
			);
		}

		// ---Studio Legal Starter Kit (OLD)---
		if(isset($order_json->starterKitPrice) && $order_json->starterKitPrice > 0 && $order_json->subscriptionType == 66){
			$codes[self::LEGAL_ST_SK] = array('price' => $order_json->starterKitPrice);
		}
		
		// ---Studio Legal Starter Kit 3 e 5 (NEW)---
		if(isset($order_json->newYearsUserPacketPrice) && $order_json->newYearsUserPacketPrice > 0 && !empty($order_json->userPacket) && $order_json->subscriptionType == 66){
			$codes[$order_json->userPacket->code] = array('price' => $order_json->newYearsUserPacketPrice);
		}

		// ---Enterprise pacchetto utenti---
		if(isset($order_json->enterpriseUsers) && isset($order_json->newYearsUserPacketPrice) && $order_json->newYearsUserPacketPrice > 0){
			$codes[$order_json->enterpriseUsers] = array('price' => $order_json->newYearsUserPacketPrice);
		}

		// ---Enterprise utente aggiuntivo---
		if(isset($order_json->numberOfEnterpriseUpgUsers) && isset($order_json->newYearsUsersPrice) && $order_json->numberOfEnterpriseUpgUsers > 0 && $order_json->newYearsUsersPrice > 0){
			$codes[self::ENTERPRISE_UP_AGG] = array(
				'price' => $order_json->newYearsUsersPrice / $order_json->numberOfEnterpriseUpgUsers,
				'qty' => $order_json->numberOfEnterpriseUpgUsers
			);
		}

		// ---Netlex in Cloud canone---
		if(isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && isset($order_json->remainingDaysLawyersDeficit) && ($order_json->isLicenseExtension || $previous_state->subscription_type != 37) && $order_json->subscriptionType == 37 &&
			(!isset($order_json->cloudLawyers) || empty($order_json->cloudLawyers))){
			$codes[self::NETLEX_INCLOUD] = array('price' => $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice + $order_json->remainingDaysLawyersDeficit);
		}

		// ---Netlex in Cloud PDUA canone---
		if(isset($order_json->pduaKitPrice) && $order_json->pduaKitPrice > 0 && $order_json->subscriptionType == 41){
			$codes[self::NETLEX_PDUA] = array('price' => $order_json->pduaKitPrice);
		}

		// ---Listino Easytelematico - Netlex in Cloud---
		if(isset($order_json->cloudLawyers) && !empty($order_json->cloudLawyers) && $order_json->subscriptionType == 37){
			if(isset($order_json->cloudLawyers->firstLawyer) && !empty($order_json->cloudLawyers->firstLawyer) && 
				isset($order_json->cloudLawyers->firstLawyer->newYear) && isset($order_json->cloudLawyers->firstLawyer->remainingDays) &&
				(!empty($order_json->cloudLawyers->firstLawyer->newYear) || !empty($order_json->cloudLawyers->firstLawyer->remainingDays))){
				$codes[self::EASY_CLOUD_AVV] = array('price' => $order_json->cloudLawyers->firstLawyer->newYear + $order_json->cloudLawyers->firstLawyer->remainingDays);
			}
			if(isset($order_json->cloudLawyers->secondLawyer) && !empty($order_json->cloudLawyers->secondLawyer) && 
				isset($order_json->cloudLawyers->secondLawyer->newYear) && isset($order_json->cloudLawyers->secondLawyer->remainingDays) &&
				(!empty($order_json->cloudLawyers->firstLawyer->newYear) || !empty($order_json->cloudLawyers->firstLawyer->remainingDays))){
				$codes[self::EASY_CLOUD_AVV_2] = array('price' => $order_json->cloudLawyers->secondLawyer->newYear + $order_json->cloudLawyers->secondLawyer->remainingDays);
			}
			if(isset($order_json->cloudLawyers->upgLawyer) && !empty($order_json->cloudLawyers->upgLawyer) && 
				isset($order_json->cloudLawyers->upgLawyer->newYear) && isset($order_json->cloudLawyers->upgLawyer->remainingDays) &&
				(!empty($order_json->cloudLawyers->upgLawyer->newYear) || !empty($order_json->cloudLawyers->upgLawyer->remainingDays))){
				$codes[self::EASY_CLOUD_AVV_AGG] = array(
					'price' => $order_json->cloudLawyers->upgLawyer->newYear + $order_json->cloudLawyers->upgLawyer->remainingDays,
					'qty' => $order_json->newCloudLawyersToAdd);
			}
		}

		//*------INIZIO MODULI-----*

		// ---Netlex impegni multi-intestatario---
		if(isset($order_json->newMultiOwners) && isset($order_json->newYearsMultiOwnersPrice) && isset($order_json->remainingDaysMultiOwnersPrice) && $order_json->newMultiOwners > 0 && !in_array($order_json->subscriptionType, array(2,36,66,77)) && ($order_json->newYearsMultiOwnersPrice + $order_json->remainingDaysMultiOwnersPrice > 0)){
			$codes[self::MULTI_INTESTARIO] = array('price' => $order_json->newYearsMultiOwnersPrice + $order_json->remainingDaysMultiOwnersPrice);
		}

		// ---Netlex - Modulo antiriciclaggio---
		if(isset($order_json->newAntirec) && isset($order_json->newYearsAntirecPrice) && isset($order_json->remainingDaysAntirecPrice) && $order_json->newAntirec > 0 && !in_array($order_json->subscriptionType, array(2,66,77)) && ($order_json->newYearsAntirecPrice + $order_json->remainingDaysAntirecPrice > 0)){
			$codes[self::ANTIRICICLAGGIO] = array('price' => $order_json->newYearsAntirecPrice + $order_json->remainingDaysAntirecPrice);
		}

		// ---Netlex fatturazione elettronica---
		if(isset($order_json->newElectronicBills) && isset($order_json->newYearsElectronicBillsPrice) && isset($order_json->remainingDaysElectronicBillsPrice) && $order_json->newElectronicBills > 0 && !in_array($order_json->subscriptionType, array(2,66,77)) && ($order_json->newYearsElectronicBillsPrice + $order_json->remainingDaysElectronicBillsPrice > 0)){
			$codes[self::FATT_ELETTRONICA] = array('price' => $order_json->newYearsElectronicBillsPrice + $order_json->remainingDaysElectronicBillsPrice);
		}

		// ---Netlex NIR---
		if(isset($order_json->newNote) && isset($order_json->newYearsNotePrice) && isset($order_json->remainingDaysNotePrice) && $order_json->newNote > 0 && !in_array($order_json->subscriptionType, array(2,35,36,37,41,66,77)) && ($order_json->newYearsNotePrice + $order_json->remainingDaysNotePrice > 0)){
			$codes[self::NIR] = array('price' => $order_json->newYearsNotePrice + $order_json->remainingDaysNotePrice);
		}

		// ---Netlex Studi di Settore---
		if(isset($order_json->newStudiSettore) && isset($order_json->newYearsStudiSettorePrice) && isset($order_json->remainingDaysStudiSettorePrice) && $order_json->newStudiSettore > 0 && ($order_json->newYearsStudiSettorePrice + $order_json->remainingDaysStudiSettorePrice > 0) && !in_array($order_json->subscriptionType, array(2,36,37,66,77))){
			$codes[self::STUDI_SETTORE] = array('price' => $order_json->newYearsStudiSettorePrice + $order_json->remainingDaysStudiSettorePrice);
		}

		// ---Netlex Smart Mailer---
		if(isset($order_json->newSmartMailer) && isset($order_json->newYearsSmartMailerPrice) && isset($order_json->remainingDaysSmartMailerPrice) && $order_json->newSmartMailer > 0 && ($order_json->newYearsSmartMailerPrice + $order_json->remainingDaysSmartMailerPrice > 0) && !in_array($order_json->subscriptionType, array(2,36,37,66,77))){
			$codes[self::SMART_MAILER] = array('price' => $order_json->newYearsSmartMailerPrice + $order_json->remainingDaysSmartMailerPrice);
		}

		// ---Netlex Recupero crediti---
		if(isset($order_json->newRecuperoCrediti) && isset($order_json->newYearsRecuperoCreditiPrice) && isset($order_json->remainingDaysRecuperoCreditiPrice) && $order_json->newRecuperoCrediti > 0 && ($order_json->newYearsRecuperoCreditiPrice + $order_json->remainingDaysRecuperoCreditiPrice > 0) && !in_array($order_json->subscriptionType, array(66,77))){
			$codes[self::RECUPERO_CREDITI] = array('price' => $order_json->newYearsRecuperoCreditiPrice + $order_json->remainingDaysRecuperoCreditiPrice);
		}

		// ---Netlex Recupero crediti (Studio Legal)---
		if(isset($order_json->newRecuperoCrediti) && isset($order_json->newYearsRecuperoCreditiPrice) && isset($order_json->remainingDaysRecuperoCreditiPrice) && $order_json->newRecuperoCrediti > 0 && ($order_json->newYearsRecuperoCreditiPrice + $order_json->remainingDaysRecuperoCreditiPrice > 0) && $order_json->subscriptionType == 66){
			$codes[self::RECUPERO_CREDITI_NEW] = array('price' => $order_json->newYearsRecuperoCreditiPrice + $order_json->remainingDaysRecuperoCreditiPrice);
		}

		// ---Netlex CCT---
		if(isset($order_json->newCct) && isset($order_json->newYearsCctPrice) && isset($order_json->remainingDaysCctPrice) && $order_json->newCct > 0 && ($order_json->newYearsCctPrice + $order_json->remainingDaysCctPrice > 0)){
			$codes[self::CCT] = array('price' => $order_json->newYearsCctPrice + $order_json->remainingDaysCctPrice);
		}

		// ---Netlex One Drive---
		if(isset($order_json->newOneDrive) && isset($order_json->newYearsOneDrivePrice) && isset($order_json->remainingDaysOneDrivePrice) && $order_json->newOneDrive > 0 && ($order_json->newYearsOneDrivePrice + $order_json->remainingDaysOneDrivePrice > 0)){
			$codes[self::ONEDRIVE] = array('price' => $order_json->newYearsOneDrivePrice + $order_json->remainingDaysOneDrivePrice);
		}

		// ---Netlex Classificatore---
		if(isset($order_json->newClassificatore) && isset($order_json->newYearsClassificatorePrice) && isset($order_json->remainingDaysClassificatorePrice) && $order_json->newClassificatore > 0 && ($order_json->newYearsClassificatorePrice + $order_json->remainingDaysClassificatorePrice > 0)){
			$codes[self::CLASSIFICATORE] = array('price' => $order_json->newYearsClassificatorePrice + $order_json->remainingDaysClassificatorePrice);
		}

		// ---Netlex Full Audit---
		if(isset($order_json->newFullAudit) && isset($order_json->newYearsFullAuditPrice) && isset($order_json->remainingDaysFullAuditPrice) && $order_json->newFullAudit > 0 && ($order_json->newYearsFullAuditPrice + $order_json->remainingDaysFullAuditPrice > 0)){
			$codes[self::LEGAL_ST_LOG] = array('price' => $order_json->newYearsFullAuditPrice + $order_json->remainingDaysFullAuditPrice);
		}

		// ---Netlex Workflow---
		if(isset($order_json->newWorkflow) && isset($order_json->newYearsWorkflowPrice) && isset($order_json->remainingDaysWorkflowPrice) && $order_json->newWorkflow > 0 && ($order_json->newYearsWorkflowPrice + $order_json->remainingDaysWorkflowPrice > 0)){
			$wfCode = $order_json->subscriptionType == 77 ? self::LEGAL_ENT_WORKF : self::LEGAL_ST_WORKF;
			$codes[$wfCode] = array('price' => $order_json->newYearsWorkflowPrice + $order_json->remainingDaysWorkflowPrice);
		}

		// ---Netlex Cassazione---
		if(isset($order_json->newCassazione) && isset($order_json->newYearsCassazionePrice) && isset($order_json->remainingDaysCassazionePrice) && $order_json->newCassazione > 0 && ($order_json->newYearsCassazionePrice + $order_json->remainingDaysCassazionePrice > 0) && !in_array($order_json->subscriptionType, array(41,66,77))){
			$codes[self::NETLEX_CASSAZIONE] = array('price' => $order_json->newYearsCassazionePrice + $order_json->remainingDaysCassazionePrice);
		}

		// --- Netlex spazio X GB aggiuntivo ---
		// if(isset($order_json->diskSpaceToAdd) && isset($order_json->remainingDaysDiskSpacePrice) && isset($order_json->newYearsDiskSpacePrice) && $order_json->diskSpaceToAdd > 1 && $order_json->diskSpaceToAdd != 30 && !$order_json->isLicenseExtension){
		// 	$amount_gb = $order_json->diskSpaceToAdd;
		// 	$price_gb = $order_json->remainingDaysDiskSpacePrice + $order_json->newYearsDiskSpacePrice;
		// 	$count_30 = 0;
		// 	$count_1 = 0;
		// 	$price_30 = 0;
		// 	$price_1 = $price_gb / $amount_gb;
		// 	if(intval($order_json->diskSpaceToAdd / 30) > 0){
		// 		$count_30 = intval($order_json->diskSpaceToAdd / 30);
		// 		$amount_gb -= $count_30 * 30;
		// 		$price_30 = $price_1 * 30;
		// 	}
		// 	$count_1 = $amount_gb;

		// 	for ($i=0; $i < $count_30; $i++) { 
		// 		$codes['30GBAGGIUNTIVI_' . $i] = $price_30;
		// 	}

		// 	for ($i=0; $i < $count_1; $i++) { 
		// 		$codes['1GBAGGIUNTIVI_' . $i] = $price_1;
		// 	}
		// }

		// --- Netlex X accesso avvocato ---
		// if(isset($order_json->newLawyersToAdd) && isset($order_json->newYearsLawyersPrice) && isset($order_json->remainingDaysLawyersPrice) && $order_json->newLawyersToAdd > 1){
		// 	$amount_lawyers = $order_json->newLawyersToAdd;
		// 	$price_lawyers = $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice;
		// 	$price_1 = $price_lawyers / $amount_lawyers;
		// 	for ($i=0; $i < $amount_lawyers; $i++) { 
		// 		if($order_json->subscriptionType == 2)
		// 			$codes['ACCESSO_AVVOCATO_PLUS_'. $i] = $price_1;
		// 		elseif($order_json->subscriptionType == 1)
		// 			$codes['ACCESSO_AVVOCATO_STD_'. $i] = $price_1;
		// 	}
		// }

		// ---Netlex X accesso utente ---
		// if(isset($order_json->newUsersToAdd) && isset($order_json->remainingDaysUsersPrice) && isset($order_json->newYearsUsersPrice) && $order_json->newUsersToAdd > 1){
		// 	$amount_users = $order_json->newUsersToAdd;
		// 	$price_users = $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice;
		// 	$price_1 = $price_users / $amount_users;
		// 	for ($i=0; $i < $amount_users; $i++) { 
		// 		if($order_json->subscriptionType == 2)
		// 			$codes['ACCESSO_UTENTE_PLUS_'. $i] = $price_1;
		// 		elseif($order_json->subscriptionType == 1)
		// 			$codes['ACCESSO_UTENTE_STD_'. $i] = $price_1;
		// 	}
		// }

		// ---Netlex X accesso utente SL ---
		// if(isset($order_json->newNumberOfUsers) && isset($order_json->newYearsUsersPrice) && $order_json->newNumberOfUsers > 1 && $order_json->subscriptionType == 66){
		// 	$amount_users = $order_json->newNumberOfUsers;
		// 	$price_users = $order_json->newYearsUsersPrice;
		// 	$price_1 = $price_users / $amount_users;
		// 	for ($i=0; $i < $amount_users; $i++) { 
		// 		$codes['ACCESSO_UTENTE_SL_'. $i] = $price_1;
		// 	}
		// }

		if($order['order_type'] == 2 && isset($order_json->studio_credit_amount) && $order_json->studio_credit_amount > 0){
			$codes[self::RICARICA] = array('price' => $order_json->studio_credit_amount);
		}

		if($order['order_type'] == 2 && isset($order_json->user_credit_amount) && $order_json->user_credit_amount > 0){
			$codes[self::RICARICA] = array('price' => $order_json->user_credit_amount);
		}

		// --- Pacchetti fatturazione ---
		if(!empty($order_json->invoicesPackets)){
			foreach ($order_json->invoicesPackets as $key => $packet) {
				if($packet->prezzo > 0){
					//pacchetto base
					if(isset($packet->codice)){
						if($packet->codice == "LEG-NL-B2B250"){
							$codes["FATT_250_" . $key] = array('price' => $packet->prezzo);
						} else if($packet->codice == "LEG-NL-B2B500"){
							$codes["FATT_500_" . $key] = array('price' => $packet->prezzo);
						}
					//pacchetto aggiuntivo
					} else{
						$codes["FATT_UP_200_" . $key] = array('price' => $packet->prezzo, 'qty' => $packet->quantita);
					}
				}
			}
		}

		// --- Pacchetti fatturazione (no ordine) ---
		if($order['order_type'] == 3){
			$formatted = $order_json->formatted;
			if($formatted->agyo_packet == "LEG-NL-B2B250"){
				$codes[self::FATT_250] = array('price' => $order_json->totale);
			} else if($formatted->agyo_packet == "LEG-NL-B2B500"){
				$codes[self::FATT_500] = array('price' => $order_json->totale);
			} else {
				$upg_codes = $order_json->fatture - 500;
				$upg_codes /= 200;
				$upg_price = 30 * $upg_codes;
				$codes[self::FATT_500] = array('price' => $order_json->totale - $upg_price);
				$codes[self::FATT_UP_200] = array('price' => 30.00, 'qty' => $upg_codes);
			}
		}

		if(isset($order_json->isLicenseExtension) && $order_json->isLicenseExtension){
			// --- Canone GB aggiuntivi---
			if($order_json->remainingDaysDiskSpacePrice + $order_json->newYearsDiskSpacePrice > 0){
				$free_gb = 3;
				$key = '1GBAGGIUNTIVI';
				$unit = 1;
				if($order_json->subscriptionType == 2){
					$free_gb = 30;
				}
				elseif($order_json->subscriptionType == 66){
					$free_gb = 100;
				}
				elseif($order_json->subscriptionType == 37){
					$free_gb = 50;
					$key = '50GBAGGIUNTIVI';
					$unit = 50;
				}
				elseif($order_json->subscriptionType == 38){
					$free_gb = 10;
					$key = '50GBAGGIUNTIVI';
					$unit = 50;
				}
				elseif($order_json->subscriptionType == 41){
					$free_gb = 20;
					$key = '50GBAGGIUNTIVI';
					$unit = 50;
				}
				$payed_gb = intval(($order_json->newDiskSpace - $free_gb) / $unit);
				
					$codes[$key] = array(
						'price' => $order_json->remainingDaysDiskSpacePrice + $order_json->newYearsDiskSpacePrice,
						'qty' => $payed_gb
					);
			}

			// --- Canone Avvocato ---
			if(($order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice > 0)){
				$price_lawyers = $order_json->newYearsLawyersPrice + $order_json->remainingDaysLawyersPrice;
				if($order_json->subscriptionType == 2){
					$codes['ACCESSO_AVVOCATO_PLUS'] = array('price' => $price_lawyers, 'qty' => $order_json->newNumberOfLawyers);
				} elseif($order_json->subscriptionType == 1){
						$codes['ACCESSO_AVVOCATO_STD'] = array('price' => $price_lawyers, 'qty' => $order_json->newNumberOfLawyers);					
				} elseif($order_json->subscriptionType == 36){
					$codes['ACCESSO_AVVOCATO_GOLD'] = array('price' => $price_lawyers, 'qty' => $order_json->newNumberOfLawyers);
				} elseif($order_json->subscriptionType == 38){
					$codes['ACCESSO_AVVOCATO_CLOUD_PDA'] = array('price' => $price_lawyers, 'qty' => $order_json->newNumberOfLawyers);
				} elseif($order_json->subscriptionType == 37 && !isset($order_json->cloudLawyers)){
					if ($order_json->newNumberOfLawyers > 1){
						$codes['ACCESSO_AVVOCATO_CLOUD'] = array('price' => $price_lawyers, 'qty' => $order_json->newNumberOfLawyers);
					}
				} elseif($order_json->subscriptionType == 41 && !isset($order_json->pduaLawyers)){
					if ($order_json->newNumberOfLawyers > 1){
						$codes['ACCESSO_AVVOCATO_PDUA'] = array('price' => $price_lawyers, 'qty' => $order_json->newNumberOfLawyers);
					}
				}
			}

			// --- Canone Utente ---
			if($order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice > 0){
				$price_users = $order_json->remainingDaysUsersPrice + $order_json->newYearsUsersPrice;
				if($order_json->subscriptionType == 2){
					$codes['ACCESSO_UTENTE_PLUS'] = array('price' => $price_users, 'qty' => $order_json->newNumberOfUsers);
				} elseif($order_json->subscriptionType == 1){
					$codes['ACCESSO_UTENTE_STD'] = array('price' => $price_users, 'qty' => $order_json->newNumberOfUsers);
				} elseif($order_json->subscriptionType == 37){
					if ($order_json->newNumberOfUsers > 1){
						$codes['ACCESSO_UTENTE_CLOUD'] = array('price' => $price_users, 'qty' => $order_json->newNumberOfUsers);
					}
				} elseif($order_json->subscriptionType == 38){
					$codes['ACCESSO_UTENTE_CLOUD_PDA'] = array('price' => $price_users, 'qty' => $order_json->newNumberOfUsers);
				} elseif($order_json->subscriptionType == 41){
					if ($order_json->newNumberOfUsers > 1){
						$codes['ACCESSO_UTENTE_PDUA'] = array('price' => $price_users, 'qty' => $order_json->newNumberOfUsers);
					}
				}
			}
			
			// --- Canone Utente basic ---
			if($order_json->remainingDaysBasicUsersPrice + $order_json->newYearsBasicUsersPrice > 0){
				$price_users = $order_json->remainingDaysBasicUsersPrice + $order_json->newYearsBasicUsersPrice;
				if($order_json->subscriptionType == 66){
					$codes['ACCESSO_UTENTE_BASIC_SL'] = array('price' => $price_users, 'qty' => $order_json->newNumberOfBasicUsers);
				} elseif($order_json->subscriptionType == 77){
					$codes['ACCESSO_UTENTE_BASIC_EL'] = array('price' => $price_users, 'qty' => $order_json->newNumberOfBasicUsers);
				}
			}
		}

		if(isset($order_json->kitUpgrade) && isset($order_json->kitUpgradePrice) && !empty($order_json->kitUpgradePrice)){
			$codes[self::NETLEX_UPGRADE_DEPOSITI] = array('price' => $order_json->kitUpgradePrice);
		}

		return $codes;
	}

	public function FEPAInit($name, $post_fields){	
		//$headers = array("Content-Type: text/xml; charset=utf-8;", "SOAPAction: \"https://licenze.teamsystem.com/WS_Coupon/".$name."\"", "Host: licenzetest.teamsystem.com");
		$headers = array("Content-Type: text/xml; charset=utf-8;", "SOAPAction: \"https://licenze.teamsystem.com/WS_Coupon/".$name."\"", "Host: lycenze.teamsystem.com");
		$curl = curl_init();
	    
		curl_setopt($curl, CURLOPT_URL, self::KFEPA_HOST);
		curl_setopt($curl, CURLOPT_TIMEOUT, 60);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $post_fields);

//		$this->log("\npost_fields: \n" . print_r($post_fields, 1));

		$data = curl_exec($curl);

//		$this->log("\nrepsonse: \n" . print_r($data, 1));

		if ( strpos($data, '_Incapsula_Resource?') !== false ) {
			throw new Exception( "Errore richiesta !\nREQUEST: " . print_r($post_fields, 1) . "\nRESPONSE: " . print_r($data, 1) );
		}

		$xmlNode = simplexml_load_string($data);
		$array = xml_to_array($xmlNode);
		$array = remove_namespaces($array);

	    return $array;
	}

	public function createEnvelope($request, $data){
	    $body = "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"><soap:Body>";
	    $body .= "<".$request." xmlns=\"https://licenze.teamsystem.com/WS_Coupon/\"><Dati>";
	    $body .= $data;
	    $body .= "</Dati></".$request."></soap:Body></soap:Envelope>";
	    return $body;
	}

	public function doRequest($name, $arguments){
		$data = $arguments;
	    if($name=="Verifica_Anagrafica_Cliente" || $name=="Verifica_Cliente" || $name=="Crea_Coupon_FEPA"){
	        $body = $this->createEnvelope($name, $data);
	        //print_r($body);
	        $resp = $this->FEPAInit($name, $body);
	    }
	    else{
	        $resp = array(
	            "Error" => true,
	            "ErrorCode" => -1,
	            "ErrorMessage" => "Il metodo richiamato non esiste.",
	            "ErrorSubject" => "Local"
	        );
	    }
	    return $resp;
	}

	// Funzioni di supporto

	public function buildRequest($params)
	{
	    $xml_data = new SimpleXMLElement("<?xml version=\"1.0\" encoding=\"UTF-8\"?><Coupon version=\"1\"></Coupon>");
	    $arr = array("Autenticazione" => array("Utente" => self::KUTENTE, "Password" => self::KPASSWORD), 'Dati' => $params);
	    array_to_xml($arr, $xml_data);
	    $xml_string = $xml_data->asXml();
	    return $xml_string;
	}

	public function encryptXML64($string) {

		$rsa = new Crypt_RSA();
		$rsa->setPublicKeyFormat(CRYPT_RSA_PUBLIC_FORMAT_XML);
		$rsa->loadKey(self::KRSA_KEY, CRYPT_RSA_PUBLIC_FORMAT_XML);
		$rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
		$encrypted = $rsa->encrypt64($string);
		return $encrypted;
	}

	// Funzioni "da chiamare"

	public function VerificaCliente($nome,$piva,$cf,$indirizzo,$cap,$citta,$provincia,$tel,$email,$nome_cliente, $cognome_cliente, $articolo, $rivenditore, $campagna)
	{
	    $req = array(
	        "PartitaIVA" => $piva,
	        "CodiceFiscale" => $cf,
	        "RagioneSociale" => $nome,
	        "Indirizzo" => $indirizzo,
	        "CAP" => $cap,
	        "Citta" => $citta,
	        "Provincia" => $provincia,
	        "Telefono" => $tel,
	        "Email" => $email,
	        "Nome" => $nome_cliente,
	        "Cognome" => $cognome_cliente,
	        "Categoria" => "0",
	        "Servizio" => "NETLEX",
	        "Articolo" => $articolo,
	        "IdRivenditore" => $rivenditore,
	        "Campagna" => $campagna
	    );
	    $req = $this->buildRequest($req);

//	    $this->log('Verifica_Anagrafica_Cliente: ' . $req);

	    $xml_string = $this->encryptXML64($req);
	    $result = $this->doRequest('Verifica_Anagrafica_Cliente', $xml_string);
	    return $result;
	}

	public function verificaStatoCliente($piva, $codice, $campagna=null)
	{   
	    $req = array(
	        "PIVA" => $piva,
	        "Articolo" => $codice,
	        "Servizio" => "NETLEX",
	        "Campagna" => isset($campagna) ? $campagna : "SW212"
	    );

	    $req = $this->buildRequest($req);

//		$this->log('Verifica_Cliente: ' . $req);

	    $xml_string = $this->encryptXML64($req);
	    $result = $this->doRequest('Verifica_Cliente', $xml_string);
	    return $result;
	}

	public function buildCouponString($nome,$piva,$cf,$indirizzo,$cap,$citta,$provincia,$tel,$email,$nome_cliente, $cognome_cliente,$id_transazione,$data_transazione,$importo, $articolo, $rivenditore_id, $campagna, $quantita, $promo=null)
	{
		if(!isset($promo))
			$promo = '';
	    $req = array(
	        "PartitaIVA" => $piva,
	        "CodiceFiscale" => $cf,
	        "RagioneSociale" => $nome,
	        "Indirizzo" => $indirizzo,
	        "CAP" => $cap,
	        "Citta" => $citta,
	        "Provincia" => $provincia,
	        "Telefono" => $tel,
	        "Email" => $email,
	        "Nome" => $nome_cliente,
	        "Cognome" => $cognome_cliente,
	        "Categoria" => "0",
	        "IdRivenditore" => $rivenditore_id,
	        "Servizio" => "NETLEX",
			"Articolo" => $articolo,
			"Quantita" => $quantita,
	        "Promo" => $promo,
	        "Importo" => $importo,
	        "IdTransazione" => $id_transazione,
	        "Campagna" => $campagna,
	        "DataTransazione" => $data_transazione
	    );
	    $req = $this->buildRequest($req);

//		$this->log('Crea_Coupon_FEPA: ' . $req);

	    $xml_string = $this->encryptXML64($req);
	    $result = $this->doRequest('Crea_Coupon_FEPA', $xml_string);
	    return $result;
	}
	public function checkCliente($vatnumber, $campagna, $code = 'WCNLX1GB001'){


			$result = $this->verificaStatoCliente($vatnumber, $code, $campagna);
			//
			// echo '<pre>';
			// print_r($result);
			// echo '</pre>';


		$result = $result['Envelope']['Body']['Verifica_ClienteResponse']['Verifica_ClienteResult'];
		$rivenditore_id = $result['Rivenditore']['ID'];
		//Errore generico nella chiamata
		if($result['Esito']['Tipo'] != 'OK') {
			return $result;
		} else {
			//piva corretta, cliente non libero
			if($result['Stato_Cliente'] == 'Occupato') {
				return 2;
			} else if($result['Stato_Cliente'] == 'Libero_acquisto_aggiuntivo') {
				// Cliente libero per un acquisto aggiuntivo dallo stesso rivenditore
				return 4;
			}
		}
		//apposto, cliente libero
		return 3;
	}
	public function sendLicense($customer, $order, $campagna, $error_mode=false, $error_codes=null){

		if(!$error_mode)
			$codes = $this->assignCodes($order);
		else
			$codes = json_decode($error_codes, true);

		$status = 0;
		$response_ok = "";
		$response_ko = "";
		$coupons = array();

		// return $codes;
		foreach ($codes as $code => $data) {

//			$this->log("Code: " . $code);
//			$this->log("Data: " . print_r($data, 1));

			if(strpos($code, '30GBAGGIUNTIVI') !== false)
				$code = self::NETLEX30GB;
			if(strpos($code, '1GBAGGIUNTIVI') !== false)
				$code = self::NETLEX1GB;
			if(strpos($code, '50GBAGGIUNTIVI') !== false)
				$code = self::NETLEX_INCLOUD_50;
			if(strpos($code, 'ACCESSO_AVVOCATO_PLUS') !== false)
				$code = self::NETLEXPLUS_ACC_AVV;
			if(strpos($code, 'ACCESSO_AVVOCATO_GOLD') !== false)
				$code = self::GOLD_CAN;
			if(strpos($code, 'ACCESSO_AVVOCATO_STD') !== false)
				$code = self::NETLEXBASE_ACC_AVV;
			if(strpos($code, 'ACCESSO_AVVOCATO_CLOUD') !== false)
				$code = self::NETLEX_INCLOUD_AVV;
			if(strpos($code, 'ACCESSO_AVVOCATO_PDUA') !== false)
				$code = self::NETLEX_PDA;
			if(strpos($code, 'ACCESSO_AVVOCATO_CLOUD_PDA') !== false)
				$code = self::NETLEX_PDA;
			if(strpos($code, 'ACCESSO_UTENTE_PLUS') !== false)
				$code = self::NETLEXPLUS_ACC_USER;
			if(strpos($code, 'ACCESSO_UTENTE_STD') !== false)
				$code = self::NETLEXBASE_ACC_USER;
			if(strpos($code, 'ACCESSO_UTENTE_SL') !== false)
				$code = self::LEGAL_ST_AVV_AGG;
			if(strpos($code, 'ACCESSO_UTENTE_CLOUD') !== false)
				$code = self::NETLEX_INCLOUD_SGR;
			if(strpos($code, 'ACCESSO_UTENTE_PDUA') !== false)
				$code = self::NETLEX_PDUA_SGR;
			if(strpos($code, 'ACCESSO_UTENTE_CLOUD_PDA') !== false)
				$code = self::NETLEX_PDA_SGR;
			if(strpos($code, 'FATT_250_') !== false)
				$code = self::FATT_250;
			if(strpos($code, 'FATT_500_') !== false)
				$code = self::FATT_500;
			if(strpos($code, 'FATT_UP_200_') !== false)
				$code = self::FATT_UP_200;
			if(strpos($code, 'ACCESSO_UTENTE_BASIC_SL') !== false)
				$code = self::LEGAL_ST_AVV_BASIC;
			if(strpos($code, 'ACCESSO_UTENTE_BASIC_EL') !== false)
				$code = self::LEGAL_ENT_AVV_BASIC;

			$result = $this->controllaPartitaIVA($customer['vatnumber']);
			if(!$result){
				$error = "ERRORE -- P.Iva " . $customer['vatnumber'] . " associata a " .  $customer['customer_name'] . " non valida";
				$response_ko .= $error . "<br/><br/>";
				break;
			}

			$result = $this->verificaStatoCliente($customer['vatnumber'], $code, $campagna);
			$result = $result['Envelope']['Body']['Verifica_ClienteResponse']['Verifica_ClienteResult'];
			$result_stato_cliente = $result;
			$rivenditore_id = $result['Rivenditore']['ID'];
			//Errore generico nella chiamata
			if($result['Esito']['Tipo'] != 'OK'){
				$error = "ERRORE -- Esito di tipo " . $result['Esito']['Tipo'] . " con messaggio \"" . $result['Esito']['Messaggio'] . "\" con ordine " . $order['order_code'] . " sul codice " . $code . " --";
				$response_ko .= $error . "<br/><br/>";
				$failed_codes[$code] = $data['price'];
				continue;
			}

			else{
				//piva corretta, cliente non libero
				if(($result['Stato_Cliente'] == 'Errore' || $result['Stato_Cliente'] == 'Occupato')){
					$error = "ERRORE -- PIVA corretta, stato cliente: " . $result['Stato_Cliente'] . " sul codice " . $code . " --";
					$response_ko .= $error . "<br/><br/>";
					$failed_codes[$code] = $data['price'];
					continue;
				}

				//piva corretta, articolo non corretto
				if($result['Stato_Articolo'] != 'OK'){
					$error = "ERRORE -- PIVA corretta, stato articolo: " . $result['Stato_Cliente'] . " sul codice " . $code . " --";
					$response_ko .= $error . "<br/><br/>";
					$failed_codes[$code] = $data['price'];
					continue;
				}

				/*
				if($result['Rivenditore']['Campagna'] != $campagna){
					$error = "ERRORE -- PIVA corretta, campagna non corretta: restituito " . $result['Rivenditore']['Campagna'] . ", previsto " . $campagna . " --";
					$response_ko .= $error . "<br/><br/>";
					continue;
				}*/

				//piva corretta, cliente libero, articolo corretto
				if(($result['Stato_Cliente'] == 'Libero_primo_acquisto' || $result['Stato_Cliente'] == 'Libero_acquisto_aggiuntivo') && $result['Stato_Articolo'] == 'OK'){
					//*------CHECK ANAGRAFICA------*
					$result = $this->VerificaCliente(
						$customer['customer_name'],
						$customer['vatnumber'],	
						$customer['customer_fiscal_code'],
						$customer['address'],
						$customer['zipcode'],
						$customer['city'],
						$customer['state'],
						$customer['customer_phone'],
						$customer['customer_email'],
						$customer['firstname'],
						$customer['lastname'],
						$code,
						$rivenditore_id,
						$campagna
					);
					//return $result;
					$result = $result['Envelope']['Body']['Verifica_Anagrafica_ClienteResponse']['Verifica_Anagrafica_ClienteResult'];
					$result_verifica_cliente = $result;
					//anagrafica non corretta
					if($result['Esito']['Tipo'] != "OK"){
						$error = "ERRORE -- Anagrafica non corretta con messaggio: " . $result['Esito']['Messaggio'] . " sul codice " . $code . " Per l'utente " . $customer['customer_name'] ." --";
						$response_ko .= $error . "<br/><br/>";
						$failed_codes[$code] = $data['price'];
						continue;
					}

					$data_efficacia = $this->getDataEfficacia($order);
					
					//*-----INVIO VOUCHER-----*
					$result = $this->buildCouponString(
						$customer['customer_name'],
						$customer['vatnumber'],
						$customer['customer_fiscal_code'],
						$customer['address'],
						$customer['zipcode'],
						$customer['city'],
						$customer['state'],
						$customer['customer_phone'],
						$customer['customer_email'],
						$customer['firstname'],
						$customer['lastname'],
						$order['id'],
						$data_efficacia,
						number_format($data['price'],2,",","."),
						$code,
						$rivenditore_id,
						$campagna,
						isset($data['qty']) ? $data['qty'] : 1,
						$order['discount_code'] == '' ? null : $order['discount_code']
					);
					
						
					$result = $result['Envelope']['Body']['Crea_Coupon_FEPAResponse']['Crea_Coupon_FEPAResult'];
					
					//Invio Coupon fallito
					if($result['Esito']['Tipo'] != "OK"){
						$error = "ERRORE -- Invio Coupon non riuscito con messaggio: " . $result['Esito']['Messaggio'] . " sul codice " . $code . " Per l'utente " . $customer['customer_name'] . " --";
						$response_ko .= $error . "<br/><br/>";
						$failed_codes[$code] = $data['price'];
						continue;
					}
					else{
						array_push($coupons, array($code => $result['Id_Coupon']));
						$result = json_encode($result);
						$response_ok .= "*------INVIATO CODICE " . $code . " CON SUCCESSO------*<br/><br/>";
					}
				//Errore generico
				} else {
					$result = json_encode($result);
					$error = "ERRORE -- Invio Coupon non riuscito: " . $result . " --";
					$response_ko .= $error . "<br/><br/>";
				}
			}
		}

//		$this->log("post foreach");

		if(empty($response_ko)){
			$status = 1;
		}

		$response = [
			"status" => $status,
			"response_ok" => $response_ok,
			"response_ko" => $response_ko,
			"failed_codes" => isset($failed_codes) ? json_encode($failed_codes) : null,
			"result" => isset($result) ? json_encode($result) : null,
			"result_stato_cliente" => isset($result_stato_cliente) ? json_encode($result_stato_cliente) : null,
			"result_verifica_cliente" => isset($result_verifica_cliente) ? json_encode($result_verifica_cliente) : null,
			"coupons" => json_encode($coupons),
		];

		return $response;
	}

	function controllaPartitaIVA($pi)
	{
	  if($pi == '00000000000'){
	  	return false;
	  }
	  if ($pi === '') return false;
	  elseif (strlen($pi) != 11) return false;
	  elseif (preg_match("/^[0-9]+$/", $pi) != 1) return false;
	  else {
	    $s = $c = 0;
	    for($i=0; $i<=9; $i+=2) {
	      $s += ord($pi[$i]) - ord('0');
	    }
	    for ($i=1; $i<=9; $i+=2) {
	      $c = 2*(ord($pi[$i]) - ord('0'));
	      if ($c > 9) $c = $c - 9;
	      $s += $c;
	    }
	    $controllo = (10 - $s%10)%10;
	    if ($controllo != (ord($pi[10]) - ord('0'))) {
	      return false;
	    }else{
	      return true;
	    }  
	  }
	}

	function getDataEfficacia($order){
		$order_date = $order['order_date'];
		$order_json = json_decode($order['order_json']);
		//se l'ordine è di rinnovo, la data di efficacia è la vecchia data di scadenza
		if($order['order_type'] == 0 && $order_json->isLicenseExtension){
			$previous_state = json_decode($order['previous_state']);
			$order_date = $previous_state->expiration_date;
		}

		return $order_date;
	}

	function normalizeCode($code){
		switch ($code) {
			case strpos($code, '30GBAGGIUNTIVI') !== false:
				return self::NETLEX30GB;
			case strpos($code, '1GBAGGIUNTIVI') !== false:
				return self::NETLEX1GB;
			case strpos($code, '50GBAGGIUNTIVI') !== false:
				return self::NETLEX_INCLOUD_50;
			case strpos($code, 'ACCESSO_AVVOCATO_PLUS') !== false:
				return self::NETLEXPLUS_ACC_AVV;
			case strpos($code, 'ACCESSO_AVVOCATO_STD') !== false:
				return self::NETLEXBASE_ACC_AVV;
			case strpos($code, 'ACCESSO_AVVOCATO_GOLD') !== false:
				return self::GOLD_CAN;
			case strpos($code, 'ACCESSO_AVVOCATO_CLOUD') !== false:
				return self::NETLEX_INCLOUD_AVV;
			case strpos($code, 'ACCESSO_AVVOCATO_PDUA') !== false:
				return self::NETLEX_PDA;
			case strpos($code, 'ACCESSO_AVVOCATO_CLOUD_PDA') !== false:
				return self::NETLEX_PDA;
			case strpos($code, 'ACCESSO_UTENTE_PLUS') !== false:
				return self::NETLEXPLUS_ACC_USER;
			case strpos($code, 'ACCESSO_UTENTE_STD') !== false:
				return self::NETLEXBASE_ACC_USER;
			case strpos($code, 'ACCESSO_UTENTE_SL') !== false:
				return self::LEGAL_ST_AVV_AGG;
			case strpos($code, 'ACCESSO_UTENTE_CLOUD') !== false:
				return self::NETLEX_INCLOUD_SGR;
			case strpos($code, 'ACCESSO_UTENTE_PDUA') !== false:
				return self::NETLEX_PDUA_SGR;
			case strpos($code, 'ACCESSO_UTENTE_CLOUD_PDA') !== false:
				return self::NETLEX_PDA_SGR;
			case strpos($code, 'FATT_250_') !== false:
				return self::FATT_250;
			case strpos($code, 'FATT_500_') !== false:
				return self::FATT_500;
			case strpos($code, 'FATT_UP_200_') !== false:
				return self::FATT_UP_200;
			default:
				return $code;
		}
	}

	function getDescrizioneArticolo($codes, $priceList, $order, $previousOrders){
		//default, ordine non di rinnovo
		$dataAttivazione = $order['order_date'];
		$dataRinnovo = $order['new_expiration_date'];
		$order_json = json_decode($order['order_json']);
		$renew = $order['is_renew'];
		foreach ($codes as $code => &$value) {
			switch ($code) {	
				
				////
				case 'NETLEX-STUDISETTORE':
					$value['description'] = 'Netlex in Cloud STUDI DI SETTORE';
					$value['price'] = $priceList['studi_settore'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-ONEDRIVE':
					$value['description'] = 'Netlex in Cloud - Modulo Gestione documenti Onedrive';
					$value['price'] = $priceList['one_drive'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-INCLOUD-SGR':
					$value['description'] = 'Netlex in Cloud - Utente segreteria agg.vo';
					$value['price'] = ($priceList['cloud_user']* 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					if (!empty($value['passaggio'])){
						$value['passaggio'] = true;
					}
					break;
				case 'NETLEX-PDUA-SGR':
					$value['description'] = 'Netlex in Cloud PDUA - Utente segreteria agg.vo';
					$value['price'] = ($priceList['pdua_user']* 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					if (!empty($value['passaggio'])){
						$value['passaggio'] = true;
					}
					break;
				case 'NETLEX-INCLOUD-AVV':
					$pdua = $order_json->subscriptionType == 41 ? "PDUA" : "";
					$value['description'] = "Netlex in Cloud $pdua - Utente avvocato agg.vo";
					$value['price'] = ($priceList['cloud_lawyer']* 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-INCLOUD':
					$value['description'] = 'Netlex in Cloud attivazione';
					$value['price'] = ($priceList['cloud_activation'] / $priceList['price_divider']);
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-PDUA':
					$value['description'] = 'Netlex in Cloud PDUA';
					$value['price'] = ($priceList['pdua_activation'] / $priceList['price_divider']);
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-PDA_FREE':
					$value['description'] = 'Netlex in Cloud BASIC';
					$value['price'] = ($priceList['basic_activation'] / $priceList['price_divider']);
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-CCT':
					$value['description'] = 'Netlex in Cloud - Modulo Conservazione Sostitutiva 5GB';
					$value['price'] = $priceList['cct'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WCNLXFRAGG':
					$value['description'] = 'Netlex firma remota aggiuntiva';
					$value['price'] = $priceList['certificate'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
					
					//////
				case 'WCNLX1GB001':
					$value['description'] = 'Netlex in Cloud spazio 1 GB aggiuntivo';
					$value['price'] = $priceList['disk_space'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, '1GBAGGIUNTIVI') !== false:
					$value['description'] = 'Netlex in Cloud spazio 1 GB aggiuntivo';
					$value['price'] = $priceList['disk_space'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newDiskSpace è uguale tra ordine precedente e ordine attuale e c'è diskSpaceToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if(isset($prev_json->newDiskSpace) && isset($order_json->newDiskSpace) && isset($prev_json->diskSpaceToAdd) && 
							$prev_json->newDiskSpace == $order_json->newDiskSpace && $prev_json->diskSpaceToAdd > 0){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WCNLX30GB001':
					$value['description'] = 'Netlex in Cloud spazio 30 GB aggiuntivo';
					$value['price'] = $priceList['disk_space'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, '30GBAGGIUNTIVI') !== false:
					$value['description'] = 'Netlex in Cloud spazio 30 GB aggiuntivo';
					$value['price'] = $priceList['disk_space'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newDiskSpace è uguale tra ordine precedente e ordine attuale e c'è diskSpaceToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if(isset($prev_json->newDiskSpace) && isset($order_json->newDiskSpace) && isset($prev_json->diskSpaceToAdd) && 
						$prev_json->newDiskSpace == $order_json->newDiskSpace && $prev_json->diskSpaceToAdd > 0){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-INCLOUD-50':
					$value['description'] = 'Netlex in Cloud spazio 50 GB aggiuntivo';
					$value['price'] = $priceList['disk_space_50'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, '50GBAGGIUNTIVI') !== false:
					$value['description'] = 'Netlex in Cloud spazio 50 GB aggiuntivo';
					$value['price'] = $priceList['disk_space_50'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newDiskSpace è uguale tra ordine precedente e ordine attuale e c'è diskSpaceToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if(isset($prev_json->newDiskSpace) && isset($order_json->newDiskSpace) && isset($prev_json->diskSpaceToAdd) && 
						$prev_json->newDiskSpace == $order_json->newDiskSpace && $prev_json->diskSpaceToAdd > 0){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WANLXSTD001':
					$value['description'] = 'Netlex in Cloud Attivazione Standard';
					$value['price'] = $priceList['standard_activation'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WANLXPLU001':
					$value['description'] = 'Netlex in Cloud Attivazione Plus';
					$value['price'] = $priceList['premium_activation'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-PDA':
					$value['description'] = 'Netlex in Cloud Attivazione PDA';
					$value['price'] = $priceList['pda_activation'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-PDA_BASIC':
					$value['description'] = 'Netlex in Cloud PDA BASIC';
					$value['price'] = $priceList['pda_basic_activation'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-GOLD-ATT':
					$value['description'] = 'Netlex in Cloud Gold Edition - attivazione';
					$value['price'] = $priceList['gold_activation'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ST_ATT':
					$value['description'] = 'TeamSystem Studio Legal - Attivazione';
					$value['price'] = $priceList['studio_legal_activation'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					break;
				case 'LEGAL_ST_AVV_AGG':
					$value['description'] = 'TeamSystem Studio Legal - Utente Avvocato Aggiuntivo';
					$value['price'] = $priceList['studio_legal_user'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					break;
				case 'LEGAL_ST_14081':
					$value['description'] = 'TeamSystem Studio Legal - Utente Aggiuntivo (Offerta per clienti LEGAL SYSTEM)';
					$value['price'] = $priceList['studio_legal_user_2'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					break;
				case strpos($code, 'ACCESSO_UTENTE_SL') !== false:
					$value['description'] = 'TeamSystem Studio Legal - Utente Avvocato Aggiuntivo';
					$value['price'] = $priceList['studio_legal_user'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						$previous_state = json_decode($previousOrder['previous_state']);
						//newUsersToAdd è sempre valorizzato, l'attivazione si ha quando newNumberOfUsers è differente tra 
						//previous_state e prev_json
						if((isset($prev_json->newNumberOfUsers) && isset($previous_state->number_of_users) && isset($prev_json->newUsersToAdd) &&
							$prev_json->newNumberOfUsers != $previous_state->number_of_users && $prev_json->newUsersToAdd > 0) ||
							$previousOrder['is_first'] == 1){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ST_SK':
					$value['description'] = 'TeamSystem Studio Legal - Starter Kit (5 PdL Avvocati, 5 PdL Segretaria)';
					$value['price'] = $priceList['studio_legal_lawyer'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					break;
				case 'LEGAL_ST_SK3':
					$value['description'] = 'TeamSystem Studio Legal - Starter Kit (3 PdL Avvocati, 3 PdL Segretaria)';
					$value['price'] = $priceList['studio_legal_lawyer_2'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					break;
				case 'LEGAL_ST_14080':
					$value['description'] = 'TeamSystem Studio Legal - Starter Kit 5 accessi (Offerta per clienti LEGAL SYSTEM)';
					$value['price'] = $priceList['studio_legal_lawyer_4'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					break;
				case 'LEGAL_ST_14079':
					$value['description'] = 'TeamSystem Studio Legal - Starter Kit 3 accessi (Offerta per clienti LEGAL SYSTEM)';
					$value['price'] = $priceList['studio_legal_lawyer_3'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Studio Legal';
					break;
				case 'WCNLXACAPLU001':
					$value['description'] = 'Netlex in Cloud plus accesso avvocato';
					$value['price'] = ($priceList['premium_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_AVVOCATO_PLUS') !== false:
					$value['description'] = 'Netlex in Cloud plus accesso avvocato';
					$value['price'] = ($priceList['premium_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsLawyersPrice;
					}
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfLawyers è uguale tra ordine precedente e ordine attuale e c'è newLawyersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						//SE INVECE l'ordine è di un avvocato si confronta con il payment_type di previous_state 
						if((isset($prev_json->newNumberOfLawyers) && isset($order_json->newNumberOfLawyers) && isset($prev_json->newUsersToAdd) &&
							$prev_json->newNumberOfLawyers == $order_json->newNumberOfLawyers && $prev_json->newLawyersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 2){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WCNLXACASTD001':
					$value['description'] = 'Netlex in Cloud standard accesso avvocato';
					$value['price'] = ($priceList['lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_AVVOCATO_STD') !== false:
					$value['description'] = 'Netlex in Cloud standard accesso avvocato';
					$value['price'] = ($priceList['lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsLawyersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfLawyers è uguale tra ordine precedente e ordine attuale e c'è newLawyersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						//SE INVECE l'ordine è di un avvocato si confronta con il payment_type di previous_state 
						if((isset($prev_json->newNumberOfLawyers) && isset($order_json->newNumberOfLawyers) && isset($prev_json->newUsersToAdd) && 
							$prev_json->newNumberOfLawyers == $order_json->newNumberOfLawyers && $prev_json->newLawyersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 1){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-GOLD-CAN':
					$value['description'] = 'Netlex in Cloud Gold Edition - canone';
					$value['price'] = ($priceList['gold_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_AVVOCATO_GOLD') !== false:
					$value['description'] = 'Netlex in Cloud Gold Edition - canone';
					$value['price'] = ($priceList['gold_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsLawyersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfLawyers è uguale tra ordine precedente e ordine attuale e c'è newLawyersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						//SE INVECE l'ordine è di un avvocato si confronta con il payment_type di previous_state 
						if((isset($prev_json->newNumberOfLawyers) && isset($order_json->newNumberOfLawyers) && isset($prev_json->newUsersToAdd) && 
							$prev_json->newNumberOfLawyers == $order_json->newNumberOfLawyers && $prev_json->newLawyersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 36){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WCNLXACUPLU001':
					$value['description'] = 'Netlex in Cloud plus accesso segretaria/collaboratore';
					$value['price'] = ($priceList['premium_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_UTENTE_PLUS') !== false:
					$value['description'] = 'Netlex in Cloud plus accesso segretaria/collaboratore';
					$value['price'] = ($priceList['premium_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsUsersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfUsers è uguale tra ordine precedente e ordine attuale e c'è newUsersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if((isset($prev_json->newNumberOfUsers) && isset($order_json->newNumberOfUsers) && isset($prev_json->newUsersToAdd) && 
							$prev_json->newNumberOfUsers == $order_json->newNumberOfUsers && $prev_json->newUsersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 2){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WCNLXACUSTD001':
					$value['description'] = 'Netlex in Cloud standard accesso segretaria/collaboratore';
					$value['price'] = ($priceList['user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_UTENTE_STD') !== false:
					$value['description'] = 'Netlex in Cloud standard accesso segretaria/collaboratore';
					$value['price'] = ($priceList['user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsUsersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfUsers è uguale tra ordine precedente e ordine attuale e c'è newUsersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if((isset($prev_json->newNumberOfUsers) && isset($order_json->newNumberOfUsers) && isset($prev_json->newUsersToAdd) &&
							$prev_json->newNumberOfUsers == $order_json->newNumberOfUsers && $prev_json->newUsersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 1){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WCNLXAMI001':
					$value['description'] = 'Netlex in Cloud impegni multiutente';
					$value['price'] = $priceList['impegni_multiutente'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newMultiOwners è differente tra 
							//previous_state e prev_json
							if($prev_json->newMultiOwners == 1 && $previous_state->impegni_multiutente == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'WCNLXANT001':
					$value['description'] = 'Netlex in Cloud Antiriciclaggio';
					$value['price'] = $priceList['antiriciclaggio'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newAntirec è differente tra 
							//previous_state e prev_json
							if($prev_json->newAntirec == 1 && $previous_state->antiriciclaggio == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;		
					break;
				case 'WCNLXFEA001':
					$value['description'] = 'Netlex in Cloud fatturazione elettronica PA';
					$value['price'] = $priceList['fatturazione_elettronica'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newElectronicBills è differente tra 
							//previous_state e prev_json
							if($prev_json->newElectronicBills == 1 && $previous_state->fatturazione_elettronica == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;		
					break;
				case 'WCNLXNIR001':
					$value['description'] = 'Netlex in Cloud Nota Iscrizione a Ruolo';
					$value['price'] = $priceList['note'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newNote è differente tra 
							//previous_state e prev_json
							if($prev_json->newNote == 1 && $previous_state->nota_iscrizione_ruolo == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;	
					break;
				case 'NETLEX-STUDISETTORE':
					$value['description'] = 'Netlex in Cloud Studi di Settore';
					$value['price'] = $priceList['studi_settore'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newStudiSettore è differente tra 
							//previous_state e prev_json
							if($prev_json->newStudiSettore == 1 && $previous_state->studi_settore == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-PDA-ABB':
					$value['description'] = 'Netlex in Cloud PDA';
					$value['price'] = ($priceList['pda_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					//avvocato PDA viene attivato al primo ordine, bisogna comunque fare un ciclo completo degli ordini
					//in quanto le prime attivazioni non avevano il flag is_first
					foreach ($previousOrders as $previousOrder) {
						$value['dataAttivazione'] = $previousOrder['order_date'];
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-PDA_BASIC-ABB':
					$value['description'] = 'Netlex in Cloud PDA BASIC';
					$value['price'] = ($priceList['pda_basic_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					//avvocato PDA viene attivato al primo ordine
					foreach ($previousOrders as $previousOrder) {
						if($previousOrder['is_first']){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-ULOF-CAN':
					$value['description'] = 'Netlex in Cloud ULOF -- canone';
					$value['price'] = ($priceList['pda_ulof_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					//avvocato ULOF viene attivato al primo ordine
					foreach ($previousOrders as $previousOrder) {
						if($previousOrder['is_first']){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-NIR-CAN':
					$value['description'] = 'Netlex in Cloud EasyNota -- canone';
					$value['price'] = ($priceList['easynota_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;					
					//avvocato ULOF viene attivato al primo ordine
					foreach ($previousOrders as $previousOrder) {
						if($previousOrder['is_first']){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-REDATTORE':
					$value['description'] = 'Netlex in Cloud redattore';
					break;
				case 'NETLEX-TRAINING':
					$value['description'] = 'Netlex in Cloud training';
					break;
				case 'WCNLXRICARICA':
					$value['description'] = 'Netlex in Cloud ricarica';
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEG-NL-B2B250':
					$value['description'] = 'Netlex in Cloud Invio e ricezione e conservazione fino a 250 fatture';
					$value['price'] = $priceList['LEG-NL-B2B250'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $order_json->scadenza;
					break;
				case strpos($code, 'FATT_250_') !== false:
					$value['description'] = 'Netlex in Cloud Invio e ricezione e conservazione fino a 250 fatture';
					$value['price'] = $priceList['LEG-NL-B2B250'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['years'] = $order_json->year;
					break;
				case 'LEG-NL-B2B500':
					$value['description'] = 'Netlex in Cloud Invio e ricezione e conservazione fino a 500 fatture';
					$value['price'] = $priceList['LEG-NL-B2B500'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $order_json->scadenza;
					break;
				case strpos($code, 'FATT_500_') !== false:
					$value['description'] = 'Netlex in Cloud Invio e ricezione e conservazione fino a 500 fatture';
					$value['price'] = $priceList['LEG-NL-B2B500'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['years'] = $order_json->year;
					break;
				case 'LEG-NL-B2BUPG200':
					$value['description'] = 'Netlex in Cloud Invio e ricezione e conservazione 200 fatture ulteriori';
					$value['price'] = 30.00;
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $order_json->scadenza;
					break;
				case strpos($code, 'FATT_UP_200_') !== false:
					$value['description'] = 'Netlex in Cloud Invio e ricezione e conservazione 200 fatture ulteriori';
					$value['price'] = 30.00;
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['years'] = $order_json->year;
					break;
				case 'NETLEX-14036':
					$value['description'] = 'Netlex in Cloud - Recupero Crediti';
					$value['price'] = $priceList['recupero_crediti'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newRecuperoCrediti è differente tra 
							//previous_state e prev_json
							if($prev_json->newRecuperoCrediti == 1 && $previous_state->recupero_crediti == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ST_REC':
					$value['description'] = 'TeamSystem Studio Legal – Recupero Crediti';
					$value['price'] = $priceList['recupero_crediti'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newRecuperoCrediti è differente tra 
							//previous_state e prev_json
							if($prev_json->newRecuperoCrediti == 1 && $previous_state->recupero_crediti == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX_CAM_DIN':
					$value['description'] = 'NetLex in Cloud – Campi dinamici';
					$value['price'] = $priceList['campi_dinamici'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newClassificatore è differente tra 
							//previous_state e prev_json
							if($prev_json->newClassificatore == 1 && $previous_state->classificatore == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ST_LOG':
					$value['description'] = 'TeamSystem Studio Legal – log modifiche';
					$value['price'] = $priceList['full_audit'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newFullAudit è differente tra 
							//previous_state e prev_json
							if($prev_json->newFullAudit == 1 && $previous_state->full_audit == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ST_WORKF':
					$value['description'] = 'TeamSystem Studio Legal – workflow';
					$value['price'] = $priceList['workflow'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newWorkflow è differente tra 
							//previous_state e prev_json
							if($prev_json->newWorkflow == 1 && $previous_state->workflow == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ENT_WORKF':
					$value['description'] = 'TTeamSystem Enterprise Legal – workflow';
					$value['price'] = $priceList['workflow'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newWorkflow è differente tra 
							//previous_state e prev_json
							if($prev_json->newWorkflow == 1 && $previous_state->workflow == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-14035':
					$value['description'] = 'Netlex in Cloud - Smart Mailer';
					$value['price'] = $priceList['smart_mailer'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newSmartMailer è differente tra 
							//previous_state e prev_json
							if($prev_json->newSmartMailer == 1 && $previous_state->smart_mailer == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-CCT':
					$value['description'] = 'Netlex in Cloud - Conservazione Sostitutiva';
					$value['price'] = $priceList['cct'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newCct è differente tra 
							//previous_state e prev_json
							if($prev_json->newCct == 1 && $previous_state->cct == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-CASSAZIONE':
					$value['description'] = 'Netlex in Cloud Deposito Cassazione';
					$value['price'] = $priceList['cassazione'] / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando newCassazione è differente tra 
							//previous_state e prev_json
							if($prev_json->newCassazione == 1 && $previous_state->cassazione == 0){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-14037':
					$value['description'] = 'Netlex in Cloud - Editor di Testo';
					break;
				case 'LEGAL_ST_14082':
					$value['description'] = 'Netlex in Cloud - Utenza avvocato (offerta per clienti Easytelematico)';
					$value['price'] = $priceList['easytelematico_cloud_lawyer'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando cloudLawyer->firstLawyer è differente tra 
							//previous_state e prev_json
							if(isset($prev_json->cloudLawyers) && isset($prev_json->cloudLawyers->firstLawyer) && 
								(!isset($previous_state->cloudLawyer) || !isset($previous_state->cloudLawyer->firstLawyer))){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ST_14083':
					$value['description'] = 'Netlex in Cloud - secondo utente avvocato (offerta per clienti Easytelematico)';
					$value['price'] = $priceList['easytelematico_cloud_lawyer_2'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando cloudLawyer->secondLawyer è differente tra 
							//previous_state e prev_json
							if(isset($prev_json->cloudLawyers) && isset($prev_json->cloudLawyers->secondLawyer) && 
								(!isset($previous_state->cloudLawyer) || !isset($previous_state->cloudLawyer->secondLawyer))){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ST_14084':
					$value['description'] = 'Netlex in Cloud - ulteriore utente avvocato (offerta per clienti Easytelematico)';
					$value['price'] = $priceList['easytelematico_cloud_lawyer_agg'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($renew){
						foreach ($previousOrders as $previousOrder) {
							$prev_json = json_decode($previousOrder['order_json']);
							$previous_state = json_decode($previousOrder['previous_state']);
							//l'attivazione si ha quando cloudLawyer->upgLawyer è differente tra 
							//previous_state e prev_json
							if(isset($prev_json->cloudLawyers) && isset($prev_json->cloudLawyers->upgLawyer) && 
								(!isset($previous_state->cloudLawyer) || !isset($previous_state->cloudLawyer->upgLawyer))){
								$value['dataAttivazione'] = $previousOrder['order_date'];
							}
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX-UPGD-DEP':
					$value['description'] = 'Netlex in Cloud - Kit Upgrade';
					$value['price'] = $priceList['kit_upgrade'];
					$value['dataAttivazione'] = $dataAttivazione;
					break;
				case 'LEGAL_ST_AVV_BASIC':
					$value['description'] = 'TeamSystem Studio Legal utente basic';
					$value['price'] = ($priceList['basic_sl_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_UTENTE_BASIC_SL') !== false:
					$value['description'] = 'TeamSystem Studio Legal utente basic';
					$value['price'] = ($priceList['basic_sl_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsBasicUsersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfBasicUsers è uguale tra ordine precedente e ordine attuale e c'è newBasicUsersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if((isset($prev_json->newNumberOfBasicUsers) && isset($order_json->newNumberOfBasicUsers) && isset($prev_json->newBasicUsersToAdd) && 
							$prev_json->newNumberOfBasicUsers == $order_json->newNumberOfBasicUsers && $prev_json->newBasicUsersToAdd > 0) ||
							$previousOrder['is_first'] == 1){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ENT_AVV_BASIC':
					$value['description'] = 'TeamSystem Enterprise Legal utente basic';
					$value['price'] = ($priceList['basic_el_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_UTENTE_BASIC_EL') !== false:
					$value['description'] = 'TeamSystem Enterprise Legal utente basic';
					$value['price'] = ($priceList['basic_el_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsBasicUsersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfBasicUsers è uguale tra ordine precedente e ordine attuale e c'è newBasicUsersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if((isset($prev_json->newNumberOfBasicUsers) && isset($order_json->newNumberOfBasicUsers) && isset($prev_json->newBasicUsersToAdd) && 
							$prev_json->newNumberOfBasicUsers == $order_json->newNumberOfBasicUsers && $prev_json->newBasicUsersToAdd > 0) ||
							$previousOrder['is_first'] == 1){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_AVVOCATO_CLOUD_PDA') !== false:
					$value['description'] = 'Netlex in Cloud pda accesso avvocato';
					$value['price'] = ($priceList['cloud_pda_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsLawyersPrice;
					}
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfLawyers è uguale tra ordine precedente e ordine attuale e c'è newLawyersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						//SE INVECE l'ordine è di un avvocato si confronta con il payment_type di previous_state 
						if((isset($prev_json->newNumberOfLawyers) && isset($order_json->newNumberOfLawyers) &&
							$prev_json->newNumberOfLawyers == $order_json->newNumberOfLawyers && $prev_json->newLawyersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 38){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_AVVOCATO_PDUA') !== false:
					$value['description'] = 'Netlex in Cloud pdua accesso avvocato';
					$value['price'] = ($priceList['pdua_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsLawyersPrice;
					}
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfLawyers è uguale tra ordine precedente e ordine attuale e c'è newLawyersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						//SE INVECE l'ordine è di un avvocato si confronta con il payment_type di previous_state 
						if((isset($prev_json->newNumberOfLawyers) && isset($order_json->newNumberOfLawyers) &&
							$prev_json->newNumberOfLawyers == $order_json->newNumberOfLawyers && $prev_json->newLawyersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 41){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_AVVOCATO_CLOUD') !== false:
					$value['description'] = 'Netlex in Cloud accesso avvocato';
					$value['price'] = ($priceList['cloud_lawyer'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsLawyersPrice;
					}
					$value['years'] = $order_json->year;
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfLawyers è uguale tra ordine precedente e ordine attuale e c'è newLawyersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						//SE INVECE l'ordine è di un avvocato si confronta con il payment_type di previous_state 
						if((isset($prev_json->newNumberOfLawyers) && isset($order_json->newNumberOfLawyers) &&
							$prev_json->newNumberOfLawyers == $order_json->newNumberOfLawyers && $prev_json->newLawyersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 37){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_UTENTE_CLOUD') !== false:
					$value['description'] = 'Netlex in Cloud accesso segretaria/collaboratore';
					$value['price'] = ($priceList['cloud_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsUsersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfUsers è uguale tra ordine precedente e ordine attuale e c'è newUsersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if((isset($prev_json->newNumberOfUsers) && isset($order_json->newNumberOfUsers) && isset($prev_json->newUsersToAdd) &&
							$prev_json->newNumberOfUsers == $order_json->newNumberOfUsers && $prev_json->newUsersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 37){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'NETLEX_PDA_SGR':
					$value['description'] = 'Netlex in Cloud pda accesso segretaria/collaboratore';
					$value['price'] = ($priceList['cloud_pda_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case strpos($code, 'ACCESSO_UTENTE_CLOUD_PDA') !== false:
					$value['description'] = 'Netlex in Cloud pda accesso segretaria/collaboratore';
					$value['price'] = ($priceList['cloud_pda_user'] * 12) / $priceList['price_divider'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['years'] = $order_json->year;
					if($order_json->year > 1){
						$value['pricePromo'] = $order_json->newYearsUsersPrice;
					}
					foreach ($previousOrders as $previousOrder) {
						$prev_json = json_decode($previousOrder['order_json']);
						//se newNumberOfUsers è uguale tra ordine precedente e ordine attuale e c'è newUsersToAdd > 0
						//significa che l'attivazione di quel codice c'è stata in quell'ordine precedente
						if((isset($prev_json->newNumberOfUsers) && isset($order_json->newNumberOfUsers) && isset($prev_json->newUsersToAdd) &&
							$prev_json->newNumberOfUsers == $order_json->newNumberOfUsers && $prev_json->newUsersToAdd > 0) ||
							$previousOrder['is_first'] == 1 && $prev_json->subscriptionType == 38){
							$value['dataAttivazione'] = $previousOrder['order_date'];
						}
					}
					$value['dataRinnovo'] = $dataRinnovo;
					break;
				case 'LEGAL_ENT_5':
					$value['description'] = 'TeamSystem Enterprise - 5 Utenti interni o esterni';
					$value['price'] = $priceList['LEGAL_ENT_5'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
					break;
				case 'LEGAL_ENT_10':
					$value['description'] = 'TeamSystem Enterprise - 10 Utenti interni o esterni';
					$value['price'] = $priceList['LEGAL_ENT_10'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
					break;
				case 'LEGAL_ENT_20':
					$value['description'] = 'TeamSystem Enterprise - 20 Utenti interni o esterni';
					$value['price'] = $priceList['LEGAL_ENT_20'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
					break;
				case 'LEGAL_ENT_50':
					$value['description'] = 'TeamSystem Enterprise - 50 Utenti interni o esterni';
					$value['price'] = $priceList['LEGAL_ENT_50'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
					break;
				case 'LEGAL_ENT_80':
					$value['description'] = 'TeamSystem Enterprise - 80 Utenti interni o esterni';
					$value['price'] = $priceList['LEGAL_ENT_80'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
					break;
				case 'LEGAL_ENT_100':
					$value['description'] = 'TeamSystem Enterprise - 100 Utenti interni o esterni';
					$value['price'] = $priceList['LEGAL_ENT_100'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
					break;
				case 'LEGAL_ENT_UT_AGG':
					$value['description'] = 'TeamSystem Enterprise - Postazione di lavoro aggiuntiva';
					$value['price'] = $priceList['LEGAL_ENT_UT_AGG'];
					$value['dataAttivazione'] = $dataAttivazione;
					$value['dataRinnovo'] = $dataRinnovo;
					$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
					break;
				// case 'LEGAL_ENT_PGT':
				// 	$value['description'] = 'TeamSystem Enterprise - 100 Utenti interni o esterni';
				// 	$value['price'] = $priceList['LEGAL_ENT_PGT'];
				// 	$value['dataAttivazione'] = $dataAttivazione;
				// 	$value['dataRinnovo'] = $dataRinnovo;
				// 	$value['descrizioneProcedura'] = 'TeamSystem Enterprise';
				// 	break;
					
			}
		}
		return $codes;
	}
}

