<?php

function array_to_xml_string($data) {
  $xml_data = new SimpleXMLElement();
	array_to_xml($arr, $xml_data);
	return $xml_data->asXml();
}

function array_to_xml($data, &$xml_data) {
    foreach($data as $key => $value) {
        if(is_array($value)) {
            if(!is_numeric($key)){
                $subnode = $xml_data->addChild("$key");
                array_to_xml($value, $subnode);
            }
            else{
                foreach($value as $sub_key => $sub_value)
                {
                  $subnode = $xml_data->addChild($sub_key);
                  array_to_xml($sub_value, $subnode);
                }
            }
        }
        else {
            $xml_data->addChild("$key",htmlspecialchars("$value"));
        }
    }
}

function remove_namespaces($array){
  $newarray = array();
  foreach($array as $key => $value){
    $newkey = str_replace("s:", "", $key);
    $newkey = str_replace("soap:", "", $newkey);
    if(is_array($value)){
      if(count($value)>0)
        $newvalue = remove_namespaces($value);
      else $newvalue = null;
    }
    else $newvalue = $value;
    $newarray[$newkey] = $newvalue;
  }
  return $newarray;
}

function xml_to_array($xml, $options = array()) {
  $defaults = array(
    'namespaceSeparator' => ':',//you may want this to be something other than a colon
    'attributePrefix' => '@',   //to distinguish between attributes and nodes with the same name
    'alwaysArray' => array(),   //array of xml tag names which should always become arrays
    'autoArray' => true,        //only create arrays for tags which appear more than once
    'textContent' => '$',       //key used for the text content of elements
    'autoText' => true,         //skip textContent key if node has no attributes or child nodes
    'keySearch' => false,       //optional search and replace on tag and attribute names
    'keyReplace' => false       //replace values for above search values (as passed to str_replace())
  );
  $options = array_merge($defaults, $options);
  $namespaces = $xml->getDocNamespaces();
  $namespaces[''] = null; //add base (empty) namespace

  //get attributes from all namespaces
  $attributesArray = array();
  foreach ($namespaces as $prefix => $namespace) {
    foreach ($xml->attributes($namespace) as $attributeName => $attribute) {
      //replace characters in attribute name
      if ($options['keySearch']) $attributeName =
              str_replace($options['keySearch'], $options['keyReplace'], $attributeName);
      $attributeKey = $options['attributePrefix']
              . ($prefix ? $prefix . $options['namespaceSeparator'] : '')
              . $attributeName;
      $attributesArray[$attributeKey] = (string)$attribute;
    }
  }

  //get child nodes from all namespaces
  $tagsArray = array();
  foreach ($namespaces as $prefix => $namespace) {
    foreach ($xml->children($namespace) as $childXml) {
      //recurse into child nodes
      $childArray = xml_to_array($childXml, $options);
      list($childTagName, $childProperties) = each($childArray);

      //replace characters in tag name
      if ($options['keySearch']) $childTagName =
              str_replace($options['keySearch'], $options['keyReplace'], $childTagName);
      //add namespace prefix, if any
      if ($prefix) $childTagName = $prefix . $options['namespaceSeparator'] . $childTagName;

      if (!isset($tagsArray[$childTagName])) {
          //only entry with this key
          //test if tags of this type should always be arrays, no matter the element count
          $tagsArray[$childTagName] =
                  in_array($childTagName, $options['alwaysArray']) || !$options['autoArray']
                  ? array($childProperties) : $childProperties;
      } elseif (
          is_array($tagsArray[$childTagName]) && array_keys($tagsArray[$childTagName])
          === range(0, count($tagsArray[$childTagName]) - 1)
      ) {
          //key already exists and is integer indexed array
          $tagsArray[$childTagName][] = $childProperties;
      } else {
          //key exists so convert to integer indexed array with previous value in position 0
          $tagsArray[$childTagName] = array($tagsArray[$childTagName], $childProperties);
      }
    }
  }

  //get text content of node
  $textContentArray = array();
  $plainText = trim((string)$xml);
  if ($plainText !== '') $textContentArray[$options['textContent']] = $plainText;

  //stick it all together
  $propertiesArray = !$options['autoText'] || $attributesArray || $tagsArray || ($plainText === '')
          ? array_merge($attributesArray, $tagsArray, $textContentArray) : $plainText;

  //return node as array
  return array(
    $xml->getName() => $propertiesArray
  );
}

?>