<?php

require_once( __DIR__ . '/../commcrypt/Commcrypt.php');

class Database_ConnectionsHandler
{

	private $sitekey;
	private $config;
	private $default_adapter = 'Mysql';
	private $default_adapter_namespace = 'DexmaZend_Db_Adapter';

	public function setSitekey( string $sitekey )
	{
		$this->sitekey = $sitekey;
	}

	public function get_connection_params() : array
	{

		return $this->connectionParams;

	}

	public function set_connection_params( array $connectionParams )
	{

		$this->connectionParams = $connectionParams;

	}

	public function __construct( string $sitekey, $config )
	{

		$this->sitekey = $sitekey;
		$this->config = $config;

	}

	public function get_default_connection(): Zend_Db_Adapter_Pdo_Mysql
	{

		$adapter = $this->config->adapter ?? $this->default_adapter;

		return Zend_Db::factory( $adapter, [
			'adapterNamespace' => $this->default_adapter_namespace,
			'driver_options' => [ PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8, lc_time_names = it_IT' ],
			'profiler' => 'My_Db_Profiler_Log',
			'host' => $this->config->database->hostname ?? '',
			'username' => $this->config->database->username ?? '',
			'password' => $this->config->database->password ?? '',
			'dbname' => $this->sitekey,
		] );

	}

	public function get_customer_db_connection( array $cryptedCredentials ): Zend_Db_Adapter_Pdo_Mysql
	{

		$credentials = $this->decryptCredentials( [
			'username' => $cryptedCredentials['db_user'],
			'password' => $cryptedCredentials['db_password'],
		] );
		$connection = $this->get_default_connection();
		$connectionConfig = $connection->getConfig();
		$connectionConfig['adapterNamespace'] =  $this->default_adapter_namespace;
		$connectionConfig['username'] = $credentials['username'];
		$connectionConfig['password'] = $credentials['password'];

		return Zend_Db::factory( $this->default_adapter, $connectionConfig );

	}

	public function get_connection_by_config( Zend_Config $config ): Zend_Db_Adapter_Pdo_Mysql
	{

		return Zend_Db::factory( $config );

	}

	public function decryptCredentials( array $credentials ): array
	{

		$cryptClass = new Commcrypt();
		$dbCredentialsKey = $this->config->database->credentials_key;

		/*** bypass decrypt and use global user ***/
//		return [
//			'username' => $this->config->database->username,
//			'password' => $this->config->database->password,
//		];

		return [
			'username' => $cryptClass->comm_decrypt_password( $credentials['username'], $dbCredentialsKey, $this->sitekey ),
			'password' => $cryptClass->comm_decrypt_password( $credentials['password'], $dbCredentialsKey, $this->sitekey ),
		];

	}

	public function cryptCredentials( array $credentials ): array
	{

		$cryptClass = new Commcrypt();
		$dbCredentialsKey = $this->config->database->credentials_key;

		return [
			'username' => $cryptClass->comm_crypt_password( $credentials['username'], $dbCredentialsKey, $this->sitekey ),
			'password' => $cryptClass->comm_crypt_password( $credentials['password'], $dbCredentialsKey, $this->sitekey ),
		];

	}

}