<?php
define("SITE_CUSTOMER", dirname(dirname(__FILE__)));
require(SITE_CUSTOMER . '/SpreadsheetReader.php');


class IllimityBankSpa
{
    private $azure;
    protected $db;
    protected $applogger;
    protected $manager;
    protected $config;
    protected $logMail = '<EMAIL>';

    //MAPPATURE HEADERS PER ARRAY ASSOCIATIVO
    private $headersProcedure = [
        'ABI',
        'NDG',
        'codiceproc',
        'id_delibera',
        'Tipo',
        'Competenza',
        'Tribunale',
        'Capitale',
        'Gestore',
        'Legale',
        'NoteAvvio'
    ];

    private $headersAnagrafiche = [
        'ABI',
        'codiceproc',
        'RUOLO',
        'NDG',
        'NDG_COLLEGATO',
        'Nome',
        'Cognome',
        'Intestazione',
        'Tipo',
        'DataCarico',
        'CodiceFiscale',
        'PartitaIva'
    ];

    private $estensione = '.csv';

    const PROCEDURE = 'ELP_PROCEDURE';
    const ANAGRAFICHE = 'ELP_ANAGRAFICHE';

    //conta le pratiche importate per l'applogger
    private $counter_practice = 0;


    public function __construct($db, $applogger, $config )
    {
        $this->db = $db;
        $this->applogger = $applogger;
        $this->config = $config;

        $this->azure = $this->setAzure();

        require_once(dirname(__FILE__) . '/ImportManager.php');
        $this->manager = new ImportManager($db,$applogger);

    }


    //funzione che esegue la creazione della pratica partendo dal file "FLUSSO PRATICHE"
    function execute()
    {
        $this->applogger->info('<--Inizio Import-->');

        $extention = $this->estensione;
        $name = self::PROCEDURE;
        $type = explode('_',$name);
        $headersType = $type[1];
        $todayDate = date('Ymd');
        //$name = $name . '_' . $todayDate . $extention;
        $name = $name . '_20220131' . $extention;
        $file = $this->openFile($name, $headersType);

        /* Per import definitivo commentare da $filename fino a segnale di chiusura e togliere il commento al blocco sopra */
        /*$fileName = $post["filePath"];
        $Reader = new SpreadsheetReader($fileName);
        //$counter_row = 0;

        if(!empty($Reader)){
            $this->applogger->info('(1) - Aperto il file delle PRATICHE');
        }else{
            $this->applogger->info('(1) - Errore durante apertura file PRATICHE');
        }



        foreach ($Reader as $key => $row ) {
            // per limitare l'import ad un numero di righe, da tenere a mente che la prima riga sono gli headers e non una pratrica
            //$counter_row ++;
            //if ($counter_row >= 7) continue;

            //pulisco le stringhe dai caratteri speciali ( utile soprattutto se csv esportato da excel)
            foreach ($row as &$col){
                $col= preg_replace('/\xc2\xa0/', ' ', $col);
                $col = preg_replace('/[\xC2\xA0]/', ' ', $col);
                $col = trim($col);
            }

            //CREAZIONE PRATICA
            if($key == 0){// creazione headers
                $headers = $row;
            }else{
                if($headers){
                    $fields= array_combine($headers, $row);// crea array associativo
                }else{
                    $fields= $row;
                }
                $this->applogger->info('(2) ---------------INIZIO CREAZIONE PRATICA N. '.$key.'---------------');
                $this->setPractice($fields);
            }
        }*/
        /* chiusura commento per import definitivo */

        if(!empty($file)){
            $this->applogger->info('(2) ---------------INIZIO CREAZIONE PRATICA N. '.$key.'---------------');
            foreach($file as $pratica){
                $this->setPractice($pratica);
            }

        }else{
            $this->applogger->info('(2) ---------------File Pratica N. '.$key.' non trovato o vuoto---------------');
        }
    }



    //CREA LA PRATICA
    public function setPractice($fields){
        try {
            // CREAZIONE CAMPO DINAMICO
            $idCampoDinamico = $this->manager->getCampoDinamico('Id Delibera');
            if (empty($idCampoDinamico)) {
                $this->manager->createCampoDinamico('Id Delibera');
                $idCampoDinamico = $this->manager->getCampoDinamico('Id Delibera');
                if (!empty($idCampoDinamico)) {
                    $this->applogger->info('(3) - Creato Campo Dinamico');
                } else {
                    $this->applogger->info('(3) - ERRORE creazione Campo Dinamico');
                }
            } else {
                $this->applogger->info('(3) - Creato Campo Dinamico');
            }
            // END

            //Create Categoria per Campo dinamico
            $this->manager->createCategoria('Import Pratiche', array($idCampoDinamico));


            // CREAZIONE-AGGIORNAMENTO PRATICA
            if ($fields) {
                //Prepara il File con dati anagrafici della pratica
                $fileAnagrafica = $this->getAnagrafeData($fields['codiceproc']);
                if (count($fileAnagrafica) >= 0) {
                    $this->applogger->info('(4) - Caricato file ANAGRAFICHE');
                } else {
                    $this->applogger->info('(4) - ERRORE caricamento file ANAGRAFICHE');
                }
                //END


                //Recupero Responsabile e Gestore della pratica tramite il codice esterno
                $idResponsabile = $this->db->fetchOne('SELECT id FROM utente WHERE external_sw_id=?', $fields['Legale']);
                if (!empty($idResponsabile)) {
                    $this->applogger->info('(6) - Recuperato RESPONSABILE');
                } else {
                    $this->applogger->info('(6) - ERRORE recupero file RESPONSABILE');
                }

                $idGestore = $this->db->fetchOne('SELECT id FROM anagrafiche WHERE external_sw_id=?', $fields['Gestore']);
                if (empty($idGestore)) {
                    $idGestore = $this->getGestore($fields['Gestore']);
                    if (!empty($idGestore)) {
                        $this->applogger->info('(5) - Recuperato GESTORE');
                    } else {
                        $this->applogger->info('(5) - ERRORE recupero GESTORE');
                    }
                } else {
                    $this->applogger->info('(5) - Recuperato GESTORE');
                }


                /*$idCoiResponsabile= $this->db->fetchOne('SELECT id FROM utente WHERE external_sw_id=?', $fields['Legale']);
                if(!empty($idCoiResponsabile)){
                    $this->applogger->info('(5) - Recuperato COINTESTARIO');
                }else{
                    $this->applogger->info('(5) - ERRORE recupero COINTESTARIO');
                }*/
                //END


                //Cerca - Crea id pratica
                $practice['avvocato'] = $idResponsabile;
                $practice['valore'] = $fields['Capitale'];
                $idPractice = $this->db->fetchOne('SELECT id FROM archivio WHERE codicearchivio =?', $fields['codiceproc']);
                if (empty($idPractice)) {
                    $idPractice = $this->manager->createPratica($practice);
                    if (!empty($idPractice)) {
                        $this->applogger->info('(7) - Creata PRATICA');
                    } else {
                        $this->applogger->info('(7) - ERRORE creazione PRATICA');
                    }
                } else {
                    $existAvvocato = $this->db->fetchOne('SELECT avvocato FROM archivio WHERE codicearchivio =?', $fields['codiceproc']);
                    if ($existAvvocato && $practice['avvocato'] !== $existAvvocato) {
                        $this->db->update('archivio', array("avvocato" => $practice['avvocato']), array("id" => $idPractice));
                        $this->applogger->info('(7-1) - aggiornato AVVOCATO');
                    }

                    $existValore = $this->db->fetchOne('SELECT valore FROM archivio WHERE codicearchivio =?', $fields['codiceproc']);
                    if ($existValore && $practice['valore'] !== $existValore) {
                        $this->db->update('archivio', array("valore" => $practice['valore']), array("id" => $idPractice));
                        $this->applogger->info('(7-1) - aggiornato VALORE');
                    }
                    $this->applogger->info('(7) - Recuperata PRATICA');
                }

                //END


                //Creazione Cliente
                $idCliente = $this->db->fetchOne('SELECT id FROM anagrafiche WHERE external_sw_id =?', $fields['ABI']);
                $existCliente = $this->db->fetchOne('SELECT id FROM anagrafica_pratica WHERE file_id=? AND person_id =? AND relazione = 1 AND role_id=?', array($idPractice, $idCliente, 5));
                if (!empty($idPractice) && !empty($idCliente) && empty($existCliente)) {
                    $this->manager->createSoggetto($idPractice, $idCliente, 1, 'CLIENTE');
                    $this->applogger->info('(8) - Creato CLIENTE');
                }
                if ($existCliente) {
                    $this->applogger->info('(8) -  CLIENTE già esistente');
                }
                //END

                //Creazione Soggetti Utenti
                //Responsabile
                $existResponsabile = $this->db->fetchRow('SELECT id, referent_id FROM archivio WHERE id =?', $idPractice);
                if (!empty($idResponsabile) && empty($existResponsabile)) {
                    $this->manager->createUtenteSoggetto($idPractice, $idResponsabile, 'responsabile');
                    $this->applogger->info('(9) -  creato RESPONSABILE');
                } else if (!empty($existResponsabile) && $existResponsabile['referent_id'] !== $idResponsabile) {
                    $this->db->update('utente_pratica', array('person_id' => $idResponsabile), array("file_id=?" => $idPractice, 'relazione=?' => 2));
                    $this->db->update('archivio', array('referent_id'=>$idResponsabile), array('id=?' => $idPractice));
                }
                if ($existResponsabile) {
                    $this->applogger->info('(9) -  RESPONSABILE già esistente');
                }


                $roleGestore = $this->db->fetchRow("SELECT id, nome FROM tabellaruoloprocessuale WHERE nome LIKE ?", 'Gestore');
                if (empty($idRoleGestore)) {
                    $this->db->insert("tabellaruoloprocessuale", array('nome' => 'Gestore', 'uniqueid' => $this->db->getuid()));
                    $roleGestore = $this->db->fetchRow("SELECT id, nome FROM tabellaruoloprocessuale WHERE nome LIKE ?", 'Gestore');
                }

                $existGestore = $this->db->fetchRow('SELECT id, person_id FROM anagrafica_pratica WHERE file_id=? AND relazione =?', array($idPractice, $idGestore, $roleGestore['id']));

                if (!empty($existGestore) && $existGestore['person_id'] !== $idGestore) {
                    $this->db->update('anagrafica_pratica', array('person_id' => $idGestore), array('id=?' => $existGestore));
                } else if (empty($existGestore)) {
                    $this->manager->createSoggetto($idPractice, $idGestore, 4, $roleGestore['nome']);
                }


                //Cointestatario
                /*$existCoi= $this->db->fetchOne('SELECT file_id FROM utente_pratica WHERE file_id =? AND person_id =? AND relazione =?', array($idPractice, $idCoiResponsabile, 1));
                if(!empty($idResponsabile && empty($existCoi))){
                    //$responsabile = array('id' => $idCoiResponsabile, 'tabella'=>'utente');
                    $this->createUtenteSoggetto($idPractice,$idCoiResponsabile,'cointestatario');
                    $this->applogger->info('(10) -  creato COINTESTATARIO');
                }
                if($existCoi){
                    $this->applogger->info('(10) -  COINTESTATARIO già esistente');
                }*/
                //END

                //Creazione - Aggiornamento Soggetti Controparte
                $typeMap = array(
                    'DI' => 'DIM',
                    'PG' => 'PGI',
                    'PF' => 'PFI',
                    'SCO' => 'SOC',
                );

                $this->applogger->info('(11) --- INIZIO CREAZIONE SOGGETTI CONTROPARTE ---');
                foreach ($fileAnagrafica as $index => $line) {
                    $nameRole = $this->setRole($line['RUOLO']);

                    $anagrafeType = null;
                    foreach ($typeMap as $key => $type) {
                        if ($key == $line['Tipo']) {
                            $anagrafeType = $type;
                            break;
                        }
                    }

                    $existAnagrafica = $this->db->fetchOne('SELECT id FROM anagrafiche WHERE codicefiscale LIKE ? OR partitaiva LIKE ?', array($line['CodiceFiscale'], $line['PartitaIva']));

                    if (!empty($existAnagrafica)) {
                        $newAnagrafica = array(
                            'denominazione' => $line['Intestazione'],
                            'tipo' => $anagrafeType,
                        );

                        $idAnagrafica = $this->manager->getAnagrafica($newAnagrafica);

                        if (empty($idAnagrafica)) {
                            $this->applogger->info('ERRORE recupero ANAGRAFICA SOGGETTO ' . ($index + 1));
                        } else {
                            $this->applogger->info('recuperata ANAGRAFICA SOGGETTO n.' . ($index + 1));
                        }

                        if (!empty($line['CodiceFiscale'] && $line['CodiceFiscale'] !== "NULL")) {
                            $codicefiscale = $this->db->fetchOne('SELECT codicefiscale FROM anagrafiche WHERE id =?', $idAnagrafica);
                            if ($codicefiscale !== $line['CodiceFiscale']) {
                                $updateAnagrafica['codicefiscale'] = $line['CodiceFiscale'];
                                $this->db->update('anagrafiche', $updateAnagrafica, array('id=?' => $idAnagrafica));
                                $this->applogger->info('aggiornato CODICE FISCALE SOGGETTO n.' . ($index + 1));
                            }
                            $this->applogger->info('CODICE FISCALE già presente SOGGETTO n.' . ($index + 1));
                        }

                        if (!empty($line['PartitaIva']) && $line['PartitaIva'] !== "NULL") {
                            $partitaiva = $this->db->fetchOne('SELECT partitaiva FROM anagrafiche WHERE id =?', $idAnagrafica);
                            if ($partitaiva !== $line['PartitaIva']) {
                                $updateAnagrafica['partitaiva'] = $line['PartitaIva'];
                                $this->db->update('anagrafiche', $updateAnagrafica, array('id=?' => $idAnagrafica));
                                $this->applogger->info('aggiornata PARTITA IVA SOGGETTO n.' . ($index + 1));
                            }
                            $this->applogger->info('PARTITA IVA già presente SOGGETTO n.' . ($index + 1));
                        }
                    } else {
                        $idAnagrafica = $existAnagrafica;
                    }

                    $id_ruolo = $this->db->fetchOne("SELECT id FROM tabellaruoloprocessuale WHERE nome LIKE ?", $nameRole);
                    $idSoggetto = $this->db->fetchOne('SELECT id FROM anagrafica_pratica WHERE file_id=? AND person_id =? AND relazione =2 AND role_id=?', array($idPractice, $idAnagrafica, $id_ruolo));

                    if (empty($idSoggetto)) {
                        $this->manager->createSoggetto($idPractice, $idAnagrafica, 2, $nameRole);
                        $this->applogger->info('creato SOGGETTO' . ($index + 1));
                    } else {
                        $this->applogger->info('SOGGETTO esistente' . ($index + 1));
                    }

                    $result['data'] = date('Y-m-d H:i:s', strtotime($line['DataCarico']));
                }
                //END

                $result['annotazioni'] = $fields['NoteAvvio'];
                $result['valori_dinamici'] = json_encode(
                    array(
                        array("valore" => $fields['id_delibera'], "id" => $idCampoDinamico)
                    ));

                $result['oggetto'] = $this->setTypePractice($fields['Tipo']);
                $result['codicearchivio'] = $fields['codiceproc'];
                $result['ufficio_giudiziario'] = $this->setAutorita($fields);

                $this->db->update('archivio', $result, array('id=?' => $idPractice));
                $this->applogger->info('(15) - PRATICA CONCLUSA');
                $this->counter_practice++;
                $this->applogger->info('-------------' . $this->counter_practice . ' PRATICHE COMPLETATE-------------');

            }// END CREAZIONE-AGGIORNAMENTO PRATICA
        }catch(Exception $e){
            $this->applogger->info('ERRORE IMPORT ' . print_r($e));
            $this->sendMail($this->config, $this->logMail,$e);
        }
    }

   public function sendMail($config, $to, $message) {
        $transport = new Zend_Mail_Transport_Smtp(
            $config->amazonSender->hostname,
            array(
                "auth" => "login",
                "username" => $config->amazonSender->username,
                "password" => $config->amazonSender->password,
                "ssl" => "tls",
                "port" => 587
            )
        );
        $mail = new Zend_Mail("UTF-8");
        $mail->addTo($to);
        $mail->setFrom("<EMAIL>", "Netlex");
        $mail->setSubject('ERRORE IMPORT ILLIMITY');
        $mail->setBodyHtml($message);
        $mail->send($transport);
    }

    //Inserimento dei Ruoli del Cliente
    public function setRole($role){
        $nameRole= null;
        $file= $this->openFile('ruoli.csv');

        if(!empty($file)) {
            foreach ($file as $row){
                if($role && $row['Codice'] ==  $role){
                    $row['Descrizione'] = ucfirst(strtolower($row['Descrizione']));
                    $nameRole = $this->db->fetchOne("SELECT nome FROM tabellaruoloprocessuale WHERE nome LIKE ?",$row['Descrizione']);
                    if(empty($nameRole)){
                        $this->db->insert("tabellaruoloprocessuale", array('nome' => ucfirst($row['Descrizione']), 'uniqueid'=> $this->db->getuid()));
                        $nameRole = $this->db->fetchOne("SELECT nome FROM tabellaruoloprocessuale WHERE nome LIKE ?",$row['Descrizione']);
                    }

                }
            }
        }

        return $nameRole;
    }



    //Ricava l'id dell'dell'oggetto andandolo a prendere da una lista di oggetti. verifica l'oggetto della pratica da importare, se non esiste nel db lo crea e restituisce l'id
    public function setTypePractice($tipo){

        $idType = null;
        $file= $this->openFile('codici_procedure.csv');

        if(!empty($file)) {
            foreach ($file as $row){
                if($tipo && $row['Codice'] ==  $tipo){
                    $idType = $this->manager->getOggetto($row['Descrizione']);
                }
            }
        }

        return $idType;
    }

    //Ricava id Autorita per la pratica inserendola se necessario. Confronta campo competenza e tribunale con i dati nel nostro db
    public function setAutorita($fields){
        $idAutorita = null;

        //ricavo la citta
        $citta = $this->dbShared->fetchOne('SELECT citta FROM citta WHERE codice_citta=?',$fields['Tribunale']);

        $file = $this->openFile('competenze.csv');
        if(!empty($file)){
            foreach($file as $item){
                if($item['Codice'] == $fields['Competenza']){
                    $autorita = strtoupper($item['Descrizione']." DI ".$citta);
                    $idAutorita = $this->manager->getAutorita($autorita, $citta);
                    break;
                }
            }
        }

        return $idAutorita;
    }

    public function getGestore($codice){
        //$gestori = $this->openFile('GESTORI');
        $idAnagrafica = null;
        foreach ($gestori as $gestore){
            if($gestore['CODICE EPC'] == $codice){
                $gestore['external_sw_id'] = $gestore['CODICE EPC'];
                unset($gestore['CODICE EPC']);

                $idAnagrafica = $this->manager->getAnagrafica($gestore);
            }
        }
        return $idAnagrafica;
    }

    //Crea Array con i Dati del file "Flusso Anagrafe" inerenti alla pratica
    public function getAnagrafeData($codiceproc)
    {
        $anagrafica = [];
        $codicearchivio= $codiceproc;
        $extention = $this->estensione;
        $name = self::ANAGRAFICHE;
        $type = explode('_',$name);
        $headersType = $type[1];
        $todayDate = date('Ymd');
        $name = $name . '_' . $todayDate . $extention;
        $file = $this->openFile('ELP_ANAGRAFICHE_20220131.csv', $headersType);//usare $name al posto della stringa -> attualmente forzato per test

        if($file && $codiceproc){
            foreach ($file as $item){
                if($item['codiceproc'] === $codicearchivio){
                    array_push($anagrafica, $item);
                }
            }
        }

        return $anagrafica;
    }



    //Apre il file richiesto e Crea Array associativo
    public function openFile($fileName, $type = NULL)
    {
        $blob = $this->azure->getBLOB($fileName );

        file_put_contents("/tmp/import_banca_illimity.csv", $blob->getContentStream());

        $file = '/tmp/import_banca_illimity.csv';

        $Reader = new SpreadsheetReader($file);
        $data = [];

        foreach ($Reader as $key => $row) {
            $row = explode('|', $row[0]);
            //pulisco le stringhe dai caratteri speciali ( utile se csv esportato da excel)
            foreach ($row as &$col) {
                $col = preg_replace('/\xc2\xa0/', ' ', $col);
                $col = preg_replace('/[\xC2\xA0]/', ' ', $col);
                $col = trim($col);
            }

            if(!empty($type))
            {
                switch($type){
                    case 'ANAGRAFICHE':
                        $headers = $this->headersAnagrafiche;
                        break;
                    case 'PROCEDURE':
                        $headers = $this->headersProcedure;
                        unset($row[count($row)-1]);
                        break;
                    default:
                        $headers = null;
                }
            }


            if (!empty($headers)) {
                $fields = array_combine($headers, $row);// crea array associativo
            }else{
                $fields = $row;
            }
            array_push($data,$fields);

        }
        unlink($file);

        return $data;
    }

    //instanzia AzureHandler
    public function setAzure()
    {
        require_once(SITE_CUSTOMER . '/AzureManager/AzureHandler.php');
        $azure = new AzureHandler(null,null,null,null,null,null, null, $this->config);

        return $azure;
    }

} //END CLASS
?>