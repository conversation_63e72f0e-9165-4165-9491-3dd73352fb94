<?php

class ImportManager
{

    protected $post;
    protected $missingUsers = array();
    protected $db;
    protected $applogger;

    protected $anagraficaRelationsMap = [
        'cliente'               => 1,
        'controparte'           => 2,
        'avversario'            => 3,
        'altro'                 => 4,
        'dominus'               => 5,
        'procuratore'           => 6,
        'cointestatario'        => 20,
        'domiciliatario'        => 21,
        'corrispondente'        => 22,
        'cliente_fatturazione'  => 23,
    ];

    protected $utenteRelationsMap = [
        'cointestatario'            => 1,
        'responsabile'              => 2,
        'collaboratore'             => 3,
        'cointestatario praticante' => 4,
        'utente interno'            => 30,
    ];



//
//
//                                                   █████████     ███████    ███████████   ██████████
//                                                  ███░░░░░███  ███░░░░░███ ░░███░░░░░███ ░░███░░░░░█
//                                                 ███     ░░░  ███     ░░███ ░███    ░███  ░███  █ ░
//                                                ░███         ░███      ░███ ░██████████   ░██████
//                                                ░███         ░███      ░███ ░███░░░░░███  ░███░░█
//                                                ░░███     ███░░███     ███  ░███    ░███  ░███ ░   █
//                                                 ░░█████████  ░░░███████░   █████   █████ ██████████
//                                                   ░░░░░░░░░     ░░░░░░░    ░░░░░   ░░░░░ ░░░░░░░░░░
//


    public function __construct($db, $applogger )
    {
        $this->db = $db;
        $this->applogger = $applogger;
    }

    //ESTRATTORI

    //ESTRATTORE_DATE
    public function extractData($data){
        if (!empty($data)){
            $extracts = array();
            preg_match('/[0-9]{1,2}[\/\-\.][0-9]{1,2}[\/\-\.][0-9]{2,4}/', $data, $extracts);
            if (!empty($extracts[0])){
                return $extracts[0];
            }
        }
        return NULL;
    }

    //ESTRATTORE_ORE
    public function extractOre($ore){
        if (!empty($ore)){
            $extracts = array();
            preg_match('/[0-9]{1,2}:[0-9]{2,2}/', $ore, $extracts);
            if (!empty($extracts[0])){
                return $extracts[0];
            }
        }
        return NULL;
    }

    //RITORNA ORA FORMATO hh:mm
    public function getPadded0OreMinuti($ora)
    {

        if( empty($ora) ) return '00:00';

        if ( strpos($ora, ':') === false ) $ora += ':00';

        $parts = explode(':', $ora);

        $hh = str_pad($parts[0], 2, '0', STR_PAD_LEFT);
        $mm = str_pad($parts[1], 2, '0', STR_PAD_RIGHT);

        return $hh . ':' . $mm;

    }


    //DATEFORMAT
    public function getData($data, $order = 'dmy', $time = true, $empty = false){

        if (!empty($data)){

            $separator = NULL;

            $len = NULL;

            //separator -
            if (strpos( $data,'-')){
                $separator = '-';

                //fix openfiber REMOVE ME
                //$order = 'mdy';
            }

            //separator /
            elseif (strpos( $data,'/')){
                $separator = '/';
            }

            //separator .
            if (strpos( $data, '.')){
                $separator = '.';
            }


            //len 8
            if (strlen($data)==8){
                $len = 8;
            }
            //len 10
            elseif (strlen($data)==10){
                $len = 10;
            }
            //len ?
            elseif (strlen($data)>5){
                $len = 6;
            }


            if (!empty($separator) && !empty($len)){

                $dataarray = explode($separator, $data);

                if ($order === 'dmy'){
                    $d = 0;
                    $m = 1;
                    $y = 2;
                }elseif ($order === 'mdy'){
                    $d = 1;
                    $m = 0;
                    $y = 2;
                }elseif ($order === 'ymd'){
                    $d = 2;
                    $m = 1;
                    $y = 0;
                }
                elseif ($order === 'ydm'){
                    $d = 1;
                    $m = 2;
                    $y = 0;
                }



                $year = strlen($dataarray[$y]) == 4 ? $dataarray[$y] : '20'.$dataarray[$y];

                $month = strlen($dataarray[$m]) == 2 ? $dataarray[$m] : '0'.$dataarray[$m];

                $day = strlen($dataarray[$d]) == 2 ? $dataarray[$d] : '0'.$dataarray[$d];


                $dateformat = $year."-".$month."-".$day;
            } elseif (empty($separator) && !empty($len)){ //custom format senza separatore, per convenzione i giorni sono a due valori, i mesi a due valori e l'anno a 4 valori
                $d = $m = $y = 0;

                if ($order === 'dmy'){
                    $d = 0;
                    $m = 2;
                    $y = 4;
                }elseif ($order === 'mdy'){
                    $d = 2;
                    $m = 0;
                    $y = 4;
                }elseif ($order === 'ymd'){
                    $d = 6;
                    $m = 4;
                    $y = 0;
                }
                elseif ($order === 'ydm'){
                    $d = 4;
                    $m = 2;
                    $y = 0;
                }



                $year = $data[$y].$data[$y+1].$data[$y+2].$data[$y+3];

                $month = $data[$m].$data[$m+1];

                $day = $data[$d].$data[$d+1];


                $dateformat = $year."-".$month."-".$day;
            }

        }

        if ($empty){
            return empty($dateformat)? NULL : $dateformat;
        }

        if ($time){ //by default ritorna datetime
            return empty($dateformat)? '1970-01-01 00:00:00' : $dateformat. " 00:00:00";
        }

        return empty($dateformat)? '1970-01-01' : $dateformat;
    }


    //AUTORITA
    public function getAutorita($autorita, $citta = "", $default = null){
        $autoritaId = $default;

        if (!empty($autorita)){


            //************** REGEX MATCHING ********************//
            $autorita = preg_replace('/(G|g)\. ?(D|d)(I|i) (P|p)(A|a)(C|c)(E|e)/', 'GIUDICE DI PACE', $autorita);

            $autorita = preg_replace('/^(G|g)(D|d)(P|p)$/', 'GIUDICE DI PACE', $autorita);

            //************** REGEX MATCHING ********************//



            //** Match perfezionati per autorità scritte in modo errato */

            if (strtoupper($autorita) == 'TRIBUNALE' && !empty($citta)){
                $autorita = 'TRIBUNALE DI '.strtoupper($citta);
            }

            elseif (strtoupper($autorita) == 'GIUDICE DI PACE' && !empty($citta)){
                $autorita = 'GIUDICE DI PACE DI '.strtoupper($citta);
            }

            //********************************************************* */




            $autoritaId = $this->db->fetchOne("SELECT id FROM tabellaautorita WHERE nome LIKE ?",strtoupper($autorita));

            if (empty($autoritaId)){

                //CITTA
                if (empty($citta)){ //se è su colonna divisa dell'import altrimenti provo a recuperarla dalla stringa
                    $autorita = strtoupper($autorita);
                    if (strpos($autorita, 'GIUDICE DI PACE') || strpos($autorita, 'CORTE DI CASSAZIONE')){
                        $citta = explode(' DI ' , $autorita )[2]; //hanno più "di" nel nome
                    }else{
                        $citta = explode(' DI ' , $autorita )[1];
                    }

                    if (empty($citta)){
                        return NULL;  //se la città è vuota e l'autorità non è già creata non si può creare
                    }

                }

                $citta = strtoupper($citta);


                $cittaId = $this->db->fetchOne("SELECT id FROM tabellacitta WHERE nome LIKE ?", $citta);

                if (empty($cittaId)){
                    return NULL;  //se la città in db mancherebbe la provincia non si può creare
                }

                $aut = array(
                    'nome' => $autorita,
                    'uniqueid' => $this->db->getUid(),
                    'preferita' => 0,
                    'citta' => $cittaId
                );


                $this->db->insert('tabellaautorita' ,  $aut);
                $autoritaId = $this->db->fetchOne('SELECT id FROM tabellaautorita WHERE uniqueid = ?' ,  $aut['uniqueid']);

            }
        }

        return $autoritaId;
    }


    //ISTRUTTORE
    public function getIstruttore($istruttore, $default = null){
        $istruttoreId = $default;
        if (!empty($istruttore)){
            $istruttoreId = $this->db->fetchOne("SELECT id FROM tabellaistruttori WHERE nome LIKE ?", $istruttore);

            if (empty($istruttoreId)) {
                $this->db->insert("tabellaistruttori", [
                    'immessoil' => date('Y-m-d'),
                    'immessoda' => 1,
                    'nome' => $istruttore,
                    'uniqueid'=> $this->db->getuid()
                ]);
                $istruttoreId = $this->db->fetchOne("SELECT id FROM tabellaistruttori WHERE nome LIKE ?", $istruttore);
            }
        }
        return $istruttoreId;
    }

    //TIPOLOGIA
    public function getTipologia($tipologia, $polisweb=true)
    {
        $tipologiaId = 0;
        if (!empty($tipologia)){
            $tipologiaId = $this->db->fetchOne("SELECT id FROM tabellatipologiapratica WHERE nome LIKE ?", $tipologia);

            if (empty($tipologiaId)){

                if($polisweb){
                    $func = "MAX(CAST(pwid AS SIGNED)) + 1";
                } else {
                    $func = "MIN(CAST(pwid AS SIGNED)) - 1";
                }

                //** FIX: Fix su pwid delle tipologie, evitare duplicati          ** /
                $newPWID = (INT) $this->db->fetchOne("SELECT $func FROM tabellatipologiapratica ");
                //** ************************************************************* /

                $this->db->insert("tabellatipologiapratica", array('nome' => $tipologia, 'pwid'=> $newPWID, 'uniqueid'=> $this->db->getuid()));
                $tipologiaId = $this->db->fetchOne("SELECT id FROM tabellatipologiapratica WHERE nome LIKE ?", $tipologia);
            }
        }
        return $tipologiaId;
    }

    //TIPOSCADENZA
    public function getTipoScadenza($tipologia){
        $tipologiaId = 0;
        if (!empty($tipologia)){
            $tipologiaId = $this->db->fetchOne("SELECT id FROM tabellatiposcadenza WHERE nome LIKE ?", $tipologia);

            if (empty($tipologiaId)){
                $this->db->insert("tabellatiposcadenza", array('nome' => $tipologia, 'tipo'=> 0, 'uniqueid'=> $this->db->getuid()));
                $tipologiaId = $this->db->fetchOne("SELECT id FROM tabellatiposcadenza WHERE nome LIKE ?", $tipologia);
            }
        }
        return $tipologiaId;
    }




    //RGNR
    public function getRgnr($rgnr){
        $value = NULL;
        if (!empty($rgnr)){

            if (empty(explode('/', $rgnr)[0])){
                $value = intval(preg_replace('/[^0-9]+/', '', explode('-', $rgnr)[0]), 10);
            }else{
                $value = intval(preg_replace('/[^0-9]+/', '', explode('/', $rgnr)[0]), 10);
            }
        }
        return empty($value) ? NULL : $value;
    }

    //RGNR ANNO
    public function getRgnrAnno($rgnranno){
        $value = NULL;
        if (!empty($rgnranno)){

            if (empty(explode('/', $rgnranno)[1])){
                $value = intval(preg_replace('/[^0-9]+/', '', explode('-', $rgnranno)[1]), 10);
            }else{
                $value = intval(preg_replace('/[^0-9]+/', '', explode('/', $rgnranno)[1]), 10);
            }

            if (!empty($value) && $value < 1000){
                $value += 2000;
            }

        }
        return empty($value) ? NULL : $value;
    }

    //OGGETTO
    public function getOggetto($oggetto, $default = null){
        $oggettoId = $default;

        if (!empty($oggetto)){


            $oggettoId = $this->db->fetchOne("SELECT id FROM tabellaoggetto WHERE nome LIKE ?", $oggetto);

            if (empty($oggettoId)){
                $this->db->insert("tabellaoggetto", array('nome' => $oggetto, 'uniqueid'=> $this->db->getuid()));
                $oggettoId = $this->db->fetchOne("SELECT id FROM tabellaoggetto WHERE nome LIKE ?", $oggetto);
            }


        }

        return $oggettoId;
    }


    //ATTIVO/PASSIVO
    public function getAttivoPassivo($attivopassivo){
        if (!empty($attivopassivo)){
            if (strpos($attivopassivo,'passivo') !== false || strpos($attivopassivo,'P') !== false){
                return 0;
            }else{
                return 1;
            }
        }else{
            return 0;
        }
    }


    //VALORE
    public function getValore($valore){
        if (!empty($valore)){

            $valore = explode(',',$valore);

            if (empty($valore[1])){
                $valore = explode('.',$valore[0]);
            }

            $valore = filter_var( $valore[0] , FILTER_SANITIZE_NUMBER_FLOAT) . '.' . filter_var( $valore[1] , FILTER_SANITIZE_NUMBER_FLOAT);
            return $valore;
        }else{
            return '0.00';
        }
    }


    //STATO
    public function getStato($stato){
        $statoId = 1;
        if (!empty($stato)){
            $statoId = $this->db->fetchOne("SELECT id FROM tabellastati WHERE nome LIKE ?", $stato);

            if (empty($statoId)){
                $this->db->insert("tabellastati", array('nome' => $stato, 'uniqueid'=> $this->db->getuid()));
                $statoId = $this->db->fetchOne("SELECT id FROM tabellastati WHERE nome LIKE ?", $stato);
            }
        }

        return $statoId;
    }


    //ANAGRAFICA
    public function getAnagrafica($fields){
        if (!empty($fields['denominazione'])){


            $anagraficaId = $this->db->fetchOne('SELECT id FROM anagrafiche WHERE denominazione LIKE ?', $fields['denominazione'] );

            if (empty($anagraficaId)){
                $anagrafica = array(
                    'tipo' => empty($fields['tipo']) ? 54 : $fields['tipo'],
                    'denominazione' => $fields['denominazione'],
                    'uniqueid' => $this->db->getUid(),
                    'immessoil' => date("Y-m-d H:i:s"),
                );

                if(!empty($fields['nome']) and !empty($fields['cognome'])){
                    $anagrafica['nome'] = $fields['nome'];
                    $anagrafica['cognome'] = $fields['cognome'];
                }

                if(!empty($fields['codicefiscale'])){
                    $anagrafica['codicefiscale'] = $fields['codicefiscale'];
                }

                if(!empty($fields['partitaiva'])){
                    $anagrafica['partitaiva'] = $fields['partitaiva'];
                }

                if(!empty($fields['telefono']) OR !empty($fields['cellulare']) OR !empty($fields['fax']) OR !empty($fields['email']) OR !empty($fields['pec']) OR !empty($fields['custom'])){
                    $contatti = '{"1":"'.$fields['telefono'].'", "3":"'.$fields['cellulare'].'", "5":"'.$fields['fax'].'", "6":"'.$fields['email'].'", "7":"'.$fields['pec'].'"';

                    if(!empty($fields['custom'])){
                        $contatti .= ', ';
                        foreach ($fields['custom'] as $key => $value){
                            $id = $this->createOrGetNomeCampi($key,2);
                            $contatti .= '"'.$id.'":"'.$value.'"';
                            if(next($fields['custom'])){
                                $contatti .= ', ';
                            }
                        }
                    }

                    $contatti .= '}';
                    $anagrafica['contatti'] = $contatti;
                }

                if(!empty($fields['via']) || !empty($fields['cap']) || !empty($fields['citta'])){
                    $citta = $this->createOrGetCitta($fields['citta'], $fields['provincia'] ?? null);
                    $citta = !empty($citta) ? $citta : -1;
                    $anagrafica['indirizzi'] = '{"9":{"via":"'.$fields['via'].'", "cap":"'.$fields['cap'].'", "citta":"'.$citta.'", "nazione":"118"}}';

                }

                if(!empty($fields['external_sw_id'])){
                    $anagrafica['external_sw_id'] = $fields['external_sw_id'];
                }

                $this->db->insert('anagrafiche', $anagrafica);
                $anagraficaId = $this->db->fetchOne('SELECT id FROM anagrafiche WHERE uniqueid = ?', $anagrafica['uniqueid']);
            }

        }else{
            $anagraficaId = NULL;
        }

        return $anagraficaId;

    }


    /**
     * @param $name
     * @param $provincia
     * @param int $nazione_id
     * @return int|string
     */
    public function createOrGetCitta($name, $provincia, $nazione_id = 118){
        $campoId = $this->db->fetchOne("SELECT id FROM tabellacitta WHERE nome LIKE ?", $name);
        if (empty($campoId) && $provincia !== null){
            $campoId = $this->db->insert("tabellacitta", array('nome' => $name, 'provincia'=> $provincia, 'nazione_id' => $nazione_id, 'uniqueid' => $this->db->getUid()));
        }
        return $campoId;
    }

    /**
     * @param $name
     * @param $relation
     */
    public function createOrGetNomeCampi($name, $relation){
        $campoId = $this->db->fetchOne("SELECT id FROM nome_campi WHERE nome LIKE ? and visible = 1", $name);
        if (empty($campoId)){
            $campoId = $this->db->insert("nome_campi", array('nome' => $name, 'relazione'=> $relation));
        }
        return $campoId;
    }


    public function createUser($user, $segretaria = NULL){

        if(!empty($user))
        {
            $newUserData = $user;
            $tabellaAvvocati= array();

            $tabellaAvvocati['uniqueid'] = $this->db->getUid();

            if((isset($newUserData['nome']) && !empty($newUserData['nome'])) && (isset($newUserData['cognome']) && !empty($newUserData['cognome'])))
            {
                $tabellaAvvocati['nome'] = ucwords(strtolower($newUserData['nome'])) . " " . ucwords(strtolower($newUserData['cognome']));
            }

            if(isset($newUserData['Luogo di nascita']) && !empty($newUserData['Luogo di nascita'])){
                $tabellaAvvocati['natoa'] = $newUserData['Luogo di nascita'];
            }

            if(isset($newUserData['Data di nascita']) && !empty($newUserData['Data di nascita']))
            {
                $tabellaAvvocati['natoil'] = $newUserData['Data di nascita'];
            }

            $this->db->insert('tabellaavvocati', $tabellaAvvocati);

            $idTabellaAvvocati = $this->db->fetchOne('SELECT id FROM tabellaavvocati ORDER BY id DESC');

            if( !empty($idTabellaAvvocati)){
                $utente = array();

                // il , ['nome'] = username netlex, creato sarà sempre iniziale del nome . cognome: es. Renato Rossi genererà r.rossi
                if((isset($newUserData['nome']) && !empty($newUserData['nome'])) && (isset($newUserData['cognome']) && !empty($newUserData['cognome']))){
                    $utente['nome'] = substr(strtolower($newUserData['nome']), 0) . '.' . strtolower(str_replace(" ", "",$newUserData['cognome']));
                    $utente['nomeutente'] = ucwords(strtolower($newUserData['nome'])) . " " . ucwords(strtolower($newUserData['cognome']));
                    $utente['nomepersonale'] = ucwords(strtolower($newUserData['nome']));
                    $utente['cognomepersonale'] = ucwords(strtolower($newUserData['cognome']));
                }

                if(isset($newUserData['Luogo di nascita']) && !empty($newUserData['Luogo di nascita'])){
                    $utente['natoa'] = $newUserData['Luogo di nascita'];
                }

                if(isset($newUserData['Data di nascita']) && !empty($newUserData['Data di nascita']))
                {
                    $utente['natoil'] = $newUserData['Data di nascita'];
                }

                if(isset($newUserData['email']) && !empty($newUserData['email']))
                {
                    $utente['Email'] = $newUserData['email'];
                }

                $utente['codiceavvocato'] = $idTabellaAvvocati;

                if(empty($segretaria)){
                    $utente['tipoutente'] = 1;
                }else{
                    $utente['tipoutente'] = 0;
                }

                $utente['attivo'] = 1;
                $utente['external_can_upload'] = 0;
                $utente['external_upload_notification'] = 0;
                $utente['pa_tipo_ritenuta'] = 0;
                $utente['tipoutentenome'] = 'usersl';
                $utente['min_hourly_rate'] = 0.00;
                $utente['hourly_rate'] = 0.00;
                $utente['max_hourly_rate'] = 0.00;
                $utente['costo_risorsa'] = 0.00;
                $utente['password'] = hash('sha256', $utente['nome']);

                if(isset($newUserData['external_sw_id']) && !empty($newUserData['external_sw_id']))
                {
                    $utente['external_sw_id'] = $newUserData['external_sw_id'];
                }

                $utente['uniqueid'] = $this->db->getUid();

            }

        }

    }

    //INTESTATARIO
    public function getIntestatario($intestatario, $default = null){
        $codiceId = $default;

        if (!empty($intestatario)){

            $codiceId = $this->db->fetchOne("SELECT codiceavvocato FROM utente  WHERE nomeutente LIKE ? OR nome LIKE ?", array($intestatario,$intestatario));

        }

        return $codiceId;
    }


    //COINTESTATARIO
    public function getCointestatario($cointestatario) {
        $codiceId = array("id"=>NULL, "tabella"=>'utente');

        if (!empty($cointestatario)){

            $codiceId['id']  = $this->db->fetchOne("SELECT id FROM utente  WHERE ( nomeutente LIKE ? OR nome LIKE ? ) AND tipoutente = 1", array($cointestatario,$cointestatario));

            if (empty($codiceId['id'])){
                $codiceId['tabella']    = 'anagrafiche';
                $codiceId['id']         = $this->getAnagrafica(array('denominazione'=>$cointestatario));
            }
        }

        return $codiceId;
    }


    //RESPONSABILE
    public function getResponsabile($responsabile, $default = null){
        $codiceId = $default;

        if (!empty($responsabile)){

            $codiceId = $this->db->fetchOne("SELECT id FROM utente  WHERE nomeutente LIKE ? OR nome LIKE ?", array('%'.$responsabile.'%',$responsabile));

        }

        if (empty($codiceId)){
            if (!in_array(  $responsabile, $this->missingUsers) ){
                array_push($this->missingUsers, $responsabile);
            }
            $this->applogger->info("EMPTY: $responsabile");
        }
        return $codiceId ;
    }


    //COLLABORATORE
    public function getCollaboratore($collaboratore, $default = null){
        $codiceId = $default;

        if (!empty($collaboratore)){

            $codiceId = $this->db->fetchOne("SELECT id FROM utente  WHERE nomeutente LIKE ? OR nome LIKE ?", array($collaboratore,$collaboratore));

        }

        return $codiceId;
    }

    //GRUPPI
    public function getGruppo($gruppo){
        $codiceId = 1;

        if (!empty($gruppo)){

            $codiceId = $this->db->fetchOne("SELECT id FROM gruppo  WHERE name LIKE ?", array($gruppo));

        }

        return $codiceId;
    }


    //CATEGORIA
    public function getCategoria($categoria){
        $categoriaId = 0;

        if (!empty($categoria)) {

            $categoriaId = $this->db->fetchOne("SELECT id FROM categorie WHERE nome LIKE ?", $categoria);

        }

        return $categoriaId;
    }



    //CAMPO DINAMICO
    public function getCampoDinamico($campo){
        $campoId = 0;

        if (!empty($campo)){
            $campoId = $this->db->fetchOne("SELECT id FROM campi_dinamici WHERE nome LIKE ?", $campo);
        }

        return $campoId ;

    }




    //CREATE CAMPO
    public function createCampoDinamico($name, $type = "regex" , $opt = NULL){
        if (!empty($name)){


            $campoId = $this->db->fetchOne("SELECT id FROM campi_dinamici WHERE nome LIKE ?", $name);

            if (empty($campoId)){
                $this->db->insert("campi_dinamici", array('nome' => $name, 'tipo'=> $type, 'opt' => $opt));
            }
        }
    }



    //CREATE CATEGORIA nome e array con id dei campi dinamici
    public function createCategoria($name, $campiArray, $inPratica = 1, $inAnagrafica = 1){
        if (!empty($name)){

            $categoriaId = $this->db->fetchOne("SELECT id FROM categorie WHERE nome LIKE ?", $name);

            if (empty($categoriaId)){
                $this->db->insert("categorie", array('nome' => $name, 'campi' => json_encode($campiArray) ,'pratica'=> $inPratica, 'anagrafica' => $inAnagrafica));
                $categoriaId = $this->db->fetchOne("SELECT id FROM categorie WHERE nome LIKE ?", $name);
            }


            foreach ($campiArray as $campo){
                $exist = $this->db->fetchOne('SELECT id_categoria FROM campi_dinamici_categorie WHERE id_categoria = ? AND id_campo_dinamico = ? ', array($categoriaId,$campo));

                if (empty($exist)){
                    $this->db->insert("campi_dinamici_categorie", array("id_campo_dinamico" => $campo, "id_categoria"=>$categoriaId));

                    $checkCampi= json_decode($this->db->fetchOne("SELECT campi FROM categorie WHERE id = ?", $categoriaId),1);

                    if (!in_array($campo,$checkCampi)){ // aggiornamento campi dentro la categoria se mancano
                        $checkCampi[] = $campo;

                        $this->db->update("categorie", array("campi"=>json_encode($checkCampi)), array("id = ?" => $categoriaId));
                    }

                }
            }
        }
    }


    //CREATE SEZIONALE
    public function createSezionale($name)
    {
        if (!empty($name)) {
            $json_identificatori = $this->db->fetchOne('SELECT identificatori_pratica FROM settings WHERE id = 1');

            $identificatori = json_decode($json_identificatori, 1);
            if (empty($identificatori)) {
                $identificatori = array();
            }

            $already_in = false;

            foreach ($identificatori as $identificatore) {
                if ($identificatore == $name) {
                    $already_in = true;
                }
            }


            if (!$already_in) {
                array_push($identificatori, $name);
                $json_identificatori = json_encode($identificatori);
                $this->db->update('settings', array('identificatori_pratica' => $json_identificatori), array('id = ?' => 1));
            }
        }
    }


    //VALUTAZIONE DEL RISCHIO
    public function createValoreRischio($pratica, $name, $perc , $valore , $motivo){

        if (!empty($name) && $name != 'Da valutare'){

            $valutazione = array();
            $this->applogger->info($name);
            if ($name == 'Possibile' || $name == 'possibile'){
                $valutazione['probabilita'] = 1;
            }elseif ($name == 'Probabile' || $name == 'probabile'){
                $valutazione['probabilita'] = 2;
            }elseif ($name == 'Remoto' || $name == 'remoto'){
                $valutazione['probabilita'] = 3;
            }else{
                return NULL;
            }
            $this->applogger->info($valutazione['probabilita']);

            $valutazione['data'] = date("Y-m-d H:i:s");;

            $valutazione['uniqueid'] = $this->db->getUid();


            $valutazione['pratica_id'] = $pratica;

            $valutazione['valore'] = $valore;

            $valutazione['attivo'] = 1;

            $valutazione['stima'] = $perc * 100;//intval(preg_replace('/[^0-9]+/', '', $perc), 10);

            $valutazione['motivo'] = $motivo;


            if (!empty($valutazione['probabilita'])){
                $this->applogger->info(print_r($valutazione,1));
                $this->db->insert('rischi', $valutazione);
            }

        }

    }

    //SOGGETTI
    public function createSoggetto($pratica, $anagrafica, $relazione , $ruolo = NULL){
        if (!empty($pratica) && !empty($anagrafica) && !empty($relazione)){
            $id_ruolo = NULL;

            if (!empty($ruolo)){
                $id_ruolo = $this->db->fetchOne("SELECT id FROM tabellaruoloprocessuale WHERE nome LIKE ?", $ruolo);

                if (empty($id_ruolo)){
                    $this->db->insert('tabellaruoloprocessuale', array(
                        'nome'=>$ruolo,
                        'uniqueid'=>$this->db->getUid()
                    ));
                    $id_ruolo = $this->db->fetchOne("SELECT id FROM tabellaruoloprocessuale WHERE nome LIKE ?", $ruolo);
                }
            }

            $this->db->insert('anagrafica_pratica', array('person_id' => $anagrafica, 'file_id' => $pratica, 'relazione' => $relazione, 'role_id' => $id_ruolo));
        }
    }


    //COINTESTATARIO RESPONSABILE
    public function createUtenteSoggetto($pratica, $utente, $relazione ){
        if (!empty($pratica) && !empty($utente) && !empty($relazione)){

            if ($relazione == 'cointestatario' && !empty($utente['id']) && !empty($utente['tabella'])){

                if ($utente['tabella'] == 'anagrafiche'){

                    $this->createSoggetto($pratica , $utente['id'] , 20 ) ;

                }else{
                    if (!empty($utente['id'])){
                        $utente = $utente['id'];
                    }
                    $intestatario = $this->db->fetchOne('SELECT avvocato FROM archivio WHERE id = ? ', $pratica);

                    $cointestatario = $this->db->fetchOne('SELECT codiceavvocato FROM utente WHERE id = ? AND tipoutente = 1', $utente);

                    if (!empty($cointestatario) && $intestatario != $cointestatario ){
                        $this->db->insert('cointestataripratica', array('file_id'=>$pratica, 'lawyer_id'=>$cointestatario));
                        $this->db->insert('utente_pratica', array('file_id'=>$pratica, 'person_id'=>$utente, 'relazione'=>1));
                    }
                }

            }elseif ($relazione == 'responsabile'){
                $this->db->delete('utente_pratica' , array('relazione=?'=> 2 , 'file_id=?'=> $pratica));

                $this->db->update('archivio', array('referent_id'=>$utente), array('id=?'=>$pratica));
                $this->db->insert('utente_pratica', array('file_id'=>$pratica, 'person_id'=>$utente, 'relazione'=>2));

            }elseif ($relazione == 'collaboratore'){
                $this->db->delete('utente_pratica' , array('relazione=?'=> 3 , 'file_id=?'=> $pratica, 'person_id=?'=> $utente));

                $this->db->insert('utente_pratica', array('file_id'=>$pratica, 'person_id'=>$utente, 'relazione'=>3));
            }

        }
    }

    //GRUPPO

    public function createGruppoSoggetto($pratica, $group_id){
        $file_id = $pratica;
        $is_already_reserved = $this->db->fetchOne("SELECT group_id FROM gruppo_pratica WHERE group_id = ? AND file_id = ?", array($group_id,$file_id));



        $utenti = $this->db->fetchCol("SELECT id_utente FROM utente_gruppo WHERE id_gruppo = ?", $group_id);

        foreach($utenti as $user_id){
            $this->addRiserva($file_id, $user_id);
            // $this->handleOneDrivePermissionsAction($user_id, $this->workflow->file_id, 1, 1,$this->params);
        }

        if (!$is_already_reserved){
            $this->db->insert("gruppo_pratica", array("group_id"=>$group_id, "file_id"=>$file_id));
        }

    }




    //RISERVA

    public function addRiserva($pratica, $user_id){
        $file_id = $pratica;
        $is_already_reserved = $this->db->fetchOne("SELECT id FROM utentipraticariservata WHERE person_id = ? AND file_id = ?", array($user_id,$file_id));

        if (!empty($is_already_reserved)){
            return FALSE;
        }else{
            $intestatario = $this->db->fetchOne("SELECT avvocato FROM archivio WHERE id = ?", array($file_id));
            if (empty($intestatario)){
                return FALSE;
            }

            $intestatario_userRow = $this->db->fetchRow("SELECT id,nomeutente FROM utente WHERE codiceavvocato = ? AND tipoutente = 1", $intestatario);
            $archivio_reserved = $this->db->fetchOne("SELECT riservata FROM archivio WHERE id = ?", $file_id);
            if (empty($archivio_reserved)){
                $this->db->update("archivio", array("riservata"=>1), array("id=?"=>$file_id));
            }
            if ($intestatario_userRow['id'] == $user_id){
                $this->db->insert("utentipraticariservata" , array("person_id"=>$user_id , "file_id"=>$file_id));
            }else{
                $insteatario_is_reserved = $this->db->fetchOne("SELECT id FROM utentipraticariservata WHERE person_id = ? AND file_id = ?", array($intestatario_userRow['id'],$file_id));

                if (empty($insteatario_is_reserved)){
                    $this->db->insert("utentipraticariservata" , array("person_id"=>$intestatario_userRow['id'] , "file_id"=>$file_id));
                    $intestatario_has_relation = $this->db->fetchOne("SELECT person_id FROM utente_pratica WHERE person_id = ? AND file_id = ?", array($intestatario_userRow['id'],$file_id));
                    if (empty($intestatario_has_relation)){
                        $this->db->insert("utente_pratica", array("person_id"=>$intestatario_userRow['id'],"file_id"=>$file_id,"relazione"=>30));
                    }
                }

                $this->db->insert("utentipraticariservata" , array("person_id"=>$user_id , "file_id"=>$file_id));

            }
            return TRUE;
        }

    }



    //PRATICA
    public function createPratica($fields, $bypassDuplicateId = false)
    {

        $fields['immessoil'] 	 	= date("Y-m-d H:i:s");
        $fields['modificatoil']  	= date("Y-m-d H:i:s");
        $fields['uniqueid'] 		= $this->db->getUid();
        $fields['immessoda'] 		= $this->db->fetchOne("SELECT id FROM utente WHERE codiceavvocato = ? AND tipoutente = 1", $fields['avvocato']);
        $fields['modificatoda'] 	= $fields['immessoda'];

        $fields['tipologiapratica'] = empty($fields['tipologiapratica']) ? 1 : $fields['tipologiapratica'];

        $fields['valore'] 			= empty($fields['valore']) ? '0.00' : $fields['valore'];
        $fields['stato'] 			= empty($fields['stato']) ? 8 : $fields['stato'];

        $this->applogger->info(print_r($fields,1));

        try {
            $this->db->insert('archivio', $fields);
        } catch (Exception $e) {

            if ( $bypassDuplicateId && $e->getCode() === 23000 ) {
                return $fields['id'];
            }
            throw $e;

        }

        return $this->db->fetchOne('SELECT id FROM archivio WHERE uniqueid = ?', $fields['uniqueid'] );

    }

    //UDIENZA
    public function createUdienza($fields){

        $result = null;

        if (!empty($fields['data'] && !empty($fields['autorita']) && $fields['data'] != '1970-01-01 00:00:00')){

            $today_time = strtotime(date("Y-m-d H:i:s"));
            $expire_time = strtotime($fields['data']);

            if ($expire_time < $today_time) {
                $fields['stato_evasa'] = 1;
            }

            $agenda = array(
                'pratica' => $fields['pratica'],
                'data' => $fields['data'],
                'ora' => empty($fields['ora']) ? '00:00' : $fields['ora'],
                'immessoil' => date("Y-m-d H:i:s"),
                'modificatoil' => date("Y-m-d H:i:s"),
                'immessoda' => 1,
                'modificatoda' => 1,
                'stato_evasa' => empty($fields['stato_evasa']) ? 0 : $fields['stato_evasa'],
                'referente' => empty($fields['referente']) ? 1 : $fields['referente'],
                'citta' => $this->db->fetchOne('SELECT citta FROM tabellaautorita WHERE id = ?', $fields['autorita'] ),
                'autorita' => $fields['autorita'],
                'istruttore' => empty($fields['istruttore']) ? NULL : $fields['istruttore'],
                'attivita' => empty($fields['attivita']) ? 'UDIENZA' : $fields['attivita'] ,
                'uniqueid' => $this->db->getUid(),
            );

            // $this->applogger->info(print_r($agenda,1));

            $this->db->insert('agenda' ,  $agenda);

            return $this->db->fetchOne('SELECT id FROM agenda WHERE uniqueid=?', $agenda['uniqueid']);

        }

    }


    //IMPEGNO
    public function createImpegno($fields, $intestatari = NULL){

        $result = null;

        if (!empty($fields) && !empty($fields['data']) && $fields['data'] != '1970-01-01 00:00:00'){

            $today_time = strtotime(date("Y-m-d H:i:s"));
            $expire_time = strtotime($fields['data']);

            if ($expire_time < $today_time) {
                $fields['evasa'] = 1;
            }

            $impegnoUid = $this->db->getUid();
            $intestatario = empty($fields['intestatario']) ? 1 : $fields['intestatario'];
            $impegno = array(
                'testo' => empty($fields['testo']) ? 'impegno importato' : $fields['testo'] ,
                'data' 	=> $fields['data'],
                'dataavviso' 	=> empty($fields['dataavviso']) ? NULL : $fields['dataavviso'],
                'tiposcadenza' => empty($fields['tiposcadenza'])? 1 : $fields['tiposcadenza'] ,
                'evasa' => empty($fields['evasa']) ? 0 : $fields['evasa'],
                'ora' => empty($fields['ora'])? '09:00' : $fields['ora'] ,
                'immessoil' => empty($fields['immessoil']) ? date("Y-m-d H:i:s") : $fields['immessoil'],
                'modificatoil' => date("Y-m-d H:i:s"),
                'immessoda' => empty($fields['immessoda']) ? $intestatario : $fields['immessoda'],
                'modificatoda' => empty($fields['immessoda']) ? $intestatario : $fields['immessoda'],
                'annotazioni' => empty($fields['annotazioni']) ? '' : $fields['annotazioni'] ,
                'pratica' => $fields['pratica'],
                'linkuid' => $this->db->getUid(),
                'uniqueid' => $impegnoUid
            );

            $this->db->insert('scadenzario', $impegno);

            $result = $this->db->fetchOne("SELECT id FROM scadenzario WHERE uniqueid = ?", $impegnoUid);

            if (!empty($intestatari)){
                foreach ($intestatari as $utente){
                    $this->db->insert('scadenzarioutente', array('id_utente' => $utente, 'id_scadenza'=> $result));
                }
            }else{
                $this->db->insert('scadenzarioutente', array('id_utente' => $intestatario, 'id_scadenza'=> $result));
            }

            $this->applogger->info(print_r($impegno,1));

        }

        return $result;

    }

    public function trim_columns($row)
    {

        foreach ($row as &$col) {
            $col = trim($col);
        }

        return $row;

    }

    public function set_empty_to_null($data)
    {

        foreach ($data as &$col) {
            $col = !empty($col) ? $col : NULL;
        }

        return $data;

    }

    // CREA ARRAY ASSOCIATIVO DA FILE CSV O XLS
    public function arrayFromFile($path, $fileName , $deleteFile = NULL)
    {

        $file = $path . $fileName;

        $Reader = new SpreadsheetReader($file);
        $data = [];

        foreach ($Reader as $key => $row) {

            //pulisco le stringhe dai caratteri speciali ( utile se csv esportato da excel)
            foreach ($row as &$col) {
                $col = preg_replace('/\xc2\xa0/', ' ', $col);
                $col = preg_replace('/[\xC2\xA0]/', ' ', $col);
                $col = trim($col);
            }

            if ($key == 0) {// crezione headers
                $headers = $row;
            } else {
                if ($headers) {
                    $fields = array_combine($headers, $row);// crea array associativo
                }else{
                    $fields = $row;
                }
                array_push($data,$fields);
            }
        }
        if($deleteFile){
            unlink($file);
        }


        return $data;
    }


    function match_format( $headerMap, $expectedColumns )
    {

        $counter            = 0;
        $modelId            = 1;
        $fileName = $_FILES["file"]["tmp_name"];

        if ($_FILES["file"]["size"] > 0 ) {

            rename($fileName, $fileName.$_POST['type']);
            $_FILES["file"]["tmp_name"] = $fileName.$_POST['type'];
            $Reader = new SpreadsheetReader($fileName.$_POST['type']);

            foreach ($Reader as $Row){
                foreach ($Row as $Col){
                    $counter += 1;
                }
                break;
            }

        }

        if ( empty($expectedColumns) || $counter == $expectedColumns ) {
            return [
                "modelId"       => $modelId,
                "header_map"    => $headerMap,
            ];
        }

    }


    private function GiudiziExArtUdienza($post){
        if (isset($post["modello"])) {

            $fileName = $post["filePath"];

            $Reader = new SpreadsheetReader($fileName);


            $now = date("Y-m-d H:i:s");


            //exec('rm -rf ' .  $fileName);
            // exec('rm -rf ' .  $fileName2);



            //$counter

            $counter_row = 0;




            foreach ($Reader as $row){


                $archivio = NULL;
                $udienzaFields = array();



                $counter_row ++;


                if ($counter_row < 6){
                    $this->applogger->info(print_r($row,1));
                    continue;
                }else if ($counter_row > 9){
                    // break;
                }




                $i = 0;
                foreach ($row as &$col){
                    $col = trim($col);
                }




                //=================     INIZIO        ========================//


                $archivio = $this->db->fetchRow("SELECT * FROM archivio WHERE codicearchivio = ? ", $row[1].'/'.$row[0]);


                $udienzaFields['pratica']	 	= $archivio['id'];

                $udienzaFields['referente'] 	= $this->db->fetchOne('SELECT id FROM utente WHERE codiceavvocato = ? AND tipoutente = 1', $archivio['avvocato']);

                $udienzaFields['data'] 			= $this->getData($row[2], 'mdy', true, true);

                $udienzaFields['autorita'] 		= $archivio['ufficio_giudiziario'];



                $this->createUdienza($udienzaFields);

                //=================     FINE        ========================//



            }


        }
    }



    private function IncarichiExArt($post){
        if (isset($post["modello"])) {

            $fileName = $post["filePath"];

            $Reader = new SpreadsheetReader($fileName);


            $now = date("Y-m-d H:i:s");


            //exec('rm -rf ' .  $fileName);
            // exec('rm -rf ' .  $fileName2);



            //$counter

            $counter_row = 0;


            $this->createCampoDinamico('DATA NOTIFICA ATTO', 'date');
            $this->createCampoDinamico('DEL/DET. INCARICO', 'regex');
            $this->createCampoDinamico('STATO PROCEDIMENTO', 'regex');
            $this->createCampoDinamico('DATA ARRIVO SENTENZA', 'date');
            $this->createCampoDinamico('ESITO', 'regex');
            $this->createCampoDinamico('FALD.', 'regex');



            $this->createCategoria('Incarichi Ex Art 615', array(
                $this->getCampoDinamico('DATA NOTIFICA ATTO'),
                $this->getCampoDinamico('DEL/DET. INCARICO'),
                $this->getCampoDinamico('STATO PROCEDIMENTO'),
                $this->getCampoDinamico('DATA ARRIVO SENTENZA'),
                $this->getCampoDinamico('ESITO'),
                $this->getCampoDinamico('FALD.')
            ));




            foreach ($Reader as $row){






                $counter_row ++;


                if ($counter_row < 5){
                    $this->applogger->info(print_r($row,1));
                    continue;
                }else if ($counter_row > 366){
                    break;
                }




                $i = 0;
                foreach ($row as &$col){
                    $col = trim($col);
                }




                //=================     INIZIO        ========================//


                $archivioFields = array();
                $anagraficheFields = array();
                $udienzaFields = array();



                $archivioFields['codicearchivio']  = $row[0].'/'.$row[1];
                $this->createSezionale($row[1]);


                $anagraficheFields['cliente']      = $this->getAnagrafica(array('denominazione' => 'Comune di Pozzuoli'));
                $anagraficheFields['controparte']  = $this->getAnagrafica(array('denominazione' => $row[2]));

                $archivioFields['descrizione'] = $row[3];

                $archivioFields['oggetto'] = $this->getOggetto($row[4]);

                $archivioFields['avvocato'] = $this->getIntestatario("Anna Sannino");

                $dataApertura = '1900-01-01';
                //data inizio
                if(!empty($row[6])){
                    $dataApertura = $this->getData($row[6],'dmy',false, true);
                    //data notifica
                } elseif(!empty($row[5])){
                    $dataApertura = $this->getData($row[5],'dmy',false, true);
                }

                $archivioFields['data'] = $dataApertura;

                $archivioFields['valori_dinamici'] = json_encode(
                    array(
                        array("valore" => $this->getData($row[5],'dmy',false, true), "id" => $this->getCampoDinamico('DATA NOTIFICA ATTO')),
                        array("valore" => $row[10], "id" => $this->getCampoDinamico('DEL/DET. INCARICO')),
                        array("valore" => $row[13], "id" => $this->getCampoDinamico('STATO PROCEDIMENTO')),
                        array("valore" => $this->getData($row[14],'dmy',false, true), "id" => $this->getCampoDinamico('DATA ARRIVO SENTENZA')),
                        array("valore" => $row[15], "id" => $this->getCampoDinamico('ESITO')),
                        array("valore" => $row[16], "id" => $this->getCampoDinamico('FALD.')),
                    )
                );

                $archivioFields['id_categoria'] = $this->getCategoria('Incarichi Ex Art 615');

                $archivioFields['ufficio_giudiziario'] = $this->getAutorita($row[7]. ' DI '. $row[8], $row[8]);



                $archivioFields['annotazioni'] = $row[17];
                $archivioFields['tipologiapratica'] = $this->getTipologia("Civile");



                $anagraficheFields['dominus']      = $this->getAnagrafica(array('denominazione' => $row[11]));
                $anagraficheFields['avversario']   = $this->getAnagrafica(array('denominazione' => $row[12]));



                $anagraficheFields['pratica'] 	   = $this->createPratica($archivioFields);


                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['cliente'] , 1 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['controparte'] , 2 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['dominus'] , 5 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['avversario'] , 3 ) ;

                //Udienza

                $udienzaFields['pratica']	 	= $anagraficheFields['pratica'];
                $udienzaFields['referente'] 	= 6;
                $udienzaFields['data'] 			= $this->getData($row[9], 'dmy', true, true);
                $udienzaFields['attivita'] 		= 'UDIENZA';
                $udienzaFields['autorita'] 		= $archivioFields['ufficio_giudiziario'];

                $this->createUdienza($udienzaFields);


                //=================     FINE        ========================//






            }


        }
    }




    private function ElencoCause($post){
        if (isset($post["modello"])) {

            $fileName = $post["filePath"];

            $Reader = new SpreadsheetReader($fileName);


            $now = date("Y-m-d H:i:s");


            //exec('rm -rf ' .  $fileName);
            // exec('rm -rf ' .  $fileName2);



            //$counter

            $counter_row = 0;


            $this->createCampoDinamico("DATA NOTIFICA ATTO", 'date');
            $this->createCampoDinamico("DELIB/DETER INCARICO", 'regex');
            $this->createCampoDinamico('PROCEDIMENTO', 'regex');
            $this->createCampoDinamico('DATA ARRIVO SENTENZA', 'regex');
            $this->createCampoDinamico('DISPOSITIVO SENTENZA', 'regex');
            $this->createCampoDinamico('CONDANNA/RIGETTO DOMANDA', 'regex');
            $this->createCampoDinamico('IMPORTO CONDANNA/RIGETTO DOMANDA', 'regex');
            $this->createCampoDinamico('ONERI', 'regex');
            $this->createCampoDinamico('FALD.', 'regex');



            $this->createCategoria('Elenco Cause', array(
                $this->getCampoDinamico("DATA NOTIFICA ATTO"),
                $this->getCampoDinamico('DELIB/DETER INCARICO'),
                $this->getCampoDinamico('PROCEDIMENTO'),
                $this->getCampoDinamico('DATA ARRIVO SENTENZA'),
                $this->getCampoDinamico('DISPOSITIVO SENTENZA'),
                $this->getCampoDinamico('CONDANNA/RIGETTO DOMANDA'),
                $this->getCampoDinamico('IMPORTO CONDANNA/RIGETTO DOMANDA'),
                $this->getCampoDinamico('ONERI'),
                $this->getCampoDinamico('FALD.'),
            ));




            foreach ($Reader as $row){






                $counter_row ++;


                if ($counter_row < 5){
                    $this->applogger->info(print_r($row,1));
                    continue;
                }else if ($counter_row > 10307){
                    break;
                }




                $i = 0;
                foreach ($row as &$col){
                    $col = trim($col);
                }




                //=================     INIZIO        ========================//


                $archivioFields = array();
                $anagraficheFields = array();
                $udienzaFields = array();



                $archivioFields['codicearchivio']  = $row[0].'/'.$row[1];
                $this->createSezionale($row[1]);


                $anagraficheFields['cliente']      = $this->getAnagrafica(array('denominazione' => 'Comune di Pozzuoli'));

                $archivioFields['avvocato'] = $this->getIntestatario("Anna Sannino");

                $dataApertura = '1900-01-01';
                //data apertura
                if(!empty($row[7])){
                    $dataApertura = $this->getData($row[7], 'dmy', true, true);
                }

                $archivioFields['data'] = $dataApertura;



                $archivioFields['valori_dinamici'] = json_encode(
                    array(
                        array("valore" => $this->getData($row[8], 'dmy', false, true), "id" => $this->getCampoDinamico("DATA NOTIFICA ATTO")),
                        array("valore" => $row[14], "id" => $this->getCampoDinamico('DELIB/DETER INCARICO')),
                        array("valore" => $row[17], "id" => $this->getCampoDinamico('PROCEDIMENTO')),
                        array("valore" => $row[18], "id" => $this->getCampoDinamico('DATA ARRIVO SENTENZA')),
                        array("valore" => $row[19], "id" => $this->getCampoDinamico('DISPOSITIVO SENTENZA')),
                        array("valore" => $row[20], "id" => $this->getCampoDinamico('CONDANNA/RIGETTO DOMANDA')),
                        array("valore" => $row[21], "id" => $this->getCampoDinamico('IMPORTO CONDANNA/RIGETTO DOMANDA')),
                        array("valore" => $row[22], "id" => $this->getCampoDinamico('ONERI')),
                        array("valore" => $row[23], "id" => $this->getCampoDinamico('FALD.'))
                    )
                );

                $archivioFields['id_categoria'] = $this->getCategoria('Elenco Cause');

                $archivioFields['ufficio_giudiziario'] = $this->getAutorita($row[9]. ' DI '. $row[11], $row[11]);



                $archivioFields['annotazioni'] = $row[24];
                $archivioFields['tipologiapratica'] = $this->getTipologia("Civile");
                $archivioFields['valore'] = $row[5];
                $archivioFields['descrizione'] = $row[4];
                $archivioFields['rgnr'] = $this->getRgnr($row[10]);
                $archivioFields['rgnranno'] = $this->getRgnrAnno($row[10]);


                $anagraficheFields['dominus']      = $this->getAnagrafica(array('denominazione' => $row[15]));
                $anagraficheFields['avversario']   = $this->getAnagrafica(array('denominazione' => $row[16]));
                $anagraficheFields['altro']   = $this->getAnagrafica(array('denominazione' => $row[6]));

                $anagraficheFields['pratica'] 	   = $this->createPratica($archivioFields);


                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['cliente'] , 1 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['dominus'] , 5 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['avversario'] , 3 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['altro'] , 4 , 'Ufficio') ;

                //gestione clienti aggiuntivi
                $clienti = explode(",", $row[3]);
                foreach($clienti as $cliente){
                    if(strpos($cliente, "Comune di Pozzuoli") !== false){
                        continue;
                    }
                    $this->createSoggetto($anagraficheFields['pratica'] , $this->getAnagrafica(array('denominazione' => $cliente)) , 1 ) ;
                }

                //gestione controparti
                $anagraficheFields['controparte']  = $this->getAnagrafica(array('denominazione' => $row[2]));
                $controparti = explode(",", $row[2]);
                foreach($controparti as $controparte){
                    $this->createSoggetto($anagraficheFields['pratica'] , $this->getAnagrafica(array('denominazione' => $controparte)) , 2 ) ;
                }

                //Udienza

                $udienzaFields['pratica']	 	= $anagraficheFields['pratica'];
                $udienzaFields['referente'] 	= 6;
                $udienzaFields['data'] 			= $this->getData($row[13], 'dmy', true, true);
                $udienzaFields['attivita'] 		= 'UDIENZA';
                $udienzaFields['autorita'] 		= $archivioFields['ufficio_giudiziario'];
                $udienzaFields['istruttore']	= $this->getIstruttore($row[12]);

                $this->createUdienza($udienzaFields);

                //=================     FINE        ========================//
            }
        }
    }

    private function IncarichiIngiunzioni($post){
        if (isset($post["modello"])) {

            $fileName = $post["filePath"];

            $Reader = new SpreadsheetReader($fileName);


            $now = date("Y-m-d H:i:s");


            //exec('rm -rf ' .  $fileName);
            // exec('rm -rf ' .  $fileName2);



            //$counter

            $counter_row = 0;


            $this->createCampoDinamico("DATA NOTIFICA ATTO", 'regex');
            $this->createCampoDinamico("DEL/DET. INCARICO", 'regex');
            $this->createCampoDinamico('PROCEDIMENTO', 'regex');
            $this->createCampoDinamico('DATA ARRIVO SENTENZA', 'regex');
            $this->createCampoDinamico('DISPOSITIVO SENTENZA', 'regex');
            $this->createCampoDinamico('FALD.', 'regex');



            $this->createCategoria('Incarichi Ingiunzioni', array(
                $this->getCampoDinamico("DATA NOTIFICA ATTO"),
                $this->getCampoDinamico('DEL/DET. INCARICO'),
                $this->getCampoDinamico('PROCEDIMENTO'),
                $this->getCampoDinamico('DATA ARRIVO SENTENZA'),
                $this->getCampoDinamico('DISPOSITIVO SENTENZA'),
                $this->getCampoDinamico('FALD.'),
            ));




            foreach ($Reader as $row){






                $counter_row ++;


                if ($counter_row < 6){
                    $this->applogger->info(print_r($row,1));
                    continue;
                }else if ($counter_row > 71){
                    break;
                }




                $i = 0;
                foreach ($row as &$col){
                    $col = trim($col);
                }




                //=================     INIZIO        ========================//


                $archivioFields = array();
                $anagraficheFields = array();
                $udienzaFields = array();



                $archivioFields['codicearchivio']  = $row[0].'/'.$row[1];
                $this->createSezionale($row[1]);


                $anagraficheFields['cliente']      = $this->getAnagrafica(array('denominazione' => 'Comune di Pozzuoli'));
                $anagraficheFields['controparte']  = $this->getAnagrafica(array('denominazione' => $row[2]));

                $archivioFields['oggetto'] = $this->getOggetto($row[4]);

                $archivioFields['avvocato'] = $this->getIntestatario("Anna Sannino");

                $dataApertura = '1900-01-01';
                //data notifica atto
                if(!empty($row[7])){
                    $dataApertura = $this->getData($row[5], 'dmy', true, true);
                }

                $archivioFields['data'] = $dataApertura;



                $archivioFields['valori_dinamici'] = json_encode(
                    array(
                        array("valore" => $row[5], "id" => $this->getCampoDinamico("DATA NOTIFICA ATTO")),
                        array("valore" => $row[9], "id" => $this->getCampoDinamico('DEL/DET. INCARICO')),
                        array("valore" => $row[12], "id" => $this->getCampoDinamico('PROCEDIMENTO')),
                        array("valore" => $row[13], "id" => $this->getCampoDinamico('DATA ARRIVO SENTENZA')),
                        array("valore" => $row[14], "id" => $this->getCampoDinamico('DISPOSITIVO SENTENZA')),
                        array("valore" => $row[15], "id" => $this->getCampoDinamico('FALD.'))
                    )
                );

                $archivioFields['id_categoria'] = $this->getCategoria('Incarichi Ingiunzioni');

                $archivioFields['ufficio_giudiziario'] = $this->getAutorita($row[6]. ' DI '. $row[7], $row[7]);



                $archivioFields['annotazioni'] = $row[16];
                $archivioFields['tipologiapratica'] = $this->getTipologia("Civile");
                $archivioFields['descrizione'] = $row[3];


                $anagraficheFields['dominus']      = $this->getAnagrafica(array('denominazione' => $row[10]));
                $anagraficheFields['avversario']   = $this->getAnagrafica(array('denominazione' => $row[11]));

                $anagraficheFields['pratica'] 	   = $this->createPratica($archivioFields);

                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['cliente'] , 1 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['controparte'] , 2 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['dominus'] , 5 ) ;
                $this->createSoggetto($anagraficheFields['pratica'] , $anagraficheFields['avversario'] , 3 ) ;

                //Udienza

                $udienzaFields['pratica']	 	= $anagraficheFields['pratica'];
                $udienzaFields['referente'] 	= 6;
                $udienzaFields['data'] 			= $this->getData($row[8], 'dmy', true, true);
                $udienzaFields['attivita'] 		= 'UDIENZA';
                $udienzaFields['autorita'] 		= $archivioFields['ufficio_giudiziario'];

                $this->createUdienza($udienzaFields);

                //=================     FINE        ========================//
            }
        }
    }


    public function compareRow($row , $arrayOfNames){

        $row_headers = array();
        foreach ($row as $key=>$column){
            array_push($row_headers,trim($column));
        }


        if ($row_headers == $arrayOfNames){
            return TRUE;
        }else{
            return FALSE;
        }
    }

    /**
     * Secondo step import
     * @throws Exception
     */
    public function executeAction()
    {
        ini_set('max_execution_time', '1000000');
        ini_set('post_max_size', '640M');
        ini_set('upload_max_filesize', '640M');

        $className = ucfirst(SITEKEY) . ( isset($_REQUEST['documents']) ? 'Documents' : '' );
        require_once('import/customers/' . $className . '.php');
        $importer = new $className($this->getRequest(), $this->getResponse(), ['sitekey' => SITEKEY]);
        $importer->execute($_POST);

        $this->_helper->viewRenderer->setNoRender(true);

    }

}

?>