<?php
class RTFManager_RTF {
	static function encodeSpecialChars($rtfContent) {
		$chars = array();
		preg_match_all('/[\x80-\xFF]/u', $rtfContent, $chars);
		$chars[0] = array_values(array_unique($chars[0]));

		foreach ($chars[0] as $char)
		{
			$hex = substr(bin2hex(mb_convert_encoding($char, 'UTF-16', 'UTF-8')), 2);
			$dec = hexdec($hex);
			$rtfContent = str_replace($char, "\u$dec\'$hex", $rtfContent);
		}
		return $rtfContent;
	}

	static function send($rtfContent, $filename)
	{
		ob_start();
		if (function_exists('mb_strlen'))
		{
			$size = mb_strlen($rtfContent, '8bit');
		}
		else
		{
			$size = strlen($rtfContent);
		}

		ob_end_clean();

		header('Content-Description: File Transfer');
		header('Content-Type: application/octet-stream');
		header("Content-Disposition: attachment; filename=$filename");
		header('Content-Transfer-Encoding: binary');
		header('Expires: 0');
		header('Cache-Control: must-revalidate');
		header('Pragma: public');
		header('Content-Length: ' . $size);
		echo $rtfContent;
	}
}