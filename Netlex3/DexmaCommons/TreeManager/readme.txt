1 - creare l'alias nel file di configurazione di apache aggiungendo la riga: 
	 		Alias /public /home/<USER>/DexmaCommons/public

2 - nella pagina dove viene creato l'albero importare la pagina:
	 	"client.tpl" (per albero lato utente) aggiungendo la riga:
    		{include file="$dexmaCommonsPath/public/TreeManager/client.tpl"}
    	"manager.tpl" (per gestione albero lato amministratore) aggiungendo la riga:
    		{include file="$dexmaCommonsPath/public/TreeManager/manager.tpl"}

3 - implementare i metodi per la gestione dell'albero, segue un esempio di classe PHP:
		<?php
			require_once(DEXMA_COMMONS . '/TreeManager/TreeManager.php');

			class Admin_TreemanagerController extends AdminControllerAction {
	
				private $treeManager;
	
				public function init()
				{
					parent::init();
					//Array contenente la struttura della tabella necessaria per memorizzare le preferenze (stato dell'albero) di un utente utilizzatore:					
					$refererTable = array(nonetabella, nomeCampoIdNodo, nomeCampoIdOggettoCollegato);
					$idOggettoCollegato = ...;
					$this->treeManager = new TreeManager(nomeTabellaAlbero, $refererTable, nomeController, $this->db, $this->view, $this->dexmaCommons, 
																	 $this->_helper, $idOggettoCollegato);
				}
				//Metodo che disagnare l'albero lato amministratore
				public function ........Action()
				{
					//Validazione utente se richiesta
					$this->treeManager->drawTree();
					$this->view->dexmaCommonsPath = DEXMA_COMMONS;
				}
				//Metodo per inserire/modificare un nodo dell'albero
				public function insertupdatenodeAction()		
				{
					//Validazione utente se richiesta
					$this->treeManager->insertUpdateNode();
				}
				//Metodo per gestire il drag and drop di un nodo dell'albero
				public function movenodeAction()
				{
					//Validazione utente se richiesta
					$this->treeManager->moveNode();
				}
				//Metodo per eliminare un nodo dell'albero
				public function removenodeAction()
				{
					//Validazione utente se richiesta
					$this->treeManager->removeNode();
				}
				//Metodo che disagnare l'albero lato utente
				public function ........Action()
				{
					//Validazione utente se richiesta
					$this->treeManager->drawClientTree();
					$this->view->dexmaCommonsPath = DEXMA_COMMONS;
					//Se valorizzato a true sulla pagina è presente il pulsante "Salva" che gestisce il salvataggio delle preferenze in modo remoto.
					//Se valorizzato con false il salvataggio dovrà essere eseguito in maniera manuale a questo scopo è presente il metodo javascript
					//getClientConfiguration() che restituisce un array contenente gli uniqueid dei nodi selezionati. Lato server il metodo PHP
					//$this->treeManager->saveClientConfiguration(array contenente gli uniqueid dei nodi selezionati) effettua il salvataggio delle preferenze.				
					$this->view->remoteSave = false;
				}
				//Metodo per il salvataggio delle preferenze quando $this->view->remoteSave = true, da non implementare quando remotesave è valorizzato 
				//a false
				public function saveclientconfigurationAction() {
					//Validazione utente se richiesta
					$userId = parent::getAttribute(parent::$USER_ID_KEY);
					$this->treeManager->remoteSaveClientConfiguration();
				}
			}
		?>

10 - importare come primi files css, quelli di Bootstrap, aggiungendo le righe di codice:
	  <link href="/public/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	  <link href="/public/bootstrap/css/bootstrap-responsive.min.css" rel="stylesheet">

	  <style type="text/css">
	  		body {
    	  			padding-top: 60px;
          		padding-bottom: 40px;
    	 	}
    	 	legend {
    	  			margin-bottom: 0px;
    	 	}
	   </style>

11 - importare il file bootstrap.js aggiungendo la riga:
	  <script src="/public/bootstrap/js/bootstrap.min.js"></script>

12 - versione jquery: da 1.7.2 in poi;