function saveClientConfiguration() {
	var tree = $('#treeContainer');
	var	checkedNodes = getClientConfiguration();
	$.post(tree.data('controllername') + '/saveclientconfiguration', {checkedNodes: checkedNodes}, function(data) {
		if(data == null || data == false) {
			$('#clientMessage').show();
			$('#clientMessage').html(gettext('Errore durante il salvataggio delle modifiche'));
		} else if(data != null && data == true)	{
			$('#clientMessage').show();
			$('#clientMessage').html(gettext('Salvataggio completato'));
			$('#clientMessage').fadeOut(3000);
		}		
	});
}
function getClientConfiguration() {
	var tree = $('#treeContainer');
	var checkedNodes = [];
	tree.jstree('get_checked', null, true).each(function() { 
		if(tree.jstree('is_leaf', $(this))) {
			checkedNodes.push($(this).attr('id'));
		}
	});
	return checkedNodes;	
}
$(function() {
	var tree = $('#treeContainer');
	tree.bind('loaded.jstree',function(e,data) {
		//Opens all descendants
		data.inst.open_all(-1);
		tree.find('li').first().attr('id', 'treeRoot');
		$.each(checkedNodes, function(index,value) {
			tree.jstree('check_node', '#'+value['uniqueid']);
		});
						
	}).jstree({
				'ui': {select_limit: 0},
				'plugins' : ['themes', 'html_data', 'checkbox', 'sort', 'ui']
	});	
	$('#clientMessage').hide();	
});