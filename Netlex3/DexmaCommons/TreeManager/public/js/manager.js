var newNodeLabel = 'nuovo';
var errorMessage = gettext('Errore durante il salvataggio delle modifiche');
function toggleNode() {
	var tree = $('#treeContainer');
	if(tree.jstree('is_open', '#treeRoot')) {
		tree.jstree('close_all', '#treeRoot'); 
	} else {
		tree.jstree('open_all', '#treeRoot');
	}
}
function newNode() {
	var tree = $('#treeContainer');
	if($.jstree._reference('#treeContainer').get_selected().length > 0) {
		tree.jstree('create', null, 'inside', {'data': newNodeLabel}, null, null);
	} else if (tree.find('li').length == 0) {
		tree.jstree('create', null, 'first', {'data': newNodeLabel}, null, null);
	}
}
function deleteNode() {
	var tree = $('#treeContainer');
	if($.jstree._reference('#treeContainer').get_selected().length > 0 || tree.find('li').length == 0) {		
		if(confirm(gettext('Eliminare definitivamente il nodo selezionato?'))) {
			tree.jstree('remove');		
		}
	}
}
function updateNode() {
	var tree = $('#treeContainer');
	if($.jstree._reference('#treeContainer').get_selected().length > 0 || tree.find('li').length == 0) {
		tree.jstree('rename');
	}
}
$(function () {
	var tree = $('#treeContainer');
	tree.bind('loaded.jstree',function(e,data) {
		//Opens all descendants
		data.inst.open_all(-1);
		tree.find('li').first().attr('id', 'treeRoot');
	}).jstree({
				'crrm': {'move' : {
									'check_move' : function(data) {
				                									if($(data.np[0]).data('nodeid')) {
				                										return true;
				                									}
				                									return false;
				                  				   }
					     		  }							
				},
				'ui': {select_limit: 1},
				'contextmenu': {'items': {
											'create': {
														'label': 'Nuovo',
														'action': function(obj) {
																	  this.create(obj, 'inside', {'data': newNodeLabel}, null, null);
														  		  }	
											},
											'rename': {
														'label': 'Rinomina'
											},
											'remove': {
														'label': 'Elimina',
														'action': function(obj) {
																	  if(confirm(gettext('Eliminare definitivamente il nodo selezionato?'))) {
																		  this.remove(obj);
																	  }
																  }
											},
											'ccp': {
														'label': 'Modifica',
														'submenu': {
															'copy': {
																		'label': 'Copia',
																		'_disabled': true,
																			
																		
															},
															'cut': {
																		'label': 'Taglia'
															},
															'paste': {
																		'label': 'Incolla'
															}
														}
											}
							   }
				},
				'plugins' : ['themes', 'html_data', 'sort', 'ui', 'crrm', 'dnd', 'contextmenu']
	});
	tree.bind('rename_node.jstree', function(event, data) {
		var li = data.rslt.obj;
		var id = li.data('nodeid');
		var description = tree.jstree('get_text', li);
		var params = null;
		var fatherId = '';
		
		if(id != undefined) {
			params = {id: id, description: description};
		} else {
			if(data.inst._get_parent(li) != -1) {
				fatherId = data.inst._get_parent(li).data('nodeid');
			}
			params = {fatherId: fatherId, description: description};
		}
		$.post($('#treeContainer').data('controllername') + '/insertupdatenode', params, function(data) {			
			if(data != null && data != false && data != true) {
				li.attr('data-nodeid', data);
				if(fatherId == ''){
					li.attr('id', 'treeRoot');
				}
			} else if(data == null || data == false) {
				alert(errorMessage);
			}
		});
	});
	tree.bind('move_node.jstree', function(event, data) {
		var li = data.rslt.o;
		var id = li.data('nodeid');
		var fatherId = data.inst._get_parent(li).data('nodeid');
		var params = {id: id, fatherId: fatherId}; 
		$.post($('#treeContainer').data('controllername') + '/movenode', params, function(data){
			if(data == null || data == false) {
				alert(errorMessage);
			}
		});
	});
	tree.bind('delete_node.jstree', function(event, data) {
		var li = data.rslt.obj;
		var params = {id: li.data('nodeid')};
		$.post($('#treeContainer').data('controllername') + '/removenode', params, function(data){
			if(data != null && data == false) {
				alert(errorMessage);
			}
		});
	});
});