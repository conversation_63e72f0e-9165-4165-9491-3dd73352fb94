/*
 * jsTree classic theme 1.0
 * Supported features: dots/no-dots, icons/no-icons, focused, loading
 * Supported plugins: ui (hovered, clicked), checkbox, contextmenu, search
 */

.jstree-classic li, 
.jstree-classic ins { background-image:url("d.png"); background-repeat:no-repeat; background-color:transparent; }
.jstree-classic li { background-position:-90px 0; background-repeat:repeat-y;  }
.jstree-classic li.jstree-last { background:transparent; }
.jstree-classic .jstree-open > ins { background-position:-72px 0; }
.jstree-classic .jstree-closed > ins { background-position:-54px 0; }
.jstree-classic .jstree-leaf > ins { background-position:-36px 0; }

.jstree-classic .jstree-hovered { background:#e7f4f9; border:1px solid #e7f4f9; padding:0 2px 0 1px; }
.jstree-classic .jstree-clicked { background:navy; border:1px solid navy; padding:0 2px 0 1px; color:white; }
.jstree-classic a .jstree-icon { background-position:-56px -19px; }
.jstree-classic .jstree-open > a .jstree-icon { background-position:-56px -36px; }
.jstree-classic a.jstree-loading .jstree-icon { background:url("throbber.gif") center center no-repeat !important; }

.jstree-classic.jstree-focused { background:white; }

.jstree-classic .jstree-no-dots li, 
.jstree-classic .jstree-no-dots .jstree-leaf > ins { background:transparent; }
.jstree-classic .jstree-no-dots .jstree-open > ins { background-position:-18px 0; }
.jstree-classic .jstree-no-dots .jstree-closed > ins { background-position:0 0; }

.jstree-classic .jstree-no-icons a .jstree-icon { display:none; }

.jstree-classic .jstree-search { font-style:italic; }

.jstree-classic .jstree-no-icons .jstree-checkbox { display:inline-block; }
.jstree-classic .jstree-no-checkboxes .jstree-checkbox { display:none !important; }
.jstree-classic .jstree-checked > a > .jstree-checkbox { background-position:-38px -19px; }
.jstree-classic .jstree-unchecked > a > .jstree-checkbox { background-position:-2px -19px; }
.jstree-classic .jstree-undetermined > a > .jstree-checkbox { background-position:-20px -19px; }
.jstree-classic .jstree-checked > a > .jstree-checkbox:hover { background-position:-38px -37px; }
.jstree-classic .jstree-unchecked > a > .jstree-checkbox:hover { background-position:-2px -37px; }
.jstree-classic .jstree-undetermined > a > .jstree-checkbox:hover { background-position:-20px -37px; }

#vakata-dragged.jstree-classic ins { background:transparent !important; }
#vakata-dragged.jstree-classic .jstree-ok { background:url("d.png") -2px -53px no-repeat !important; }
#vakata-dragged.jstree-classic .jstree-invalid { background:url("d.png") -18px -53px no-repeat !important; }
#jstree-marker.jstree-classic { background:url("d.png") -41px -57px no-repeat !important; text-indent:-100px; }

.jstree-classic a.jstree-search { color:aqua; }
.jstree-classic .jstree-locked a { color:silver; cursor:default; }

#vakata-contextmenu.jstree-classic-context, 
#vakata-contextmenu.jstree-classic-context li ul { background:#f0f0f0; border:1px solid #979797; -moz-box-shadow: 1px 1px 2px #999; -webkit-box-shadow: 1px 1px 2px #999; box-shadow: 1px 1px 2px #999; }
#vakata-contextmenu.jstree-classic-context li { }
#vakata-contextmenu.jstree-classic-context a { color:black; }
#vakata-contextmenu.jstree-classic-context a:hover, 
#vakata-contextmenu.jstree-classic-context .vakata-hover > a { padding:0 5px; background:#e8eff7; border:1px solid #aecff7; color:black; -moz-border-radius:2px; -webkit-border-radius:2px; border-radius:2px; }
#vakata-contextmenu.jstree-classic-context li.jstree-contextmenu-disabled a, 
#vakata-contextmenu.jstree-classic-context li.jstree-contextmenu-disabled a:hover { color:silver; background:transparent; border:0; padding:1px 4px; }
#vakata-contextmenu.jstree-classic-context li.vakata-separator { background:white; border-top:1px solid #e0e0e0; margin:0; }
#vakata-contextmenu.jstree-classic-context li ul { margin-left:-4px; }

/* IE6 BEGIN */
.jstree-classic li, 
.jstree-classic ins,
#vakata-dragged.jstree-classic .jstree-invalid, 
#vakata-dragged.jstree-classic .jstree-ok, 
#jstree-marker.jstree-classic { _background-image:url("d.gif"); }
.jstree-classic .jstree-open ins { _background-position:-72px 0; }
.jstree-classic .jstree-closed ins { _background-position:-54px 0; }
.jstree-classic .jstree-leaf ins { _background-position:-36px 0; }
.jstree-classic .jstree-open a ins.jstree-icon { _background-position:-56px -36px; }
.jstree-classic .jstree-closed a ins.jstree-icon { _background-position:-56px -19px; }
.jstree-classic .jstree-leaf a ins.jstree-icon { _background-position:-56px -19px; }
#vakata-contextmenu.jstree-classic-context ins { _display:none; }
#vakata-contextmenu.jstree-classic-context li { _zoom:1; }
.jstree-classic .jstree-undetermined a .jstree-checkbox { _background-position:-20px -19px; }
.jstree-classic .jstree-checked a .jstree-checkbox { _background-position:-38px -19px; }
.jstree-classic .jstree-unchecked a .jstree-checkbox { _background-position:-2px -19px; }
/* IE6 END */