<script src="/public/TreeManager/js/jquery.jstree.js"></script>
<script src="/public/TreeManager/js/client.js"></script>
{*<input type="button" class="btn btn-primary" value="{t}Apri{/t}/{t}Chiudi{/t}" onclick="toggleNode()" />*}
{if $remoteSave}
	<input type="button" class="btn" value="{t}Salva{/t}" onclick="saveClientConfiguration()" />
{/if}
<strong id="clientMessage"></strong>
{$tree_js}
<div id="treeContainer" data-controllername="{$treeControllerName}">
	{$tree}
</div>