<script src="/public/TreeManager/js/jquery.jstree.js"></script>
<script src="/public/TreeManager/js/manager.js"></script>
<input type="button" class="btn btn-primary"  value="{t}Apri{/t}/{t}Chiudi{/t}" onclick="toggleNode()"/>
<input type="button" class="btn" value="{t}Nuovo{/t}" onclick="newNode()"/>
<input type="button" class="btn"  value="{t}Rinomina{/t}" onclick="updateNode()"/>
<input type="button" class="btn"  value="{t}Elimina{/t}" onclick="deleteNode()"/>
<div id="treeContainer" data-controllername="{$treeControllerName}">
	{$tree}
</div>
