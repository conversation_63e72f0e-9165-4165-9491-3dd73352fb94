<?php
class TreeManager
{
	private $tableName;
	private	$refererTable;
	private $db;
	private $view;
	private $dexmaCommons;
	private $helper;
	private $refererId;
	/**
	 * 
	 * @param $tableName nome tabella che mappa l'albero nel database
	 * @param $refererTable array contenente la struttura della tabella necessaria per memorizzare le preferenze (stato dell'albero) di un utente utilizzatore:
	 * 						posizione 1: nome tabella
	 * 						posizione 2: nome campo per id nodo
	 * 						posizione 3: nome campo per id oggetto collegato
	 * @param $controllerName nome del controller in cui sono implementati i metodi per la gestione dell'albero
	 * @param $db
	 * @param $view
	 * @param $dexmaCommons
	 * @param $helper
	 * @param $refererId id dell'oggetto collegato
	 */
	public function TreeManager($tableName, $refererTable, $controllerName, $db, $view, $dexmaCommons, $helper, $refererId) {
		$this->tableName = $tableName;
		$this->refererTable = $refererTable;
		$this->db = $db;
		$this->view = $view;
		$this->dexmaCommons = $dexmaCommons;
		$this->helper = $helper;
		$this->view->treeControllerName = $controllerName;
		$this->refererId = $refererId;
	}
	
    public function drawTree() {
    	$this->getNodes(true);
    }
    
    public function drawClientTree() {
    	$this->getNodes(false);    	 
    	$sql = 'SELECT i.uniqueid
    			FROM ' . $this->refererTable[0] . ' ui LEFT JOIN ' . $this->tableName . ' i ON i.id = ui.' . $this->refererTable[1] . '
    			WHERE ui.' . $this->refererTable[2] . ' = ' . $this->refererId;
    	$results = $this->db->fetchAll($sql);
    	$results = json_encode($results);
    	$data = "<script>var checkedNodes = $results;</script>";
    	$this->view->tree_js = $data;
    }
    
    private function getNodes($nodeid) {
    	$selectNodes = "SELECT id, description, uniqueid, father_id
    					FROM $this->tableName";
    	$nodes = $this->db->fetchAll($selectNodes);
    	$this->view->tree = $this->parseTreeToHtml($nodes, null, $nodeid);
    }    
    
    private function parseTreeToHtml($nodes, $root = null, $nodeid = false) {
    	$html = '';
    	if(!is_null($nodes) && count($nodes) > 0) {
    		$html .= '<ul>';
    		foreach($nodes as $key => $node) {
    			if($node['father_id'] == $root) {
    				unset($nodes[$key]);
    				$html .= '<li ';
					if($nodeid) {
    					$html  .= 'data-nodeid="' . $node['id'];
					} else {
						$html .= 'id="' . $node['uniqueid'];
					}
					$html .= '">' .
    				         '<a href="#">' . $node['description'] . '</a>';
    				$html .= $this->parseTreeToHtml($nodes, $node['id'], $nodeid);
    				$html .= '</li>';
    			}
    		}
    		$html .= '</ul>';
    	}
    	return $html;
    }
    
    private function parseTreeToArray($nodes, $root = null) {
    	$return = array();
    	foreach($nodes as $key => $node) {
    		if($node['father_id'] == $root) {
    			unset($nodes[$key]);
    			$return[] = array(
    					'name' => $node['description'],
    					'children' => $this->parseTreeToArray($nodes, $node['id'])
    			);
    		}
    	}
    	return empty($return) ? null : $return;
    }

    public function remoteSaveClientConfiguration() {
    	$result = false;
    	$this->db->beginTransaction();
    	try {
    		$where = array($this->refererTable[2] . ' = ' . $this->refererId);
    		$this->db->delete($this->refererTable[0], $where);
    		//if(!empty($_POST['checkedNodes'])) {
    		$nodes = $_POST['checkedNodes'];
    		$select = 'SELECT id
    				   FROM ' . $this->tableName . '
    				   WHERE uniqueid = ?';
    		foreach($nodes as $nodeUniqueId) {
    			$nodeId = $this->db->fetchOne($select, array($nodeUniqueId));   				
    			$fieldsList = array($this->refererTable[2]=>$this->refererId,
    								$this->refererTable[1]=>$nodeId);
    			$this->db->insert($this->refererTable[0], $fieldsList);
    		}
    		$this->db->commit();
    		$result = true;
    	} catch(Exception $e) {
    		$this->db->rollBack();
    	}
    	echo $result;
    	$this->helper->viewRenderer->setNoRender(true);
    }
    
    public function saveClientConfiguration($checkedNodes) {
    	$result = false;
    	$where = array($this->refererTable[2] . ' = ' . $this->refererId);
    	$this->db->delete($this->refererTable[0], $where);
		$select = 'SELECT id
    			   FROM ' . $this->tableName . '
    			   WHERE uniqueid = ?';
    	foreach($checkedNodes as $nodeUniqueId) {
    		$nodeId = $this->db->fetchOne($select, array($nodeUniqueId));
    		$fieldsList = array($this->refererTable[2]=>$this->refererId,
    							$this->refererTable[1]=>$nodeId);
    		$this->db->insert($this->refererTable[0], $fieldsList);
    	}
   		$result = true;
    	return $result;
    }    
    
    public function insertUpdateNode() {
    	$result = false;
    	if(!empty($_POST['id']) && !empty($_POST['description'])) {
    		$fieldsList = array('description'=>$_POST['description']);
    		$whereCondition = array('id = ' . $_POST['id']);
    		$this->db->update($this->tableName, $fieldsList, $whereCondition);
    			$result = true;
    	} else if(!empty($_POST['description'])) {
    		$uid = $this->dexmaCommons->getUid();
    		$fatherId = null;
    		if(!empty($_POST['fatherId'])) {
    			$fatherId = $_POST['fatherId'];
    		}
    		$fieldsList = array(
    				'father_id'=>$fatherId,
    				'description'=>$_POST['description'],
    				'uniqueid'=>$uid
    		);
    		$this->db->insert('interests', $fieldsList);
    		 
    		$select = 'SELECT id
    				   FROM interests
    				   WHERE uniqueid = ?';
    		$result = $this->db->fetchOne($select, array($uid));
    	}
    	echo $result;
    	$this->helper->viewRenderer->setNoRender(true);
    }
    
    public function moveNode()
    {
    	$result = false;
    	if(!empty($_POST['id']) && !empty($_POST['fatherId'])) {
    		$fieldsList = array('father_id'=>$_POST['fatherId']);
    		$whereCondition = array('id = ' . $_POST['id']);
    		if($this->db->update('interests', $fieldsList, $whereCondition) == 1) {
    			$result = true;
    		}
    	}
    	echo $result;
    	$this->helper->viewRenderer->setNoRender(true);
    }
    
    public function removeNode() {
    	$result = true;
    	$this->db->beginTransaction();
    	try {
    		$this->remove();
    		$this->db->commit();
    	} catch(Exception $e) {
    		$this->db->rollBack();
    		$result = false;
    	}
    	echo $result;
    	$this->helper->viewRenderer->setNoRender(true);
    }
    
    private function remove($nodeId=null) {
    	try {
	    	if($nodeId == null && !empty($_POST['id'])) {
	    		$nodeId = $_POST['id'];
	    	}
	    	
	    	$select = "SELECT id
	    			   FROM $this->tableName
	    			   WHERE father_id = ?";
	    	$childrens = $this->db->fetchAll($select, array($nodeId));
	    	
	    	foreach($childrens as $child) {
	    		$this->remove($child['id']);
	    	}
	
	    	$where = array("interest_id = $nodeId");
	    	$this->db->delete('user_interests', $where);
	    	
	    	$where = array("id = $nodeId");
	    	$this->db->delete('interests', $where);
    	} catch(Exception $e) {
    		throw $e;
    	}
    }
}    
?>