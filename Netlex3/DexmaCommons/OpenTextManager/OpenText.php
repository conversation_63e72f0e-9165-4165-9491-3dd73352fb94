<?php

use Util\_Constants\Globals\ZendRegistry;

class OpenText
{
    private $config;
    private $db;
    private $loggedUser;
    private $subdomain;
	protected $applogger;
    protected $netlexSettings;
    private $codes = array(
        0 => 'Errore generico o UserID errato',
        1 => 'OK',
        2 => 'Path non trovato',
        3 => 'Category non trovata',
        4 => 'Name già esistente',
        5 => 'ID non trovato',
        6 => 'Name o path non trovato'
    );


    public function __construct($db, $config, $netlexSettings, $subdomain, $loggedUser){
        $this->db = $db;
        $this->loggedUser = $loggedUser;

        if(empty($subdomain)){
            $subdomain = SITEKEY;
        }
        $this->subdomain = $subdomain;
        
        if(empty($config)){
            $config = new Zend_Config_Ini(SITE_ROOT . '/conf/config.ini', APPLICATION_ENV, array('allowModifications' => true));
            $config->merge(new Zend_Config_Ini(SITE_ROOT . '/conf/config_secrets.ini', APPLICATION_ENV));
        }
        $this->config = $config;

        if ( empty($netlexSettings) ) {
					$netlexSettings = Zend_Registry::get( ZendRegistry::NETLEX_SETTINGS );
				}
			$this->netlexSettings = $netlexSettings;

        $applogger = new Zend_Log(new Zend_Log_Writer_Stream(SITE_ROOT .  "/Software/data/logs/openText.log"));
		$filter = new Zend_Log_Filter_Priority(6);
		$applogger->addFilter($filter);
        $this->applogger = $applogger;
    }
    
    /**
     * call
     * Chiamata SOAP ad OpenText.
     *
     * @param  array $data = [
     *                  'action' => (string) Action da chiamare. Required.
     *                  'xml' => (integer) Corpo xml. Required.
     *                  'request => (mixed) Dati passati con la request. Required.
     *              ]
     * @return void
     */
    public function call($data=array()){
        $soapAction = $data['action'];
        $xml = $data['xml'];
        $userID = $this->netlexSettings['opentext_uniqueid'];

        $envelope = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:ser='http://services.ofc.pic.it/'>
                    <soapenv:Header/>
                    <soapenv:Body>
                        <ser:$soapAction>
                            <arg0>
                                <userID>$userID</userID>
                                $xml
                            </arg0>
                        </ser:$soapAction>
                    </soapenv:Body>
                    </soapenv:Envelope>";

        $curlHeader = array(
            'Content-type: text/xml; charset="UTF-8"',
//            "SOAPAction: 'http://services.ofc.pic.it/$soapAction'",
            "Content-length: ".strlen($envelope),
        );


		$curlParams = array(
            // ENDPOINT TEST
			// CURLOPT_URL => 'https://dms-intq.enel.com/OfcWs2/ofcService',
			// CURLOPT_URL => 'https://dmsof.enel.com/OfcWs/ofcService',
            CURLOPT_URL => 'https://dmsof.openfiber.it/OfcWs/ofcService',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $envelope,
            CURLOPT_HTTPHEADER => $curlHeader,
		);
		
		$curl = curl_init();
		curl_setopt_array($curl, $curlParams);
		$response = curl_exec($curl);
		$info = curl_getinfo($curl);
        $error = curl_error($curl);
        curl_close($curl);

        if(!isset($info) || $info['http_code'] != 200){
            return array();
        }
        $xmlString = preg_replace("/(<\/?)(\w+):([^>]*>)/", "$1$2$3", $response);
        $xml = new SimpleXMLElement($xmlString);
        $xml = json_decode(json_encode((array) $xml), 1);
        // $this->applogger->info(print_r($response,1));
        $response = 'ns3' . $soapAction . 'Response';
        $xml = $xml['SBody'][$response]['return'];

        $this->handleResponse($soapAction, $data['request'], $xml);

		//Aggiungo il redirect_url in caso di download del file
		// if($info['redirect_url']){
        //     $response = json_decode($response,1);
        //     $response['download_url'] = $info['redirect_url'];
        //     $response = json_encode($response);
        // }

        return $xml;
    }
        
    /**
     * insert
     * Inserimento nuovo documento su OpenText.
     *
     * @param  mixed $upload_handler
     * @param  array $file = [
     *                  'fileUid' => (string) Uniqueid del documento.
     *                  'fileId' => (integer) Id della pratica in cui è presente il documento. Required.
     * 					'fileName' => (string) Nome del file. Required.
     *                  'fileSize' => (integer) Dimensione del file in bytes. Required.
     *                  'filePath' => (string) Percorso temporaneo del file. Required.
     *                  'openTextPath' => (string) Percorso dove verrà salvato il file. Required.
     *              ]
     * @return array
     */
    public function insert($upload_handler, array $file){
        $xml = '<name>' . $file['fileName'] . '</name>';
        $path = $this->netlexSettings['opentext_root_folder'];
        if(!empty($file['openTextPath'])){
            $path .= $file['openTextPath'];
        }
        $xml .= "<path>$path</path>";
        if(!empty($file['fileId']) || !empty($file['group'])){
            $xml .= '<category>BSN</category>';
        }
        if(!empty($file['fileId'])){
            $xml .= '<metadata>
                        <field>numPratica</field>
                        <value>' . $file['fileId'] . '</value>
                    </metadata>';
        }
        if(!empty($file['group'])){
            $xml .= '<metadata>
                        <field>gruppo</field>
                        <value>' . $file['group'] . '</value>
                    </metadata>';
        }
        $handle = fopen($file['filePath'], 'rb');
        $xml .= '<attach>' . base64_encode(fread($handle, $file['fileSize'])) . '</attach>';
        
        $data = array(
            "action" => 'insertDocument',
            "xml" => $xml,
            "request" => $file
        );
        $response = $this->call($data);

        if (method_exists($upload_handler, 'getUniqueid')) {
            $doc_uniqueid = $upload_handler->getUniqueid();
        }else{
            $doc_uniqueid = !empty($upload_handler) ? $upload_handler->getDocUniqueid() : $file['fileUid'];
        }

        if(isset($response['id']) && !empty($doc_uniqueid)){
            $this->db->update("documento",
                array(
                    "opentext_uniqueid" => $response['id'],
                    "size" => $file['fileSize'],
                    "nomefile" => $file['fileName']),
                array("uniqueid = ?" => $doc_uniqueid)
            );
        }
        //in caso di errore cancello la riga che ha creato l'upload handler
//      elseif(!empty($upload_handler)){
//            $output = json_decode(ob_get_clean());
//            $output->files[0]->error = 1;
//            $output->files[0]->errorMessage = htmlentities($response['message']);
//			echo json_encode($output);
//            $this->db->delete("documento", array("uniqueid = ?" => $doc_uniqueid));
//        }
        return $response;
    }
    
    /**
     * update
     *
     * @return void
     */
    public function update($docUid, array $metadata){
        $xml = "<id>$docUid</id>";
        foreach($metadata as $key => $value){
            $xml .= "<metadata>
                        <field>$key</field>
                        <value>$value</value>
                    </metadata>";
        }

        $data = array(
            "action" => 'updateDocument',
            "xml" => $xml,
            "request" => array("docUid" => $docUid, "metadata" => $metadata)
        );
        $response = $this->call($data);
        return $response;
    }
    
    /**
     * delete
     *
     * @param  mixed $docUid
     * @return void
     */
    public function delete($docUid){
        $xml = "<id>$docUid</id>";

        $data = array(
            "action" => 'DeleteDocument',
            "xml" => $xml,
            "request" => $docUid
        );
        $response = $this->call($data);
        return $response;
    }
    
    /**
     * download
     *
     * @return void
     */
    public function download($docUid){
        $xml = "<id>$docUid</id>";

        $data = array(
            "action" => "DownloadFile",
            "xml" => $xml,
            "request" => $docUid
        );
        $response = $this->call($data);
        return $response;
    }
    
    /**
     * checkDowload
     *
     * @return void
     */
    public function checkDownload($ticketId){
        $xml = "<ticketID>$ticketId</ticketID>";

        $data = array(
            "action" => "CheckDownload",
            "xml" => $xml,
            "request" => $ticketId
        );
        $response = $this->call($data);
        return $response;
    }
        
    /**
     * createFolder
     *
     * @return void
     */
    public function createFolder($folder){
        $path = $this->netlexSettings['opentext_root_folder'];
        if(isset($folder['folderPath']) && !empty($folder['folderPath'])){
            $path .= $folder['folderPath'];
        }
        $xml = "<nameFolder>" . $folder['folderName'] . "</nameFolder>
                <path>$path</path>";
        
        $data = array(
            "action" => 'CreateFolder',
            "xml" => $xml,
            "request" => $folder
        );
        $response = $this->call($data);
        return $response;
    }

    public function getDocumentList($path){
        $path = $this->netlexSettings['opentext_root_folder'] . (isset($path)) ? $path : '';
        $xml = "<path>$path</path>";

        $data = array(
            "action" => 'GetDocumentList',
            "xml" => $xml,
            "request" => $path
        );
        $response = $this->call($data);
    }

    public function getDocumentId($name, $path){
        $path = $this->netlexSettings['opentext_root_folder'] . ((isset($path)) ? $path : '');
        $xml = "<name>$name</name>
                <path>$path</path>";
                $this->applogger->info(print_r($path,1));
        
        $data = array(
            "action" => 'getDocumentId',
            "xml" => $xml,
            "request" => array("name" => $name, "path" => $path)
        );
        $response = $this->call($data);
        return $response;
    }
        
    /**
     * handleResponse
     *
     * @param  mixed $action
     * @param  mixed $response
     * @return void
     */
    public function handleResponse($action, $request, $response){

//        $this->applogger->info("*** Opentext response" . ((!empty($this->subdomain)) ? " for " . $this->subdomain : "") . ", action: " . $action . ", user_id: " . (!empty($this->loggedUser) ? $this->loggedUser->id : '') . " ***");
        
        $codeDescription = isset($this->codes[$response['code']]) ? $this->codes[$response['code']] : $this->codes[0];
        $this->applogger->info("$action: $codeDescription");
        //Operazione eseguita con successo
        if($response['code'] == 1){
            switch ($action) {
                case 'insertDocument':
                    $logOK = ((isset($request['moved']) && $request['moved']) ? "SPOSTATO SU OPENTEXT" : "INSERITO") . " DOCUMENTO CON ID " . $response['id'];
                    break;
                case 'CreateFolder':
                    $path = $this->netlexSettings['opentext_root_folder'];
                    if(isset($request['folderPath']) && !empty($request['folderPath'])){
                        $path .= $request['folderPath'];
                    }
                    $logOK = "CREATA FOLDER CON NAME " . $request['folderName'] . " SUL PATH " . $path;
                    break;
                case 'DeleteDocument':
                    $logOK = "CANCELLATO DOCUMENTO CON ID " . $request;
                    break;
                case 'updateDocument':
                    $logOK = "MODIFICATO DOCUMENTO CON ID " . $request['docUid'] . " CON I SEGUENTI METADATI \n";
                    foreach($request['metadata'] as $key => $value){
                        $logOK .= $key . ": " . $value . '\n'; 
                    }
                    break;
                case 'DownloadFile':
                    $logOK = "DOWNLOAD DEL DOCUMENTO CON ID " . $request;
                    if(isset($response['ticketID']) && !empty($response['ticketID'])){
                        $logOK .= "\n\nINIZIATO DOWNLOAD ASINCRONO CON TICKET ID " . $response['ticketID'];
                    }
                    break;
                case 'CheckDownload':
                    $logOK = "RICHIESTA DI DOWNLOAD ASINCRONO PRONTA PER TICKET ID " . $request;
                    break;
                case 'getDocumentId':
                    $logOK = "FILE CON NOME " . $request['name'] . " ESISTENTE SUL PATH " . $request['path'] . " CON ID " . $response['id'];
                    break; 
            }
            $this->applogger->info($logOK);
        }
        if(isset($response['message'])){
            $this->applogger->info("Message: " . $response['message']);
        }
        $this->applogger->info("*** END Opentext response ***");
    }
    
}