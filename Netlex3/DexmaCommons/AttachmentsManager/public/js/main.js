/***
 * Crea l'upload
 * 
 * @param refererUid riferimento dell'oggetto a cui verranno collegati gli allegati 
 * @param maxNumberOfFiles numero massimo di file che possono essere allegati
 */
function createUpload(refererUid, maxNumberOfFiles) {
    'use strict';
    // Initialize the jQuery File Upload widget:
    $('#fileupload').fileupload(); 
    // Enable iframe cross-domain access via redirect option:
    $('#fileupload').fileupload('option', 'redirect', window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s'));
    if(maxNumberOfFiles != null && maxNumberOfFiles != '') {
    	$('#fileupload').fileupload('option', 'maxNumberOfFiles', parseInt(maxNumberOfFiles));
    }
    if(refererUid != null && refererUid != '') {
    	loadExistingFiles();
    }
}
/**
 * Svuota, soltanto a video, la lista degli allegati
 */
function clearGallery() {
	$('#fileuploadGallery').html('');
}
/**
 * Carica gli allegati, tramite lo uniqueid, dell'oggeto a cui sono collegati
 */
function loadExistingFiles() {
	//Load Existing Files
	$('#fileupload').each(function () {
		var that = this;
	    $.getJSON(this.action, $('#fileupload').serialize(), function (result) {
	    	if (result && result.length) {
	    		$(that).fileupload('option', 'done').call(that, null, {result: result});
	        }
	    });
	});
}
/**
 * Imposta uno specifico refererUid, utile per cambiare lo uniqueid di riferimento
 * 
 * @param refererUid
 */
function setRefererUid(refererUid) {
	$('#refererUid').val(refererUid);
}
/**
 * Imposta uno specifico tableName, utile per cambiare il nome della tabella di riferimento
 * 
 * @param tableName
 */
function setTableName(tableName) {
	$('#tableName').val(tableName);
}
