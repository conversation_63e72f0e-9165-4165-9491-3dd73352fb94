@charset 'UTF-8';
/*
 * jQuery File Upload UI Plugin CSS 6.3
 * https://github.com/blueimp/jQuery-File-Upload
 *
 * Copyright 2010, <PERSON>
 * https://blueimp.net
 *
 * Licensed under the MIT license:
 * http://www.opensource.org/licenses/MIT
 */

.fileinput-button {
  position: relative;
  overflow: hidden;
  float: left;
  margin-right: 4px;
}
.fileinput-button input {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  border: solid transparent;
  border-width: 0 0 100px 200px;
  opacity: 0;
  filter: alpha(opacity=0);
  -moz-transform: translate(-300px, 0) scale(4);
  direction: ltr;
  cursor: pointer;
}
.fileupload-buttonbar .toggle{vertical-align:middle}
.files .progress{width:150px}
.progress-animated .bar {
  background: url(../images/progressbar.gif) !important;
  filter: none;
}
.fileupload-loading {
  position: absolute;
  left: 50%;
  width: 128px;
  height: 128px;
  background: url(../images/loading.gif) center no-repeat;
  display: none;
}
.fileupload-processing .fileupload-loading{display:block}

/* Fix for IE 6: */
*html .fileinput-button {
  line-height: 22px;
  margin: 1px -3px 0 0;
}

/* Fix for IE 7: */
*+html .fileinput-button {
  margin: 1px 0 0 0;
}

.container{width:auto}
.files{font-size:14px}
.template-upload, .template-download{border-top:1px solid #CCCCCC}
.template-download:HOVER, .template-download:nth-child(odd):HOVER{background-color:#F5F5F5}
.template-upload > div,.template-download > div{box-sizing:border-box;-moz-box-sizing:border-box;display:inline-block;padding:8px;line-height:18px;text-align:left;vertical-align:top}
.template-upload div.preview,.template-download div.preview{width:100px}
.template-upload div.name,.template-download div.name{width:250px;word-wrap:break-word}
.template-upload div.size,.template-download div.size{width:85px}
.template-upload div.progressBar{width:165px}
.dialog .template-upload div.progressBar{width:160px}
.template-upload div.error,.template-download div.error,.template-download div.progressBar{width:265px}
.dialog .template-upload div.error,.dialog .template-download div.error,.dialog .template-download div.progressBar{width:260px}
.template-upload div.start{width:100px}
.template-upload div.cancel,.template-download div.delete{width:140px}
.template-download div.delete input[type="checkbox"]{vertical-align:middle}
.template-download:nth-child(odd){background-color:#F9F9F9}
.page-header{border-bottom:0 none;margin:0}

@media (max-width: 480px) {
  .files .btn span {
    display: none;
  }
  .files .preview * {
    width: 40px;
  }
  .files .name * {
    width: 80px;
    display: inline-block;
    word-wrap: break-word;
  }
  .files .progress {
    width: 20px;
  }
  .files .delete {
    width: 60px;
  }
}
