{literal}
<script>
$(function() {
	createUpload('{/literal}{$refererUid}{literal}', '{/literal}{$maxNumberOfFiles}{literal}');
	
	$("body").append('<div id="modal-gallery" class="modal modal-gallery hide fade" data-filter=":odd">' +
    				 '    <div class="modal-header">' +
        			 '    <a class="close" data-dismiss="modal">&times;</a>' + 
        			 '    <h3 class="modal-title"></h3>' + 
        			 '</div>' + 
					 '<div class="modal-body"><div class="modal-image"></div></div>' +
					 '    <div class="modal-footer">' +
					 '        <a class="btn modal-download" target="_blank">' +
					 '            <i class="icon-download"></i>' +
					 '     		  <span>{t}Scarica{/t}</span>' +
					 '		  </a>' + 
					 '		  <a class="btn btn-success modal-play modal-slideshow" data-slideshow="5000">' +
					 '            <i class="icon-play icon-white"></i>' +
					 '            <span>Slideshow</span>' +
					 '        </a>' +
					 '        <a class="btn btn-info modal-prev">' +
					 '            <i class="icon-arrow-left icon-white"></i>' +
					 '            <span>{t}Precedente{/t}</span>' +
					 '        </a>' +
					 '        <a class="btn btn-primary modal-next">' +
					 '            <span>{t}Successivo{/t}</span>' +
					 '            <i class="icon-arrow-right icon-white"></i>' +
					 '        </a>' +
					 '    </div>' +
					 '</div>');
});
</script>
{/literal}
<link rel="stylesheet" href="/public/AttachmentsManager/css/bootstrap-image-gallery.min.css" />
<link rel="stylesheet" href="/public/AttachmentsManager/css/jquery.fileupload-ui.css" />
<div class="container">
    <div class="page-header"></div>
    <form id="fileupload" action="/attachments/attachments" method="POST" enctype="multipart/form-data">
    	<input type="hidden" id="refererUid" name="refererUid" value="{$refererUid}" />
    	<input type="hidden" id="tableName" name="tableName" value="{$tableName}" />
        <div class="row fileupload-buttonbar">
            <div class="span8">
                <span class="btn btn-success fileinput-button">
                    <i class="icon-plus icon-white"></i>
                    <span>{t}Aggiungi file{/t}</span>
                    <input type="file" name="files[]" multiple>
                </span>
                <button type="submit" class="btn btn-primary start">
                    <i class="icon-upload icon-white"></i>
                    <span>{t}Avvia caricamento{/t}</span>
                </button>
                <button type="reset" class="btn btn-warning cancel">
                    <i class="icon-ban-circle icon-white"></i>
                    <span>{t}Annulla caricamento{/t}</span>
                </button>
                <button type="button" class="btn btn-danger delete">
                    <i class="icon-trash icon-white"></i>
                    <span>{t}Cancella{/t}</span>
                </button>
                <input type="checkbox" class="toggle">
            </div>
            <div class="span2 fileupload-progress fade">
                <div class="progress progress-success progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100">
                    <div class="bar" style="width:0%;"></div>
                </div>
                <div class="progress-extended">&nbsp;</div>
            </div>
        </div>
        <div class="fileupload-loading"></div>
        <br>        
        <div role="presentation" class="table dialog table-striped">
        	<div id="fileuploadGallery" class="files" data-toggle="modal-gallery" data-target="#modal-gallery"></div>
        </div>
    </form>   
</div>
{literal}
<!-- The template to display files available for upload -->
<script id="template-upload" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <div class="template-upload fade">
        <div class="preview"><span class="fade"></span></div>
        <div class="name"><span>{%=file.name%}</span></div>
        <div class="size"><span>{%=o.formatFileSize(file.size)%}</span></div>
        {% if (file.error) { %}
            <div class="error"><span class="label label-important">{%=locale.fileupload.error%}</span> {%=locale.fileupload.errors[file.error] || file.error%}</div>
        {% } else if (o.files.valid && !i) { %}
            <div class="progressBar">
               	<div class="progress progress-success progress-sdiviped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="bar" style="width:0%;"></div></div>
           	</div><div class="start">{% if (!o.options.autoUpload) { %}
               	<button class="btn btn-primary">
                   	<i class="icon-upload icon-white"></i>
                   	<span>{%=locale.fileupload.start%}</span>
               	</button>
           	{% } %}</div>
        {% } %}
        <div class="cancel">{% if (!i) { %}
            <button class="btn btn-warning">
                <i class="icon-ban-circle icon-white"></i>
                <span>{%=locale.fileupload.cancel%}</span>
            </button>
        {% } %}</div>
    </div>
{% } %}
</script>
<!-- The template to display files available for download -->
<script id="template-download" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <div class="template-download fade">
        {% if (file.error) { %}
            <div class="preview"></div>
            <div class="name"><span>{%=file.name%}</span></div>
            <div class="size"><span>{%=o.formatFileSize(file.size)%}</span></div>
            <div class="error"><span class="label label-important">{%=locale.fileupload.error%}</span> {%=locale.fileupload.errors[file.error] || file.error%}</div>
        {% } else { %}
            <div class="preview">{% if (file.thumbnail_url) { %}
                <a href="{%=file.url%}" title="{%=file.name%}" rel="gallery" download="{%=file.name%}"><img src="{%=file.thumbnail_url%}"></a>
            {% } %}</div>
            <div class="name">
                <a href="{%=file.url%}" title="{%=file.name%}" rel="{%=file.thumbnail_url&&'gallery'%}" download="{%=file.name%}">{%=file.name%}</a>
            </div>
            <div class="size"><span>{%=o.formatFileSize(file.size)%}</span></div>
            <div class="progressBar"></div>
        {% } %}
        <div class="delete">
            <button class="btn btn-danger" data-type="{%=file.delete_type%}" data-url="{%=file.delete_url%}">
                <i class="icon-trash icon-white"></i>
                <span>{%=locale.fileupload.destroy%}</span>
            </button>
            <input type="checkbox" name="delete" value="1">
        </div>
    </div>
{% } %}
</script>
{/literal}
<script src="/public/AttachmentsManager/js/tmpl.min.js"></script>
<script src="/public/AttachmentsManager/js/load-image.min.js"></script>
<script src="/public/AttachmentsManager/js/canvas-to-blob.min.js"></script>
<script src="/public/js/bootstrap.min.js"></script>
<script src="/public/AttachmentsManager/js/bootstrap-image-gallery.min.js"></script>
<script src="/public/AttachmentsManager/js/jquery.iframe-transport.js"></script>
<script src="/public/AttachmentsManager/js/jquery.fileupload.js"></script>
<script src="/public/AttachmentsManager/js/jquery.fileupload-fp.js"></script>
<script src="/public/AttachmentsManager/js/jquery.fileupload-ui.js"></script>
<script src="/public/AttachmentsManager/js/locale.js"></script>
<script src="/public/AttachmentsManager/js/main.js"></script>
