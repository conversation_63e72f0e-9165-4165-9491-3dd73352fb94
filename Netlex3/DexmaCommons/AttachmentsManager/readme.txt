1 - creare l'alias nel file di configurazione di apache aggiungendo la riga: 
	 		Alias /public /home/<USER>/DexmaCommons/public

2 - eseguire lo script "attachments.dump" che creerà la tabella degli allegati;

3 - creare la action che richiama la pagina in cui è presente l'uploader;

4 - passare alla pagina attraverso Smarty le seguenti variabili:
	 		$this->view->refererUid = uniqueid dell'oggetto a cui sarà collegato l'allegato
	 		$this->view->tableName = nome delle tabella dell'oggetto a cui sarà collegato l'allegato 
	 		$this->view->dexmaCommonsPath = percorso delle Dexma Commons;
	 		$this->view->maxNumberOfFiles = numero massimo di file che possono essere allegati(da non passare alla pagina se non necessario) 
	 		N.B. se il 'refererUid' NON viene passato alla pagina il caricamento remoto degli allegati non verrà avviato in automatico e dovrà
	 			  essere gestito manualmente attraverso la chiamata al metodo 'loadExistingFiles()'.
	 			  SOLO in questa circostanza possono essere utilizzati i seguenti metodi di utilità: 
	 			  		'clearGallery()' svuota, soltanto a video, la lista degli allegati;
	 			  		'setRefererUid()' imposta uno specifico refererUid, utile per cambiare lo uniqueid di riferimento
	 			  					  		   prima di avviare il caricamento remoto degli allegati;
	 			  		'setTableName()' imposta il nome della tabella, utilizzabile come 'setRefererUid()'.

5 - nella pagina richiamata dalla action importare la pagina "uploads.tpl" aggiungendo la riga:
    		{include file="$dexmaCommonsPath/public/AttachmentsManager/uploads.tpl"}
    N.B. nei casi in cui la pagina deve essere importata all'interno di una dialog Jquery bisogna utilizzare la seguente riga:
    		{include file="$dexmaCommonsPath/public/AttachmentsManager/uploads.ui.dialog.tpl"}

6 - se non presente tra i .js importare il file "jquery.ui.widget.js";
    
7 - creare il controller per gli allegati, in cui dovrà essere presente la seguente struttura minimale:
			<?php
				class AttachmentsController extends CustomControllerAction
				{
					public function attachmentsAction() 
					{
						//Validazione utente se richiesta
						require_once(DEXMA_COMMONS . '/AttachmentsManager/UploadHandler.php');

						$upload_handler = new UploadHandler(null, $this->dexmaCommons, $this->db);
		
						$upload_handler->manager();
		
						$this->_helper->viewRenderer->setNoRender(true);
					}
				}
			?>

8 - il costruttore dell'oggetto UploadHandler ha come primo parametro l'array delle opzioni che possono essere personalizzate all'occorrenza.
	 Per far ciò è necessario creare un ARRAY SOLO con le opzioni che si vogliono personalizzare tra quelle presenti nell'elenco sottostante:	 		
			'max_file_size' => null,
			'min_file_size' => 1,			
			'max_number_of_files' => null,
			// Image resolution restrictions:
			'max_width' => null,
			'max_height' => null,
			'min_width' => 1,
			'min_height' => 1
			
9 - nel progetto DEVE essere presente la cartella trunk/Software/data/uploads con permessi di lettura e scrittura;

10 - importare come primi files css, quelli di Bootstrap, aggiungendo le righe di codice:
	<link href="/public/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="/public/bootstrap/css/bootstrap-responsive.min.css" rel="stylesheet">

	<style type="text/css">
	 body {
    	  padding-top: 60px;
          padding-bottom: 40px;
    	 }
    	 legend {
    	  margin-bottom: 0px;
    	 }
	</style> 

11 - versione jquery: da 1.7.2 in poi;

12 - necessaria le presenza della classe 'UploadHandler.php' nelle DexmaCommonos;

13 - se tutto è configurato bene potrete allegare file trovando: 1 - la tupla nella base di dati
								 													  2 - il file nella cartella /trunk/Software/data/uploads/tableName/refererUid
								 													  
13 - in caso di eliminazione dell'oggetto a cui sono collegati ancora degli allegati, bisogna invocare(come prima operazione del metodo delete) 
	  il metodo statico UploadHandler::removeAttachments($tableName, $refererUid, $db, $applogger) per eliminare definitivamente tutti gli allegati. 
	  In caso di esito positivo sarà restituito il valore true, false altrimenti.