<?php

namespace DexmaCom<PERSON>\Modelli;

use Carbon\Carbon;
use DexmaCommons\OneDriveManager\OneDriveHandler;
use Netlex3\Software\patterns\Domains\AnagraficaPratica\Services\AnagraficaPraticaService;
use Netlex3\Software\patterns\Domains\Anagrafiche\Services\AnagraficaService;
use Netlex3\Software\patterns\Domains\ArchiveClaims\Enums\PayerType;
use Netlex3\Software\patterns\Domains\ArchiveClaims\Enums\PaymentType;
use Netlex3\Software\patterns\Domains\ArchiveClaims\Services\ClaimInterestService;
use Netlex3\Software\patterns\Domains\ArchiveClaims\Services\ClaimPaymentsService;
use Netlex3\Software\patterns\Domains\ArchiveClaims\Services\ClaimsService;
use Netlex3\Software\patterns\Domains\Nazioni\Classes\Nazione;
use Netlex3\Software\patterns\Domains\Pct\Classes\PctSiecicTipologiaClasseImmobiliare;
use Netlex3\Software\patterns\Domains\Users\Services\UsersService;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;

use DexmaCommons\PrintsManager\clsTinyButStrong;
use Netlex3\Software\patterns\Builders\DocumentHttpRequestBuilder;
use Netlex3\Software\patterns\Classes\DocumentAbstract;
use Netlex3\Software\patterns\Repositories\ArchiveRepository;
use S3Connector_S3Connector;
use Util\Utility;
use Exception;
use Netlex3\Software\patterns\Repositories\Settings\SettingsRepository;

final class Engine
{
    private $db;
    private $dbProvisioning;
    private $dbShared;

    /** @var S3Connector_S3Connector */
    private $s3Connector;
    private $loggedUser;
    private $sitekey;
    private $main_bucket;


    /*public static $MAIN_BUCKET = "netlex-main";*/
    public static $RECYCLE_BIN_BUCKET = 'netlex-recycle-bin';
    public static $DOCUMENTS = "documents";
    protected static $TEMPLATE_DIR = "templates";
    public static $LETTERHEADS = 'letterheads';


    private static $EMPTY_VALUE = "..............................";
    /** Prefisso pratica **/
    private static $PRATICA_ATTIVITA_PROSSIMA_UDIENZA = "ATTIVITA_PROSSIMA_UDIENZA";
    private static $PRATICA_ATTIVITA_ULTIMA_UDIENZA = "ATTIVITA_ULTIMA_UDIENZA";
    private static $PRATICA_NOME = "NOME";
    private static $PRATICA_CODICE = "CODICE";
    private static $PRATICA_CODICE_ARCHIVIO = "CODICE_ARCHIVIO";
    private static $PRATICA_CATEGORIA = "CATEGORIA";
    private static $PRATICA_ANNOTAZIONE = "ANNOTAZIONE";
    private static $PRATICA_DATA_OGGI = "DATA_OGGI";
    private static $PRATICA_DATA_APERTURA = "DATA_APERTURA";
    private static $PRATICA_DATA_PROSSIMA_UDIENZA = "DATA_PROSSIMA_UDIENZA";
    private static $PRATICA_DATA_PROSSIMA_UDIENZA_EVASA = "DATA_PROSSIMA_UDIENZA_EVASA";
    private static $PRATICA_DATA_REATO = "DATA_REATO";
    private static $PRATICA_DATA_ULTIMA_UDIENZA = "DATA_ULTIMA_UDIENZA";
    private static $PRATICA_DATA_ULTIMA_UDIENZA_EVASA = "DATA_ULTIMA_UDIENZA_EVASA";
    private static $PRATICA_DECRETO_INGIUNTIVO = "DECRETO_INGIUNTIVO";
    private static $PRATICA_NOME_AUTORITA = "AUTORITA";
    private static $PRATICA_NOME_AUTORITA_PROSSIMA_UDIENZA = "AUTORITA_PROSSIMA_UDIENZA";
    private static $PRATICA_NOME_AUTORITA_ULTIMA_UDIENZA = "AUTORITA_ULTIMA_UDIENZA";
    private static $PRATICA_NOME_CITTA = "CITTA";
    private static $PRATICA_NOME_CITTA_PROSSIMA_UDIENZA = "CITTA_PROSSIMA_UDIENZA";
    private static $PRATICA_NOME_CITTA_ULTIMA_UDIENZA = "CITTA_ULTIMA_UDIENZA";
    private static $PRATICA_NOME_GIUDICE = "GIUDICE";
    private static $PRATICA_NOME_ISTRUTTORE = "ISTRUTTORE";
    private static $PRATICA_SEZIONE = "SEZIONE";
    private static $PRATICA_OGGETTO = "OGGETTO";
    private static $PRATICA_DATA_ISCRIZIONE_RUOLO = 'DATA_ISCRIZIONE_RUOLO';
    private static $PRATICA_ORA_PROSSIMA_UDIENZA = "ORA_PROSSIMA_UDIENZA";
    private static $PRATICA_ORA_ULTIMA_UDIENZA = "ORA_ULTIMA_UDIENZA";
    private static $PRATICA_RG = "RG";
    private static $PRATICA_RIFERIMENTO_TEMPORALE_UTC = "RIFERIMENTO_TEMPORALE_UTC";
    private static $PRATICA_TIPOLOGIA = "TIPOLOGIA";
    private static $PRATICA_VALORE = "VALORE";
    private static $PRATICA_SEDE = "SEDE";
    private static $PRATICA_PALCHETTO = "PALCHETTO";
    private static $PRATICA_FALDONE = "FALDONE";
    private static $PRATICA_SENTENZA = "SENTENZA";
    private static $PRATICA_DATA_SENTENZA = "DATA_SENTENZA";
    private static $PRATICA_DATA_DECRETO_INGIUNTIVO = "DATA_DECRETO_INGIUNTIVO";
    private static $PRATICA_DATA_CHIUSURA = "DATA_CHIUSURA";
    private static $PRATICA_DATA_ARCHIVIO = "DATA_ARCHIVIO";
    private static $PRATICA_DESCRIZIONE = "DESCRIZIONE";
    private static $PRATICA_PROTOCOLLO_GENERALE = "PROTOCOLLO_GENERALE";
    private static $PRATICA_COLLABORATORI = "COLLABORATORI";
    private static $PRATICA_EMISSIONE_DI = "EMISSIONE_DI";
    private static $PRATICA_NOTIFICA_DI = "NOTIFICA_DI";
    private static $PRATICA_DECRETO_ESECUTORIETA = "DECRETO_ESECUTORIETA";
    private static $PRATICA_FORMULA_ESECUTIVA = "FORMULA_ESECUTIVA";
    private static $PRATICA_NOTIFICA_PRECETTO = "NOTIFICA_PRECETTO";
    /** Prefisso pratica (penale) **/
    private static $PRATICA_REATO = "REATO";
    private static $PRATICA_RGAPP = "RGAPP";
    private static $PRATICA_RGCASS = "RGCASS";
    private static $PRATICA_RGGIP = "RGGIP";
    private static $PRATICA_RGGUP = "RGGUP";
    private static $PRATICA_RGNR = "RGNR";
    private static $PRATICA_RGRIESAME = "RGRIESAME";
    private static $PRATICA_RGSIEP = "RGSIEP";
    private static $PRATICA_RGSIUS = "RGSIUS";
    private static $PRATICA_RGTRIB = "RGTRIB";
    private static $PRATICA_PUBBLICO_MINISTERO = "PUBBLICO_MINISTERO";
    private static $PRATICA_STATO = "STATO";
    /** Prefisso avvocato **/
    private static $AVVOCATO_CAP = "CAP";
    private static $AVVOCATO_CITTA = "CITTA";
    private static $AVVOCATO_CODICE_FISCALE = "CODICE_FISCALE";
    private static $AVVOCATO_EMAIL = "EMAIL";
    private static $AVVOCATO_FAX = "FAX";
    private static $AVVOCATO_INDIRIZZO = "INDIRIZZO";
    private static $AVVOCATO_MOBILE = "MOBILE";
    private static $AVVOCATO_NOME = "NOME";
    private static $AVVOCATO_NOME_COGNOME = "NOME_COGNOME";
    private static $AVVOCATO_NOME_STUDIO = "NOME_STUDIO";
    private static $AVVOCATO_PARTITA_IVA = "PARTITA_IVA";
    private static $AVVOCATO_PEC = "PEC";
    private static $AVVOCATO_PROVINCIA = "PROVINCIA";
    private static $AVVOCATO_TELEFONO = "TELEFONO";
    /** Prefissi cliente - controparte **/
    private static $PARTE_CAP = "CAP";
    private static $PARTE_NOME_CITTA = "CITTA";
    private static $PARTE_NOME_CITTA_NASCITA = "CITTA_NASCITA";
    private static $PARTE_CODICE_FISCALE = "CODICE_FISCALE";
    private static $PARTE_DATA_NASCITA = "DATA_NASCITA";
    private static $PARTE_EMAIL = "EMAIL";
    private static $PARTE_FAX = "FAX";
    private static $PARTE_INDIRIZZO = "INDIRIZZO";
    private static $PARTE_MOBILE_1 = "MOBILE_1";
    private static $PARTE_MOBILE_2 = "MOBILE_2";
    private static $PARTE_NOME = "NOME";
    private static $PARTE_PARTITA_IVA = "PARTITA_IVA";
    private static $PARTE_PEC = "PEC";
    private static $PARTE_NOME_PEC = "NOME_PEC";
    private static $PARTE_PROVINCIA = "PROVINCIA";
    private static $PARTE_PROVINCIA_NASCITA = "PROVINCIA_NASCITA";
    private static $PARTE_TELEFONO_CASA = "TELEFONO_CASA";
    private static $PARTE_TELEFONO_UFFICIO = "TELEFONO_UFFICIO";
    private static $PARTE_TIPO = "TIPO";
    private static $CODICE_ESTERNA = "CODICE_ESTERNA";
    private static $PARTE_COMUNE = "COMUNE";
    private static $PARTE_SEZIONE = "SEZIONE";
    private static $PARTE_NOTA = "NOTA";
    private static $PARTE_NUMEROREA = "NUMEROREA";
    private static $PARTE_DATAISCRIZIONE = "DATAISCRIZIONE";
    private static $PARTE_DELIBERATO = "DELIBERATO";
    private static $PARTE_SOTTOSCRITTO = "SOTTOSCRITTO";
    private static $PARTE_VERSATO = "VERSATO";
    private static $PARTE_VALUTA = "VALUTA";
    private static $PARTE_IBAN = "IBAN";
    private static $PARTE_IMMOBILE_DESCRIZIONE = "IMMOBILE_DESCRIZIONE";
    private static $PARTE_IMMOBILE_INDIRIZZO = "IMMOBILE_INDIRIZZO";
    private static $PARTE_IMMOBILE_CIVICO = "IMMOBILE_CIVICO";
    private static $PARTE_IMMOBILE_CITTA = "IMMOBILE_CITTA";
    private static $PARTE_IMMOBILE_CAP = "IMMOBILE_CAP";
    private static $PARTE_IMMOBILE_PROVINCIA = "IMMOBILE_PROVINCIA";
    private static $PARTE_IMMOBILE_NAZIONE = "IMMOBILE_NAZIONE";
    private static $PARTE_IMMOBILE_VALORE = "IMMOBILE_VALORE";
    private static $PARTE_IMMOBILE_TIPOCATASTO = "IMMOBILE_TIPOCATASTO";
    private static $PARTE_IMMOBILE_CLASSE = "IMMOBILE_CLASSE";
    private static $PARTE_IMMOBILE_DIRITTO = "IMMOBILE_DIRITTO";
    private static $PARTE_IMMOBILE_QUOTA = "IMMOBILE_QUOTA";
    private static $DATA_DELLA_SENT = "DATA_DELLA_SENT";

    /** Prefisso documento **/
    private static $DOCUMENTO_HASH = "HASH";
    private static $DOCUMENTO_NOME = "NOME";
    /** Prefisso utente loggato **/
    private static $UTENTELOGGED_CARTA_INTESTATA = "CARTA_INTESTATA";

    /** Prefisso utente **/
    private static $UTENTE_NOME = "NOME";
    private static $UTENTE_COGNOME = "COGNOME";
    private static $UTENTE_NOME_COGNOME = "NOME_COGNOME";
    private static $UTENTE_SIGLA = "SIGLA";
    private static $UTENTE_DATA_NASCITA = "DATA_NASCITA";
    private static $UTENTE_EMAIL = "EMAIL";
    private static $UTENTE_CAP = "CAP";
    private static $UTENTE_CITTA = "CITTA";
    private static $UTENTE_CODICE_FISCALE = "CODICE_FISCALE";
    private static $UTENTE_FAX = "FAX";
    private static $UTENTE_INDIRIZZO = "INDIRIZZO";
    private static $UTENTE_MOBILE = "MOBILE";
    private static $UTENTE_NOME_STUDIO = "NOME_STUDIO";
    private static $UTENTE_PARTITA_IVA = "PARTITA_IVA";
    private static $UTENTE_PEC = "PEC";
    private static $UTENTE_PROVINCIA = "PROVINCIA";
    private static $UTENTE_TELEFONO = "TELEFONO";

    /** Prefisso recuperocrediti **/
    private static $RECUPEROCREDITI_SOGGETTO = "SOGGETTO";
    private static $RECUPEROCREDITI_SOGGETTO_NOME = "SOGGETTO_NOME";
    private static $RECUPEROCREDITI_SOGGETTO_CAP = "SOGGETTO_CAP";
    private static $RECUPEROCREDITI_SOGGETTO_CITTA = "SOGGETTO_CITTA";
    private static $RECUPEROCREDITI_SOGGETTO_PROVINCIA = "SOGGETTO_PROVINCIA";
    private static $RECUPEROCREDITI_SOGGETTO_PEC = "SOGGETTO_PEC";
    private static $RECUPEROCREDITI_INTESTATARIO = "INTESTATARIO";
    private static $RECUPEROCREDITI_DATA = "DATA";
    private static $RECUPEROCREDITI_INTERESSI = "INTERESSI";
    private static $RECUPEROCREDITI_DIRITTI = "DIRITTI";
    private static $RECUPEROCREDITI_ONORARI = "ONORARI";
    private static $RECUPEROCREDITI_TOTALE_ODI = "TOTALE_ODI";
    private static $RECUPEROCREDITI_ONERI_RIFLESSI = "ONERI_RIFLESSI";
    private static $RECUPEROCREDITI_PERC_ONERI_RIFLESSI = "PERC_ONERI_RIFLESSI";
    private static $RECUPEROCREDITI_SPESE_GENERALI = "SPESE_GENERALI";
    private static $RECUPEROCREDITI_PERC_SPESE_GENERALI = "PERC_SPESE_GENERALI";
    private static $RECUPEROCREDITI_TOTALE_OS = "TOTALE_OS";
    private static $RECUPEROCREDITI_SPESE_ESENTI = "SPESE_ESENTI";
    private static $RECUPEROCREDITI_RITENUTA_ACCONTO = "RITENUTA_ACCONTO";
    private static $RECUPEROCREDITI_PERC_RITENUTA_ACCONTO = "PERC_RITENUTA_ACCONTO";
    private static $RECUPEROCREDITI_NUMERO_FATTURA = "NUMERO_FATTURA";
    private static $RECUPEROCREDITI_DATA_SCADENZA = "DATA_SCADENZA";
    private static $RECUPEROCREDITI_DATA_SOLLECITO = "DATA_SOLLECITO";
    private static $RECUPEROCREDITI_ANNO = "ANNO";
    private static $RECUPEROCREDITI_NUMERO = "NUMERO";
    private static $RECUPEROCREDITI_TIPO_DOCUMENTO = "TIPO_DOCUMENTO";

    private static $RECUPEROCREDITI_TOTALE_DA_RECUPERARE = "TOTALE_DA_RECUPERARE";
    private static $RECUPEROCREDITI_TOTALE_INTERESSI = "TOTALE_INTERESSE";
    private static $RECUPEROCREDITI_IMPORTO_FATTURA = "IMPORTO_FATTURA";

    // lista interessi legati a un recupero crediti
    private static $RECUPERO_CREDITI_INTERESSE_DATA_INIZIO = "DATA_INIZIO";
    private static $RECUPERO_CREDITI_INTERESSE_DATA_FINE = "DATA_FINE";
    private static $RECUPERO_CREDITI_INTERESSE_TOTALE_GIORNI = "TOTALE_GIORNI";
    private static $RECUPERO_CREDITI_INTERESSE_INTERESSI = "INTERESSI";
    private static $RECUPERO_CREDITI_INTERESSE_TOTALE = "TOTALE";
    private static $RECUPERO_CREDITI_INTERESSE_DATA_CREAZIONE = "DATA_CREAZIONE";
    private static $RECUPERO_CREDITI_INTERESSE_AGGIUNTIVA = "AGGIUNTIVA";
    private static $RECUPERO_CREDITI_INTERESSE_COSTI_AGGIUNTIVI = "COSTI_AGGIUNTIVI";
    private static $RECUPERO_CREDITI_INTERESSE_IMPORTO_SOGLIA = "IMPORTO_SOGLIA";

    // lista movimenti collegati a un recupero credito
    private static $RECUPERO_CREDITI_MOVIMENTO_TIPO = "TIPO_DOCUMENTO";
    private static $RECUPERO_CREDITI_MOVIMENTO_DATA_CREAZIONE = "DATA_CREAZIONE";
    private static $RECUPERO_CREDITI_MOVIMENTO_IMPORTO = "IMPORTO";
    private static $RECUPERO_CREDITI_MOVIMENTO_CC = "CC";
    private static $RECUPERO_CREDITI_MOVIMENTO_DESCRIZIONE = "DESCRIZIONE";

    /** Prefisso contratto **/
    private const CONTRATTO_NOME = 'NOME';
    private const CONTRATTO_NUMERO = 'NUMERO';
    private const CONTRATTO_DATA_DECORRENZA = 'DATA_DECORRENZA';
    private const CONTRATTO_DATA_SCADENZA = 'DATA_SCADENZA';
    private const CONTRATTO_OGGETTO = 'OGGETTO';
    private const CONTRATTO_CATEGORIA = 'CATEGORIA';
    private const CONTRATTO_DATA_OGGI = 'DATA_OGGI';
    private const CONTRATTO_DATA_DI_RICHIESTA = 'DATA_DI_RICHIESTA';
    private const CONTRATTO_DATA_PERFEZIONAMENTO_RICHIESTA = 'DATA_PERFEZIONAMENTO_RICHIESTA';
    private const CONTRATTO_DATA_RINNOVO = 'DATA_RINNOVO';

    /** Prefisso contratto utenti **/
    private const CONTRATTO_UTENTE_EMAIL = 'EMAIL';
    private const CONTRATTO_UTENTE_PEC = 'PEC';
    private const CONTRATTO_UTENTE_CAP = 'CAP';
    private const CONTRATTO_UTENTE_CITTA = 'CITTA';
    private const CONTRATTO_UTENTE_CODICE_FISCALE = 'CODICE_FISCALE';
    private const CONTRATTO_UTENTE_FAX = 'FAX';
    private const CONTRATTO_UTENTE_INDIRIZZO = 'INDIRIZZO';
    private const CONTRATTO_UTENTE_MOBILE = 'MOBILE';
    private const CONTRATTO_UTENTE_NOME = 'NOME';
    private const CONTRATTO_UTENTE_COGNOME = 'COGNOME';
    private const CONTRATTO_UTENTE_NOME_COGNOME = 'NOME_COGNOME';
    private const CONTRATTO_UTENTE_NOME_STUDIO = 'NOME_STUDIO';
    private const CONTRATTO_UTENTE_PARTITA_IVA = 'PARTITA_IVA';
    private const CONTRATTO_UTENTE_PROVINCIA = 'PROVINCIA';
    private const CONTRATTO_UTENTE_TELEFONO = 'TELEFONO';


    /** Prefisso contratto anagrafiche **/
    private const CONTRATTO_ANAGRAFICA_DATA_NASCITA = 'DATA_NASCITA';
    private const CONTRATTO_ANAGRAFICA_EMAIL = 'EMAIL';
    private const CONTRATTO_ANAGRAFICA_PEC = 'PEC';
    private const CONTRATTO_ANAGRAFICA_CAP = 'CAP';
    private const CONTRATTO_ANAGRAFICA_CITTA = 'CITTA';
    private const CONTRATTO_ANAGRAFICA_CODICE_FISCALE = 'CODICE_FISCALE';
    private const CONTRATTO_ANAGRAFICA_FAX = 'FAX';
    private const CONTRATTO_ANAGRAFICA_INDIRIZZO = 'INDIRIZZO';
    private const CONTRATTO_ANAGRAFICA_MOBILE_1 = 'MOBILE_1';
    private const CONTRATTO_ANAGRAFICA_MOBILE_2 = 'MOBILE_2';
    private const CONTRATTO_ANAGRAFICA_NOME = 'NOME';
    private const CONTRATTO_ANAGRAFICA_COGNOME = 'COGNOME';
    private const CONTRATTO_ANAGRAFICA_DENOMINAZIONE = 'DENOMINAZIONE';
    private const CONTRATTO_ANAGRAFICA_NOME_COGNOME = 'NOME_COGNOME';
    private const CONTRATTO_ANAGRAFICA_PARTITA_IVA = 'PARTITA_IVA';
    private const CONTRATTO_ANAGRAFICA_PROVINCIA = 'PROVINCIA';
    private const CONTRATTO_ANAGRAFICA_TELEFONO_CASA = 'TELEFONO_CASA';
    private const CONTRATTO_ANAGRAFICA_TELEFONO_UFFICIO = 'TELEFONO_UFFICIO';

    /** CLAIMS */
    private const CLAIMS_NUMERO = 'NUMERO';
    private const CLAIMS_RIF_ESTERNO = 'RIF_ESTERNO';
    private const CLAIMS_DATA_SINISTRO = 'DATA_SINISTRO';
    private const CLAIMS_DATA_NOTIFICA = 'DATA_NOTIFICA';
    private const CLAIMS_IMPORTO_DANNO = 'IMPORTO_DANNO';
    private const CLAIMS_IMPORTO_DA_RECUPERARE = 'IMPORTO_DA_RECUPERARE';
    private const CLAIMS_DESCRIZIONE = 'DESCRIZIONE';

    /** CLAIM INTEREST */
    private const CLAIMS_INTEREST_AGGIUNTIVA = 'AGGIUNTIVA';
    private const CLAIMS_INTEREST_COSTI_AGGIUNTIVI = 'COSTI_AGGIUNTIVI';
    private const CLAIMS_INTEREST_DATA_INIZIO = 'DATA_INIZIO';
    private const CLAIMS_INTEREST_DATA_FINE = 'DATA_FINE';
    private const CLAIMS_INTEREST_INTERESSI = 'INTERESSI';
    private const CLAIMS_INTEREST_TOTALE = 'TOTALE';
    private const CLAIMS_INTEREST_TOTALE_GIORNI = 'TOTALE_GIORNI';

    /** CLAIMS PAYMENTS */
    private const CLAIMS_PAYMENTS_DATA_REVERSALE = 'DATA_REVERSALE';
    private const CLAIMS_PAYMENTS_DATA_VALUTA = 'DATA_VALUTA';
    private const CLAIMS_PAYMENTS_NUM_REVERSALE = 'NUM_REVERSALE';
    private const CLAIMS_PAYMENTS_NUM_CONTABILE = 'NUM_CONTABILE';
    private const CLAIMS_PAYMENTS_TIPO = 'TIPO';
    private const CLAIMS_PAYMENTS_RIF_DOCUMENTO = 'RIF_DOCUMENTO';
    private const CLAIMS_PAYMENTS_CAPITALE = 'CAPITALE';
    private const CLAIMS_PAYMENTS_INTERESSI = 'INTERESSI';
    private const CLAIMS_PAYMENTS_SPESE = 'SPESE';
    private const CLAIMS_PAYMENTS_IMPORTO = 'IMPORTO';
    private const CLAIMS_PAYMENTS_TIPO_PAGANTE = 'TIPO_PAGANTE';
    private const CLAIMS_PAYMENTS_PAGANTE = 'PAGANTE';


    /** Prefisso liquidazioni **/
//    private static $LIQUIDAZIONE_R_NUMERO = "R_NUMERO";
//    private static $LIQUIDAZIONE_R_DATA = "R_DATA";
//    private static $LIQUIDAZIONE_R_NUMERO_SID = "R_NUMERO_SID";
//    private static $LIQUIDAZIONE_R_DATA_SID = "R_DATA_SID";
//    private static $LIQUIDAZIONE_R_IMPORTO = "R_IMPORTO";
//    private static $LIQUIDAZIONE_R_MANDATO = "R_MANDATO";
//    private static $LIQUIDAZIONE_R_DATA_MANDATO = "R_DATA_MANDATO";
//    private static $LIQUIDAZIONE_R_ASSEGNATARIO = "R_ASSEGNATARIO";
//    private static $LIQUIDAZIONE_R_IMPOSTA = "R_IMPOSTA";

    /** @var OneDriveHandler */
    private $oneDriveHandler;

    /** @var ArchiveRepository */
    private $archiveRepository;

    private $infrastructure;
    /**
     * @var SettingsRepository
     */
    private $settingsRepository;
    /**
     * @var boolean
     */
    private $isPa;

    public function __construct(InfrastructureInterface $infrastructure, $s3Connector)
    {
        $this->s3Connector = $s3Connector;
        $this->db = $infrastructure->getCustomerBucket();
        $this->dbProvisioning = $infrastructure->getProvisioningBucket();
        $this->dbShared = $infrastructure->getSharedBucket();

        $this->loggedUser = $infrastructure->getUser();
        $this->sitekey = $infrastructure->getCustomerName();
        $this->main_bucket = $infrastructure->getConfig()->awsBucketName;

        $this->oneDriveHandler = new OneDriveHandler($infrastructure->getCustomerBucket(), null, $this->sitekey, $infrastructure->getUser());

        $this->archiveRepository = new ArchiveRepository($infrastructure);

        $this->settingsRepository = new SettingsRepository();
        $settings = $this->settingsRepository->getAllSettings();
        $this->isPa = $settings['liquidationPA'];

        $this->infrastructure = $infrastructure;
        unset($settings);

    }


    public function work($params, $forceContent = false)
    {
        try {
            $fUid = $params['fUid'] ?? null;
            $contractId = $params['contractId'] ?? null;
            $tUid = $params['tUid'] ?? null;

            $tags = [];

            if ($tUid && ($fUid || $contractId)) {
                $template = 'SELECT id, title, filename
							  FROM templates
							  WHERE uniqueid = ?';
                $template = $this->dbProvisioning->fetchRow($template, array($tUid));
                if (!empty($params['nomemodello'])) {
                    $template['title'] = $params['nomemodello'];
                }
                include_once("tbs_class.php");
                include_once("tbs_plugin_opentbs.php");
                $s3Connector = $this->s3Connector;
                $file = $s3Connector->getBody($this->main_bucket, self::$TEMPLATE_DIR . "/" . $template["filename"]);
                $tmpDir = sys_get_temp_dir() . "/" . $this->sitekey . "-" . self::$TEMPLATE_DIR . "-" . $template["id"] . "-" . $this->dbShared->getUid() . "-" . $this->dbShared->getUid() . "-" . $this->dbShared->getUid();
                $handle = fopen($tmpDir, "w");
                fwrite($handle, $file);
                fclose($handle);
                $TBS = new clsTinyButStrong;
                $TBS->Plugin(TBS_INSTALL, OPENTBS_PLUGIN);
                $TBS->LoadTemplate($tmpDir, OPENTBS_ALREADY_UTF8);

                // estraggo i tag usati
                preg_match_all('/\[([\w\d\. _]*)\]/m', $TBS->Source, $tags, PREG_PATTERN_ORDER);
                // creo hashmap dei tag utilizzati
                $tags = array_fill_keys($tags[1], true);

                $TBS->NoErr = true; //commentare in debug

                $fileId = null;
                $fileUniqueid = null;
                $others = null;
                if ($fUid) {
                    //get subject's rules used
                    $stmt = $this->db->select()->from(['pa' => 'anagrafica_pratica'], ['UPPER(trp.nome) as nome', 'a.uniqueid'])
                        ->joinInner(['a' => 'anagrafiche'], "a.id = pa.person_id", null)
                        ->joinInner(['trp' => 'tabellaruoloprocessuale'], "trp.id = pa.role_id", null)
                        ->joinInner(['p' => 'archivio'], 'p.id = pa.file_id', null)
                        ->where('p.uniqueid=?');

                    $roleSubjects = $this->db->fetchAll($stmt, [$fUid]);


                    $file = $this->getFile($fUid);
                    $originalFile = $file;

                    $fileId = $file["id"];
                    unset($file["id"]);
                    $fileLawyer = $file["avvocato"];
                    unset($file["avvocato"]);
                    $fileUniqueid = $file["uniqueid"];
                    unset($file["uniqueid"]);
                    $lastHearing = $this->getLastHearing($fileId);
                    $file = array_merge($file, $lastHearing);
                    $lastHearingEvasa = $this->getLastHearingEvasa($fileId);
                    $file = array_merge($file, $lastHearingEvasa);
                    $nextHearing = $this->getNextHearing($fileId);
                    $file = array_merge($file, $nextHearing);
                    $nextHearingEvasa = $this->getNextHearingEvasa($fileId);
                    $file = array_merge($file, $nextHearingEvasa);
                    $poliswebData = $this->getPoliswebData($fileId);
                    $file = array_merge($file, $poliswebData);
                    $TBS->MergeBlock("pratica", array($file));
                    $lawyer = $this->getLawyer($fileLawyer);
                    $TBS->MergeBlock("avvocato", array($lawyer));
                    $user = $this->getLoggedUser($fileLawyer, $s3Connector);
                    $TBS->MergeBlock("utente", array($user));

                    // DOMINUS
                    if ($file["dominus"] !== self::$EMPTY_VALUE) {
                        $dominus = $this->getPart($file["dominus"], "dominus");
                    } else {
                        $dominus = $this->getEmptyPart();
                    }
                    $TBS->MergeBlock("dominus", array($dominus));

                    // CUSTOMERS
                    $customers = $this->getPart($params["customers"], "cliente");
                    $TBS->MergeBlock("cliente", array($customers));

                    $explodedCustomers = explode(",", $params["customers"]);

                    $this->mergeBlockActorPart($tags,
                        'cliente', $explodedCustomers, $roleSubjects, $TBS);

                    // COUNTERPARTS
                    $counterparts = $this->getPart($params["counterparts"], "controparte");
                    $TBS->MergeBlock("controparte", array($counterparts));

                    $explodedCounterparts = explode(",", $params["counterparts"]);

                    $this->mergeBlockActorPart($tags,
                        'controparte', $explodedCounterparts, $roleSubjects, $TBS);

                    // COLLABORATORS
                    $collaborators = $this->getUser($params["collaborators"], $fileId, 3);
                    $TBS->MergeBlock("collaboratore", array($collaborators));

                    $explodedCollaborators = explode(",", $params["collaborators"]);

                    $this->mergeBlockUserPart($fileId,
                        3, 'collaboratore', $explodedCollaborators, $tags, $TBS);


                    // COOWNER
                    $coowners = $this->getUser($params["coowners"], $fileId, 1);
                    $TBS->MergeBlock("cointestatario", array($coowners));

                    $explodedCoowners = explode(",", $params["coowners"]);

                    $this->mergeBlockUserPart($fileId,
                        1, 'cointestatario', $explodedCoowners, $tags, $TBS);


                    // RESPONSABLES
                    $responsables = $this->getUser($params["responsables"], $fileId, 2);
                    $TBS->MergeBlock("responsabile", array($responsables));

                    // EXTERNALS
                    $externals = $this->getPart($params["externals"], "esterno");
                    $TBS->MergeBlock("esterno", array($externals));

                    $explodedExternals = explode(",", $params["externals"]);

                    $this->mergeBlockActorPart($tags,
                        'esterno', $explodedExternals, $roleSubjects, $TBS);

                    // OTHERS
                    if (isset($params['others']) && !empty($params['others'])) {

                        $others = $this->getPart($params["others"], "altro");

                        $explodedOthers = explode(",", $params["others"]);

                        $this->mergeBlockActorPart($tags,
                            'altro', $explodedOthers, $roleSubjects, $TBS);
                    }

                    // OPPONENTS
                    if (isset($params['opponents']) && !empty($params['opponents'])) {
                        $opponents = $this->getPart($params["opponents"], "avversario");
                    } else {
                        $opponents = $this->getEmptyPart();
                    }
                    $TBS->MergeBlock("avversario", array($opponents));

                    // DOCUMENTS
                    if (isset($params['documents']) && !empty($params['documents']) && $params['documents'] != 'null') {
                        $documents = $this->getDocuments($params["documents"]);
                    } else {
                        $documents = [];
                    }
                    $TBS->MergeBlock("documento", "array", $documents);

                    // CREDITRECOVERIES
                    if (isset($params['creditrecoveries']) && !empty($params['creditrecoveries'])) {
                        if ($this->isPa) {
                            $creditrecoveries = $this->getCreditrecoveriesPa($params["creditrecoveries"]);
                            $interests = $this->getInterestList($params["creditrecoveries"]);
                            $movements = $this->getRecoveryCreditMovementsList($params["creditrecoveries"]);
                        } else {
                            $creditrecoveries = $this->getCreditrecoveries($params["creditrecoveries"]);
                            $interests = $this->getInterestList($params["creditrecoveries"]);
                            $movements = $this->getRecoveryCreditMovementsList($params["creditrecoveries"]);

                        }
                    } else {
                        $creditrecoveries = $this->getEmptyCreditrecoveries();
                        $interests = [];
                        $movements = [];
                    }
                    $TBS->MergeBlock("recuperocrediti.LISTA_MOVIMENTI", $movements);
                    $TBS->MergeBlock("recuperocrediti.LISTA_INTERESSI", $interests);
                    $TBS->MergeBlock("recuperocrediti", array($creditrecoveries));

                    // CLAIM
                    $claimInterest = [];
                    $claimPayments = [];
                    if (!empty($params['claims'])) {
                            $claim = $this->getClaim($params["claims"], $originalFile);
                            $claimInterest = $this->getClaimInterest($params["claims"], $originalFile);
                            $claimPayments = $this->getClaimPayments($params["claims"], $originalFile);
                    } else {
                        $claim = $this->getEmptyClaim();
                    }
                    $TBS->MergeBlock("sinistri.LISTA_PAGAMENTI", $claimPayments);
                    $TBS->MergeBlock("sinistri.LISTA_INTERESSI", $claimInterest);
                    $TBS->MergeBlock("sinistri", array($claim));

                    // TODO commentato per il momento (bisogna capire che campi aggiungere)
                    // LIQUIDATIONS
                    /*if (isset($params['liquidations']) && !empty($params['liquidations'])) {
                        $liquidations = $this->getLiquidations($fileId);
                    } else {
                        $liquidations = [];
                    }
                    $TBS->MergeBlock("liquidazione", array($liquidations));*/

                }

                // OTHERS E CLAUSOLE
                if (! $others) {
                    $others = $this->getEmptyPart();
                }

                $clausesSqlList = $this->getClauses($params["clauses"] ?? null);
                if ($clausesSqlList !== false) {
                    // CLAUSOLE
                    $clausesArray = [];
                    $explode = explode(',', $params["clauses"] ?? '');

                    // lambda function to filter clauses by expression
                    $filterClauseByExpression = function($rexpression, $needle) {
                        return array_filter(array_keys($needle), function ($tag) use ($rexpression) {
                            return preg_match($rexpression, $tag);
                        });
                    };

                    // check if there are clauses with ordinal numbers
                    $clausesByExpression = $filterClauseByExpression('/.+\.CLAUSOLE\.\d+$/', $tags);
                    $containsOrdinalClauses = !empty($clausesByExpression);
                    // get the tags that contain clause names
                    $tagsContainsNames = $filterClauseByExpression('/.+\.CLAUSOLE\.[\w\d_]+$/', $tags);

                    $selectedClauses = array_map(function($item){
                        $exploded = explode('.', $item);
                        return end($exploded);
                    }, !empty($tagsContainsNames) ? $tagsContainsNames : $clausesByExpression);

                    $clausesArray['numeric'][0] = '[VALORE NON AMMESSO]';
                    foreach ($selectedClauses as $key => $item) {
                        $clauseItem = $clausesSqlList[$explode[$key]];

                        // if the clause is not in the clausesSqlList, skip it
                        if (!isset($clauseItem)) {
                            $clausesArray['name'][$item] = '[CLAUSOLA MANCANTE]';
                            continue;
                        }

                        // if there are no ordinal do not insert the ordinal number
                        if($containsOrdinalClauses){
                            $clausesArray['numeric'][$key+1] = $clauseItem['content'];
                        }

                        $clausesArray['name'][$clauseItem['nome']] = $clauseItem['content'];
                    }

                    if($containsOrdinalClauses) {
                        $clausesArray['numeric'] = array_pad(
                            $clausesArray['numeric'],
                            20,
                            '[CLAUSOLA MANCANTE]'
                        );
                    }

                    $others['CLAUSOLE'] = array_merge($clausesArray['name'], $clausesArray['numeric'] ?? []);
                }

                $TBS->MergeBlock('altro', [$others]);


                $contractUuid = null;
                if ($contractId) {
                    // CONTRACTS
                    $contract = $this->getContract($contractId);
                    $contractUuid = $contract['contract_uuid'] ?? null;
                    unset($contract['contract_uuid']);
                    $TBS->MergeBlock('contratto', [$contract]);


                    if ($params['anagrafiche']) {
                        $contractAnagrafiche = $this->getContractAnagrafiche($params['anagrafiche'], $contractId);
                        $TBS->MergeBlock('contratto_anagrafica', [$contractAnagrafiche]);
                    }

                    if ($params['utenti']) {
                        $contractUtenti = $this->getContractUtenti($params['utenti'], $contractId);
                        $TBS->MergeBlock('contratto_utente', [$contractUtenti]);
                    }
                }

                $fileName = $this->getFileName($template["title"], $template["filename"]);
                if ($params["saveInDoc"] === "false") {

                    $method = $forceContent ? OPENTBS_STRING : OPENTBS_DOWNLOAD;
                    $TBS->Show($method, utf8_decode($fileName));

                    unlink($tmpDir);
                    if (!empty($user[self::$UTENTELOGGED_CARTA_INTESTATA])) {
                        unlink($user[self::$UTENTELOGGED_CARTA_INTESTATA]);
                    }

                    if($forceContent){
                        $document['content'] = $TBS->Source;
                        $document['filename'] = utf8_decode($fileName);
                        return $document;
                    }

                } else {
                    $counter = 1;
                    $fileArray = explode(".", $fileName);
                    $ext = $fileArray[count($fileArray) - 1];
                    unset($fileArray[count($fileArray) - 1]);
                    $prefix = implode(".", $fileArray);
                    $fileName = $prefix . "." . $ext;
                    if (!empty($params['nomemodello'])) {
                        $prefix   = preg_replace('/\.[^.]+$/', '', trim($params['nomemodello']));
                        $fileName = $this->escapeSpecialChars($prefix) . '.' . $ext;
                    }
                    //sostituito il calcolo del nome file perché era errato il controllo su s3 e onedrive,
                    //il nome veniva ricalcolato nel setValidFilename quindi il controllo di esistenza era inutile perché i filename erano diversi
                    //questo metodo serve solo per dare un titolodocumento diverso all'interno della stessa pratica o contratto
                    //il controllo di esistenza del file c'è direttamente nel setValidFilename di s3 e onedrive
                    $sql = 'SELECT titolodocumento FROM documento WHERE titolodocumento = ? ';

                    if ($fileId) {
                        $sql .= 'AND codicepratica = ' . $fileId . ' ';
                    }

                    if ($contractId) {
                        $sql .= 'AND contract_id = ' . $contractId . ' ';
                    }
                    while ($this->db->fetchOne($sql, $fileName)) {
                        $fileName = $prefix . '(' . $counter++ . ').' . $ext;
                    }

                    $now = date("Y-m-d H:i:s");
                    $uId = $this->loggedUser['id'];
                    $document = [
                        'titolodocumento' => $fileName,
                        'data' => date('Y-m-d'),
                        'immessoil' => $now,
                        'immessoda' => $uId,
                        'modificatoil' => $now,
                        'modificatoda' => $uId,
                        'nomefile' => $fileName,
                        'visible' => 0,
                        'da_associare' => 0,
                        'legal_storage' => 0,
                    ];

                    if ($fileId) {
                        $document['codicepratica'] = $fileId;
                    }

                    if ($contractId) {
                        $document['contract_id'] = $contractId;
                    }

                    if (isset($params['relation_type'])) {
                        $document['relation_type'] = $params['relation_type'];
                    }

                    $TBS->Show(OPENTBS_STRING);
                    unlink($tmpDir);
                    if (!empty($user[self::$UTENTELOGGED_CARTA_INTESTATA])) {
                        unlink($user[self::$UTENTELOGGED_CARTA_INTESTATA]);
                    }

                    $archive = null;
                    if ($fileId) {
                        $archive = $this->archiveRepository->findArchiveById($fileId);
                    }

                    $tmpUid = $this->db->getUid();
                    $fileDir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $tmpUid . DIRECTORY_SEPARATOR;
                    if (!file_exists($fileDir)) {
                        mkdir($fileDir, 0777);
                    }
                    $file = $fileDir . $fileName;
                    file_put_contents($file, $TBS->Source);

                    $document["file"] = $file;
                    $document["external"] = !empty($params['external']) ? (int)$params['external'] : null;

                    try {
                        $newDocument = DocumentAbstract::createHttpRequestBuilder($document)->build($archive, $this->infrastructure);
                    } catch (Exception $e) {
                        throw $e;
                    }

                    $newDocument->setValidFilename($newDocument->getNomefile());
                    $newDocument->setFilesize(filesize($newDocument->getFile()));
                    // if (!empty($params['excludingColumns'])) {
                    //     $fields = $newDocument->getFields();
                    //     $excludingColumns = $params['excludingColumns'];
                    //     $fields = array_filter($fields, static function ($value, $key) use ($excludingColumns) {
                    //         return !in_array($key, $excludingColumns);
                    //     }, ARRAY_FILTER_USE_BOTH);
                    //     $newDocument->setFields($fields);
                    // }
                    $newDocument->save();

                    Utility::rmdir_nw($fileDir);

                    $document['fileUnqID'] = $fileUniqueid;

                    if ($contractUuid) {
                        $document['contractUuid'] = $contractUuid;
                    }

                    return $document;
                }
            }
        } catch (Exception $e) {
            if (!empty($tmpDir)) {
                unlink($tmpDir);
            }
            if (!empty($user) && !empty($user[self::$UTENTELOGGED_CARTA_INTESTATA])) {
                unlink($user[self::$UTENTELOGGED_CARTA_INTESTATA]);
            }

            echo $e;
        }

        return false;
    }

    /**
     * contratto.NOME
     * contratto.NUMERO
     * contratto.DATA_DECORRENZA
     * contratto.DATA_SCADENZA
     * contratto.OGGETTO
     * contratto.CATEGORIA
     * contratto.DATA_DI_RICHIESTA
     * contratto.DATA_PERFEZIONAMENTO_RICHIESTA
     * contratto.DATA_RINNOVO
     * contratto.CAMPO_DINAMICO_X
     *
     * @return array<array-key, mixed>
     */
    private function getContract(int $contractId): array
    {
        $sql = 'SELECT
            uuid as contract_uuid,
            contract_name as ' . self::CONTRATTO_NOME . ',
            contract_identifier as ' . self::CONTRATTO_NUMERO . ",
            DATE_FORMAT(start_at, '%d/%m/%Y') as " . self::CONTRATTO_DATA_DECORRENZA . ",
            DATE_FORMAT(end_at, '%d/%m/%Y') as " . self::CONTRATTO_DATA_SCADENZA . ',
            contract_objects.name as ' . self::CONTRATTO_OGGETTO . ',
            categorie.nome as ' . self::CONTRATTO_CATEGORIA . ",
            DATE_FORMAT(NOW(), '%d/%m/%Y') AS " . self::CONTRATTO_DATA_OGGI . ",
            DATE_FORMAT(requested_at, '%d/%m/%Y') as " . self::CONTRATTO_DATA_DI_RICHIESTA . ",
            DATE_FORMAT(finalized_at, '%d/%m/%Y') as " . self::CONTRATTO_DATA_PERFEZIONAMENTO_RICHIESTA . ",
            DATE_FORMAT(renews_at, '%d/%m/%Y') as " . self::CONTRATTO_DATA_RINNOVO . '
        FROM contracts
        LEFT JOIN contract_states ON contracts.contract_state_id = contract_states.id
        LEFT JOIN contract_objects ON contracts.contract_object_id = contract_objects.id
        LEFT JOIN contract_utente ON contracts.id = contract_utente.contract_id
        LEFT JOIN anagrafica_contract ON contracts.id = anagrafica_contract.contract_id
        LEFT JOIN contract_campo ON contracts.id = contract_campo.contract_id
        LEFT JOIN categorie ON contracts.categoria_id = categorie.id
        WHERE contracts.id = ?';

        $contract = $this->db->fetchRow($sql, [$contractId]);

        $sqlCampi = "SELECT
            campo_id,
            CASE
                WHEN tipo = 'date' THEN DATE_FORMAT(value, '%d/%m/%Y')
                WHEN tipo = 'checkbox' THEN IF(value = 'on', 'si', 'no')
            ELSE value END AS value,
            tipo,
            opt
        FROM contract_campo
        JOIN campi_dinamici ON campo_id = campi_dinamici.id
        WHERE contract_id = ?";

        $contractCampi = $this->db->fetchAll($sqlCampi, [$contractId]);

        foreach ($contractCampi as $campo) {
            if ($campo['tipo'] == 'select') {
                $options = json_decode($campo['opt'], true);
                $campo['value'] = array_search($campo['value'], $options);
            }

            $contract['CAMPO_DINAMICO_' . $campo['campo_id']] = $campo['value'];
        }

        $this->checkFields($contract);

        return $contract;
    }

    /**
     *   contratto_anagrafica.NOME
     *   contratto_anagrafica.COGNOME
     *   contratto_anagrafica.CODICE_FISCALE
     *   contratto_anagrafica.DATA_NASCITA
     *   contratto_anagrafica.PARTITA_IVA
     *   contratto_anagrafica.EMAIL
     *   contratto_anagrafica.INDIRIZZO
     *   contratto_anagrafica.MOBILE_1
     *   contratto_anagrafica.MOBILE_2
     *   contratto_anagrafica.FAX
     *   contratto_anagrafica.CAP
     *   contratto_anagrafica.CITTA
     *   contratto_anagrafica.TELEFONO_CASA
     *   contratto_anagrafica.TELEFONO_UFFICIO
     *   contratto_anagrafica.NOME_COGNOME
     *   contratto_anagrafica.PROVINCIA
     *
     * @return array<array-key, mixed>
     */
    private function getContractAnagrafiche(string $anagrafiche, int $contractId): array
    {
        $arrayAnagrafiche = explode(',', $anagrafiche);

        $contractRelations = [];
        $anagraficheIds = [];
        foreach ($arrayAnagrafiche as $arrayAnagrafica) {
            $relationAndAnagrafica = explode('-', $arrayAnagrafica);
            $contractRelations[$relationAndAnagrafica[0]][] = $relationAndAnagrafica[1];
            $anagraficheIds[] = $relationAndAnagrafica[1];
        }
        $anagraficheIds = implode(',', $anagraficheIds);

        foreach ($contractRelations as $key => $value) {
            $contractRelations[$key] = array_flip($value);
        }

        $sql = 'SELECT
                    anagrafiche.id AS anagrafica_id,
                    nome AS ' . self::CONTRATTO_ANAGRAFICA_NOME . ', 
                    cognome AS ' . self::CONTRATTO_ANAGRAFICA_COGNOME . ', 
                    denominazione AS ' . self::CONTRATTO_ANAGRAFICA_DENOMINAZIONE . ', 
                    codicefiscale AS ' . self::CONTRATTO_ANAGRAFICA_CODICE_FISCALE . ", 
                    DATE_FORMAT(datanascita, '%d/%m/%Y') AS " . self::CONTRATTO_ANAGRAFICA_DATA_NASCITA . ', 
                    partitaiva AS ' . self::CONTRATTO_ANAGRAFICA_PARTITA_IVA . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_EMAIL . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_PEC . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_INDIRIZZO . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_MOBILE_1 . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_MOBILE_2 . ",                    
                    '' AS " . self::CONTRATTO_ANAGRAFICA_FAX . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_CAP . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_CITTA . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_TELEFONO_CASA . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_TELEFONO_UFFICIO . ",
                    '' AS " . self::CONTRATTO_ANAGRAFICA_PROVINCIA . ",
                    CONCAT(nome, ' ', cognome) AS " . self::CONTRATTO_ANAGRAFICA_NOME_COGNOME . ",
                    contatti,
					indirizzi,
                    contract_relations.name as relation_name,
                    contract_relation_id
                FROM anagrafiche 
                    JOIN anagrafica_contract ON anagrafiche.id = anagrafica_contract.anagrafica_id
                    JOIN contract_relations ON anagrafica_contract.contract_relation_id = contract_relations.id
                WHERE anagrafica_contract.contract_id = ? AND anagrafiche.id IN (" . $anagraficheIds . ')';

        $anagraficheResult = $this->db->fetchAll($sql, [$contractId]);

        $resultArray = [];
        foreach ($anagraficheResult as $anagrafica) {
            $relation = strtoupper(str_replace(' ', '_', $anagrafica['relation_name']));

            if (! isset($contractRelations[$anagrafica['contract_relation_id']][$anagrafica['anagrafica_id']])) {
                continue;
            }

            $relation .= '_' . ($contractRelations[$anagrafica['contract_relation_id']][$anagrafica['anagrafica_id']] + 1);

            if (!empty($anagrafica['indirizzi'])) {
                $indirizzi = self::getIndirizzi($anagrafica);
                $anagrafica[self::CONTRATTO_ANAGRAFICA_CITTA] = $indirizzi['citta'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_PROVINCIA] = $indirizzi['provincia'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_INDIRIZZO] = $indirizzi['via'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_CAP] = $indirizzi['cap'] ?? null;
            }

            if (!empty($anagrafica['contatti'])) {
                $contatti = self::getContatti($anagrafica);
                $anagrafica[self::CONTRATTO_ANAGRAFICA_EMAIL] = $contatti['email'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_PEC] = $contatti['pec'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_TELEFONO_CASA] = $contatti['telefonocasa'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_TELEFONO_UFFICIO] = $contatti['telefonoufficio'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_MOBILE_1] = $contatti['mobile1'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_MOBILE_2] = $contatti['mobile2'] ?? null;
                $anagrafica[self::CONTRATTO_ANAGRAFICA_FAX] = $contatti['fax'] ?? null;
            }

            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_NOME] = $anagrafica[self::CONTRATTO_ANAGRAFICA_NOME];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_COGNOME] = $anagrafica[self::CONTRATTO_ANAGRAFICA_COGNOME];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_DENOMINAZIONE] = $anagrafica[self::CONTRATTO_ANAGRAFICA_DENOMINAZIONE];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_CODICE_FISCALE] = $anagrafica[self::CONTRATTO_ANAGRAFICA_CODICE_FISCALE];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_DATA_NASCITA] = $anagrafica[self::CONTRATTO_ANAGRAFICA_DATA_NASCITA];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_PARTITA_IVA] = $anagrafica[self::CONTRATTO_ANAGRAFICA_PARTITA_IVA];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_EMAIL] = $anagrafica[self::CONTRATTO_ANAGRAFICA_EMAIL];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_INDIRIZZO] = $anagrafica[self::CONTRATTO_ANAGRAFICA_INDIRIZZO];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_MOBILE_1] = $anagrafica[self::CONTRATTO_ANAGRAFICA_MOBILE_1];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_MOBILE_2] = $anagrafica[self::CONTRATTO_ANAGRAFICA_MOBILE_2];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_FAX] = $anagrafica[self::CONTRATTO_ANAGRAFICA_FAX];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_CAP] = $anagrafica[self::CONTRATTO_ANAGRAFICA_CAP];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_CITTA] = $anagrafica[self::CONTRATTO_ANAGRAFICA_CITTA];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_TELEFONO_CASA] = $anagrafica[self::CONTRATTO_ANAGRAFICA_TELEFONO_CASA];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_TELEFONO_UFFICIO] = $anagrafica[self::CONTRATTO_ANAGRAFICA_TELEFONO_UFFICIO];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_NOME_COGNOME] = $anagrafica[self::CONTRATTO_ANAGRAFICA_NOME_COGNOME];
            $resultArray[$relation . '_' . self::CONTRATTO_ANAGRAFICA_PROVINCIA] = $anagrafica[self::CONTRATTO_ANAGRAFICA_PROVINCIA];

        }

        $this->checkFields($resultArray);

        return $resultArray;
    }

    /**
     *  contratto_utente.EMAIL
     *  contratto_utente.CAP
     *  contratto_utente.CITTA
     *  contratto_utente.CODICE_FISCALE
     *  contratto_utente.FAX
     *  contratto_utente.INDIRIZZO
     *  contratto_utente.MOBILE
     *  contratto_utente.NOME
     *  contratto_utente.COGNOME
     *  contratto_utente.NOME_COGNOME
     *  contratto_utente.NOME_STUDIO
     *  contratto_utente.PARTITA_IVA
     *  contratto_utente.PROVINCIA
     *  contratto_utente.TELEFONO
     *
     * @return array<array-key, mixed>
     */
    private function getContractUtenti(string $utenti, int $contractId): array
    {
        $arrayUtenti = explode(',', $utenti);

        $contractRelations = [];
        $utentiIds = [];
        foreach ($arrayUtenti as $arrayUtente) {
            $relationAndUtente = explode('-', $arrayUtente);
            $contractRelations[$relationAndUtente[0]][] = $relationAndUtente[1];
            $utentiIds[] = $relationAndUtente[1];
        }
        $utentiIds = implode(',', $utentiIds);

        foreach ($contractRelations as $key => $value) {
            $contractRelations[$key] = array_flip($value);
        }

        $sql = 'SELECT
                    utente.id AS utente_id,
                    datistudio.cap AS ' . self::CONTRATTO_UTENTE_CAP . ',
                    datistudio.citta AS ' . self::CONTRATTO_UTENTE_CITTA . ',
                    datistudio.codicefiscale AS ' . self::CONTRATTO_UTENTE_CODICE_FISCALE . ',
                    datistudio.email AS ' . self::CONTRATTO_UTENTE_EMAIL . ',
                    datistudio.fax AS ' . self::CONTRATTO_UTENTE_FAX . ',
                    datistudio.indirizzo AS ' . self::CONTRATTO_UTENTE_INDIRIZZO . ',
                    datistudio.mobile AS ' . self::CONTRATTO_UTENTE_MOBILE . ",
                    CONCAT(utente.nomepersonale, ' ', utente.cognomepersonale) AS " . self::CONTRATTO_UTENTE_NOME_COGNOME . ',
                    datistudio.nome AS ' . self::CONTRATTO_UTENTE_NOME_STUDIO . ',
                    datistudio.partitaiva AS ' . self::CONTRATTO_UTENTE_PARTITA_IVA . ',
                    datistudio.provincia AS ' . self::CONTRATTO_UTENTE_PROVINCIA . ',
                    datistudio.telefono AS ' . self::CONTRATTO_UTENTE_TELEFONO . ',
                    utente.nomepersonale AS ' . self::CONTRATTO_UTENTE_NOME . ', 
                    utente.cognomepersonale AS ' . self::CONTRATTO_UTENTE_COGNOME . ', 
                    utente_email_account.email AS ' . self::CONTRATTO_UTENTE_PEC . ', 
                    contract_relations.name as relation_name,
                    contract_relation_id
                FROM utente 
                    JOIN tabellaavvocati ON utente.codiceavvocato = tabellaavvocati.id  
					JOIN datistudio ON tabellaavvocati.id = datistudio.avvocato 
                    JOIN contract_utente ON utente.id = contract_utente.utente_id
                    JOIN contract_relations ON contract_utente.contract_relation_id = contract_relations.id
                    LEFT JOIN utente_email_account ON utente.id = utente_email_account.user_id 
                         AND utente_email_account.reginde = 1 AND utente_email_account.email_type = 1
                WHERE contract_utente.contract_id = ? AND utente.id IN (' . $utentiIds . ') group by utente_email_account.user_id';

        $utentiResult = $this->db->fetchAll($sql, [$contractId]);

        $resultArray = [];
        foreach ($utentiResult as $utente) {
            $relation = strtoupper(str_replace(' ', '_', $utente['relation_name']));

            if (! isset($contractRelations[$utente['contract_relation_id']][$utente['utente_id']])) {
                continue;
            }

            $relation .= '_' . ($contractRelations[$utente['contract_relation_id']][$utente['utente_id']] + 1);

            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_CAP] = $utente[self::CONTRATTO_UTENTE_CAP];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_CITTA] = $utente[self::CONTRATTO_UTENTE_CITTA];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_CODICE_FISCALE] = $utente[self::CONTRATTO_UTENTE_CODICE_FISCALE];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_EMAIL] = $utente[self::CONTRATTO_UTENTE_EMAIL];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_PEC] = $utente[self::CONTRATTO_UTENTE_PEC];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_FAX] = $utente[self::CONTRATTO_UTENTE_FAX];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_INDIRIZZO] = $utente[self::CONTRATTO_UTENTE_INDIRIZZO];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_MOBILE] = $utente[self::CONTRATTO_UTENTE_MOBILE];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_NOME] = $utente[self::CONTRATTO_UTENTE_NOME];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_COGNOME] = $utente[self::CONTRATTO_UTENTE_COGNOME];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_NOME_COGNOME] = $utente[self::CONTRATTO_UTENTE_NOME_COGNOME];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_NOME_STUDIO] = $utente[self::CONTRATTO_UTENTE_NOME_STUDIO];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_PARTITA_IVA] = $utente[self::CONTRATTO_UTENTE_PARTITA_IVA];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_PROVINCIA] = $utente[self::CONTRATTO_UTENTE_PROVINCIA];
            $resultArray[$relation . '_' . self::CONTRATTO_UTENTE_TELEFONO] = $utente[self::CONTRATTO_UTENTE_TELEFONO];
        }

        $this->checkFields($resultArray);

        return $resultArray;
    }

    /**
     * pratica.AUTORITA
     * pratica.CITTA
     * pratica.NOME
     * pratica.CODICE
     * pratica.CODICE_ARCHIVIO
     * pratica.DATA_OGGI
     * pratica.DATA_REATO
     * pratica.DECRETO_INGIUNTIVO
     * pratica.OGGETTO
     * pratica.REATO
     * pratica.RG
     * pratica.RGAPP
     * pratica.RGCASS
     * pratica.RGGIP
     * pratica.RGGUP
     * pratica.RGNR
     * pratica.RGRIESAME
     * pratica.RGSIEP
     * pratica.RGSIUS
     * pratica.RGTRIB
     * pratica.RIFERIMENTO_TEMPORALE_UTC
     * pratica.TIPOLOGIA
     * pratica.VALORE
     * pratica.SEDE
     * pratica.PALCHETTO
     * pratica.FALDONE
     * pratica.SENTENZA
     * pratica.DATA_SENTENZA
     * pratica.DATA_DECRETO_INGIUNTIVO
     * pratica.DATA_CHIUSURA
     * pratica.DATA_ARCHIVIO
     * pratica.COLLABORATORI
     * pratica.EMISSIONE_DI
     * pratica.NOTIFICA_DI
     * pratica.FORMULA_ESECUTIVA
     * pratica.NOTIFICA_PRECETTO
     */
    private function getFile($fUid)
    {


        $file = "SELECT a.id,
						a.id AS " . self::$PRATICA_CODICE . ",
						a.nome_pratica AS " . self::$PRATICA_NOME . ",
						a.ruologeneralenumero,
						a.ruologeneraleanno,
						a.subprocedimento,
						a.numerodecretoingiuntivo,
						a.numerodecretoingiuntivoanno,
						CONCAT(a.numero_sentenza, '/', a.numero_sentenza_anno) AS " . self::$PRATICA_SENTENZA . ",
						DATE_FORMAT(a.data_sentenza, '%d/%m/%Y') AS " . self::$PRATICA_DATA_SENTENZA . ",
						DATE_FORMAT(a.data_decreto, '%d/%m/%Y') AS " . self::$PRATICA_DATA_DECRETO_INGIUNTIVO . ",
						DATE_FORMAT(a.data_chiusura, '%d/%m/%Y') AS " . self::$PRATICA_DATA_CHIUSURA . ",
						DATE_FORMAT(a.data_archivio, '%d/%m/%Y') AS " . self::$PRATICA_DATA_ARCHIVIO . ",
						DATE_FORMAT(a.data, '%d/%m/%Y') AS " . self::$PRATICA_DATA_APERTURA . ",
						a.avvocato,
						a.annotazioni AS " . self::$PRATICA_ANNOTAZIONE . " ,
						(SELECT c.nome FROM categorie c WHERE a.id_categoria = c.id) " . self::$PRATICA_CATEGORIA . " ,
						(SELECT ad.uniqueid FROM anagrafiche ad INNER JOIN anagrafica_pratica ap ON ad.id = ap.person_id WHERE ap.file_id = a.id AND ap.relazione = 5) AS dominus,
						DATE_FORMAT(NOW(), '%d/%m/%Y') AS " . self::$PRATICA_DATA_OGGI . ",
						replace(replace(replace(format(convert(replace(valore, ',', '.'), decimal(10,2)), 2), ',', 'x'), '.', ','), 'x', '.') AS " . self::$PRATICA_VALORE . ",
						ta.nome AS " . self::$PRATICA_NOME_AUTORITA . ",
						tc.nome AS " . self::$PRATICA_NOME_CITTA . ",
						togg.nome AS " . self::$PRATICA_OGGETTO . ",
						a.codicearchivio AS " . self::$PRATICA_CODICE_ARCHIVIO . ",
						ttp.nome AS " . self::$PRATICA_TIPOLOGIA . ",
						a.uniqueid,
						DATE_FORMAT(a.datareato, '%d/%m/%Y') AS " . self::$PRATICA_DATA_REATO . ",
						tr.nome AS " . self::$PRATICA_REATO . ",
						a.descrizione AS " . self::$PRATICA_DESCRIZIONE . ",
						a.general_protocol AS " . self::$PRATICA_PROTOCOLLO_GENERALE . ",
						ts.nome AS " . self::$PRATICA_SEDE . ",
						a.palchetto AS " . self::$PRATICA_PALCHETTO . ",
						a.faldone AS " . self::$PRATICA_FALDONE . ",
						a.rgnr,
						a.rgnranno,
						a.rggip,
						a.rggipanno,
						a.rggup,
						a.rggupanno,
						a.rgtrib,
						a.rgtribanno,
						a.rgapp,
						a.rgappanno,
						a.rgcass,
						a.rgcassanno,
						a.rgsiep,
						a.rgsiepanno,
						a.rgsius,
						a.rgsiusanno,
						a.rgriesame,
						a.rgriesameanno,
						a.valori_dinamici,
						co.valori_dinamici AS contrattualistica_valori_dinamici,
						a.valori_extra_giud_dinamici,
						(SELECT GROUP_CONCAT(u2.nomeutente SEPARATOR ', ') FROM utente_pratica up LEFT JOIN utente u2 ON u2.id = up.person_id WHERE up.file_id = a.id AND up.relazione = 3) AS " . self::$PRATICA_COLLABORATORI . ",
						DATE_FORMAT(a.emissione_di, '%d/%m/%Y') AS " . self::$PRATICA_EMISSIONE_DI . ",
						DATE_FORMAT(a.notifica_di, '%d/%m/%Y') AS " . self::$PRATICA_NOTIFICA_DI . ",
						DATE_FORMAT(a.decreto_esecutorieta, '%d/%m/%Y') AS " . self::$PRATICA_DECRETO_ESECUTORIETA . ",
						DATE_FORMAT(a.formula_esecutiva, '%d/%m/%Y') AS " . self::$PRATICA_FORMULA_ESECUTIVA . ",
						DATE_FORMAT(a.notifica_precetto, '%d/%m/%Y') AS " . self::$PRATICA_NOTIFICA_PRECETTO . ",
						tbs.nome AS " . self::$PRATICA_STATO . ",
						tpm.nome AS " . self::$PRATICA_PUBBLICO_MINISTERO . ",
						DATE_FORMAT(a.data_della_sentenza, '%d/%m/%Y')  AS " . self::$DATA_DELLA_SENT . "
				 FROM archivio a
					LEFT JOIN tabellaautorita ta ON a.ufficio_giudiziario = ta.id
					LEFT JOIN tabellacitta tc ON ta.citta = tc.id
					LEFT JOIN tabellaoggetto togg ON a.oggetto = togg.id
					LEFT JOIN tabellareato tr ON a.reato = tr.id
					LEFT JOIN tabellastati tbs ON a.stato = tbs.id
					LEFT JOIN tabellasedi ts ON a.sede = ts.id
					LEFT JOIN contratti co ON a.id = co.id_pratica
					LEFT JOIN tabellapubbliciministeri tpm ON a.pubblico_ministero = tpm.id
					INNER JOIN tabellatipologiapratica ttp ON a.tipologiapratica = ttp.id
				 WHERE a.uniqueid = ?";
        $file = $this->db->fetchRow($file, array($fUid));
        $this->createRG("ruologeneralenumero", "ruologeneraleanno", "subprocedimento", self::$PRATICA_RG, $file);
        $this->createRG("numerodecretoingiuntivo", "numerodecretoingiuntivoanno", null, self::$PRATICA_DECRETO_INGIUNTIVO, $file);
        $this->createRG("rgnr", "rgnranno", null, self::$PRATICA_RGNR, $file);
        $this->createRG("rggip", "rggipanno", null, self::$PRATICA_RGGIP, $file);
        $this->createRG("rggup", "rggupanno", null, self::$PRATICA_RGGUP, $file);
        $this->createRG("rgtrib", "rgtribanno", null, self::$PRATICA_RGTRIB, $file);
        $this->createRG("rgapp", "rgappanno", null, self::$PRATICA_RGAPP, $file);
        $this->createRG("rgcass", "rgcassanno", null, self::$PRATICA_RGCASS, $file);
        $this->createRG("rgsiep", "rgsiepanno", null, self::$PRATICA_RGSIEP, $file);
        $this->createRG("rgsius", "rgsiusanno", null, self::$PRATICA_RGSIUS, $file);
        $this->createRG("rgriesame", "rgriesameanno", null, self::$PRATICA_RGRIESAME, $file);
        if (!empty($file[self::$PRATICA_VALORE])) {
            $fileValue = $file[self::$PRATICA_VALORE];
            if ($fileValue == "-1,00") {
                $file[self::$PRATICA_VALORE] = "Indeterminato medio";
            } else if ($fileValue == "-2,00") {
                $file[self::$PRATICA_VALORE] = "Indeterminato modesto";
            } else if ($fileValue == "-3,00") {
                $file[self::$PRATICA_VALORE] = "Indeterminato rilevante";
            }
        }
        $file[self::$PRATICA_RIFERIMENTO_TEMPORALE_UTC] = gmdate("Y-m-d\TH:i:s\Z");

        // campi dinamici pratica
        if (!empty($file['valori_dinamici']) && $file['valori_dinamici'] != 'null') {
            $campi = json_decode($file['valori_dinamici'], true);

            foreach ($campi as $campo) {
                $valoreCampo = $campo['valore'];
                $idCampo = $campo['id'];
                $tipo = $this->db->fetchOne("SELECT tipo FROM campi_dinamici WHERE id = ?", $idCampo);
                if ($tipo == 'date') {
                    $valoreCampo = date('d/m/Y', strtotime($valoreCampo));
                }
                if ($tipo == 'select') {
                    $opt = $this->db->fetchOne("SELECT opt FROM campi_dinamici WHERE id = ?", $idCampo);
                    $opt = json_decode($opt, true);
                    foreach ($opt as $value => $idx) {
                        if ($idx == $campo['valore']) {
                            $valoreCampo = $value;
                            break;
                        }
                    }
                }
                $file["CAMPO_DINAMICO_$idCampo"] = $valoreCampo;
            }
        }

        if (!empty($file['valori_extra_giud_dinamici']) && $file['valori_extra_giud_dinamici'] != 'null') {
            $campi = json_decode($file['valori_extra_giud_dinamici'], true);
            $file = $this->formatCampiDinamici($campi, $file, 'giudiziale');

        }

        //Campi dinamici contrattualistica
        if (!empty($file['contrattualistica_valori_dinamici']) && $file['contrattualistica_valori_dinamici'] != 'null') {
            $campi = json_decode($file['contrattualistica_valori_dinamici'], true);
            $file = $this->formatCampiDinamici($campi, $file, 'contrattualistica', "CONTR_CAMPO_DINAMICO_");
        }


        $this->checkFields($file);
        return $file;
    }


    /**
     * pratica.ATTIVITA_ULTIMA_UDIENZA
     * pratica.AUTORITA_ULTIMA_UDIENZA
     * pratica.DATA_ULTIMA_UDIENZA
     * pratica.CITTA_ULTIMA_UDIENZA
     * pratica.ORA_ULTIMA_UDIENZA
     */
    private function getLastHearing($fId)
    {
        $hearing = "SELECT a.attivita AS " . self::$PRATICA_ATTIVITA_ULTIMA_UDIENZA . ",
						   DATE_FORMAT (a.data, '%d/%m/%Y') AS " . self::$PRATICA_DATA_ULTIMA_UDIENZA . ", 
						   c.nome AS " . self::$PRATICA_NOME_CITTA_ULTIMA_UDIENZA . ",
						   aut.nome AS " . self::$PRATICA_NOME_AUTORITA_ULTIMA_UDIENZA . ",
						   a.ora AS " . self::$PRATICA_ORA_ULTIMA_UDIENZA . "
					FROM agenda a
					LEFT JOIN tabellacitta c ON c.id = a.citta
					INNER JOIN tabellaautorita aut ON aut.id = a.autorita
					WHERE a.pratica = ?
					ORDER BY a.data DESC, a.ora DESC
					LIMIT 1";
        $hearing = $this->db->fetchRow($hearing, array($fId));
        if (empty($hearing)) {
            $hearing = array(self::$PRATICA_ATTIVITA_ULTIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_DATA_ULTIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_NOME_CITTA_ULTIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_NOME_AUTORITA_ULTIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_ORA_ULTIMA_UDIENZA => self::$EMPTY_VALUE);
        } else {
            $this->checkFields($hearing);
        }
        return $hearing;
    }


    private function getLastHearingEvasa($fId)
    {
        $hearing = "SELECT a.attivita AS " . self::$PRATICA_ATTIVITA_ULTIMA_UDIENZA . ",
						   DATE_FORMAT (a.data, '%d/%m/%Y') AS " . self::$PRATICA_DATA_ULTIMA_UDIENZA_EVASA . ", 
						   c.nome AS " . self::$PRATICA_NOME_CITTA_ULTIMA_UDIENZA . ",
						   aut.nome AS " . self::$PRATICA_NOME_AUTORITA_ULTIMA_UDIENZA . ",
						   a.ora AS " . self::$PRATICA_ORA_ULTIMA_UDIENZA . "
					FROM agenda a
					LEFT JOIN tabellacitta c ON c.id = a.citta
					INNER JOIN tabellaautorita aut ON aut.id = a.autorita
					WHERE a.pratica = ? AND a.stato_evasa = 1
					ORDER BY a.data DESC, a.ora DESC
					LIMIT 1";
        $hearing = $this->db->fetchRow($hearing, array($fId));
        if (empty($hearing)) {
            $hearing = array(self::$PRATICA_ATTIVITA_ULTIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_DATA_ULTIMA_UDIENZA_EVASA => self::$EMPTY_VALUE,
                self::$PRATICA_NOME_CITTA_ULTIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_NOME_AUTORITA_ULTIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_ORA_ULTIMA_UDIENZA => self::$EMPTY_VALUE);
        } else {
            $this->checkFields($hearing);
        }
        return $hearing;
    }


    /**
     * pratica.ATTIVITA_PROSSIMA_UDIENZA
     * pratica.AUTORITA_PROSSIMA_UDIENZA
     * pratica.DATA_PROSSIMA_UDIENZA
     * pratica.CITTA_PROSSIMA_UDIENZA
     * pratica.ORA_PROSSIMA_UDIENZA
     */
    private function getNextHearing($fId)
    {
        $hearing = "SELECT a.attivita AS " . self::$PRATICA_ATTIVITA_PROSSIMA_UDIENZA . ",
						   DATE_FORMAT (a.data, '%d/%m/%Y') AS " . self::$PRATICA_DATA_PROSSIMA_UDIENZA . ",
						   c.nome AS " . self::$PRATICA_NOME_CITTA_PROSSIMA_UDIENZA . ",
						   aut.nome AS " . self::$PRATICA_NOME_AUTORITA_PROSSIMA_UDIENZA . ",
						   a.ora AS " . self::$PRATICA_ORA_PROSSIMA_UDIENZA . "
					FROM agenda a
					INNER JOIN tabellacitta c ON c.id = a.citta
					INNER JOIN tabellaautorita aut ON aut.id = a.autorita
					WHERE a.pratica = ? AND a.data > NOW()
					ORDER BY a.data ASC, a.ora ASC
					LIMIT 1";
        $hearing = $this->db->fetchRow($hearing, array($fId));
        if (empty($hearing)) {
            $hearing = array(self::$PRATICA_ATTIVITA_PROSSIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_DATA_PROSSIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_NOME_CITTA_PROSSIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_NOME_AUTORITA_PROSSIMA_UDIENZA => self::$EMPTY_VALUE,
                self::$PRATICA_ORA_PROSSIMA_UDIENZA => self::$EMPTY_VALUE);
        } else {
            $this->checkFields($hearing);
        }
        return $hearing;
    }

    private function getNextHearingEvasa($fId)
    {
        $hearing = "SELECT DATE_FORMAT (a.data, '%d/%m/%Y') AS " . self::$PRATICA_DATA_PROSSIMA_UDIENZA_EVASA . "
					FROM agenda a
					WHERE a.pratica = ? AND a.data > NOW() AND a.stato_evasa = 1
					ORDER BY a.data ASC, a.ora ASC
					LIMIT 1";
        $hearing = $this->db->fetchRow($hearing, array($fId));
        if (empty($hearing)) {
            $hearing = array(self::$PRATICA_DATA_PROSSIMA_UDIENZA_EVASA => self::$EMPTY_VALUE);
        } else {
            $this->checkFields($hearing);
        }
        return $hearing;
    }

    /**
     * pratica.GIUDICE
     * pratica.SEZIONE
     * pratica.DATA_ISCRIZIONE_RUOLO
     */
    private function getPoliswebData($fId)
    {
        $poliswebData = "SELECT pp.giudice AS " . self::$PRATICA_NOME_GIUDICE . ", 
								pp.sezione AS " . self::$PRATICA_SEZIONE . ",
								DATE_FORMAT(pp.data_iscrizione, \"%d/%m/%Y\") as " . self::$PRATICA_DATA_ISCRIZIONE_RUOLO . " 					   
					FROM poliswebpratica pp
					WHERE pp.codice_pratica = ?";
        $poliswebData = $this->db->fetchRow($poliswebData, array($fId));

        $poliswebDataArchive = "SELECT giudice AS " . self::$PRATICA_NOME_GIUDICE . ", 
                                        giudice AS " . self::$PRATICA_NOME_ISTRUTTORE . ",
										sezione AS " . self::$PRATICA_SEZIONE . " 
										FROM archivio pp WHERE id = ?";
        $poliswebDataArchive = $this->db->fetchRow($poliswebDataArchive, array($fId));

        if (empty($poliswebData) && empty($poliswebDataArchive)) {
            $poliswebData = array(self::$PRATICA_NOME_GIUDICE => self::$EMPTY_VALUE,
                self::$PRATICA_SEZIONE => self::$EMPTY_VALUE,
                self::$PRATICA_DATA_ISCRIZIONE_RUOLO => self::$EMPTY_VALUE
            );
        } else if (empty($poliswebData) && $poliswebDataArchive) {
            $poliswebData = array(self::$PRATICA_NOME_GIUDICE => $poliswebDataArchive[self::$PRATICA_NOME_GIUDICE],
                self::$PRATICA_NOME_ISTRUTTORE => $poliswebDataArchive[self::$PRATICA_NOME_ISTRUTTORE],
                self::$PRATICA_SEZIONE => $poliswebDataArchive[self::$PRATICA_SEZIONE]);
            $this->checkFields($poliswebData);
        }
        $this->checkFields($poliswebData);
        return $poliswebData;
    }


    /**
     * avvocato.CAP
     * avvocato.CITTA
     * avvocato.CODICE_FISCALE
     * avvocato.EMAIL
     * avvocato.FAX
     * avvocato.INDIRIZZO
     * avvocato.MOBILE
     * avvocato.NOME
     * avvocato.NOME_COGNOME
     * avvocato.NOME_STUDIO
     * avvocato.PARTITA_IVA
     * avvocato.PEC
     * avvocato.PROVINCIA
     * avvocato.TELEFONO
     */
    private function getLawyer($lId)
    {
        $lawyer = "SELECT ds.cap AS " . self::$AVVOCATO_CAP . ",
							ds.citta AS " . self::$AVVOCATO_CITTA . ",
							ds.codicefiscale AS " . self::$AVVOCATO_CODICE_FISCALE . ",
							ds.email AS " . self::$AVVOCATO_EMAIL . ",
							ds.fax AS " . self::$AVVOCATO_FAX . ",
							ds.indirizzo AS " . self::$AVVOCATO_INDIRIZZO . ",
							ds.mobile AS " . self::$AVVOCATO_MOBILE . ",
							avv.nome AS " . self::$AVVOCATO_NOME . ",
							CONCAT(utn.nomepersonale, ' ', utn.cognomepersonale) AS " . self::$AVVOCATO_NOME_COGNOME . ",
							ds.nome AS " . self::$AVVOCATO_NOME_STUDIO . ",
							ds.partitaiva AS " . self::$AVVOCATO_PARTITA_IVA . ",
							ds.provincia AS " . self::$AVVOCATO_PROVINCIA . ",
							ds.telefono AS " . self::$AVVOCATO_TELEFONO . "
					FROM tabellaavvocati avv 
					LEFT JOIN utente utn ON utn.codiceavvocato = avv.id
					INNER JOIN datistudio ds ON avv.id = ds.avvocato
					WHERE avv.id = ? AND tipoutente = 1";
        $lawyer = $this->db->fetchRow($lawyer, array($lId));
        $lawyerFound = !empty($lawyer);

        $pec = "SELECT uea.email
				FROM utente u INNER JOIN utente_email_account uea ON uea.user_id = u.id
				WHERE u.codiceavvocato = ? AND u.tipoutente = 1 AND u.nome <> 'specialuser' AND
						uea.reginde = 1 AND email_type = 1";
        $pec = $this->db->fetchOne($pec, array($lId));

        $lawyer[self::$AVVOCATO_PEC] = $pec;
        if (!$lawyerFound) {
            $lawyer = array(self::$AVVOCATO_CAP => self::$EMPTY_VALUE,
                self::$AVVOCATO_CITTA => self::$EMPTY_VALUE,
                self::$AVVOCATO_CODICE_FISCALE => self::$EMPTY_VALUE,
                self::$AVVOCATO_EMAIL => self::$EMPTY_VALUE,
                self::$AVVOCATO_FAX => self::$EMPTY_VALUE,
                self::$AVVOCATO_INDIRIZZO => self::$EMPTY_VALUE,
                self::$AVVOCATO_MOBILE => self::$EMPTY_VALUE,
                self::$AVVOCATO_NOME => self::$EMPTY_VALUE,
                self::$AVVOCATO_NOME_COGNOME => self::$EMPTY_VALUE,
                self::$AVVOCATO_NOME_STUDIO => self::$EMPTY_VALUE,
                self::$AVVOCATO_PARTITA_IVA => self::$EMPTY_VALUE,
                self::$AVVOCATO_PROVINCIA => self::$EMPTY_VALUE,
                self::$AVVOCATO_PEC => self::$EMPTY_VALUE,
                self::$AVVOCATO_TELEFONO => self::$EMPTY_VALUE);
        } else {
            $this->checkFields($lawyer);
        }
        return $lawyer;
    }

    /**
     * avversario.CAP
     * avversario.CITTA
     * avversario.CITTA_NASCITA
     * avversario.CODICE_FISCALE
     * avversario.DATA_NASCITA
     * avversario.EMAIL
     * avversario.FAX
     * avversario.INDIRIZZO
     * avversario.MOBILE_1
     * avversario.MOBILE_2
     * avversario.NOME
     * avversario.PARTITA_IVA
     * avversario.PEC
     * avversario.PROVINCIA
     * avversario.PROVINCIA_NASCITA
     * avversario.TELEFONO_CASA
     * avversario.TELEFONO_UFFICIO
     * avversario.TIPO
     * avversarsio.NOME_PEC
     *
     * dominus.CAP
     * dominus.CITTA
     * dominus.CITTA_NASCITA
     * dominus.CODICE_FISCALE
     * dominus.DATA_NASCITA
     * dominus.EMAIL
     * dominus.FAX
     * dominus.INDIRIZZO
     * dominus.MOBILE_1
     * dominus.MOBILE_2
     * dominus.NOME
     * dominus.PARTITA_IVA
     * dominus.PEC
     * dominus.PROVINCIA
     * dominus.PROVINCIA_NASCITA
     * dominus.TELEFONO_CASA
     * dominus.TELEFONO_UFFICIO
     *
     * cliente.CAP
     * cliente.CITTA
     * cliente.CITTA_NASCITA
     * cliente.CODICE_FISCALE
     * cliente.DATA_NASCITA
     * cliente.EMAIL
     * cliente.FAX
     * cliente.INDIRIZZO
     * cliente.MOBILE_1
     * cliente.MOBILE_2
     * cliente.NOME
     * cliente.PARTITA_IVA
     * cliente.PEC
     * cliente.PROVINCIA
     * cliente.PROVINCIA_NASCITA
     * cliente.TELEFONO_CASA
     * cliente.TELEFONO_UFFICIO
     * cliente.TIPO
     *
     * controparte.CAP
     * controparte.CITTA
     * controparte.CITTA_NASCITA
     * controparte.CODICE_FISCALE
     * cliente.DATA_NASCITA
     * controparte.EMAIL
     * controparte.FAX
     * controparte.INDIRIZZO
     * controparte.MOBILE_1
     * controparte.MOBILE_2
     * controparte.NOME
     * controparte.PARTITA_IVA
     * controparte.PEC
     * controparte.PROVINCIA
     * controparte.PROVINCIA_NASCITA
     * controparte.TELEFONO_CASA
     * controparte.TELEFONO_UFFICIO
     * controparte.TIPO
     *
     * controparte-cliente-dominus-avversario.COMUNE
     * controparte-cliente-dominus-avversario.DATAISCRIZIONE
     * controparte-cliente-dominus-avversario.SEZIONE
     * controparte-cliente-dominus-avversario.NUMEROREA
     * controparte-cliente-dominus-avversario.DELIBERATO
     * controparte-cliente-dominus-avversario.SOTTOSCRITTO
     * controparte-cliente-dominus-avversario.VERSATO
     * controparte-cliente-dominus-avversario.VALUTA
     * controparte-cliente-dominus-avversario.IBAN
     */
    private function getPart($pUid, $tableName)
    {

        $part = "SELECT '' AS " . self::$PARTE_CAP . ",
						'' AS " . self::$PARTE_NOME_CITTA . ",
						group_concat(IF(tc2.nome='', null, tc2.nome) SEPARATOR ', ') AS " . self::$PARTE_NOME_CITTA_NASCITA . ",
						group_concat(IF(c.codicefiscale='', null, c.codicefiscale) SEPARATOR ', ') AS " . self::$PARTE_CODICE_FISCALE . ",
						CASE WHEN (DATE_FORMAT(c.datanascita, '%d/%m/%Y') = '00/00/0000') THEN '' ELSE group_concat(IF(DATE_FORMAT(c.datanascita, '%d/%m/%Y')='', null, DATE_FORMAT(c.datanascita, '%d/%m/%Y')) SEPARATOR ', ') END AS " . self::$PARTE_DATA_NASCITA . ",
						'' AS " . self::$PARTE_EMAIL . ",
						'' AS " . self::$PARTE_FAX . ",
						'' AS " . self::$PARTE_INDIRIZZO . ",
						'' AS " . self::$PARTE_MOBILE_1 . ",
						'' AS " . self::$PARTE_MOBILE_2 . ",
						group_concat(IF(c.denominazione='', null, c.denominazione) SEPARATOR ', ') AS " . self::$PARTE_NOME . ",
						group_concat(IF(c.partitaiva='', null, c.partitaiva) SEPARATOR ', ') AS " . self::$PARTE_PARTITA_IVA . ",
						'' AS " . self::$PARTE_PEC . ",
						'' AS " . self::$PARTE_NOME_PEC . ",
						'' AS " . self::$PARTE_PROVINCIA . ",
						group_concat(IF(tc2.provincia='', null, tc2.provincia) SEPARATOR ', ') AS " . self::$PARTE_PROVINCIA_NASCITA . ",
						'' AS " . self::$PARTE_TELEFONO_CASA . ",
						'' AS " . self::$PARTE_TELEFONO_UFFICIO . ",
						c.valori_dinamici,
						group_concat(IF(ttc.nome='', null, ttc.nome) SEPARATOR ', ') AS " . self::$PARTE_TIPO . ",
						group_concat(IF(c.external_sw_id='', null, c.external_sw_id) SEPARATOR ', ') AS " . self::$CODICE_ESTERNA . ",
						group_concat(IF(tc3.nome='', null, tc3.nome) SEPARATOR ', ') AS " . self::$PARTE_COMUNE . ",
						group_concat(IF(c.annotazioni='', null, c.annotazioni) SEPARATOR ', ') AS " . self::$PARTE_NOTA . ",
						group_concat(IF(c.nrea='', null, c.nrea) SEPARATOR ', ') AS " . self::$PARTE_NUMEROREA . ",
						group_concat(IF(c.sezione='', null, c.sezione) SEPARATOR ', ') AS " . self::$PARTE_SEZIONE . ",
						group_concat(IF(DATE_FORMAT(c.dataiscrizione, '%d/%m/%Y')='', null, DATE_FORMAT(c.dataiscrizione, '%d/%m/%Y')) SEPARATOR ', ') AS " . self::$PARTE_DATAISCRIZIONE . ",
						group_concat(IF(c.deliberato='', null, c.deliberato) SEPARATOR ', ') AS " . self::$PARTE_DELIBERATO . ",
						group_concat(IF(c.sottoscritto='', null, c.sottoscritto) SEPARATOR ', ') AS " . self::$PARTE_SOTTOSCRITTO . ",
						group_concat(IF(c.versato='', null, c.versato) SEPARATOR ', ') AS " . self::$PARTE_VERSATO . ",
						group_concat(IF(tv.nome='', null, tv.nome) SEPARATOR ', ') AS " . self::$PARTE_VALUTA . ",
						group_concat(IF(c.iban='', null, c.iban) SEPARATOR ', ') AS " . self::$PARTE_IBAN . ",
						group_concat(IF(c.contatti='', null, c.contatti) SEPARATOR '|') AS contatti,
						group_concat(IF(c.indirizzi='', null, c.indirizzi) SEPARATOR '|') AS indirizzi,
                        group_concat(IF(i.descrizione='', null, i.descrizione) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_DESCRIZIONE . ",
                        group_concat(IF(i.indirizzo='', null, i.indirizzo) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_INDIRIZZO . ",
                        group_concat(IF(i.civico='', null, i.civico) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_CIVICO . ",
                        group_concat(IF(tci.nome='', null, tci.nome) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_CITTA . ", 
                        group_concat(IF(i.cap='', null, i.cap) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_CAP . ",
                        group_concat(IF(tci.provincia='', null, tci.provincia) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_PROVINCIA . ", 
                        group_concat(IF(i.nazione='', null, i.nazione) SEPARATOR ',') AS nazione_ids,
                        group_concat(IF(i.classe='', null, i.classe) SEPARATOR ',') AS pct_siecic_tipologia_classe_immobiliare_ids,
                        group_concat(IF(ia.diritto='', null, ia.diritto) SEPARATOR ',') AS pct_siecic_benipignorati_tipologia_diritto_ids,
                        group_concat(IF(i.valore='', null, i.valore) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_VALORE . ",
                        group_concat(DISTINCT CASE
                            WHEN i.tipocatasto = '1' THEN 'Edilizio Urbano'
                            WHEN i.tipocatasto = '2' THEN 'Terreni'
                            ELSE NULL
                        END SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_TIPOCATASTO . ",                        
                        group_concat(IF(ia.quota='', null, ia.quota) SEPARATOR ', ') AS " . self::$PARTE_IMMOBILE_QUOTA . "
						FROM anagrafiche c LEFT JOIN tabellacitta tc2 ON tc2.id = c.luogonascita LEFT JOIN tabellatipocliente ttc ON ttc.id = c.tipo
						LEFT JOIN tabellavalute tv ON tv.id = c.valuta 
						LEFT JOIN tabellacitta tc3 ON tc3.id = c.comune
                        LEFT JOIN immobile_anagrafica ia ON ia.anag_id = c.id
                        LEFT JOIN immobili i ON i.id = ia.immobile_id
                        LEFT JOIN tabellacitta AS tci ON tci.id = i.citta
						WHERE c.uniqueid";
        $where = ' IN (';
        $uIds = explode(',', $pUid);
        foreach ($uIds as $uId) {
            $where .= '?,';
        }
        $where = substr($where, 0, -1);
        $part .= $where . ')';
        $part = $this->db->fetchRow($part, $uIds);

        $part[self::$PARTE_IMMOBILE_NAZIONE] = null;
        if ($part['nazione_ids']) {
            $part[self::$PARTE_IMMOBILE_NAZIONE] = $this->getImmobiliareNazione($part['nazione_ids']);
        }

        unset($part['nazione_id']);


        $part[self::$PARTE_IMMOBILE_CLASSE] = null;
        if ($part['pct_siecic_tipologia_classe_immobiliare_ids']) {
            $part[self::$PARTE_IMMOBILE_CLASSE] = $this->getClasseImmobiliare($part['pct_siecic_tipologia_classe_immobiliare_ids']);
        }

        unset($part['pct_siecic_tipologia_classe_immobiliare_ids']);

        $part[self::$PARTE_IMMOBILE_DIRITTO] = null;
        if ($part['pct_siecic_benipignorati_tipologia_diritto_ids']) {
            $part[self::$PARTE_IMMOBILE_DIRITTO] = $this->getImmobiliareDiritto($part['pct_siecic_benipignorati_tipologia_diritto_ids']);
        }

        unset($part['pct_siecic_benipignorati_tipologia_diritto_ids']);


        if (!empty($part['indirizzi'])) {
            $indirizzi = self::getIndirizzi($part);
            $part[self::$PARTE_NOME_CITTA] = $indirizzi['citta'];
            $part[self::$PARTE_PROVINCIA] = $indirizzi['provincia'];
            $part[self::$PARTE_INDIRIZZO] = $indirizzi['via'];
            $part[self::$PARTE_CAP] = $indirizzi['cap'];
        }

        if (!empty($part['contatti'])) {
            $contatti = self::getContatti($part, $uIds, $tableName);
            $part[self::$PARTE_EMAIL] = $contatti['email'] ?? null;
            $part[self::$PARTE_PEC] = $contatti['pec'] ?? null;
            $part[self::$PARTE_NOME_PEC] = $contatti['nomepec'] ?? null;
            $part[self::$PARTE_TELEFONO_CASA] = $contatti['telefonocasa'] ?? null;
            $part[self::$PARTE_TELEFONO_UFFICIO] = $contatti['telefonoufficio'] ?? null;
            $part[self::$PARTE_MOBILE_1] = $contatti['mobile1'] ?? null;
            $part[self::$PARTE_MOBILE_2] = $contatti['mobile2'] ?? null;
            $part[self::$PARTE_FAX] = $contatti['fax'] ?? null;
        }

        // CAMPI DINAMICI ANAGRAFICHE
        if (!empty($part['valori_dinamici']) && $part['valori_dinamici'] != 'null') {
            $campi = json_decode($part['valori_dinamici'], true);
            foreach ($campi as $campo) {
                $valoreCampo = $campo['valore'];
                $idCampo = $campo['id'];
                $tipo = $this->db->fetchOne("SELECT tipo FROM campi_dinamici WHERE id = ?", $idCampo);
                if ($tipo == 'date') {
                    if (!empty($valoreCampo)) {
                        $valoreCampo = date('d/m/Y', strtotime($valoreCampo));
                    } else {
                        $valoreCampo = '';
                    }
                }
                if ($tipo == 'select') {
                    $opt = $this->db->fetchOne("SELECT opt FROM campi_dinamici WHERE id = ?", $idCampo);
                    $opt = json_decode($opt, true);
                    foreach ($opt as $value => $idx) {
                        if ($idx == $campo['valore']) {
                            $valoreCampo = $value;
                            break;
                        }
                    }
                }
                $part["CAMPO_DINAMICO_$idCampo"] = $valoreCampo;
            }
        }

        $this->checkFields($part);


        return $part;
    }

    private function getImmobiliareNazione(string $nazioneIds): ?string
    {
        $nazioni = $this->dbShared->fetchAll("SELECT id, nome FROM nazioni WHERE id in (" . $nazioneIds . ")");

        if ($nazioni) {
            $nazioniArray = [];
            foreach ($nazioni as $nazione) {
                $nazioniArray[$nazione['id']] = $nazione['nome'];
            }

            $nazioneIds = explode(',', $nazioneIds);
            $nazioniResult = [];
            foreach ($nazioneIds as $nazioneId) {
                $nazioniResult[] = $nazioniArray[$nazioneId];
            }

            return implode(', ', $nazioniResult);
        }

        return null;
    }

    private function getImmobiliareDiritto(string $tipologiaDirittoIds): ?string
    {
        $tipologieDiritto = $this->dbShared->fetchAll("SELECT id, nome FROM pct_siecic_benipignorati_tipologia_diritto WHERE id in (" . $tipologiaDirittoIds . ")");

        if ($tipologieDiritto) {
            $tipologieDirittoArray = [];
            foreach ($tipologieDiritto as $tipologiaDiritto) {
                $tipologieDirittoArray[$tipologiaDiritto['id']] = $tipologiaDiritto['nome'];
            }

            $tipologiaDirittoIds = explode(',', $tipologiaDirittoIds);
            $tipologieDirittoResult = [];
            foreach ($tipologiaDirittoIds as $tipologiaDirittoId) {
                $tipologieDirittoResult[] = $tipologieDirittoArray[$tipologiaDirittoId];
            }

            return implode(', ', $tipologieDirittoResult);
        }

        return null;
    }

    private function getClasseImmobiliare(string $classiImmobiliareIds): ?string
    {
        $classiImmobiliari = $this->dbShared->fetchAll("SELECT id, valore, descrizione FROM pct_siecic_tipologia_classe_immobiliare WHERE id in (" . $classiImmobiliareIds . ")");

        if ($classiImmobiliari) {
            $classiImmobiliariArray = [];
            foreach ($classiImmobiliari as $classeImmobiliare) {
                $classiImmobiliariArray[$classeImmobiliare['id']] = $classeImmobiliare['valore'];

                $valore = ! empty($classeImmobiliare['valore']) ? $classeImmobiliare['valore'] : null;
                $descrizione = ! empty($classeImmobiliare['descrizione']) ? $classeImmobiliare['descrizione'] : null;

                $concat = '';

                if ($valore) {
                    $concat .= $valore;
                }
                if ($valore && $descrizione) {
                    $concat .= ' - ';
                }
                if ($descrizione) {
                    $concat .= $descrizione;
                }

                if (! empty($concat)) {
                    $classiImmobiliariArray[$classeImmobiliare['id']] = $concat;
                }
            }

            $classiImmobiliareIds = explode(',', $classiImmobiliareIds);
            $classiImmobiliariResult = [];
            foreach ($classiImmobiliareIds as $classiImmobiliareId) {
                $classiImmobiliariResult[] = $classiImmobiliariArray[$classiImmobiliareId];
            }
            return implode(', ', $classiImmobiliariResult);
        }

        return null;
    }

    private function getEmptyPart()
    {
        return [
            self::$PARTE_CAP => self::$EMPTY_VALUE,
            self::$PARTE_NOME_CITTA => self::$EMPTY_VALUE,
            self::$PARTE_CODICE_FISCALE => self::$EMPTY_VALUE,
            self::$PARTE_DATA_NASCITA => self::$EMPTY_VALUE,
            self::$PARTE_EMAIL => self::$EMPTY_VALUE,
            self::$PARTE_FAX => self::$EMPTY_VALUE,
            self::$PARTE_INDIRIZZO => self::$EMPTY_VALUE,
            self::$PARTE_NOME_CITTA_NASCITA => self::$EMPTY_VALUE,
            self::$PARTE_MOBILE_1 => self::$EMPTY_VALUE,
            self::$PARTE_MOBILE_2 => self::$EMPTY_VALUE,
            self::$PARTE_NOME => self::$EMPTY_VALUE,
            self::$PARTE_PARTITA_IVA => self::$EMPTY_VALUE,
            self::$PARTE_PEC => self::$EMPTY_VALUE,
            self::$PARTE_NOME_PEC => self::$EMPTY_VALUE,
            self::$PARTE_PROVINCIA => self::$EMPTY_VALUE,
            self::$PARTE_PROVINCIA_NASCITA => self::$EMPTY_VALUE,
            self::$PARTE_TELEFONO_CASA => self::$EMPTY_VALUE,
            self::$PARTE_TELEFONO_UFFICIO => self::$EMPTY_VALUE,
            self::$PARTE_TIPO => self::$EMPTY_VALUE,
            self::$CODICE_ESTERNA => self::$EMPTY_VALUE,
            self::$PARTE_COMUNE => self::$EMPTY_VALUE,
            self::$PARTE_NOTA => self::$EMPTY_VALUE,
            self::$PARTE_NUMEROREA => self::$EMPTY_VALUE,
            self::$PARTE_SEZIONE => self::$EMPTY_VALUE,
            self::$PARTE_DATAISCRIZIONE => self::$EMPTY_VALUE,
            self::$PARTE_DELIBERATO => self::$EMPTY_VALUE,
            self::$PARTE_SOTTOSCRITTO => self::$EMPTY_VALUE,
            self::$PARTE_VERSATO => self::$EMPTY_VALUE,
            self::$PARTE_VALUTA => self::$EMPTY_VALUE,
            self::$PARTE_IBAN => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_DESCRIZIONE => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_INDIRIZZO => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_CIVICO => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_CITTA => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_CAP => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_PROVINCIA => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_NAZIONE => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_VALORE => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_TIPOCATASTO => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_CLASSE => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_DIRITTO => self::$EMPTY_VALUE,
            self::$PARTE_IMMOBILE_QUOTA => self::$EMPTY_VALUE,
        ];
    }

    /**
     * utente.CARTA_INTESTATA
     */
    private function getLoggedUser($fileLawyer, $s3Connector)
    {
        $user = "
			SELECT l.id, l.filename
			FROM utente u LEFT JOIN letterheads l ON u.letterhead_id = l.id
			WHERE u.codiceavvocato = ?
			LIMIT 1";
        $user = $this->db->fetchRow($user, array($fileLawyer));
        if (!empty($user) && !empty($user["id"]) && !empty($user["filename"])) {
            $fileArray = explode(".", $user["filename"]);
            $ext = $fileArray[count($fileArray) - 1];
            $file = $s3Connector->getBody($this->main_bucket, $this->sitekey . "/" . self::$LETTERHEADS . "/" . $user["id"] . "/" . $user["filename"]);
            $tmpDir = sys_get_temp_dir() . "/" . $this->sitekey . "-" . self::$LETTERHEADS . "-" . $user["id"] . "-" . $this->db->getUid() . "-" . $this->db->getUid() . "-" . $this->db->getUid() . ".$ext";
// 			$this->applogger->info($tmpDir);
            $handle = fopen($tmpDir, "w");
            fwrite($handle, $file);
            fclose($handle);
            $result = array(self::$UTENTELOGGED_CARTA_INTESTATA => $tmpDir);
        } else {
            $result = array(self::$UTENTELOGGED_CARTA_INTESTATA => "");
        }
        return $result;
    }

    private function getUser($userId, $fileId, $type)
    {
        $user = "SELECT GROUP_CONCAT(IF(u.nomepersonale='', null, u.nomepersonale) SEPARATOR ', ') AS " . self::$UTENTE_NOME . ",
							GROUP_CONCAT(IF(u.cognomepersonale='', null, u.cognomepersonale) SEPARATOR ', ') AS " . self::$UTENTE_COGNOME . ",
							GROUP_CONCAT(IF(u.nomeutente='', null, u.nomeutente) SEPARATOR ', ') AS " . self::$UTENTE_NOME_COGNOME . ",
							GROUP_CONCAT(IF(u.sigla='', null, u.sigla) SEPARATOR ', ') AS " . self::$UTENTE_SIGLA . ",
							GROUP_CONCAT(IF(u.natoil='', null, u.natoil) SEPARATOR ', ') AS " . self::$UTENTE_DATA_NASCITA . ",
							GROUP_CONCAT(IF(u.Email='', null, u.Email) SEPARATOR ', ') AS " . self::$UTENTE_EMAIL . ",
							GROUP_CONCAT(IF(ds.cap='', null, ds.cap) SEPARATOR ', ') AS " . self::$UTENTE_CAP . ",
							GROUP_CONCAT(IF(ds.citta='', null, ds.citta) SEPARATOR ', ') AS " . self::$UTENTE_CITTA . ",
							GROUP_CONCAT(IF(ds.codicefiscale='', null, ds.codicefiscale) SEPARATOR ', ') AS " . self::$UTENTE_CODICE_FISCALE . ",
							GROUP_CONCAT(IF(ds.email='', null, ds.email) SEPARATOR ', ') AS " . self::$UTENTE_EMAIL . ",
							GROUP_CONCAT(IF(ds.fax='', null, ds.fax) SEPARATOR ', ') AS " . self::$UTENTE_FAX . ",
							GROUP_CONCAT(IF(ds.indirizzo='', null, ds.indirizzo) SEPARATOR ', ') AS " . self::$UTENTE_INDIRIZZO . ",
							GROUP_CONCAT(IF(ds.mobile='', null, ds.mobile) SEPARATOR ', ') AS " . self::$UTENTE_MOBILE . ",
							GROUP_CONCAT(IF(ds.nome='', null, ds.nome) SEPARATOR ', ') AS " . self::$UTENTE_NOME_STUDIO . ",
							GROUP_CONCAT(IF(ds.partitaiva='', null, ds.partitaiva) SEPARATOR ', ') AS " . self::$UTENTE_PARTITA_IVA . ",
							GROUP_CONCAT(IF(ds.provincia='', null, ds.provincia) SEPARATOR ', ') AS " . self::$UTENTE_PROVINCIA . ",
							GROUP_CONCAT(IF(ds.telefono='', null, ds.telefono) SEPARATOR ', ') AS " . self::$UTENTE_TELEFONO . "
					FROM utente u
					LEFT JOIN utente_pratica up ON u.id = up.person_id
					LEFT JOIN datistudio ds ON u.id = ds.avvocato AND u.tipoutente = 1
					WHERE up.file_id = ? AND up.relazione = ?";

        $data = array($fileId, $type);
        $where = ' AND u.id IN (';
        $ids = explode(',', $userId);
        foreach ($ids as $id) {
            $where .= '?,';
            array_push($data, $id);
        }
        $where = substr($where, 0, -1);
        $user .= $where . ')';
        $user = $this->db->fetchRow($user, $data);
        $dates = explode(',', $user[self::$UTENTE_DATA_NASCITA]);
        $dateString = "";
        foreach ($dates as $d) {
            $dateString .= date('d/m/Y', strtotime($d)) . ', ';
        }
        $user[self::$UTENTE_DATA_NASCITA] = substr($dateString, 0, -2);
        $this->checkFields($user);
        return $user;
    }


    private function getClauses($stringIdlist)
    {
        try {
            array_map(function ($item) {
                if ((int)$item === 0) {
                    throw new \Exception('no valid id given');
                }
            }, explode(',', $stringIdlist));

            $clausus = $this->db->fetchAll("SELECT id, UPPER(REPLACE(nome, ' ', '_')) as nome, content FROM clauses where id IN " . '(' . $stringIdlist . ')');
            return array_reduce($clausus, function ($acc, $item) {
                $acc[$item['id']] =
                    [
                        'nome' => $item['nome'],
                        'content' => $item['content'],
                ];
                // $acc needs also to containes the name of the clause in a property equal to the name itself
                return $acc;
            }, []);

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * documento.HASH
     * documento.NOME
     */
    private function getDocuments($documents)
    {
        $result = [];

        $documentsArr = explode(',', $documents);
        $placeholders = implode(',', array_fill(0, count($documentsArr), '?'));
        $files = $this->db->fetchCol("SELECT nomefile FROM documento WHERE uniqueid IN ($placeholders) ORDER BY nomefile ASC ", $documentsArr);

        foreach ($files as $fileName) {
// 				$documentDir = $dir . $document["codicepratica"] . "/" . $document["nomefile"];
// 				$documentHash = hash_file("sha256", $documentDir);
            $result[] = [
                [
                    /*self::$DOCUMENTO_HASH => $documentHash,*/
                    self::$DOCUMENTO_NOME => $fileName,
                ],
            ];
        }

        return $result;
    }

    /**
     * recuperocrediti.SOGGETTO
     * recuperocrediti.SOGGETTO_NOME
     * recuperocrediti.SOGGETTO_CAP
     * recuperocrediti.SOGGETTO_CITTA
     * recuperocrediti.SOGGETTO_PROVINCIA
     * recuperocrediti.SOGGETTO_PEC
     * recuperocrediti.INTESTATARIO
     * recuperocrediti.DATA
     * recuperocrediti.INTERESSI
     * recuperocrediti.DIRITTI
     * recuperocrediti.ONORARI
     * recuperocrediti.TOTALE_ODI
     * recuperocrediti.ONERI_RIFLESSI
     * recuperocrediti.PERC_ONERI_RIFLESSI
     * recuperocrediti.SPESE_GENERALI
     * recuperocrediti.PERC_SPESE_GENERALI
     * recuperocrediti.TOTALE_OS
     * recuperocrediti.SPESE_ESENTI
     * recuperocrediti.RITENUTA_ACCONTO
     * recuperocrediti.PERC_RITENUTA_ACCONTO
     * recuperocrediti.TOTALE_DA_RECUPERARE
     */
    private function getCreditrecoveriesPa($lId)
    {
        $creditrecovery = "SELECT a.denominazione AS " . self::$RECUPEROCREDITI_SOGGETTO . ",
                            a.nome AS " . self::$RECUPEROCREDITI_SOGGETTO_NOME . ",
                            a.indirizzi,
                            a.contatti,
                            '' AS '" . self::$RECUPEROCREDITI_SOGGETTO_CAP . "',
                            '' AS '" . self::$RECUPEROCREDITI_SOGGETTO_CITTA . "',
                            '' AS " . self::$RECUPEROCREDITI_SOGGETTO_PROVINCIA . ",
                            '' AS " . self::$RECUPEROCREDITI_SOGGETTO_PEC . ",
							u.nomeutente AS " . self::$RECUPEROCREDITI_INTESTATARIO . ",
							DATE_FORMAT(rcd.creation_date, '%d/%m/%Y') AS " . self::$RECUPEROCREDITI_DATA . ",
							rcd.pa_interests AS " . self::$RECUPEROCREDITI_INTERESSI . ",
							rcd.pa_rights AS " . self::$RECUPEROCREDITI_DIRITTI . ",
							rcd.pa_honoray AS " . self::$RECUPEROCREDITI_ONORARI . ",
							rcd.pa_first_total AS " . self::$RECUPEROCREDITI_TOTALE_ODI . ",
							rcd.pa_reflected_charges AS " . self::$RECUPEROCREDITI_ONERI_RIFLESSI . ",
							rcd.pa_reflected_charges_perc AS " . self::$RECUPEROCREDITI_PERC_ONERI_RIFLESSI . ",
							rcd.pa_overheads AS " . self::$RECUPEROCREDITI_SPESE_GENERALI . ",
							rcd.pa_overheads_perc AS " . self::$RECUPEROCREDITI_PERC_SPESE_GENERALI . ",
							rcd.pa_second_total AS " . self::$RECUPEROCREDITI_TOTALE_OS . ",
							rcd.pa_expenditure_net AS " . self::$RECUPEROCREDITI_SPESE_ESENTI . ",
							rcd.pa_withholding_tax AS " . self::$RECUPEROCREDITI_RITENUTA_ACCONTO . ",
							rcd.pa_withholding_tax_perc AS " . self::$RECUPEROCREDITI_PERC_RITENUTA_ACCONTO . ",
							rcd.remaining AS " . self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE . "
					FROM recuperocrediti_documenti rcd 
					LEFT JOIN utente u ON u.id = rcd.pa_assignee_id
					LEFT JOIN anagrafiche a ON a.id = rcd.pa_subject_id
                    WHERE rcd.id = ?";
        $creditrecovery = $this->db->fetchRow($creditrecovery, array($lId));
        $creditrecoveryFound = !empty($creditrecovery);

        if (!empty($creditrecovery['indirizzi'])) {
            $indirizzi = self::getIndirizzi($creditrecovery);
            $creditrecovery[self::$RECUPEROCREDITI_SOGGETTO_CITTA] = $indirizzi['citta'];
            $creditrecovery[self::$RECUPEROCREDITI_SOGGETTO_PROVINCIA] = $indirizzi['provincia'];
            $creditrecovery[self::$RECUPEROCREDITI_SOGGETTO_CAP] = $indirizzi['cap'];
        }

        if (!empty($creditrecovery['contatti'])) {
            $contatti = self::getContatti($creditrecovery);
            $creditrecovery[self::$RECUPEROCREDITI_SOGGETTO_PEC] = $contatti['pec'] ?? null;
        }

        if (!$creditrecoveryFound) {
            $creditrecovery = array(self::$AVVOCATO_CAP => self::$EMPTY_VALUE,
                self::$AVVOCATO_CITTA => self::$EMPTY_VALUE,
                self::$AVVOCATO_CODICE_FISCALE => self::$EMPTY_VALUE,
                self::$AVVOCATO_EMAIL => self::$EMPTY_VALUE,
                self::$AVVOCATO_FAX => self::$EMPTY_VALUE,
                self::$AVVOCATO_INDIRIZZO => self::$EMPTY_VALUE,
                self::$AVVOCATO_MOBILE => self::$EMPTY_VALUE,
                self::$AVVOCATO_NOME => self::$EMPTY_VALUE,
                self::$AVVOCATO_NOME_COGNOME => self::$EMPTY_VALUE,
                self::$AVVOCATO_NOME_STUDIO => self::$EMPTY_VALUE,
                self::$AVVOCATO_PARTITA_IVA => self::$EMPTY_VALUE,
                self::$AVVOCATO_PROVINCIA => self::$EMPTY_VALUE,
                self::$AVVOCATO_PEC => self::$EMPTY_VALUE,
                self::$AVVOCATO_TELEFONO => self::$EMPTY_VALUE);
        } else {
            $this->checkFields($creditrecovery);
            if ($creditrecovery[self::$RECUPEROCREDITI_ONORARI])
                $creditrecovery[self::$RECUPEROCREDITI_ONORARI] = number_format($creditrecovery[self::$RECUPEROCREDITI_ONORARI], 2, ',', '.');
            if ($creditrecovery[self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE])
                $creditrecovery[self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE] = number_format($creditrecovery[self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE], 2, ',', '.');
            if ($creditrecovery[self::$RECUPEROCREDITI_SPESE_GENERALI])
                $creditrecovery[self::$RECUPEROCREDITI_SPESE_GENERALI] = number_format($creditrecovery[self::$RECUPEROCREDITI_SPESE_GENERALI], 2, ',', '.');
            if ($creditrecovery[self::$RECUPEROCREDITI_ONERI_RIFLESSI])
                $creditrecovery[self::$RECUPEROCREDITI_ONERI_RIFLESSI] = number_format($creditrecovery[self::$RECUPEROCREDITI_ONERI_RIFLESSI], 2, ',', '.');
            if ($creditrecovery[self::$RECUPEROCREDITI_SPESE_ESENTI])
                $creditrecovery[self::$RECUPEROCREDITI_SPESE_ESENTI] = number_format($creditrecovery[self::$RECUPEROCREDITI_SPESE_ESENTI], 2, ',', '.');
        }

        return $creditrecovery;
    }


    private function getCreditrecoveries($lId)
    {
        $creditrecovery = "SELECT
           rcd.number AS " . self::$RECUPEROCREDITI_NUMERO . ",
           rcd.rif_fattura AS " . self::$RECUPEROCREDITI_NUMERO_FATTURA . ",
           rcd.year " . self::$RECUPEROCREDITI_ANNO . ",
           DATE_FORMAT(rcd.creation_date, '%d/%m/%Y') AS " . self::$RECUPEROCREDITI_DATA . ",
           DATE_FORMAT(rcd.deadline_date, '%d/%m/%Y') AS " . self::$RECUPEROCREDITI_DATA_SCADENZA . ",
           DATE_FORMAT(rcd.reminder_date, '%d/%m/%Y') AS " . self::$RECUPEROCREDITI_DATA_SOLLECITO . ",
           rcd.amount AS " . self::$RECUPEROCREDITI_IMPORTO_FATTURA . ",
           rcd.remaining AS " . self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE . ",
           (SELECT SUM(rci.total_interests) FROM recuperocrediti_interessi rci WHERE rci.recuperocrediti_doc_id = rcd.id) as " . self::$RECUPEROCREDITI_TOTALE_INTERESSI . ",
           (CASE rcd.document_type
                WHEN document_type = 1 THEN 'Fattura'
                WHEN document_type = 0 THEN 'Nota di credito'
                WHEN document_type = -1 THEN rcd.document_type_label
           END) as " . self::$RECUPEROCREDITI_TIPO_DOCUMENTO . "
           FROM recuperocrediti_documenti rcd
           WHERE rcd.id = ?";

        $creditrecovery = $this->db->fetchRow($creditrecovery, array($lId));
        $creditrecoveryFound = !empty($creditrecovery);

        if ($creditrecoveryFound) {
            $this->checkFields($creditrecovery);
            if ($creditrecovery[self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE])
                $creditrecovery[self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE] = number_format($creditrecovery[self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE], 2, ',', '.');
            if ($creditrecovery[self::$RECUPEROCREDITI_IMPORTO_FATTURA])
                $creditrecovery[self::$RECUPEROCREDITI_IMPORTO_FATTURA] = number_format($creditrecovery[self::$RECUPEROCREDITI_IMPORTO_FATTURA], 2, ',', '.');
            if ($creditrecovery[self::$RECUPEROCREDITI_TOTALE_INTERESSI])
                $creditrecovery[self::$RECUPEROCREDITI_TOTALE_INTERESSI] = number_format($creditrecovery[self::$RECUPEROCREDITI_TOTALE_INTERESSI], 2, ',', '.');
        } else {
            $creditrecovery = array(
                self::$RECUPEROCREDITI_NUMERO => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_NUMERO_FATTURA => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_ANNO => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_DATA => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_DATA_SCADENZA => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_DATA_SOLLECITO => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_IMPORTO_FATTURA => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_TOTALE_DA_RECUPERARE => self::$EMPTY_VALUE,
                self::$RECUPEROCREDITI_TIPO_DOCUMENTO => self::$EMPTY_VALUE
            );
        }
        return $creditrecovery;

    }


    /**
     * liquidazione.R_NUMERO
     * liquidazione.R_DATA
     * liquidazione.R_NUMERO_SID
     * liquidazione.R_DATA_SID
     * liquidazione.R_IMPORTO
     * liquidazione.R_MANDATO
     * liquidazione.R_DATA_MANDATO
     * liquidazione.R_ASSEGNATARIO
     * liquidazione.R_IMPOSTA
     */
    private function getLiquidations($fileId)
    {
        $liquidation = "SELECT pa.r_number AS " . self::$LIQUIDAZIONE_R_NUMERO . ",
                            DATE_FORMAT(pa.r_date, '%d/%m/%Y') AS " . self::$LIQUIDAZIONE_R_DATA . ",
                            pa.r_sid_number AS " . self::$LIQUIDAZIONE_R_NUMERO_SID . ",
                            DATE_FORMAT(pa.r_sid_date, '%d/%m/%Y') AS " . self::$LIQUIDAZIONE_R_DATA_SID . ",
                            pa.r_importo AS " . self::$LIQUIDAZIONE_R_IMPORTO . ",
                            pa.r_mandate AS " . self::$LIQUIDAZIONE_R_MANDATO . ",
                            DATE_FORMAT(pa.r_mandate_date, '%d/%m/%Y') AS " . self::$LIQUIDAZIONE_R_DATA_MANDATO . ",
                            pa.r_assignee AS " . self::$LIQUIDAZIONE_R_ASSEGNATARIO . ",
                            pa.r_imposta AS " . self::$LIQUIDAZIONE_R_IMPOSTA . "
					FROM liquidazionepa pa 
					WHERE pa.archive_id = ?";
        $liquidation = $this->db->fetchRow($liquidation, array($fileId));

        $this->checkFields($liquidation);

        return $liquidation;
    }

    private function getEmptyClaim(): array
    {
        return [
            self::CLAIMS_NUMERO => self::$EMPTY_VALUE,
            self::CLAIMS_RIF_ESTERNO => self::$EMPTY_VALUE,
            self::CLAIMS_DATA_SINISTRO => self::$EMPTY_VALUE,
            self::CLAIMS_DATA_NOTIFICA => self::$EMPTY_VALUE,
            self::CLAIMS_IMPORTO_DANNO => self::$EMPTY_VALUE,
            self::CLAIMS_IMPORTO_DA_RECUPERARE => self::$EMPTY_VALUE,
            self::CLAIMS_DESCRIZIONE => self::$EMPTY_VALUE,
        ];
    }

    private function getEmptyCreditrecoveries(): array
    {
        return [
            self::$AVVOCATO_CAP => self::$EMPTY_VALUE,
            self::$AVVOCATO_CITTA => self::$EMPTY_VALUE,
            self::$AVVOCATO_CODICE_FISCALE => self::$EMPTY_VALUE,
            self::$AVVOCATO_EMAIL => self::$EMPTY_VALUE,
            self::$AVVOCATO_FAX => self::$EMPTY_VALUE,
            self::$AVVOCATO_INDIRIZZO => self::$EMPTY_VALUE,
            self::$AVVOCATO_MOBILE => self::$EMPTY_VALUE,
            self::$AVVOCATO_NOME => self::$EMPTY_VALUE,
            self::$AVVOCATO_NOME_COGNOME => self::$EMPTY_VALUE,
            self::$AVVOCATO_NOME_STUDIO => self::$EMPTY_VALUE,
            self::$AVVOCATO_PARTITA_IVA => self::$EMPTY_VALUE,
            self::$AVVOCATO_PROVINCIA => self::$EMPTY_VALUE,
            self::$AVVOCATO_PEC => self::$EMPTY_VALUE,
            self::$AVVOCATO_TELEFONO => self::$EMPTY_VALUE,
        ];
    }

    protected function getFileName($title, $fileName)
    {
        $fileArray = explode(".", $fileName);
        $ext = $fileArray[count($fileArray) - 1];
        return $this->escapeSpecialChars($title . "." . $ext);
    }

    private function createRG($numberKey, $yearKey, $subKey, $tagName, &$file)
    {
        $rg = "";
        if (!empty($file[$numberKey]) && !empty($file[$yearKey])) {
            $rg = $file[$numberKey] . "/" . $file[$yearKey];
            if (!empty($subKey) && !empty($file[$subKey])) {
                $rg .= "-" . $file[$subKey];
            }
        }
        unset($file[$numberKey]);
        unset($file[$yearKey]);
        if (!empty($subKey)) {
            $file[$subKey];
        }
        $file[$tagName] = $rg;
    }

    private function checkFields(&$items)
    {
        foreach ($items as $key => $value) {
            $value = trim($value);
            if (empty($value)) {
                $items[$key] = self::$EMPTY_VALUE;
            }
        }
    }


    /**
     * @param $fileName
     * @return $fileName without special chars
     * @throws Exception
     */
    protected function escapeSpecialChars($fileName)
    {
        try {
            $fileName = str_replace(array(" ", '"', "$", "&", "+", "*", ",", "/", ":", ";", "=", "?", "@", "<", ">", "#", "%", "{", "}", "|", "\\", "^", "~", "[", "]", "`"),
                array("_", "_", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""),
                $fileName);
            return $fileName;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * @param $campi
     * @param $file
     * @param $type
     * @param $prefix
     * @return $file Campi dinamici formattati e aggiunti al resto
     * @throws Exception
     */
    protected function formatCampiDinamici($campi, $file, $type, $prefix = null)
    {
        foreach ($campi as $campo) {

            $valoreCampo = $campo['valore'];
            $idCampo = $campo['id'];
            $campoName = "";

            if ($type == "contrattualistica") {
                $campoData = $this->db->fetchRow('SELECT tipo, opt FROM campi_dinamici WHERE id =?', $idCampo);
                $campoName = $prefix . $idCampo;
            } else {
                $stmt = $this->dbShared->query("SELECT tipo, opt, tagprint FROM archivio_dati_giud_campi WHERE id = ?", $idCampo);
                $campoData = $stmt->fetchAll()[0];
                $prefix = $campoData['tagprint'];
                $campoName = $prefix;
            }

            switch ($campoData['tipo']) {
                case 'valore':
                    $valoreCampo = number_format($valoreCampo, 2, ",", ".");
                    break;
                case 'time':
                    $valoreCampo = date('H:i', strtotime($valoreCampo));
                    break;
                case 'checkbox':
                    if ($valoreCampo == 'on' || $valoreCampo == 'true') {
                        $valoreCampo = "Si";
                    } else {
                        $valoreCampo = "No";
                    }
                    break;
                case 'select':
                    $lista_valori_select = json_decode($campoData['opt'], true);
                    if ($valoreCampo == 0) {
                        $valoreCampo = "";
                    } else {
                        $i = 0;
                        foreach ($lista_valori_select as $valori_select) {
                            if ($valori_select == $valoreCampo) {
                                $reflect = array_keys($lista_valori_select);
                                $valoreCampo = $reflect[$i];
                                break;
                            }

                            $i = $i + 1;
                        }
                    }
                    break;
                case 'date':
                    $valoreCampo = date('d/m/Y', strtotime($valoreCampo));
                    break;
            }
            $file[$campoName] = $valoreCampo;
        }
        return $file;
    }


    /**
     * @param array $tags
     * @param string $pattern
     * @param string $blockName
     * @param array $actorUniqueidList
     * @param clsTinyButStrong $TBS
     * @param $getCallback
     * @return void
     */
    private function mergeBlockByCardinal(array            $tags, string $pattern, string $blockName, array $actorUniqueidList,
                                          clsTinyButStrong $TBS,
                                                           $getCallback): void
    {
        $matches = [];
        preg_match_all($pattern, implode(',', array_unique(array_keys($tags))),
            $matches, PREG_PATTERN_ORDER);
        $count = max(array_unique($matches[1]));

        if ($count > 0) {

            for ($i = 0; $i < $count; $i++) {
                $item = $getCallback($actorUniqueidList[$i], $blockName);
                if ($actorUniqueidList[$i]) {

                    $TBS->MergeBlock($blockName . ($i + 1), array($item));
                } else {
                    $TBS->MergeBlock($blockName . ($i + 1), array());
                }
            }
        }
    }


    /**
     * @param array $tags
     * @param string $actor
     * @param $actorUniqueidList
     * @param array $groupedRoleSubjects
     * @param clsTinyButStrong $TBS
     * @return void
     */
    private function mergeBlockActorPart(array $tags, string $actor, $actorUniqueidList, array $groupedRoleSubjects, clsTinyButStrong $TBS): void
    {
        $selectedSubjectByRole = [];
        foreach ($actorUniqueidList as $actorUniqueid) {
            $subjectFound = array_values(array_filter($groupedRoleSubjects,
                function ($subject) use ($actorUniqueid) {
                    return $subject['uniqueid'] === $actorUniqueid;
                }))[0];
            $selectedSubjectByRole[$subjectFound['nome']][] = $actorUniqueid;
        }

        /*
         * [ATTORE] => [1,2],
         * [RICORRENTE] => [4],
         * */

        $this->mergeBlockByCardinal(
            $tags, '/' . $actor . '(\d{1,2})/', $actor, $actorUniqueidList, $TBS,
            function ($item, $blockName) {
                return $this->getPart($item, $blockName);
            }
        );

        foreach ($selectedSubjectByRole as $key => $roleList) {
            $this->mergeBlockByCardinal($tags,
                '/' . $actor . '_' . preg_replace('/ /', '_', $key) . '_(\d{1,2})/',
                $actor . '_' . preg_replace('/ /', '_', $key) . '_', $roleList, $TBS,
                function ($item, $blockName) {
                    return $this->getPart($item, $blockName);
                }
            );
        }
    }

    /**
     * @param $fileUid
     * @param $userType
     * @param string $actor
     * @param $actorUniqueidList
     * @param array $tags
     * @param clsTinyButStrong $TBS
     * @return void
     */
    private function mergeBlockUserPart($fileUid, $userType,
                                        string $actor, $actorUniqueidList, array $tags, clsTinyButStrong $TBS): void
    {

        $this->mergeBlockByCardinal($tags, '/' . $actor . '(\d{1,2})/', $actor, $actorUniqueidList, $TBS,
            function ($item, $blockName) use ($fileUid, $userType) {
                return $this->getUser($item, $fileUid, $userType);
            });

    }

    /**
     * @param $rcId
     * @return array
     */
    private function getInterestList($rcId): array
    {
        $interestsList = "SELECT
             DATE_FORMAT(start_date, '%d/%m/%Y') as " . self::$RECUPERO_CREDITI_INTERESSE_DATA_INIZIO . ",
             DATE_FORMAT(end_date, '%d/%m/%Y') as " . self::$RECUPERO_CREDITI_INTERESSE_DATA_FINE . ",
            total_days as " . self::$RECUPERO_CREDITI_INTERESSE_TOTALE_GIORNI . ",
            total_interests as " . self::$RECUPERO_CREDITI_INTERESSE_INTERESSI . ",
            capital as " . self::$RECUPERO_CREDITI_INTERESSE_TOTALE . ",
            DATE_FORMAT(creation_date, '%d/%m/%Y') as " . self::$RECUPERO_CREDITI_INTERESSE_DATA_CREAZIONE . ",
            charges_applied as " . self::$RECUPERO_CREDITI_INTERESSE_AGGIUNTIVA . ",
            forfait_applied as " . self::$RECUPERO_CREDITI_INTERESSE_COSTI_AGGIUNTIVI . ",
            range_applied as " . self::$RECUPERO_CREDITI_INTERESSE_IMPORTO_SOGLIA . "
        FROM recuperocrediti_interessi as rci 
        WHERE rci.recuperocrediti_doc_id = ?";

        return $this->db->fetchAll($interestsList, $rcId);

    }

    private function getRecoveryCreditMovementsList($rcId): array
    {
        $interestsList = "SELECT
        CASE 
            WHEN rci.type = 0 THEN 'Acconto'
            WHEN rci.type = 1 THEN 'Saldo'
            WHEN rci.type = 2 THEN 'Residuo'
        END as " .self::$RECUPERO_CREDITI_MOVIMENTO_TIPO . ",      
        DATE_FORMAT(creation_date, '%d/%m/%Y') as " . self::$RECUPERO_CREDITI_MOVIMENTO_DATA_CREAZIONE . ",
        cash_amount as " . self::$RECUPERO_CREDITI_MOVIMENTO_IMPORTO . ",
        cc_data as " . self::$RECUPERO_CREDITI_MOVIMENTO_CC . ",
        description as " . self::$RECUPERO_CREDITI_MOVIMENTO_DESCRIZIONE . "
        FROM recuperocrediti_incassi as rci 
        WHERE rci.document_id = ?";

        return $this->db->fetchAll($interestsList, $rcId);

    }

    private function getClaim($claimId, $file): array
    {
        $claimService = new ClaimsService($this->infrastructure, $file);
        $claim = $claimService->get($claimId);

        return [
            self::CLAIMS_NUMERO => $claim['no'],
            self::CLAIMS_RIF_ESTERNO => $claim['external_ref'],
            self::CLAIMS_DATA_SINISTRO => $claim['claim_date'] ? (new Carbon($claim['claim_date']))->format('d/m/Y') : null,
            self::CLAIMS_DATA_NOTIFICA => $claim['notification_date'] ? (new Carbon($claim['notification_date']))->format('d/m/Y') : null,
            self::CLAIMS_IMPORTO_DANNO =>
                number_format($claim['damage_amount'], 2, ',', '') ??
                number_format(0.00, 2, ',', ''),
            self::CLAIMS_IMPORTO_DA_RECUPERARE =>
                number_format($claim['recoverable_amount'], 2, ',', '') ??
                number_format(0.00, 2, ',', ''),
            self::CLAIMS_DESCRIZIONE => $claim['description'],
        ];
    }

    private function getClaimPayments($claimId, $file): array
    {
        $claimpaymentsService = new ClaimPaymentsService($this->infrastructure, $file);
        $contactsService = AnagraficaService::getInstance($this->infrastructure);

        try {
            $payments = $claimpaymentsService->getByClaimId($claimId);
        }catch (Exception $e){
            $payments = [];
        }

        $payments = array_map(function($payment) use ($contactsService) {
            return [
                self::CLAIMS_PAYMENTS_DATA_REVERSALE => $payment['entry_date'] ?
                    (new Carbon($payment['entry_date']))->format('d/m/Y') : null,
                self::CLAIMS_PAYMENTS_DATA_VALUTA => $payment['value_date'] ?
                    (new Carbon($payment['value_date']))->format('d/m/Y') : null,
                self::CLAIMS_PAYMENTS_NUM_REVERSALE => $payment['entry_no'],
                self::CLAIMS_PAYMENTS_NUM_CONTABILE => $payment['value_no'],
                self::CLAIMS_PAYMENTS_TIPO => PaymentType::findLabel($payment['type']),
                self::CLAIMS_PAYMENTS_RIF_DOCUMENTO => $payment['document_ref'],
                self::CLAIMS_PAYMENTS_CAPITALE =>
                    number_format($payment['capital_amount'], 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_PAYMENTS_INTERESSI =>
                    number_format($payment['interest_amount'], 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_PAYMENTS_SPESE =>
                    number_format($payment['expenses_amount'], 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_PAYMENTS_IMPORTO =>
                    number_format($payment['amount'], 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_PAYMENTS_TIPO_PAGANTE => !$payment['payer_id'] ?
                    PayerType::findLabel(PayerType::COMPANY) :
                    PayerType::findLabel(PayerType::OPPOSING_PARTIES),
                self::CLAIMS_PAYMENTS_PAGANTE => !$payment['payer_id'] ? 
                    null :
                    $contactsService->findAnagrafica(
                        [ 'field' => 'id', 'value' => $payment['payer_id']]
                    )['denominazione'],
            ];
        }, $payments);

        return $payments;
    }

    private function getClaimInterest($claimId, $file): array
    {
        $claimInterestsService = new ClaimInterestService($this->infrastructure, $file);

        try{
            $interests = $claimInterestsService->getByClaimId($claimId);
        }catch (Exception $e){
            $interests = [];
        }

        $interests = array_map(function($interest) {
            return [
                self::CLAIMS_INTEREST_AGGIUNTIVA =>
                    number_format($interest['ec_amount'], 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_INTEREST_COSTI_AGGIUNTIVI =>
                    number_format($interest['ec_fixed_cost'], 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_INTEREST_DATA_INIZIO => $interest['start_date'] ?
                    (new Carbon($interest['start_date']))->format('d/m/Y') : null,
                self::CLAIMS_INTEREST_DATA_FINE => $interest['end_date'] ?
                    (new Carbon($interest['end_date']))->format('d/m/Y') : null,
                self::CLAIMS_INTEREST_INTERESSI =>
                    number_format($interest['interest_amount'], 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_INTEREST_TOTALE =>
                    number_format(($interest['capital_amount'] + $interest['interest_amount']), 2, ',', '') ??
                    number_format(0.00, 2, ',', ''),
                self::CLAIMS_INTEREST_TOTALE_GIORNI => $interest['days_count'],
            ];
        }, $interests);

        return $interests;
    }

    private function getIndirizzi($query) {
        $values = [];
        $counters = [];
        $addresses = explode('|', $query['indirizzi']);

        foreach ($addresses as $address) {
            $indirizzi = json_decode($address, 1);
            foreach ($indirizzi as $key => $indirizzo) {
                if ($key == 9 && !empty($indirizzo['citta'])) {
                    $place = $this->db->fetchRow("SELECT * FROM tabellacitta WHERE id = ?", array($indirizzo['citta']));
                    if (empty($counters['citta'])) {
                        $values['citta'] = empty($values['citta']) ? $place['nome'] : $values['citta'] . $place['nome'];
                        $counters['citta'] = 1;
                    } else {
                        $values['citta'] .= ', ' . $place['nome'];
                    }
                    if (empty($counters['provincia'])) {
                        $values['provincia'] = empty($values['provincia']) ? $place['provincia'] : $values['provincia'] . $place['provincia'];
                        $counters['provincia'] = 1;
                    } else {
                        $values['provincia'] .= ', ' . $place['provincia'];
                    }
                }
                if ($key == 9 && !empty($indirizzo['via'])) {
                    if (empty($counters['via'])) {
                        $values['via'] = empty($values['via']) ? $indirizzo['via'] : $values['via'] . $indirizzo['via'];
                        $counters['via'] = 1;
                    } else {
                        $values['via'] .= ', ' . $indirizzo['via'];
                    }
                }
                if ($key == 9 && !empty($indirizzo['cap'])) {
                    if (empty($counters['cap'])) {
                        $values['cap'] = empty($values['cap']) ? $indirizzo['cap'] : $values['cap'] . $indirizzo['cap'];
                        $counters['cap'] = 1;
                    } else {
                        $values['cap'] .= ', ' . $indirizzo['cap'];
                    }
                }
            }
        }

        return $values;
    }

    private function getContatti($query, $uIds = null, $tableName = null) {
        $values = [];
        $counters = [];
        $contacts = explode('|', $query['contatti']);

        foreach ($contacts as $contact) {
            $contatti = json_decode($contact, 1);
            foreach ($contatti as $key => $contatto) {
                if ($key == 6 && !empty($contatto)) {
                    if (empty($counters['email'])) {
                        $values['email'] = empty($values['email']) ? $contatto : $values['email'] . $contatto;
                        $counters['email'] = 1;
                    } else {
                        $values['email'] .= ', ' . $contatto;
                    }
                }
                if ($key == 7 && !empty($contatto)) {
                    if (empty($counters['pec'])) {
                        $values['pec'] = empty($values['pec']) ? $contatto : $values['pec'] . $contatto;
                        $counters['pec'] = 1;
                    } else {
                        $values['pec'] .= ', ' . $contatto;
                    }
                    if (count($uIds) == 1 && $tableName == 'avversario') {
                        if (empty($counters['nomepec'])) {
                            $values['nomepec'] = empty($values['nomepec']) ? $query[self::$PARTE_NOME] . ' - ' . $contatto : $values['nomepec'] . $part[self::$PARTE_NOME] . ' - ' . $contatto;
                            $counters['nomepec'] = 1;
                        } else {
                            $values['nomepec'] .= ', ' . $query[self::$PARTE_NOME] . ' - ' . $contatto;
                        }
                    }
                }
                if ($key == 1 && !empty($contatto)) {
                    if (empty($counters['telefonocasa'])) {
                        $values['telefonocasa'] = empty($values['telefonocasa']) ? $contatto : $values['telefonocasa'] . $contatto;
                        $counters['telefonocasa'] = 1;
                    } else {
                        $values['telefonocasa'] .= ', ' . $contatto;
                    }
                }
                if ($key == 2 && !empty($contatto)) {
                    if (empty($counters['telefonoufficio'])) {
                        $values['telefonoufficio'] = empty($values['telefonoufficio']) ? $contatto : $values['telefonoufficio'] . $contatto;
                        $counters['telefonoufficio'] = 1;
                    } else {
                        $values['telefonoufficio'] .= ', ' . $contatto;
                    }
                }
                if ($key == 3 && !empty($contatto)) {
                    if (empty($counters['mobile1'])) {
                        $values['mobile1'] = empty($values['mobile1']) ? $contatto : $values['mobile1'] . $contatto;
                        $counters['mobile1'] = 1;
                    } else {
                        $values['mobile1'] .= ', ' . $contatto;
                    }
                }
                if ($key == 4 && !empty($contatto)) {
                    if (empty($counters['mobile2'])) {
                        $values['mobile2'] = empty($values['mobile2']) ? $contatto : $values['mobile2'] . $contatto;
                        $counters['mobile2'] = 1;
                    } else {
                        $values['mobile2'] .= ', ' . $contatto;
                    }
                }
                if ($key == 5 && !empty($contatto)) {
                    if (empty($counters['fax'])) {
                        $values['fax'] = empty($values['fax']) ? $contatto : $values['fax'] . $contatto;
                        $counters['fax'] = 1;
                    } else {
                        $values['fax'] .= ', ' . $contatto;
                    }
                }
            }
        }

        return $values;
    }
}