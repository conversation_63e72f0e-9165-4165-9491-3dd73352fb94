<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\PrintsManager;

//require_once('/home/<USER>/Netlex3/DexmaCommons/' . '/PrintsManager/tbs_class_3.10.1.php');
//require_once('/home/<USER>/Netlex3/DexmaCommons/' . '/PrintsManager/tbs_plugin_opentbs_1.10.0.php');

class PrintsHandler
{
	private $PH;
	private $s3Connector;
	private $sitekey;
    private static $campi_dinamicipratica_dati_gid = '';

	// private static $MAIN_BUCKET = "netlex-main";
    // TODO JOE DA DECOMMENTARE PER AMBIENTE TEST
   private static $MAIN_BUCKET = "netlex-main-testing";
	private static $PRINTS = 'prints';


    private $pratica = array(
		array('tag' =>'CODICE_PRATICA', 'query' => 'a.id'),
		array('tag' =>'CODICE_ARCHIVIO', 'query' => 'a.codicearchivio'),
        array('tag' =>'GIUDICE', 'query' => 'COALESCE(pp.giudice, a.giudice)'),
		array('tag' =>'SEZIONE', 'query' => 'COALESCE(pp.sezione, a.sezione)'),
        array('tag' =>'SENTENZA', 'query' => 'CONCAT(a.numero_sentenza, "/", a.numero_sentenza_anno)'),
        array('tag' =>'DATA_SENTENZA', 'query' => "DATE_FORMAT(a.data_sentenza, '%d/%m/%Y')"),
        array('tag' =>'DATA_DECRETO_INGIUNTIVO', 'query' => "DATE_FORMAT(a.data_decreto, '%d/%m/%Y')"),
		array('tag' =>'DATA_APERTURA', 'query' => "DATE_FORMAT(a.data, '%d/%m%/%Y')"),
		array('tag' =>'AVVOCATO', 'query' => 'tavv.nome'),
		array('tag' =>'NOME', 'query' => 'a.nome_pratica'),
        array('tag' => 'ANNOTAZIONE','query' => 'a.annotazioni'),
        array('tag' => 'CATEGORIA','query' => '(SELECT c.nome FROM categorie c WHERE a.id_categoria = c.id)'),
        array('tag' =>'AVVOCATO_NOME_COGNOME', 'query' => 'CONCAT(utn.nomepersonale, " ", utn.cognomepersonale)'),
		array('tag' => 'DATA_REATO', 'query' => "DATE_FORMAT(a.datareato, '%d/%m/%Y')"),
		array('tag' =>'PARTI', 'query' => "(SELECT GROUP_CONCAT(CONCAT(c.denominazione, ' (cod. ', cp.person_id, ')') ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                            FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                            WHERE cp.file_id = a.id AND cp.relazione = 1
                                            GROUP BY cp.file_id)"
		),
		array('tag' =>'PARTI_NO_COD', 'query' => "(SELECT GROUP_CONCAT(c.denominazione ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                            FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                            WHERE cp.file_id = a.id AND cp.relazione = 1
                                            GROUP BY cp.file_id)"
		),
		array('tag' =>'CONTROPARTI', 'query' => "(SELECT GROUP_CONCAT(CONCAT(c.denominazione, ' (cod. ', cp.person_id, ')') ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                                FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                                WHERE cp.file_id = a.id AND cp.relazione = 2
                                                GROUP BY cp.file_id)"
		),
		array('tag' =>'CONTROPARTI_NO_COD', 'query' => "(SELECT GROUP_CONCAT(c.denominazione ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                                FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                                WHERE cp.file_id = a.id AND cp.relazione = 2
                                                GROUP BY cp.file_id)"
		),
		array('tag' =>'RG', 'query' => "CASE WHEN(a.ruologeneralenumero <> '' AND a.ruologeneraleanno <> '') THEN
                                            CASE WHEN(a.subprocedimento <> 0) THEN
                                                CONCAT(a.ruologeneralenumero, '/', a.ruologeneraleanno, '-', a.subprocedimento)
                                            ELSE
                                                CONCAT(a.ruologeneralenumero, '/', a.ruologeneraleanno)
                                            END
                                            ELSE CASE WHEN(a.rgnr <> '' AND a.rgnranno <> '') THEN
                                                CONCAT(a.rgnr, '/', a.rgnranno)
                                            ELSE
                                                ''
                                            END
                                        END"
		),
		array('tag' =>'AUTORITA', 'query' => 'ta.nome'),
		array('tag' =>'OGGETTO', 'query' => 'togg.nome'),
		array('tag' =>'CITTA', 'query' => 'tc.nome'),
		array('tag' => 'DECRETO_INGIUNTIVO', 'query' => "CASE WHEN(a.numerodecretoingiuntivo <> '' AND a.numerodecretoingiuntivoanno <> '') THEN CONCAT(a.numerodecretoingiuntivo, '/', a.numerodecretoingiuntivoanno)
                                                        ELSE '' END"
		),
		array('tag' => 'VALORE', 'query' => "replace(replace(replace(format(convert(replace(a.valore, ',', '.'), decimal(10,2)), 2), ',', 'x'), '.', ','), 'x', '.')"),
		array('tag' => 'ATTIVITA_PROSSIMA_UDIENZA', 'query' => '(SELECT ag.attivita
                                                                FROM agenda ag
                                                                INNER JOIN tabellacitta c ON c.id = ag.citta
					                                            INNER JOIN tabellaautorita aut ON aut.id = ag.autorita
                                                                WHERE ag.pratica = a.id AND ag.data > NOW() ORDER BY ag.data ASC, ag.ora ASC LIMIT 1)'),
		array('tag' => 'DATA_PROSSIMA_UDIENZA', 'query' => '(SELECT DATE_FORMAT(a.data, "%d/%m/%Y")
                                                            FROM agenda ag
                                                            INNER JOIN tabellacitta tc ON tc.id = ag.citta
					                                        INNER JOIN tabellaautorita aut ON aut.id = ag.autorita
                                                            WHERE ag.pratica = a.id AND ag.data > NOW() ORDER BY ag.data ASC, ag.ora ASC LIMIT 1)'),
		array('tag' => 'CITTA_PROSSIMA_UDIENZA', 'query' => '(SELECT tc.nome
                                                            FROM agenda ag
                                                            INNER JOIN tabellacitta tc ON tc.id = ag.citta
					                                        INNER JOIN tabellaautorita aut ON aut.id = ag.autorita
                                                            WHERE ag.pratica = a.id AND ag.data > NOW() ORDER BY ag.data ASC, ag.ora ASC LIMIT 1)'),
		array('tag' => 'AUTORITA_PROSSIMA_UDIENZA', 'query' => '(SELECT taaut.nome FROM agenda ag INNER JOIN tabellaautorita taaut ON taaut.id = ag.autorita WHERE ag.pratica = a.id AND ag.data > NOW() ORDER BY ag.data ASC, ag.ora ASC LIMIT 1)'),
		array('tag' => 'ORA_PROSSIMA_UDIENZA', 'query' => '(SELECT ag.ora FROM agenda ag WHERE ag.pratica = a.id AND ag.data > NOW() ORDER BY ag.data ASC, ag.ora ASC LIMIT 1)'),
		array('tag' => 'ATTIVITA_ULTIMA_UDIENZA', 'query' => '(SELECT ag.attivita FROM agenda ag WHERE ag.pratica = a.id ORDER BY ag.data DESC, ag.ora DESC LIMIT 1)'),
		array('tag' => 'DATA_ULTIMA_UDIENZA', 'query' => '(SELECT DATE_FORMAT(ag.data, "%d/%m/%Y") FROM agenda ag WHERE ag.pratica = a.id ORDER BY ag.data DESC, ag.ora DESC LIMIT 1)'),
		array('tag' => 'CITTA_ULTIMA_UDIENZA', 'query' => '(SELECT tc.nome FROM agenda ag WHERE ag.pratica = a.id ORDER BY ag.data DESC, ag.ora DESC LIMIT 1)'),
		array('tag' => 'AUTORITA_ULTIMA_UDIENZA', 'query' => '(SELECT taaut.nome FROM agenda ag INNER JOIN tabellaautorita taaut ON taaut.id = ag.autorita WHERE ag.pratica = a.id ORDER BY ag.data DESC, ag.ora DESC LIMIT 1)'),
		array('tag' => 'ORA_ULTIMA_UDIENZA', 'query' => '(SELECT ag.ora FROM agenda ag WHERE ag.pratica = a.id ORDER BY ag.data DESC, ag.ora DESC LIMIT 1)'),
		array('tag' => 'ISTRUTTORE_ULTIMA_UDIENZA', 'query' => '(SELECT ti.nome FROM agenda ag INNER JOIN tabellaistruttori ti ON ti.id = ag.istruttore WHERE ag.pratica = a.id ORDER BY ag.data DESC, ag.ora DESC LIMIT 1)'),
		array('tag' => 'TIPOLOGIA', 'query' => 'ttp.nome'),
		array('tag' =>'STATO', 'query' => 'ts.nome'),
		array('tag' =>'REATO', 'query' => 'tr.nome'),
		array('tag' => 'RGGIP', 'query' => "CASE WHEN(a.rggip <> '' AND a.rggipanno <> '') THEN CONCAT(a.rggip, '/', a.rggipanno)
                                                        ELSE '' END"),
		array('tag' => 'RGGUP', 'query' => "CASE WHEN(a.rggup <> '' AND a.rggupanno <> '') THEN CONCAT(a.rggup, '/', a.rggupanno)
                                                        ELSE '' END"),
		array('tag' => 'RGTRIB', 'query' => "CASE WHEN(a.rgtrib <> '' AND a.rgtribanno <> '') THEN CONCAT(a.rgtrib, '/', a.rgtribanno)
                                                        ELSE '' END"),
		array('tag' => 'RGAPP', 'query' => "CASE WHEN(a.rgapp <> '' AND a.rgappanno <> '') THEN CONCAT(a.rgapp, '/', a.rgappanno)
                ELSE '' END"),
		array('tag' => 'RGNR', 'query' => "CASE WHEN(a.rgnr <> '' AND a.rgnranno <> '') THEN CONCAT(a.rgnr, '/', a.rgnranno)
                ELSE '' END"),
		array('tag' => 'RGCASS', 'query' => "CASE WHEN(a.rgcass <> '' AND a.rgcassanno <> '') THEN CONCAT(a.rgcass, '/', a.rgcassanno)
                ELSE '' END"),
		array('tag' => 'RGSIEP', 'query' => "CASE WHEN(a.rgsiep <> '' AND a.rgsiepanno <> '') THEN CONCAT(a.rgsiep, '/', a.rgsiepanno)
                ELSE '' END"),
		array('tag' => 'RGSIUS', 'query' => "CASE WHEN(a.rgsius <> '' AND a.rgsiusanno <> '') THEN CONCAT(a.rgsius, '/', a.rgsiusanno)
                ELSE '' END"),
		array('tag' => 'RGRIESAME', 'query' => "CASE WHEN(a.rgriesame <> '' AND a.rgriesameanno <> '') THEN CONCAT(a.rgriesame, '/', a.rgriesameanno)
                ELSE '' END"),
		array('tag' =>'RESPONSABILE', 'query' => 'CONCAT(u.nomepersonale, " ", u.cognomepersonale)'),
		array('tag' =>'VALORI_DINAMICI', 'query' => 'a.valori_dinamici'),
		array('tag' =>'NOTE', 'query' => 'a.annotazioni'),
		array('tag' =>'DESCRIZIONE', 'query' => 'a.descrizione'),
		array('tag' =>'PROTOCOLLO_GENERALE', 'query' => 'a.general_protocol'),
		array('tag' =>'SITUAZIONE','query' => 'a.situazione'),
		array('tag' => 'SITUAZIONE_CONTABILE','query' => 'a.situazione_contabile'),
		array('tag' => 'CENTRO_PROFITTO','query' => 'a.centro_profitto'),
		array('tag' => 'NUMERO_SENTENZA','query' => 'CONCAT(a.numero_sentenza,"/",a.numero_sentenza_anno)'),

        array('tag' => 'DT_GIUD_ST_REC_STU', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ST_REC_CLI', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_PRAT_ATT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_COS_SOS_CORR', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_NOTIFICA', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEP_RIC', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_COST_GIUD', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_RIC_RIS', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_IMP', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ISTA_FISS', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ISTA_FISS_RG', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ISTA_PREL', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ISTA_PREL_RG', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ORD_ISTR', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ORD_ISTR_RG', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ORD_CAU', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ORD_CAU_RG', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DATA_DELLA_SENT', 'query' => "DATE_FORMAT(a.data_della_sentenza, '%d/%m/%Y')"),
        array('tag' => 'DT_GIUD_IST_SOSP', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ESEC_ORD', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_IMPUGNATA', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ESEC_SENT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DATA_UD', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ESITO_CAUT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ESITO_SENT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_INV_DEDU', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEP_DED', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ATT_CIT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEP_MEM', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DAT_DEP_SENT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_NOT_PREC', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_TRA_SENT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_TER_IMP', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_TPO_SPES', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_IMP_COND', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEP_COS_TAR', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEP_COS_CDS', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_IMPUGNATA2', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_N_SENT_REP', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DATA_TRASM', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_CONDANNA', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DATA_PUB_SEN', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_RIS_DAN', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_TERM_IMP', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_NOTIFICA2', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_COST_COM', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ESITO', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_RISARC', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ST_REC_CLI2', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_FATT_AVV_A', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ALLE', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEL', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_N_SINI', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_AUT_INTER', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DI', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_RESP_CP', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_IN_DATA', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_VIA_STRADA_P', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DIREZIONE', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_MORTALE', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_KM', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DATA_INI', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DATA_FIN', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_GG', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_CONSEGN', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DELIMIT', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ENTE_PAGA', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ESITO_CONCIL', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_ESITO_PARERE', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_IMP_MASSIMAL', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_NOTE', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEP_T_MOTIVA', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DT_GIUD_DEP_D_MOTIVA', 'query' => 'a.valori_extra_giud_dinamici'),
        array('tag' => 'DATA_ISCRIZIONE_RUOLO', 'query' => 'DATE_FORMAT(pp.data_iscrizione, "%d/%m/%Y")'),
        array('tag' => 'COLLABORATORI', 'query' => '(SELECT GROUP_CONCAT(u2.nomeutente SEPARATOR ", ") FROM utente_pratica up LEFT JOIN utente u2 ON u2.id = up.person_id WHERE up.file_id = a.id AND up.relazione = 3)'),
        array('tag' => 'EMISSIONE_DI', 'query' => 'DATE_FORMAT(a.emissione_di, "%d/%m/%Y")'),
        array('tag' => 'NOTIFICA_DI', 'query' => 'DATE_FORMAT(a.notifica_di, "%d/%m/%Y")'),
        array('tag' => 'FORMULA_ESECUTIVA', 'query' => 'DATE_FORMAT(a.formula_esecutiva, "%d/%m/%Y")'),
        array('tag' => 'NOTIFICA_PRECETTO', 'query' => 'DATE_FORMAT(a.notifica_precetto, "%d/%m/%Y")')
	);

	private $avvocato = array(
		array('tag' => 'CAP', 'query' => 'ds.cap'),
		array('tag' => 'CITTA', 'query' => 'ds.citta'),
		array('tag' => 'PROVINCIA', 'query' => 'ds.provincia'),
		array('tag' => 'CODICE_FISCALE', 'query' => 'ds.codicefiscale'),
		array('tag' => 'EMAIL', 'query' => 'ds.email'),
		array('tag' => 'FAX', 'query' => 'ds.fax'),
		array('tag' => 'INDIRIZZO', 'query' => 'ds.indirizzo'),
		array('tag' => 'MOBILE', 'query' => 'ds.mobile'),
		array('tag' => 'NOME', 'query' => 'tavv.nome'),
		array('tag' => 'NOME_STUDIO', 'query' => 'ds.nome'),
		array('tag' => 'PARTITA_IVA', 'query' => 'ds.partitaiva'),
		array('tag' => 'PEC', 'query' => 'ds.cap'),
		array('tag' => 'TELEFONO', 'query' => 'ds.telefono'),
		array('tag' => 'PEC', 'query' => '(SELECT uea.email
                                            FROM utente u
                                            INNER JOIN utente_email_account uea ON uea.user_id = u.id
                                            WHERE u.codiceavvocato = tavv.id AND u.tipoutente = 1 AND u.nome <> "specialuser" AND uea.reginde = 1 AND email_type = 1)'
		),
	);

	private $soggetti = array(
        array('tag' => 'DENOMINAZIONE', 'query' => 'a.denominazione'),
        array('tag' => 'NOME', 'query' => 'a.nome'),
        array('tag' => 'COGNOME', 'query' => 'a.cognome'),
        array('tag' => 'SESSO', 'query' => 'a.sesso'),
        array('tag' => 'PARTITA_IVA', 'query' => 'a.partitaiva'),
        array('tag' => 'CODICE_FISCALE', 'query' => 'a.codicefiscale'),
        array('tag' => 'DATA_NASCITA', 'query' => 'DATE_FORMAT(a.datanascita, "%d/%m/%Y")'),
        array('tag' => 'INDIRIZZI', 'query' => 'a.indirizzi'),
        array('tag' => 'CONTATTI', 'query' => 'a.contatti'),
        array('tag' => 'COMUNE', 'query' => 'tc.nome'),
        array('tag' => 'SEZIONE', 'query' => 'a.sezione'),
        array('tag' => 'DATA_ISCRIZIONE', 'query' => 'DATE_FORMAT(a.dataiscrizione, "%d/%m/%Y")'),
        array('tag' => 'NUMERO_REA', 'query' => 'a.nrea'),
        array('tag' => 'DELIBERATO', 'query' => 'a.deliberato'),
        array('tag' => 'SOTTOSCRITTO', 'query' => 'a.sottoscritto'),
        array('tag' => 'VERSATO', 'query' => 'a.versato'),
        array('tag' => 'VALUTA', 'query' => 'tv.nome'),
        array('tag' => 'IBAN', 'query' => 'a.iban'),
	);

	private $udienze = array(
		array('tag' => 'ATTIVITA', 'query' => 'a.attivita'),
		array('tag' => 'DATA', 'query' => 'DATE_FORMAT(a.data, "%d/%m/%Y")'),
		array('tag' => 'ORA', 'query' => 'CASE WHEN(a.ora = "" || a.ora IS NULL) THEN "" ELSE a.ora END'),
		array('tag' => 'RG', 'query' => "CASE WHEN(ar.ruologeneralenumero <> '' AND ar.ruologeneraleanno <> '') THEN
                                            CASE WHEN(ar.subprocedimento <> 0) THEN
                                                CONCAT(ar.ruologeneralenumero, '/', ar.ruologeneraleanno, '-', ar.subprocedimento)
                                            ELSE
                                                CONCAT(ar.ruologeneralenumero, '/', ar.ruologeneraleanno)
                                            END
                                            ELSE CASE WHEN(ar.rgnr <> '' AND ar.rgnranno <> '') THEN
                                                CONCAT(ar.rgnr, '/', ar.rgnranno)
                                            ELSE
                                                ''
                                            END
                                        END"
		),
		array('tag' => 'AUTORITA', 'query' => 'aut.nome'),
		array('tag' => 'CITTA', 'query' => 'c.nome'),
		array('tag' => 'ISTRUTTORE', 'query' => 'i.nome'),
		array('tag' =>'PARTI', 'query' => "(SELECT GROUP_CONCAT(CONCAT(c.denominazione, ' (cod. ', cp.person_id, ')') ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                            FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                            WHERE cp.file_id = ar.id AND cp.relazione = 1
                                            GROUP BY cp.file_id)"
		),
		array('tag' =>'PARTI_NO_COD', 'query' => "(SELECT GROUP_CONCAT(c.denominazione ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                            FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                            WHERE cp.file_id = ar.id AND cp.relazione = 1
                                            GROUP BY cp.file_id)"
		),
		array('tag' =>'CONTROPARTI', 'query' => "(SELECT GROUP_CONCAT(CONCAT(c.denominazione, ' (cod. ', cp.person_id, ')') ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                                FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                                WHERE cp.file_id = ar.id AND cp.relazione = 2
                                                GROUP BY cp.file_id)"
		),
		array('tag' =>'CONTROPARTI_NO_COD', 'query' => "(SELECT GROUP_CONCAT(c.denominazione ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                                FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                                WHERE cp.file_id = ar.id AND cp.relazione = 2
                                                GROUP BY cp.file_id)"
		),
		array('tag' => 'TIPO', 'query' => '"udienza"'),
		array('tag' => 'DATA_ULTIMA_UDIENZA', 'query' => '""'),
		array('tag' => 'TIPOLOGIA', 'query' => '""'),
		array('tag' => 'OGGETTO', 'query' => '""'),
		array('tag' => 'START', 'query' => 'CONCAT(DATE_FORMAT(a.data, "%Y-%m-%d"), " ", a.ora)'),
		array('tag' => 'SEZIONE', 'query' => 'a.sezione'),
		array('tag' =>'RESPONSABILE', 'query' => 'CONCAT(u.nomepersonale, " ", u.cognomepersonale)'), // responsabile pratica
		array('tag' =>'REFERENTE', 'query' => 'CONCAT(ref.nomepersonale, " ", ref.cognomepersonale)'), // referente udienza
		array('tag' =>'PRATICA_CODICE_PRATICA', 'query' => 'ar.id'),
		array('tag' =>'PRATICA_CODICE_ARCHIVIO', 'query' => 'ar.codicearchivio'),
		array('tag' => 'ATTIVITA_PROSSIMA_UDIENZA', 'query' => '(SELECT ag.attivita
                                                                FROM agenda ag
                                                                INNER JOIN tabellacitta c ON c.id = ag.citta
					                                            INNER JOIN tabellaautorita aut ON aut.id = ag.autorita
                                                                WHERE ag.pratica = ar.id AND ag.data > NOW() ORDER BY ag.data ASC, ag.ora ASC LIMIT 1)'),
		array('tag' => 'DATA_PROSSIMA_UDIENZA', 'query' => '(SELECT DATE_FORMAT(ag.data, "%d/%m/%Y")
                                                            FROM agenda ag
                                                            INNER JOIN tabellacitta tc ON tc.id = ag.citta
					                                        INNER JOIN tabellaautorita aut ON aut.id = ag.autorita
                                                            WHERE ag.pratica = ar.id AND ag.data > NOW() ORDER BY ag.data ASC, ag.ora ASC LIMIT 1)'),
		array('tag' =>'PRATICA_NOTE', 'query' => 'ar.annotazioni'),
		array('tag' =>'PRATICA_DESCRIZIONE', 'query' => 'ar.descrizione'),
		array('tag' =>'VALORI_DINAMICI', 'query' => 'ar.valori_dinamici'), // VALORI DINAMICI DELLA PRATICA ASSOCIATA
		array('tag' =>'PRATICA_DATA_APERTURA', 'query' => "DATE_FORMAT(ar.data, '%d/%m%/%Y')"),
		array('tag' =>'PRATICA_DATA_REATO', 'query' => "DATE_FORMAT(ar.datareato, '%d/%m/%Y')"),
		array('tag' =>'PRATICA_DECRETO_INGIUNTIVO', 'query' => "CASE WHEN(ar.numerodecretoingiuntivo <> '' AND ar.numerodecretoingiuntivoanno <> '') THEN CONCAT(ar.numerodecretoingiuntivo, '/', ar.numerodecretoingiuntivoanno)
                                                        ELSE '' END"),
		array('tag' =>'PRATICA_OGGETTO', 'query' => 'togg.nome'),
		array('tag' =>'REATO', 'query' => 'tr.nome'),
		array('tag' => 'PRATICA_RGGIP', 'query' => "CASE WHEN(ar.rggip <> '' AND ar.rggipanno <> '') THEN CONCAT(ar.rggip, '/', ar.rggipanno)
                                                        ELSE '' END"),
		array('tag' => 'PRATICA_RGGUP', 'query' => "CASE WHEN(ar.rggup <> '' AND ar.rggupanno <> '') THEN CONCAT(ar.rggup, '/', ar.rggupanno)
                                                        ELSE '' END"),
		array('tag' => 'PRATICA_RGTRIB', 'query' => "CASE WHEN(ar.rgtrib <> '' AND ar.rgtribanno <> '') THEN CONCAT(ar.rgtrib, '/', ar.rgtribanno)
                                                        ELSE '' END"),
		array('tag' => 'PRATICA_RGAPP', 'query' => "CASE WHEN(ar.rgapp <> '' AND ar.rgappanno <> '') THEN CONCAT(ar.rgapp, '/', ar.rgappanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGNR', 'query' => "CASE WHEN(ar.rgnr <> '' AND ar.rgnranno <> '') THEN CONCAT(ar.rgnr, '/', ar.rgnranno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGCASS', 'query' => "CASE WHEN(ar.rgcass <> '' AND ar.rgcassanno <> '') THEN CONCAT(ar.rgcass, '/', ar.rgcassanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGSIEP', 'query' => "CASE WHEN(ar.rgsiep <> '' AND ar.rgsiepanno <> '') THEN CONCAT(ar.rgsiep, '/', ar.rgsiepanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGSIUS', 'query' => "CASE WHEN(ar.rgsius <> '' AND ar.rgsiusanno <> '') THEN CONCAT(ar.rgsius, '/', ar.rgsiusanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGRIESAME', 'query' => "CASE WHEN(ar.rgriesame <> '' AND ar.rgriesameanno <> '') THEN CONCAT(ar.rgriesame, '/', ar.rgriesameanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_TIPOLOGIA', 'query' => 'ttp.nome'),
		array('tag' =>'PRATICA_STATO', 'query' => 'ts.nome'),
		array('tag' => 'PRATICA_VALORE', 'query' => "CASE WHEN ar.valore = -1.00 THEN 'Indeterminato medio' WHEN ar.valore = -2.00 THEN 'Indeterminato modesto' WHEN ar.valore = -3.00 THEN 'Indeterminato rilevante' END"),
        array('tag' => 'NOTE', 'query' => "a.annotazioni")
	);

	private $impegni = array(
		array('tag' => 'OGGETTO', 'query' => 's.testo'),
		array('tag' => 'IMPORTANTE', 'query' => 'CASE WHEN s.important = 1 THEN "Sì" ELSE "No" END'),
		array('tag' => 'DATA', 'query' => 'DATE_FORMAT(s.data, "%d/%m/%Y")'),
		array('tag' => 'ORA', 'query' => 'CASE WHEN(s.ora = "" || s.ora IS NULL) THEN "" ELSE s.ora END'),
		array('tag' => 'STATO', 'query' => 'CASE WHEN(s.nonevadere = 1) THEN "Non evadere" ELSE CASE WHEN(s.evasa = 1) THEN "Evaso" ELSE "Non evaso" END END'),
		array('tag' => 'AUTORITA', 'query' => 'aut.nome'),
		array('tag' => 'CITTA', 'query' => 'c.nome'),
		array('tag' =>'PARTI', 'query' => "(SELECT GROUP_CONCAT(CONCAT(c.denominazione, ' (cod. ', cp.person_id, ')') ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                            FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                            WHERE cp.file_id = ar.id AND cp.relazione = 1
                                            GROUP BY cp.file_id)"
		),
		array('tag' =>'CONTROPARTI', 'query' => "(SELECT GROUP_CONCAT(CONCAT(c.denominazione, ' (cod. ', cp.person_id, ')') ORDER BY cp.person_id ASC SEPARATOR ', ') AS nome
                                                FROM anagrafica_pratica cp INNER JOIN anagrafiche c ON cp.person_id = c.id
                                                WHERE cp.file_id = ar.id AND cp.relazione = 2
                                                GROUP BY cp.file_id)"
		),
		array('tag' => 'ISTRUTTORE', 'query' => 'CASE WHEN a.id IS NOT NULL THEN
                                                    (SELECT ti.nome FROM tabellaistruttori ti WHERE ti.id = a.istruttore)
                                                ELSE CASE WHEN a2.id IS NOT NULL THEN
                                                    (SELECT ti.nome FROM tabellaistruttori ti WHERE ti.id = a2.istruttore)
                                                    ELSE
                                                        (SELECT CASE WHEN tpm.nome IS NOT NULL THEN CONCAT(tpm.nome, " (PM)")
                                                        ELSE NULL END FROM tabellapubbliciministeri tpm WHERE tpm.id = ar.pubblico_ministero)
                                                    END
                                                END'
		),
		array('tag' => 'TIPOLOGIA', 'query' => 'tts.nome'),
		array('tag' => 'DATA_ULTIMA_UDIENZA', 'query' => 'CASE WHEN a2.id IS NOT NULL THEN DATE_FORMAT(a2.data, "%d/%m/%Y") ELSE "" END'),
		array('tag' => 'RG', 'query' => "CASE WHEN(ar.ruologeneralenumero <> '' AND ar.ruologeneraleanno <> '') THEN
                                            CASE WHEN(ar.subprocedimento <> 0) THEN
                                                CONCAT(ar.ruologeneralenumero, '/', ar.ruologeneraleanno, '-', ar.subprocedimento)
                                            ELSE
                                                CONCAT(ar.ruologeneralenumero, '/', ar.ruologeneraleanno)
                                            END
                                            ELSE CASE WHEN(ar.rgnr <> '' AND ar.rgnranno <> '') THEN
                                                CONCAT(ar.rgnr, '/', ar.rgnranno)
                                            ELSE
                                                ''
                                            END
                                        END"
		),
		array('tag' => 'TIPO', 'query' => '"impegno"'),
		array('tag' => 'ATTIVITA', 'query' => '""'),
		array('tag' => 'START', 'query' => 'CONCAT(DATE_FORMAT(s.data, "%Y-%m-%d"), " ", s.ora)'),
		array('tag' => 'SEZIONE', 'query' => 'CASE WHEN a.id IS NOT NULL THEN a.sezione
                                               ELSE CASE WHEN a2.id IS NOT NULL THEN a2.sezione END
                                               END'),
		array('tag' =>'RESPONSABILE', 'query' => 'CONCAT(u.nomepersonale, " ", u.cognomepersonale)'),
		array('tag' =>'PRATICA_NOTE', 'query' => 'ar.annotazioni'),
		array('tag' =>'PRATICA_DESCRIZIONE', 'query' => 'ar.descrizione'),
		array('tag' =>'VALORI_DINAMICI', 'query' => 'ar.valori_dinamici'), // VALORI DINAMICI DELLA PRATICA ASSOCIATA
		array('tag' =>'PRATICA_DATA_APERTURA', 'query' => "DATE_FORMAT(ar.data, '%d/%m%/%Y')"),
		array('tag' =>'PRATICA_DATA_REATO', 'query' => "DATE_FORMAT(ar.datareato, '%d/%m/%Y')"),
		array('tag' =>'PRATICA_DECRETO_INGIUNTIVO', 'query' => "CASE WHEN(ar.numerodecretoingiuntivo <> '' AND ar.numerodecretoingiuntivoanno <> '') THEN CONCAT(ar.numerodecretoingiuntivo, '/', ar.numerodecretoingiuntivoanno)
                                                        ELSE '' END"),
		array('tag' =>'PRATICA_OGGETTO', 'query' => 'togg.nome'),
		array('tag' =>'REATO', 'query' => 'tr.nome'),
		array('tag' => 'PRATICA_RGGIP', 'query' => "CASE WHEN(ar.rggip <> '' AND ar.rggipanno <> '') THEN CONCAT(ar.rggip, '/', ar.rggipanno)
                                                        ELSE '' END"),
		array('tag' => 'PRATICA_RGGUP', 'query' => "CASE WHEN(ar.rggup <> '' AND ar.rggupanno <> '') THEN CONCAT(ar.rggup, '/', ar.rggupanno)
                                                        ELSE '' END"),
		array('tag' => 'PRATICA_RGTRIB', 'query' => "CASE WHEN(ar.rgtrib <> '' AND ar.rgtribanno <> '') THEN CONCAT(ar.rgtrib, '/', ar.rgtribanno)
                                                        ELSE '' END"),
		array('tag' => 'PRATICA_RGAPP', 'query' => "CASE WHEN(ar.rgapp <> '' AND ar.rgappanno <> '') THEN CONCAT(ar.rgapp, '/', ar.rgappanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGNR', 'query' => "CASE WHEN(ar.rgnr <> '' AND ar.rgnranno <> '') THEN CONCAT(ar.rgnr, '/', ar.rgnranno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGCASS', 'query' => "CASE WHEN(ar.rgcass <> '' AND ar.rgcassanno <> '') THEN CONCAT(ar.rgcass, '/', ar.rgcassanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGSIEP', 'query' => "CASE WHEN(ar.rgsiep <> '' AND ar.rgsiepanno <> '') THEN CONCAT(ar.rgsiep, '/', ar.rgsiepanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGSIUS', 'query' => "CASE WHEN(ar.rgsius <> '' AND ar.rgsiusanno <> '') THEN CONCAT(ar.rgsius, '/', ar.rgsiusanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_RGRIESAME', 'query' => "CASE WHEN(ar.rgriesame <> '' AND ar.rgriesameanno <> '') THEN CONCAT(ar.rgriesame, '/', ar.rgriesameanno)
                ELSE '' END"),
		array('tag' => 'PRATICA_TIPOLOGIA', 'query' => 'ttp.nome'),
		array('tag' =>'PRATICA_STATO', 'query' => 'ts.nome'),
		array('tag' => 'PRATICA_VALORE', 'query' => "CASE WHEN ar.valore = -1.00 THEN 'Indeterminato medio' WHEN ar.valore = -2.00 THEN 'Indeterminato modesto' WHEN ar.valore = -3.00 THEN 'Indeterminato rilevante' END")
	);

	private $documenti = array(
		array('tag' => 'DESCRIZIONE', 'query' => 'd.titolodocumento'),
		array('tag' => 'NOMEFILE', 'query' => 'd.nomefile'),
		array('tag' => 'DATA', 'query' => 'CASE WHEN d.data IS NOT NULL THEN DATE_FORMAT(d.data, "%d/%m/%Y") ELSE "" END'),
	);

	private function getRecuperoCrediti(?string $datiBancariRaw = null) {
		$datiBancariQuery = $datiBancariRaw ?? "JSON_UNQUOTE(JSON_EXTRACT(d.payments_data, '$[0].iban'))";

		return array(
			array('tag' =>'DATA_SCADENZA_FATTURA', 'query' => 'DATE_FORMAT(rd.deadline_date, "%d-%m-%Y")'),
	//		array('tag' =>'DATA_FATTURA', 'query' => 'DATE_FORMAT(pa.data, "%d-%m-%Y")'),
			array('tag' =>'DATA_FATTURA', 'query' => 'IFNULL(DATE_FORMAT(pa.data, "%d-%m-%Y"),DATE_FORMAT(rd.creation_date, "%d-%m-%Y"))'),
			array('tag' =>'DATA_DOCUMENTO', 'query' => 'DATE_FORMAT(rd.creation_date, "%d-%m-%Y")'),
			array('tag' =>'DATA_SCADENZA_SOLLECITO', 'query' => 'DATE_FORMAT(rs.deadline_date, "%d-%m-%Y")'),
			array('tag' =>'CAPITALE', 'query' => 'REPLACE(rd.amount, "." , ",")'),
			array('tag' =>'CAPITALE_RESIDUO', 'query' => 'REPLACE(rd.remaining, "." , ",")'),
			array('tag' =>'RIF_FATTURA', 'query' => 'rd.rif_fattura'),
			array('tag' =>'DATA_INIZIO_CALCOLO', 'query' => 'DATE_FORMAT(ri.start_date, "%d-%m-%Y")'),
			array('tag' =>'DATA_FINE_CALCOLO', 'query' => 'DATE_FORMAT(ri.end_date, "%d-%m-%Y")'),
			array('tag' =>'DATA_CREAZIONE_CALCOLO', 'query' => 'DATE_FORMAT(ri.creation_date, "%d-%m-%Y")'),
			array('tag' =>'TIPO_CALCOLO', 'query' => 'CASE WHEN ri.interests_type = 1 THEN "Interessi legali"
													WHEN ri.interests_type = 2 THEN "Interessi di mora"
													WHEN ri.interests_type = 3 OR ri.interests_type = 4 THEN "Interessi" END'),
			array('tag' =>'GIORNI_CALCOLATI', 'query' => 'REPLACE(ri.total_days, "." , ",")'),
			array('tag' =>'IMPORTO_INTERESSI_APPLICATI', 'query' => 'REPLACE(ri.total_interests, "." , ",")'),
			array('tag' =>'IMPORTO_COSTI_AGGIUNTIVI', 'query' => 'REPLACE(ri.charges_applied, "." , ",")'),
			array('tag' =>'IMPORTO_SOGLIA', 'query' => 'REPLACE(ri.range_applied, "." , ",")'),
			array('tag' =>'IMPORTO_PERCENTUALE_AGGIUNTIVA', 'query' => 'REPLACE(ri.forfait_applied, "." , ",")'),
			array('tag' =>'TOTALE_CALCOLO', 'query' => 'REPLACE(rd.remaining + ri.total_interests + ri.charges_applied + ri.range_applied + ri.forfait_applied, "." , ",")'),
			array('tag' =>'CITTA', 'query' => 'tc.nome'),
			array('tag' =>'INTESTAZIONE', 'query' => 'CONCAT_WS("\n", d.nome, d.indirizzo, CONCAT_WS(" ",d.citta, d.provincia, d.cap), d.partitaiva, d.codicefiscale )'),
			array('tag' =>'DENOMINAZIONE_STUDIO', 'query' => 'd.nome'),
			array('tag' =>'DENOMINAZIONE_CONTROPARTE', 'query' => "GROUP_CONCAT(a.denominazione SEPARATOR ';')"),
			array('tag' =>'INDIRIZZO_CONTROPARTE', 'query' => "GROUP_CONCAT(a.denominazione ,'-', a.indirizzi SEPARATOR ';')"),
			array('tag' =>'CODICEFISCALE_CONTROPARTE', 'query' => "GROUP_CONCAT(a.denominazione,'-',a.codicefiscale SEPARATOR ';')"),
			array('tag' =>'PARTITAIVA_CONTROPARTE', 'query' => "GROUP_CONCAT(a.denominazione,'-',a.partitaiva SEPARATOR ';')"),
			array('tag' =>'AVVOCATO', 'query' => "CONCAT(u.nomepersonale,' ',u.cognomepersonale)"),
			array('tag' =>'GIORNI_SCADENZA_SOLLECITO', 'query' => "CASE WHEN rs.warning_type = 1 THEN sett.mora_expire
																WHEN rs.warning_type = 2 THEN sett.second_expire
																WHEN rs.warning_type = 3 THEN sett.third_expire
																WHEN rs.warning_type = 4 THEN sett.beware_expire END"),
			array('tag' =>'DATI_BANCARI_STUDIO', 'query' => $datiBancariQuery),
			array('tag' =>'TIPO_SOLLECITO', 'query' => "CASE WHEN rs.warning_type = 1 THEN 'COSTITUZIONE IN MORA'
														WHEN rs.warning_type = 2  OR rs.warning_type = 3 THEN 'SOLLECITO'
														WHEN rs.warning_type = 4 THEN 'DIFFIDA' END"),
			array('tag' =>'DATA_SOLLECITO_PRECEDENTE', 'query' => "(SELECT DATE_FORMAT(soll.creation_date,'%d-%m-%Y') FROM recuperocrediti_solleciti soll WHERE soll.recuperocrediti_doc_id = rd.id AND soll.warning_type = (rs.warning_type -1))"),
			array('tag' =>'CLIENTE', 'query' => "(SELECT GROUP_CONCAT(ana.denominazione SEPARATOR ', ')
													FROM anagrafica_pratica anpr
													LEFT JOIN anagrafiche ana ON ana.id = anpr.person_id
													WHERE anpr.file_id = arch.id AND anpr.relazione = 1)"),
			array('tag' =>'INDIRIZZO_CLIENTE', 'query' => "(SELECT ana.indirizzi FROM anagrafica_pratica anpr LEFT JOIN anagrafiche ana ON ana.id = anpr.person_id WHERE anpr.file_id = arch.id AND anpr.relazione = 1 LIMIT 1)"),
			array('tag' => 'PEC_CONTROPARTE', 'query' => 'GROUP_CONCAT(a.contatti SEPARATOR ";")'),
			array('tag' => 'COD_ESTERNA_CONTROPARTE', 'query' => 'IFNULL(a.external_sw_id, "n.c.")'),
			array('tag' => 'COD_ESTERNA_CLIENTE', 'query' => "(SELECT IFNULL(ana.external_sw_id, 'n.c.') FROM anagrafica_pratica anpr LEFT JOIN anagrafiche ana ON ana.id = anpr.person_id WHERE anpr.file_id = arch.id AND anpr.relazione = 1 LIMIT 1)"),
			array('tag' => 'ALTRI_COSTI', 'query' => "rs.other_costs"),
			array('tag' => 'AGENTE', 'query' => "(SELECT CONCAT(IFNULL(external_sw_id, 'n.c.'), ' - ', denominazione) FROM anagrafiche WHERE id = (SELECT person_id FROM anagrafica_pratica WHERE file_id = arch.id AND role_id = 8))"),
		);
	}

	private $fromUdienze = ' FROM agenda a
                             LEFT JOIN tabellaautorita aut ON a.autorita = aut.id
                             LEFT JOIN tabellacitta c ON a.citta = c.id
                             INNER JOIN archivio ar ON ar.id = a.pratica
                             LEFT JOIN tabellaoggetto togg ON ar.oggetto = togg.id
                             LEFT JOIN tabellareato tr ON ar.reato = tr.id
                             LEFT JOIN tabellastati ts ON ts.id = ar.stato
                             INNER JOIN tabellatipologiapratica ttp ON ar.tipologiapratica = ttp.id
                             LEFT JOIN utente u ON u.id = ar.referent_id
                             LEFT JOIN utente ref ON ref.id = a.referente
                             LEFT JOIN tabellaistruttori i ON a.istruttore = i.id
                             INNER JOIN tabellastati s ON ar.stato = s.id
                             LEFT JOIN tabellaavvocati tavv ON ar.avvocato = tavv.id';

	private $fromImpegni = ' FROM scadenzario s
                             INNER JOIN tabellatiposcadenza tts ON s.tiposcadenza = tts.id
                             LEFT JOIN archivio ar ON ar.id = s.pratica
                             LEFT JOIN tabellaoggetto togg ON ar.oggetto = togg.id
                             LEFT JOIN tabellareato tr ON ar.reato = tr.id
                             LEFT JOIN tabellastati ts ON ts.id = ar.stato
                             INNER JOIN tabellatipologiapratica ttp ON ar.tipologiapratica = ttp.id
                             LEFT JOIN scadenzarioutente su ON s.id = su.id_scadenza
                             LEFT JOIN utente u ON u.id = su.id_utente
                             LEFT JOIN agenda a ON s.linkuid = a.linkuid
                             LEFT JOIN agenda a2 ON a2.id = (SELECT a3.id FROM agenda a3 WHERE a3.pratica = s.pratica ORDER BY a3.data DESC, a3.ora DESC LIMIT 1)
                             LEFT JOIN tabellaautorita aut ON ar.ufficio_giudiziario = aut.id
                             LEFT JOIN tabellacitta c ON aut.citta = c.id';

	private $orderByUdienze = ' ORDER BY a.data DESC';

	private $orderByImpegni = ' ORDER BY s.data DESC, s.ora ASC';

	private $fattura = array(
		array('tag' => 'DATA', 'query' => 'DATE_FORMAT(p.data, "%d/%m/%Y")'),
		array('tag' => 'DATA_SCADENZA', 'query' => 'DATE_FORMAT(p.data_scadenza, "%d/%m/%Y")'),
		array('tag' => 'DATA_ULTIMO_PAGAMENTO', 'query' => 'DATE_FORMAT(p.data_ultimo_pagamento, "%d/%m/%Y")'),
		array('tag' => 'ANNO', 'query' => 'p.anno'),
		array('tag' => 'PROGRESSIVO', 'query' => 'p.progressivo'),
		array('tag' => 'SEZIONALE_PROGRESSIVO', 'query' => 'p.sezionale_progressivo'),
		array('tag' => 'SEZIONALE_PROGRESSIVO_BIS', 'query' => 'p.sezionale_progressivo_bis'),
		array('tag' => 'PROGRESSIVO_ALFANUMERICO', 'query' => 'p.progressivo_alfanumerico'),
		array('tag' => 'MOTIVO', 'query' => 'p.motivo'),
		array('tag' => 'INTESTAZIONE', 'query' => 'p.intestazione'),
		array('tag' => 'DESTINATARIO', 'query' => 'p.intestatario'),
		array('tag' => 'DESTINATARIO_ALTRI_DATI', 'query' => '(SELECT an.id FROM anagrafiche an WHERE an.denominazione LIKE p.nome_intestatario OR an.nome LIKE p.nome_intestatario OR an.cognome LIKE p.nome_intestatario LIMIT 0,1)'), // aggiunto LIMIT perchè se ci sono 2 anagrafiche con gli stessi dati (ed è successo con lexacta) ritorna l'errore "Cardinality violation: 1242 Subquery returns more than 1 row "
		array('tag' => 'DATI_STUDIO', 'query' => 'p.datistudio'),
		array('tag' => 'NOTE', 'query' => 'p.note'),
		array('tag' => 'EMITTENTE_EMAIL', 'query' => 'd.email'),
		array('tag' => 'EMITTENTE_TELEFONO', 'query' => 'd.telefono'),
		array('tag' => 'ESIGIBILITA_IVA', 'query' => 'CASE WHEN p.esigibilita_iva = "I" THEN "IVA ad esigibilità immediata"
                                                      WHEN p.esigibilita_iva = "D" THEN "IVA ad esigibilità differita"
                                                      WHEN p.esigibilita_iva = "S" THEN "Scissione dei pagamenti (D.L. n. 50 del 24 aprile 2017)" END'),
		array('tag' => 'TOTALE_IMPONIBILE', 'query' => 'FORMAT(p.totale_imponibile, 2, "de_DE")'),
		array('tag' => 'TOTALE_NON_IMPONIBILE', 'query' => 'FORMAT(p.totale_non_imponibile, 2, "de_DE")'),
		array('tag' => 'TOTALE', 'query' => 'FORMAT(p.totale, 2, "de_DE")'),
		array('tag' => 'IVA', 'query' => 'FORMAT(p.totale_iva, 2, "de_DE")'),
		array('tag' => 'PERCENTUALE_IVA', 'query' => 'p.iva'),
		array('tag' => 'DATI_PAGAMENTO', 'query' => 'p.datipagamento'),
		array('tag' => 'TOTALE_FATTURA', 'query' => 'FORMAT(p.totale_imponibile + p.totale_non_imponibile + p.totale_iva + p.importospesee + p.importo_speseescluse + p.importo_inversione_contabile + p.importo_regime_margine + (p.importo_bollovirtuale * p.contabilizza_bollo), 2, "de_DE")'),
		array('tag' => 'IMPORTO_CASSA', 'query' => 'FORMAT(p.importo_cassa, 2, "de_DE")'),
		array('tag' => 'PERCENTUALE_CASSA', 'query' => 'p.cassa'),
		array('tag' => 'IMPORTO_RITENUTA', 'query' => 'FORMAT(p.importo_ritenuta, 2, "de_DE")'),
		array('tag' => 'PERCENTUALE_RITENUTA', 'query' => 'p.ritenuta'),
		array('tag' => 'IMPORTO_BOLLO_VIRTUALE', 'query' => 'FORMAT(p.importo_bollovirtuale, 2, "de_DE")'),
		array('tag' => 'CODICE_PRATICA', 'query' => 'p.codicepratica'),
		array('tag' => 'PERCENTUALE_SPESE_GENERALI', 'query' => 'p.spesegeneriche'),
		array('tag' => 'IMPORTO_SPESE_GENERALI', 'query' => 'FORMAT(p.importo_spesegenerali, 2, "de_DE")'),
		array('tag' => 'IMPORTO_SPESE_ESENTI', 'query' => 'FORMAT(p.importospesee, 2, "de_DE")'),
		array('tag' => 'IMPORTO_SPESE_ESCLUSE', 'query' => 'FORMAT(p.importo_speseescluse, 2, "de_DE")'),
		array('tag' => 'IMPORTO_SPESE_IMPONIBILI', 'query' => 'FORMAT(p.importospesei, 2, "de_DE")'),
		array('tag' => 'TIPO_DOCUMENTO_FISCALE', 'query' => 'CASE WHEN p.tipo = 0 THEN "FATTURA"
                                                      WHEN p.tipo = 1 THEN "NOTA DI CREDITO"
                                                      WHEN p.tipo = 2 THEN "PREAVVISO DI PARCELLA"
                                                      WHEN p.tipo = 3 THEN "FATTURA ELETTRONICA"
                                                      WHEN p.tipo = 4 THEN "NOTA DI CREDITO ELETTRONICA"
                                                      WHEN p.tipo = 5 THEN "FATTURA DI ACQUISTO"
                                                      WHEN p.tipo = 6 THEN "FATTURA ELETTRONICA"
                                                      WHEN p.tipo = 7 THEN "NOTA DI CREDITO ELETTRONICA"
                                                      WHEN p.tipo = 8 THEN "NOTA SPESE"
                                                      WHEN p.tipo = 10 THEN "NOTA DI DEBITO ELETTRONICA"
                                                      END'),
		array('tag' => 'TOTALE_ONORARIO', 'query' => 'FORMAT(p.importo_spesegenerali + (SELECT SUM(pm.quantita * pm.importo) FROM parcellamovimenti pm WHERE pm.parcella_id = p.id AND (pm.type = 1 OR pm.type = 2)), 2, "de_DE")'),
		array('tag' => 'TOTALE_COMPETENZE', 'query' => 'FORMAT(p.importo, 2, "de_DE")'),
		array('tag' =>'PRATICA_NOTE', 'query' => 'arch.annotazioni'),
		array('tag' =>'PRATICA_DESCRIZIONE', 'query' => 'arch.descrizione'),
        array('tag' =>'LISTA_DOCUMENTI_ALLEGATI', 'query' => "
                    (SELECT GROUP_CONCAT(d.titolodocumento SEPARATOR ', ')
                    FROM documento d
                    LEFT JOIN parcella_documento pd ON d.id = pd.documento_id
                    WHERE p.id = pd.parcella_id
                    GROUP BY pd.parcella_id
                    )"
	),
		array('tag' =>'NUMERO_FATTURA', 'query' => "p.numero_fattura")
	);

	private $movimenti = array(
		array('tag' => 'IMPORTO_UNITARIO', 'query' => 'FORMAT(pm.importo, 2, "de_DE")'),
		array('tag' => 'IMPORTO', 'query' => 'FORMAT(pm.quantita * pm.importo, 2, "de_DE")'),
		array('tag' => 'IVA', 'query' => 'FORMAT(pm.iva, 2, "de_DE")'),
		array('tag' => 'QUANTITA', 'query' => 'pm.quantita'),
		array('tag' => 'DESCRIZIONE', 'query' => 'pm.descrizione'),
	);

    private $liquidazione = array(
        array('tag' => 'SOGGETTO', 'query' => 'a.denominazione'),
        array('tag' => 'SENTENZA', 'query' => 'lpc.judgment'),
        array('tag' => 'PRECETTO', 'query' => 'lpc.precept'),
        array('tag' => 'ORDINANZA', 'query' => 'lpc.order'),
        array('tag' => 'TOTALE_COMPENSI', 'query' => '(lpc.judgment+lpc.precept+lpc.order)'),
        array('tag' => 'SPESE_GENERALI', 'query' => 'lpc.overheads'),
        array('tag' => 'CPA', 'query' => 'lpc.cpa'),
        array('tag' => 'IVA', 'query' => 'lpc.iva'),
        array('tag' => 'SPESE_ESENTI', 'query' => 'lpc.expenses_exempt'),
        array('tag' => 'TOTALE_SPESE', 'query' => 'lpc.judgment_amount'),
        array('tag' => 'RITENUTA_ACCONTO', 'query' => 'lpc.withholding_tax'),
        array('tag' => 'RIMBORSO_CTU', 'query' => 'lpc.ctureimbursement'),
        array('tag' => 'REGISTRAZIONE_SENTENZA', 'query' => 'lpc.sentence_registration'),
        array('tag' => 'NETTO_PAGARE', 'query' => 'lpc.to_pay')
    );

    private $rcdocumenti = array(
        array('tag' => 'SOGGETTO', 'query' => 'a.denominazione'),
        array('tag' => 'INTESTATARIO', 'query' => 'u.nomeutente'),
        array('tag' => 'DATA', 'query' => 'DATE_FORMAT(rcd.creation_date, "%d/%m/%Y")'),
        array('tag' => 'INTERESSI', 'query' => 'rcd.pa_interests'),
        array('tag' => 'DIRITTI', 'query' => 'rcd.pa_rights'),
        array('tag' => 'ONORARI', 'query' => 'rcd.pa_honoray'),
        array('tag' => 'TOTALE_ODI', 'query' => 'rcd.pa_first_total'),
        array('tag' => 'ONERI_RIFLESSI', 'query' => 'rcd.pa_reflected_charges'),
        array('tag' => 'PERC_ONERI_RIFLESSI', 'query' => 'rcd.pa_reflected_charges_perc'),
        array('tag' => 'SPESE_GENERALI', 'query' => 'rcd.pa_overheads'),
        array('tag' => 'PERC_SPESE_GENERALI', 'query' => 'rcd.pa_overheads_perc'),
        array('tag' => 'TOTALE_OS', 'query' => 'rcd.pa_second_total'),
        array('tag' => 'SPESE_ESENTI', 'query' => 'rcd.pa_expenditure_net'),
        array('tag' => 'RITENUTA_ACCONTO', 'query' => 'rcd.pa_withholding_tax'),
        array('tag' => 'PERC_RITENUTA_ACCONTO', 'query' => 'rcd.pa_withholding_tax_perc'),
        array('tag' => 'TOTALE_DA_RECUPERARE', 'query' => 'rcd.remaining')
    );

    // TODO JOE DA DECOMMENTARE PER AMBIENTE TEST
//    public function __construct($PH, $options = ['sitekey'=>'test'], $error_messages = null)
	public function __construct($PH, $options = null, $error_messages = null)
	{
		$this->PH = $PH;
		if ($options) {
			$this->options = $options;
		}
		if ($error_messages) {
			$this->error_messages = $error_messages;
		}

		$this->s3Connector = $this->PH->s3Connector;

        // TODO JOE DA DECOMMENTARE PER AMBIENTE TEST
//        $this->sitekey = 'test';
//        $this->options['sitekey']='test';
        $this->sitekey = $this->options['sitekey'] ?? SITEKEY;
	}

	# funzione di ordinamento
	function cmp2($a, $b): int
	{
		$Atime = strtotime($a['START']);
		$Btime = strtotime($b['START']);

		if (date('Y-m-d', $Atime) == date('Y-m-d', $Btime)) {
			$CMPcity = strcmp($a['CITTA'], $b['CITTA']);

			if ($CMPcity == 0) {
				$CMPauthority = strcmp($a['AUTORITA'], $b['AUTORITA']);

				if ($CMPauthority == 0) {
					$CMPsection = strcmp($a['SEZIONE'], $b['SEZIONE']);

					if ($CMPsection == 0) {
						$CMPinstructor = strcmp($a['ISTRUTTORE'], $b['ISTRUTTORE']);

						if ($CMPinstructor == 0) {
							$Ahours = date('h', $Atime);
							$Bhours = date('h', $Btime);

							if ($Ahours == $Bhours) {
								return (date('m', $Atime) < date('m', $Btime)) ? -1 : 1;
							}
							else return ($Ahours < $Bhours) ? -1 : 1;
						}
						else return $CMPinstructor;
					}
					else return $CMPsection;
				}
				else return $CMPauthority;
			}
			else return $CMPcity;
		}
		else return ($Atime < $Btime) ? -1 : 1;
	}

	public function fillTags(?string $version = null)
	{
		$labels = $this->options['labels'];
		$fields = array();

		foreach($labels as $label) {
			// $this->PH->applogger->info('label: ' . $label);
			if ($label == 'generale') {
				continue;
			}
			$select = 'SELECT ';
			$from = $orderBy = '';
			$where = ' WHERE 1=1 ';
			$data = array();

			switch($label) {
                case 'contrattualistica':
                    $campi_contratto = $this->PH->db->fetchRow(
                        "SELECT * FROM contratti WHERE id = ? AND id_pratica = ?",
                        [$this->options['request']['contractid'], $this->options['request']['sectionid']]
                    );

                    $valori_dinamici = json_decode($campi_contratto['valori_dinamici'], true);
                    $campi_columns   = $this->PH->db->fetchAll(
                        "SELECT * FROM campi_dinamici WHERE id IN (SELECT id_campo_dinamico FROM campi_dinamici_categorie WHERE id_categoria = ?)",
                        [$this->options['request']['contractcategoryid']]
                    );

                    $campi_info = [];

                    foreach ($campi_columns as $campo) {
                        $campi_info[$campo['id']] = $campo;
                    }

                    $contrattualistica = [];

                    foreach ($valori_dinamici as $campo) {
                        $idCampo = $campo['id'];
                        $contrattualistica[0]["CAMPO_DINAMICO_$idCampo"] = $this->getValue($campo, $campi_info);
                    }

                    $fields['contrattualistica'] = $contrattualistica;
                    break;
				case 'pratica':
					$tags = $this->pratica;
					$from = ' FROM archivio a
							  LEFT JOIN poliswebpratica pp ON pp.codice_pratica = a.id
                              LEFT JOIN tabellaautorita ta ON a.ufficio_giudiziario = ta.id
                              LEFT JOIN tabellacitta tc ON ta.citta = tc.id
                              LEFT JOIN tabellaoggetto togg ON a.oggetto = togg.id
                              LEFT JOIN tabellareato tr ON a.reato = tr.id
                              LEFT JOIN tabellaavvocati tavv ON tavv.id = a.avvocato
                              LEFT JOIN datistudio ds ON tavv.id = ds.avvocato
                              LEFT JOIN tabellastati ts ON ts.id = a.stato
                              LEFT JOIN utente u ON u.id = a.referent_id
                              LEFT JOIN utente utn ON utn.codiceavvocato = a.avvocato
                              INNER JOIN tabellatipologiapratica ttp ON a.tipologiapratica = ttp.id';
					$where .= ' AND a.id = ?';
					$id = isset($this->options['request']['praticaId']) ? $this->options['request']['praticaId'] : $this->options['request']['sectionid'];
					$data[] = $id;
					break;
				case 'soggetti':
                    $tags = $this->soggetti;
                    $from = ' FROM anagrafiche a
                               LEFT JOIN tabellavalute tv ON tv.id = a.valuta
                               LEFT JOIN tabellacitta tc ON tc.id = a.comune';
                    $where .= ' AND a.id IN (' . implode(',', $this->options['request']['soggetti']) . ')';
                    break;
				case 'soggetticlienti':
                    $tags = $this->soggetti;
                    $from = ' FROM anagrafiche a 
                               LEFT JOIN tabellavalute tv ON tv.id = a.valuta
                               LEFT JOIN tabellacitta tc ON tc.id = a.comune';
                    $where .= ' AND a.id IN (' . implode(',', $this->options['request']['soggetticlienti']) . ')';
                    break;
				case 'soggetticontroparti':
					$tags = $this->soggetti;
					$from = ' FROM anagrafiche a
					            LEFT JOIN tabellavalute tv ON tv.id = a.valuta
					            LEFT JOIN tabellacitta tc ON tc.id = a.comune';
					$where .= ' AND a.id IN (' . implode(',', $this->options['request']['soggetticontroparti']) . ')';
					break;
                case 'soggettiavversari':
                    $tags = $this->soggetti;
                    $from = ' FROM anagrafiche a
					            LEFT JOIN tabellavalute tv ON tv.id = a.valuta
					            LEFT JOIN tabellacitta tc ON tc.id = a.comune';
                    $where .= ' AND a.id IN (' . implode(',', $this->options['request']['soggettiavversari']) . ')';
                    break;
                case 'rcdocumenti':
                    $tags = $this->rcdocumenti;
                    $from = ' FROM recuperocrediti_documenti rcd
                              LEFT JOIN utente u ON rcd.pa_assignee_id = u.id
                              LEFT JOIN anagrafiche a ON rcd.pa_subject_id = a.id';
                    $where .= ' AND rcd.id IN (' . implode(',', $this->options['request']['rcdocumenti']) . ')';
                    break;
				case 'avvocato':
					$tags = $this->avvocato;
					$from = ' FROM tabellaavvocati tavv
                              LEFT JOIN datistudio ds ON tavv.id = ds.avvocato';
					$where .= ' AND tavv.id = ?';
					$id = $this->options['request']['avvocatoId'];
					$data[] = $id;
					break;
				case 'udienze':
					$tags = $this->udienze;
					$from = $this->fromUdienze;
					$where .= ' AND a.id IN (' . implode(',', $this->options['request']['udienze']) . ')';
					$orderBy = $this->options && $this->options['orderBy']? $this->options['orderBy'] : $this->orderByUdienze;
					break;
				case 'impegni':
					$tags = $this->impegni;
					$from = $this->fromImpegni;
					$where .= ' AND s.id IN (' . implode(',', $this->options['request']['impegni']) . ')';
					$orderBy = $this->orderByImpegni;
					break;
				case 'documenti':
					$tags = $this->documenti;
					$from = ' FROM documento d';
					$where .= ' AND d.id IN (' . implode(',', $this->options['request']['documenti']) . ')';
					$orderBy = ' ORDER BY d.titolodocumento ASC';
					break;
				case 'recuperocrediti':
					$datiBancariRaw = null;
					if ($version && $version < "3.20") {
						$datiBancariRaw = 'd.iban';
					}
					$tags= $this->getRecuperoCrediti($datiBancariRaw);
					$from = ' FROM recuperocrediti_documenti rd
                              LEFT JOIN recuperocrediti_solleciti rs ON rd.id = rs.recuperocrediti_doc_id AND rs.warning_type = ?
                              LEFT JOIN recuperocrediti_interessi ri ON ri.id = rs.interest_applied                            
                              LEFT JOIN parcella pa ON pa.progressivo = rd.rif_fattura AND rd.file_id = pa.codicepratica AND pa.anno = rd.year
                              LEFT JOIN archivio arch ON rd.file_id = arch.id
                              LEFT JOIN tabellaautorita ta ON arch.ufficio_giudiziario = ta.id
                              LEFT JOIN tabellacitta tc ON ta.citta = tc.id
                              LEFT JOIN datistudio d ON d.avvocato = arch.avvocato
                              LEFT JOIN utente u ON u.id = arch.avvocato
                              LEFT JOIN anagrafica_pratica ap ON ap.file_id = arch.id AND ap.relazione = 2
                              LEFT JOIN anagrafiche a ON ap.person_id = a.id
                              CROSS JOIN recuperocrediti_settings sett
                              ';

					$where .= ' AND rd.id = ?';
					$id = $this->options['request']['feeId'];
					$warningType = $this->options['request']['type'];
					$data[] = $warningType;
					$data[] = $id;
					break;
				case 'calendario':
					$results = $impegni = $udienze = array();

					if (!empty($this->options['request']['impegni'])) {
						$from = $this->fromImpegni;
						$where .= ' AND s.id IN (' . implode(',', $this->options['request']['impegni']) . ')';
						$select .= $this->createSelect($this->impegni);
						$sql = $select . $from . $where . $this->orderByImpegni;
						$impegni = $this->PH->db->fetchAll($sql, $data);
						foreach ($impegni as $idx => $impegno) {
							if (!empty($impegno['VALORI_DINAMICI']) && $impegno['VALORI_DINAMICI'] != 'null') {
								$campi = json_decode($impegno['VALORI_DINAMICI'], true);
								foreach($campi as $campo) {
									$valoreCampo = $campo['valore'];
									$idCampo = $campo['id'];
									$impegno["PRATICA_CAMPO_DINAMICO_$idCampo"] = $valoreCampo;
								}
								$impegni[$idx] = $impegno;
							}
						}
					}

					if (!empty($this->options['request']['udienze'])) {
						$select = 'SELECT ';
						$from = $orderBy = '';
						$where = ' WHERE 1=1 ';
						$from = $this->fromUdienze;
						$where .= ' AND a.id IN (' . implode(',', $this->options['request']['udienze']) . ')';
						$select .= $this->createSelect($this->udienze);
						$sql = $select . $from . $where . $this->orderByUdienze;
						$udienze = $this->PH->db->fetchAll($sql, $data);
						foreach ($udienze as $idx => $udienza) {
							if (!empty($udienza['VALORI_DINAMICI']) && $udienza['VALORI_DINAMICI'] != 'null') {
								$campi = json_decode($udienza['VALORI_DINAMICI'], true);
								foreach($campi as $campo) {
									$valoreCampo = $campo['valore'];
									$idCampo = $campo['id'];
									$udienza["PRATICA_CAMPO_DINAMICO_$idCampo"] = $valoreCampo;
								}
								$udienze[$idx] = $udienza;
							}
						}
					}

					$results = array_merge($impegni, $udienze);
					usort($results, 'cmp2');
					$fields['calendario'] = $results;
					break;
				case 'fattura':
					$tags = $this->fattura;
					$from = ' FROM parcella p
                              LEFT JOIN datistudio d ON d.avvocato = p.avvocatoemittente
                              LEFT JOIN archivio arch ON arch.id = p.codicepratica
                              LEFT JOIN tabellaavvocati ta ON d.avvocato = ta.id'
                    ;
					$where .= ' AND p.id = ?';
					$id = $this->options['request']['sectionid'];
					$data[] = $id;
					break;
				case 'movimenti':
					$tags = $this->movimenti;
					$from = ' FROM parcellamovimenti pm';
					$where .= ' AND pm.parcella_id = ? AND pm.descrizione <> "Imposta di bollo virtuale ai sensi del D.M. 17/06/2014"';
					$orderBy = ' ORDER BY pm.id';
					$id = $this->options['request']['sectionid'];
					$data[] = $id;
					break;
				case 'fatture':
					$tags = $this->fattura;
					$from = ' FROM parcella p
                              LEFT JOIN datistudio d ON d.avvocato = p.avvocatoemittente
                              LEFT JOIN archivio arch ON arch.id = p.codicepratica
                              LEFT JOIN tabellaavvocati ta ON d.avvocato = ta.id';
					$where .= ' AND p.id IN (' . implode(',', $this->options['request']['fatture']) . ')';
					break;
                case 'liquidazione':
                    $tags = $this->liquidazione;
                    $from = ' FROM liquidazionepa as pa
                             LEFT JOIN liquidazionepacalcolo as lpc on lpc.id = pa.calculate_id
                             LEFT JOIN anagrafiche as a on a.id = pa.subject_id ';
                    $where .= 'AND pa.id IN (' . implode(',', $this->options['request']['liquidazione']) . ')';
			}

			if ($label != 'calendario' && $label != 'contrattualistica') {
				$select .= $this->createSelect($tags);

                $sql = $select . $from . $where . $orderBy;
				// $this->PH->applogger->info($sql);

				$results = array();
				$stmt = $this->PH->db->query($sql, $data);
				while ($result = $stmt->fetch()) {
					if (($label == 'soggetti' || $label == 'soggetticlienti' || $label == 'soggetticontroparti' || $label == 'soggettiavversari') && !empty($result['INDIRIZZI'])) {
						$indirizziDecoded = $this->getIndirizzi($result['INDIRIZZI'], $this->PH->db, $this->PH->dbShared);
                        $citta = array($indirizziDecoded['cap'], $indirizziDecoded['citta']);
                        if (is_null($result['COMUNE'])) $result['COMUNE'] = $indirizziDecoded['citta'];
						$provincia = trim($indirizziDecoded['provincia']);
						if($provincia){
							$citta[] = "($provincia)";
						}

						$indirizzi = array(
							$indirizziDecoded['via'],
							implode(' ', $citta),
							$indirizziDecoded['nazione'],
						);
						$result['INDIRIZZO'] = implode('', array('', implode(PHP_EOL, array_filter($indirizzi))));
						$result['INDIRIZZO_ESTESO'] = implode(", ", $indirizzi);

                        $result['NOME_PEC'] = $result['DENOMINAZIONE'] . ' - ' . json_decode($result['CONTATTI'], 1)['7'];
                        $result['EMAIL'] = json_decode($result['CONTATTI'], 1)['6'];
                        $result['TELEFONO'] = json_decode($result['CONTATTI'], 1)['1'];
                        $result['TELEFONO_UFFICIO'] = json_decode($result['CONTATTI'], 1)['2'];
                        $result['CELLULARE'] = json_decode($result['CONTATTI'], 1)['3'];

					} else if($label == 'recuperocrediti'){

						//Ricavo solo le pec delle controparti
						$controparte = explode(";", $result['PEC_CONTROPARTE']);
						$pecs = "";
						foreach($controparte as $c)
						{
							foreach(json_decode($c) as $key => $val)
								if($key == '7')
									$pecs .= $val . ", ";
						}
						$result['PEC_CONTROPARTE'] = substr($pecs, 0, -2);

						if($result['INDIRIZZO_CLIENTE'] && !empty($result['INDIRIZZO_CLIENTE'])){
							$indirizziClienteDecoded = $this->getIndirizzi($result['INDIRIZZO_CLIENTE'], $this->PH->db, $this->PH->dbShared );
							$viaCliente = $indirizziClienteDecoded['via'] ;
							$capCliente = $indirizziClienteDecoded['cap'] ;
							$cittaCliente= $indirizziClienteDecoded['citta'] ;
							$provinciaCliente = $indirizziClienteDecoded['provincia'] ;
							$result['INDIRIZZO_CLIENTE'] = $viaCliente . " - " . $capCliente. " " . $cittaCliente . " " . $provinciaCliente;
							$result['CITTA_CLIENTE'] = $cittaCliente;
						}

						if($result['INDIRIZZO_CONTROPARTE'] && !empty($result['INDIRIZZO_CONTROPARTE'])){

							$denominazioneArray = explode(";", $result['DENOMINAZIONE_CONTROPARTE']);
							$denominazioneList = array();
							$indirizzi = explode(';', $result['INDIRIZZO_CONTROPARTE']);
							$indirizziDecoded = array();
							$indirizziControparte = array();
							$cfArray = explode(';',$result['CODICEFISCALE_CONTROPARTE']);
							$cfList = array();
							$pivaArray = explode(';',$result['PARTITAIVA_CONTROPARTE']);
							$pivaList = array();
							$intestazioniArray = array();


							foreach($indirizzi as $indirizzo){
								list($name, $address) = explode('-', $indirizzo);
								$indirizziDecoded[$name] = $this->getIndirizzi($address,  $this->PH->db, $this->PH->dbShared);
							}

							foreach($indirizziDecoded as $name => $newAddress){
								$via= $newAddress['via'];
								$citta = $newAddress['citta'];
								$cap = $newAddress['cap'];
								$provincia = $newAddress['provincia'];
								$indirizziDecoded[$name] = $via . "\n" . $cap . " " . $citta . " " . $provincia;
								$indirizziControparte[$name] = $via . "\n" . $cap . ", " . $citta . " " . $provincia;
							}
							$result['INDIRIZZO_CONTROPARTE'] = implode("; " , $indirizziControparte);
						}

						if($result['DENOMINAZIONE_CONTROPARTE'] && !empty($result['DENOMINAZIONE_CONTROPARTE'])){
							foreach($denominazioneArray as $item){
								array_push($denominazioneList, $item);
							}

							$result['DENOMINAZIONE_CONTROPARTE'] = implode(", ", $denominazioneList);
						}

						if($result['CODICEFISCALE_CONTROPARTE'] && !empty($result['CODICEFISCALE_CONTROPARTE'])){
							foreach($cfArray as $item){
								list($name, $cf) = explode('-', $item);
								$cfList[$name] = $cf;
							}
							$result['CODICEFISCALE_CONTROPARTE'] = implode(" - ", $cfList);
						}

						if($result['PARTITAIVA_CONTROPARTE'] && !empty($result['PARTITAIVA_CONTROPARTE'])){
							foreach($pivaArray as $item){
								list($name, $piva) = explode('-', $item);
								$pivaList[$name] = $piva;
							}
							$result['PARTITAIVA_CONTROPARTE'] = implode(" - ", $pivaList);
						}

						if(!empty($denominazioneArray)){
							foreach($denominazioneArray as $nome){
								$intestazioniArray[$nome] = $nome . "\n" ;
								if(!empty($indirizziDecoded)){
									$intestazioniArray[$nome] .= $indirizziDecoded[$nome] . "\n";
								}
								if(!empty($cfList)){
									$intestazioniArray[$nome] .= $cfList[$nome] . "\n";
								}
								if(!empty($pivaList)){
									$intestazioniArray[$nome] .= $pivaList[$nome] . "\n";
								}
							}

							$result['INTESTAZIONE_CONTROPARTE'] = implode("\n\n", $intestazioniArray);
						}

					}else if ($label == 'fattura' || $label == 'fatture') {
                        if (!empty($result['SEZIONALE_PROGRESSIVO'])) {
                            $progressivo = array();
                            if ($result['PROGRESSIVO']) {
                                $progressivo[] = $result['PROGRESSIVO'];
                            } else if ($result['PROGRESSIVO_ALFANUMERICO']) {
                                $progressivo[] = $result['PROGRESSIVO_ALFANUMERICO'];
                            }
                            $progressivo[] = $result['SEZIONALE_PROGRESSIVO'];
                            if ($result['SEZIONALE_PROGRESSIVO_BIS']) {
                                $progressivo[] = $result['SEZIONALE_PROGRESSIVO_BIS'];
                            }
                            $result['NUMERO'] = join('-', $progressivo);
                        } else {
                            $result['NUMERO'] = $result['PROGRESSIVO'];
                        }

                        // Inizializza le variabili
                        $destCF = '';
                        $destPIVA = '';
                        $destCodice = '';

                        // Primo tentativo con preg_match per il Codice Fiscale
                        if (preg_match('/CF\s*:\s*([A-Za-z0-9]{11,16})/i', $result['DESTINATARIO'], $matches)) {
                            $destCF = trim($matches[1]);
                        } else {
                            // Secondo tentativo con preg_split
                            $parts = preg_split('/CF\s*:\s*/i', $result['DESTINATARIO']);
                            if (isset($parts[1])) {
                                $destCF = trim(strtok($parts[1], "\n\r"));
                            }
                        }

                        // Primo tentativo con preg_match per la Partita IVA
                        if (preg_match('/PI\s*:\s*([A-Za-z0-9]{11})/i', $result['DESTINATARIO'], $matches)) {
                            $destPIVA = trim($matches[1]);
                        } else {
                            // Secondo tentativo con preg_split
                            $parts = preg_split('/PI\s*:\s*/i', $result['DESTINATARIO']);
                            if (isset($parts[1])) {
                                $destPIVA = trim(strtok($parts[1], "\n\r"));
                            }
                        }

                        if (preg_match('/Cod\.\s*Dest\.\s*:\s*([A-Za-z0-9]{6,7})\b/i', $result['DESTINATARIO'], $matches)) {
                            $destCodice = trim($matches[1]);
                        } else {
                            $parts = preg_split('/Cod\.\s*Dest\.\s*:\s*/i', $result['DESTINATARIO']);
                            if (isset($parts[1])) {
                                $candidate = trim(strtok($parts[1], "\n\r"));
                                if (preg_match('/^[A-Za-z0-9]{6,7}$/', $candidate)) {
                                    $destCodice = $candidate;
                                }
                            }
                        }

                        $result['DESTINATARIO_CF'] = $destCF;
                        $result['DESTINATARIO_PIVA'] = $destPIVA;
                        $result['DESTINATARIO_CODDEST'] = $destCodice;

                        if (!empty($result['DESTINATARIO_ALTRI_DATI'])) {
                            $idDestinatario = $result['DESTINATARIO_ALTRI_DATI'];
                            $datiIntestatario = $this->PH->db->fetchRow(
                                'SELECT partitaiva, codicefiscale, contatti FROM anagrafiche WHERE id = ?',
                                $idDestinatario
                            );
                            $arrayDestinatario = array();
                            $arrayDestinatario[] = 'P.IVA ' . $datiIntestatario['partitaiva'];
                            $arrayDestinatario[] = 'C.F. ' . $datiIntestatario['codicefiscale'];
                            $contatti = json_decode($datiIntestatario['contatti'], true);
                            foreach ($contatti as $key => $contatto) {
                                $indice = $this->PH->db->fetchOne(
                                    'SELECT nome FROM nome_campi WHERE id = ? AND relazione = 2 and visible = 1',
                                    $key
                                );
                                if (!empty($contatto)) {
                                    $arrayDestinatario[] = strtoupper($indice) . ' ' . $contatto;
                                }
                            }
                            $result['DESTINATARIO_ALTRI_DATI'] = implode(
                                '',
                                array('', implode(PHP_EOL, array_filter($arrayDestinatario)))
                            );

                            // Rimuovi CF e PI dal campo DESTINATARIO
                            $result['DESTINATARIO'] = preg_replace('/CF\s*:\s*[A-Za-z0-9]{11,16}/i', ' ', $result['DESTINATARIO']);
                            $result['DESTINATARIO'] = preg_replace('/PI\s*:\s*[A-Za-z0-9]{11}/i', ' ', $result['DESTINATARIO']);
                            $result['DESTINATARIO_SENZA_CODDEST'] = preg_replace('/Cod\.\s*Dest\.\s*:\s*[A-Za-z0-9]{6,7}/i', ' ', $result['DESTINATARIO']);
                        }
                    }
                    else if ($label == 'pratica') {
						if (!empty($result['VALORI_DINAMICI']) && $result['VALORI_DINAMICI'] != 'null') {
							$campi = json_decode($result['VALORI_DINAMICI'], true);

							foreach($campi as $campo) {
								$valoreCampo = $campo['valore'];
								$idCampo = $campo['id'];
                                $type = $this->PH->db->fetchOne("SELECT tipo FROM campi_dinamici WHERE id = ?", $idCampo);
                                if ($type == 'date') {
                                    $valoreCampo = date('d/m/Y', strtotime($valoreCampo));
                                }
                                if ($type== 'select') {
                                    $opt = $this->PH->db->fetchOne("SELECT opt FROM campi_dinamici WHERE id = ?", $idCampo);
                                    $opt = json_decode($opt, true);
                                    foreach ($opt as $value => $idx) {
                                        if ($idx == $campo['valore']) {
                                            $valoreCampo = $value;
                                            break;
                                        }
                                    }
                                }
								$result["CAMPO_DINAMICO_$idCampo"] = $valoreCampo;
							}
						}
                        // Gestione Tag Dati Giudiziali Dinamici (Amministrativo, Penale)
                        foreach($tags as $tag) {
                            $str_tag = $tag['tag'];
                            if (str_starts_with($str_tag, 'DT_GIUD')) {
                                $valoreCampo =$this->gestioneTagPraticadatiGiud($str_tag, $result[$str_tag]);
                                $result[$str_tag] = $valoreCampo;
                            }
                        }

                        if (isset($result['SITUAZIONE'])) {
                            $nspName = $this->PH->dbShared->fetchOne('SELECT nsp.nome FROM situazione_pratica nsp WHERE nsp.id = ?', $result['SITUAZIONE']);
                            $result['SITUAZIONE'] = $nspName ?: null;
                        }

                        if (isset($result['SITUAZIONE_CONTABILE'])) {
                            $nscpName = $this->PH->dbShared->fetchOne('SELECT nscp.nome FROM situazione_contabile_pratica nscp WHERE nscp.id = ?', $result['SITUAZIONE_CONTABILE']);
                            $result['SITUAZIONE_CONTABILE'] = $nscpName ?: null;
                        }

                        if (isset($result['CENTRO_PROFITTO'])) {
                            $ncppName = $this->PH->dbShared->fetchOne('SELECT ncpp.nome FROM centro_profitto_pratica ncpp WHERE ncpp.id = ?', $result['CENTRO_PROFITTO']);
                            $result['CENTRO_PROFITTO'] = $ncppName ?: null;
                        }
					}
					$results[] = $result;
				}

				$fields[$label] = $results;
			}
			// $fields = array_merge($fields, $results);
		}

		$fields['generale'][0]['RIFERIMENTO_TEMPORALE_UTC'] = gmdate('Y-m-d\TH:i:s\Z');
		$fields['generale'][0]['DATA_ODIERNA'] = $this->PH->db->fetchOne('SELECT DATE_FORMAT(NOW(), "%d/%m%/%Y")');

		// $this->PH->applogger->info(print_r($fields, 1));
		return array('fields' => $fields, 'filename' => 'Stampa.rtf');
	}

    public function gestioneTagPraticadatiGiud($stringtag, $valori) {
        $valoreFormattag = "";
        if (empty(self::$campi_dinamicipratica_dati_gid) || self::$campi_dinamicipratica_dati_gid == 'null') {
            $stmt = $this->PH->dbShared->query("SELECT * FROM archivio_dati_giud_campi");
            self::$campi_dinamicipratica_dati_gid = $stmt->fetchAll();
        }
        $lista_valori = json_decode($valori, true);
        foreach(self::$campi_dinamicipratica_dati_gid as $campo_dinamico) {
            if($campo_dinamico['tagprint']==$stringtag) {
                foreach((array)$lista_valori as $valore) {
                    if($valore['id']==$campo_dinamico['id']) {
                        $valoreFormattag = $valore['valore'];
                        if (!empty($valoreFormattag) && $valoreFormattag != 'null') {
                            if($campo_dinamico['tipo']=='valore') {
                                $valoreFormattag = number_format($valoreFormattag, 2, ",", ".");
                            } else if($campo_dinamico['tipo']=='time') {
                                $valoreFormattag = date('H:i', strtotime($valoreFormattag));
                            } else if($campo_dinamico['tipo']=='checkbox') {
                                if($valoreFormattag=='true') {
                                    $valoreFormattag = "Si";
                                } else {
                                    $valoreFormattag = "No";
                                }
                            } else if($campo_dinamico['tipo']=='select') {
                                $lista_valori_select = json_decode($campo_dinamico['opt'], true);
                                if($valoreFormattag==0) {
                                    $valoreFormattag="";
                                } else {
                                    $i=0;
                                    foreach($lista_valori_select as $valori_select) {
                                       if($valori_select==$valoreFormattag) {
                                           $reflect = array_keys($lista_valori_select);
                                           $valoreFormattag=$reflect[$i];
                                           break;
                                       }
                                       $i=$i+1;
                                    }
                                }
                            } else if($campo_dinamico['tipo']=='date') {
                                $valoreFormattag =date('d/m/Y', strtotime($valoreFormattag));;
                            } // le regex non le formatto
                        }
                        break;
                    }
                }
                break;
            }
        }
        return $valoreFormattag;
    }

	public function createSelect($tags)
	{
		$select = '';
		foreach($tags as $key => $tag) {
			reset($tags);
			end($tags);
			$select .= ' ' . $tag['query'] . ' AS ' . $tag['tag'];
			if ($key !== key($tags)){
				$select .= ', ';
			}
		}

		return $select;
	}

	// TODO: da togliere la logica di $version quando non ci saranno più le versioni  <= 3.19.1
	public function print(?string $version = null)
	{
        include_once("tbs_class.php");
        include_once("tbs_plugin_opentbs.php");

        $print = $this->PH->db->fetchRow('SELECT * FROM stampe WHERE id = ?', $this->options['request']['docid']);
		$folderPath = $this->sitekey . DIRECTORY_SEPARATOR . self::$PRINTS . DIRECTORY_SEPARATOR . $print['id'] . DIRECTORY_SEPARATOR;
		$filename = $print['filename'];

		$fileTemplate = $this->s3Connector->getBody(self::$MAIN_BUCKET, $folderPath . $filename);
		$tmpUid = $this->getUid();
		$tmpDir = '/tmp' . DIRECTORY_SEPARATOR . $this->sitekey . '-' . $tmpUid;
		if (!file_exists($tmpDir)) {
			mkdir($tmpDir, 0777, true);
		}
		$filePath = $tmpDir . DIRECTORY_SEPARATOR . $filename;

        register_shutdown_function(function () use ($tmpDir) {
            if (is_dir($tmpDir)) {
                exec('rm -rf ' .  $tmpDir);
            }
        });

        $handle = fopen($filePath, 'w');
		fwrite($handle, $fileTemplate);
		fclose($handle);

		$TBS = new clsTinyButStrong();

		$TBS->SetOption('noerr', false); // ENABLE ERROR REPORTING FOR DEBUG
		$TBS->Plugin(TBS_INSTALL, OPENTBS_PLUGIN);
		$TBS->LoadTemplate($filePath, OPENTBS_ALREADY_UTF8);

		$resultPrint = $this->fillTags($version);
		$fields = $resultPrint['fields'];
		$this->PH->applogger->info("CALENDAR TEMPLATE DEBUG - Fields: " . print_r($fields, 1));
		$this->PH->applogger->info("CALENDAR TEMPLATE DEBUG - Labels: " . print_r($this->options['labels'], 1));
		$this->PH->applogger->info("CALENDAR TEMPLATE DEBUG - Request: " . print_r($this->options['request'], 1));
		$filenamePrint = $resultPrint['filename'];

		$mergeBlocks = $this->options['labels'];
		foreach($mergeBlocks as $block) {
			$TBS->MergeBlock($block, $fields[$block]);
		}

        // intestazione
        $header = $TBS->Plugin(OPENTBS_FILEEXISTS, 'word/header1.xml');
        if ($header) {
            $TBS->Plugin(OPENTBS_SELECT_HEADER);
            foreach ($mergeBlocks as $block) {
                $TBS->MergeBlock($block, $fields[$block]);
            }
        }

        // piè di pagina
        $footer = $TBS->Plugin(OPENTBS_FILEEXISTS, 'word/footer1.xml');
        if ($footer) {
            $TBS->Plugin(OPENTBS_SELECT_FOOTER);
            foreach ($mergeBlocks as $block) {
                $TBS->MergeBlock($block, $fields[$block]);
            }
        }

		if (!empty($this->options['output'])){
			$TBS->Show(OPENTBS_FILE,$this->options['output']);
		} else{
			$TBS->Show(OPENTBS_DOWNLOAD, utf8_decode($filenamePrint));
		}
	}

    public function printOutput($tmpDir)
    {

        include_once("tbs_class.php");
        include_once("tbs_plugin_opentbs.php");

        $print = $this->PH->db->fetchRow('SELECT * FROM stampe WHERE id = ?', $this->options['request']['docid']);
        $folderPath = $this->sitekey . DIRECTORY_SEPARATOR . self::$PRINTS . DIRECTORY_SEPARATOR . $print['id'] . DIRECTORY_SEPARATOR;
        $filename = $print['filename'];

        $fileTemplate = $this->s3Connector->getBody(self::$MAIN_BUCKET, $folderPath . $filename);

        if (!file_exists($tmpDir)) {
            mkdir($tmpDir, 0777, true);
        }
        $filePath = $tmpDir . DIRECTORY_SEPARATOR . $filename;

        $handle = fopen($filePath, 'w');
        fwrite($handle, $fileTemplate);
        fclose($handle);

        $TBS = new clsTinyButStrong();

//        $TBS->SetOption('noerr', true); // per debug decommentare così si vedono gli errori a video
        $TBS->Plugin(TBS_INSTALL, OPENTBS_PLUGIN);
        $TBS->LoadTemplate($filePath, OPENTBS_ALREADY_UTF8);

        $resultPrint = $this->fillTags();
        $fields = $resultPrint['fields'];
// $this->PH->applogger->info(print_r($fields, 1));
        $filenamePrint = $resultPrint['filename'];

        $mergeBlocks = $this->options['labels'];
        foreach($mergeBlocks as $block) {
            $TBS->MergeBlock($block, $fields[$block]);
        }

        $TBS->Show(OPENTBS_STRING,'');
        return [$TBS->Source, explode(".", $filename)[1]];

    }

    public function writeContractDocx($tmpDir): string
    {
        include_once("tbs_class.php");
        include_once("tbs_plugin_opentbs.php");

        $print      = $this->PH->db->fetchRow("SELECT * FROM stampe WHERE id = ?", $this->options['request']['docid']);
        $folderPath = $this->sitekey . DIRECTORY_SEPARATOR . self::$PRINTS . DIRECTORY_SEPARATOR . $print['id'] . DIRECTORY_SEPARATOR;
        $filename   = $print['filename'];

        $fileTemplate = $this->s3Connector->getBody(self::$MAIN_BUCKET, $folderPath . $filename);

        if (! file_exists($tmpDir)) {
            mkdir($tmpDir, 0777, true);
        }
        $filePath = $tmpDir . DIRECTORY_SEPARATOR . 'contract_template.docx';

        $handle = fopen($filePath, 'wb'); // binary safe
        fwrite($handle, $fileTemplate);
        fclose($handle);

        $TBS = new clsTinyButStrong();

        $TBS->Plugin(TBS_INSTALL, OPENTBS_PLUGIN);
        $TBS->LoadTemplate($filePath, OPENTBS_ALREADY_UTF8);

        $resultPrint = $this->fillTags();
        $fields      = $resultPrint['fields'];

        $mergeBlocks = $this->options['labels'];
        foreach ($mergeBlocks as $block) {
            $TBS->MergeBlock($block, $fields[$block]);
        }

        unlink($filePath);

        $filePath = $tmpDir . DIRECTORY_SEPARATOR . $filename;
        $TBS->Show(OPENTBS_FILE, $filePath);

        return $filePath;
    }

    /**
     * @param $s3Connector
     * @param string $folderPath
     * @param $filename
     * @return string[]
     */
    public function getTemplate($s3Connector, string $folderPath, string $tmpDir, string $filePath, $filename): void
    {
        $fileTemplate = $s3Connector->getBody(self::$MAIN_BUCKET, $folderPath . $filename);

        if (!file_exists($tmpDir)) {
            mkdir($tmpDir, 0777, true);
        }

        $handle = fopen($filePath, 'w');
        fwrite($handle, $fileTemplate);
        fclose($handle);
    }


	private function getIndirizzi($indirizzi, $db, $dbShared) {
		$result = array(
			'citta' => NULL,
			'via' => NULL,
			'idcitta' => NULL,
			'cap' => NULL,
			'provincia' => NULL,
			'nazione' => NULL
		);
		if (!empty($indirizzi)){
			$indirizziDecoded = json_decode($indirizzi, 1);
			if (!empty($indirizziDecoded['9'])){
				if (!empty($indirizziDecoded['9']['citta'])){
					$citta = $indirizziDecoded['9']['citta'];
					$place = $db->fetchRow('SELECT * FROM tabellacitta WHERE id = ?', $citta);
					$result['citta'] = $place['nome'];
					$result['idcitta'] = $place['id'];
					$result['provincia'] = $place['provincia'];
				}
				if (!empty($indirizziDecoded['9']['via'])) {
					$result['via'] = $indirizziDecoded['9']['via'];
				}
				if (!empty($indirizziDecoded['9']['cap'])) {
					$result['cap'] = $indirizziDecoded['9']['cap'];
				}
				if (!empty($indirizziDecoded['9']['nazione'])) {
					$result['nazione'] = $dbShared->fetchOne('SELECT nome FROM nazioni WHERE id = ?', $indirizziDecoded['9']['nazione']);
				}
			}
		}
		return $result;
	}

	private function getUid()
	{
		return strtoupper(base_convert(microtime(true), 10, 16) . '-' . uniqid(rand()));
	}

    private function getValue($campo, $campi_info)
    {
        $campo_value = $campo['valore'];
        $campo_info  = $campi_info[$campo['id']];

        if ($campo_info['tipo'] == 'select') {
            $opt = json_decode($campo_info['opt'], true);
            foreach ($opt as $value => $idx) {
                if ($idx == $campo['valore']) {
                    $campo_value = $value;
                    break;
                }
            }
        }

        if ($campo_info['tipo'] == 'checkbox') {
            $campo_value = 'Sì';
        }

        return $campo_value;
    }
}
