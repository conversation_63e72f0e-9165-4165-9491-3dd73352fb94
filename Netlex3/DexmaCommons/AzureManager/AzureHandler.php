<?php

// DOCUMENTAZIONE AZURE SDK:
// https://github.com/Azure/azure-storage-php

// DOCUMENTAZIONE OAUTH 2.0 AZURE PER RICHIESTA TOKEN
// https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-client-creds-grant-flow#request-an-access-token

// ESEMPIO DI CHIAMATA cURL PER RICHIESTA TOKEN
// https://jiasli.github.io/azure-notes/aad/Service-Principal-get-token-with-curl.html



require_once SITE_ROOT . "/vendor/autoload.php";
use MicrosoftAzure\Storage\Blob\BlobRestProxy;
use MicrosoftAzure\Storage\Common\Exceptions\ServiceException;

class AzureHandler
{
    private $blobClient;
    private $accountName;
    private $accountKey;
    private $tenantId;
    private $clientId;
    private $secretKey;
    private $containerName;
    private $folderName;
    private $bearerToken;
    private $tokenCounter;
    protected $applogger;



    public function __construct(
        $accountName = null,
        $accountKey = null,
        $tenantId = null,
        $clientId = null,
        $secretKey = null,
        $containerName = null,
        $folderName = null,
        $config = null
        )
    {


        $applogger = new Zend_Log(new Zend_Log_Writer_Stream(SITE_ROOT .  "/Software/data/logs/azure.log"));
        $filter = new Zend_Log_Filter_Priority(6);
        $applogger->addFilter($filter);
        $this->applogger = $applogger;

        //PASSARE $config nel caso sia lanciato da Script init2.php
        //ALTRIMENTI userà la configurazione generale
        if(!empty($config)){
            Zend_Registry::set("generalConfig", $config);
        }

        $this->accountName = !empty($accountName) ? $accountName : Zend_Registry::get('generalConfig')->azure->account_name;
        $this->accountKey = !empty($accountKey) ? $accountKey : Zend_Registry::get('generalConfig')->azure->account_key;
        $this->tenantId = !empty($tenantId) ? $tenantId : Zend_Registry::get('generalConfig')->azure->tenantId;
        $this->clientId = !empty($clientId) ? $clientId : Zend_Registry::get('generalConfig')->azure->clientId;
        $this->secretKey = !empty($secretKey) ? $secretKey : Zend_Registry::get('generalConfig')->azure->secret;
        $this->containerName = !empty($containerName) ? $containerName : Zend_Registry::get('generalConfig')->azure->container_name;
        $this->folderName = !empty($folderName) ? $folderName : Zend_Registry::get('generalConfig')->azure->folder_name;

        if(!empty($this->accountKey))
        {
            try
            {
                $connectionString = "DefaultEndpointsProtocol = https;AccountName=" . $this->accountName . ";AccountKey=" . $this->accountKey . ";EndpointSuffix=core.windows.net";
                $this->blobClient = BlobRestProxy::createBlobService($connectionString);
            } catch (ServiceException $e)
            {
                $code = $e->getCode();
                $error_message = $e->getMessage();
                $this->applogger->info(print_r($code . ' - ' . $error_message), 1);
            }

        }elseif(!empty($this->clientId) && !empty($this->tenantId) && !empty($this->secretKey))
        {
            $this->bearerToken = null;
            try
            {
                $connectionString = "DefaultEndpointsProtocol = https;AccountName=" . $this->accountName .";EndpointSuffix=core.windows.net";
                $this->blobClient = BlobRestProxy::createBlobServiceWithTokenCredential($this->bearerToken,$connectionString);
                $this->bearerToken = $this->getAzureToken($this->tenantId, $this->clientId, $this->secretKey);

            } catch (ServiceException $e)
            {
                $code = $e->getCode();
                $error_message = $e->getMessage();
                $this->applogger->info(print_r($code . ' - ' . $error_message), 1);
            }
        }else
        {
            $this->applogger->info('ERRORE: inserire le chiavi nel file di configurazione od usarle con il costruttore');
            exit;
        }
    }


    public function getBLOB($fileName)
    {
        try
        {
            $filePath = '/'.$this->folderName . $fileName;
            $blob = $this->blobClient->getBlob($this->containerName, $filePath);
            return $blob;

        }catch(ServiceException $e)
        {
            $code = $e->getCode();
            $error_message = $e->getMessage();
            $this->applogger->info(print_r($code . ' - '. $error_message),1);

            return $error_message;
        }
    }



    public function getAzureToken($tenantId, $clientId, $secretKey)
    {
        $ch = curl_init();
        $headers = array('Content-Type: application/x-www-form-urlencoded');
        $url = 'https://login.microsoftonline.com/'.$tenantId.'/oauth2/v2.0/token';

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $data = 'grant_type=client_credentials&client_id='.$clientId.'&client_secret='.$secretKey.'&scope=https://utdtkwe003.blob.core.windows.net/.default';
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS,$data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Timeout in seconds
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $authToken = json_decode(curl_exec($ch),true);

        //fa un massimo di 3 tentativi di richiesta, nel caso in cui ci sia qualche errore nella chiamata;
        if(!$authToken){
            $this->tokenCounter ++;
            if($this->tokenCounter <= 2){
                $this->bearerToken = $this->getAzureToken($this->tenantId, $this->clientId, $this->secretKey);
            }else{
                $this->applogger->info('ERRORE: token non trovato!');
                exit;
            }
        }

        if(curl_errno($ch))
        {
            $error_msg = curl_error($ch);
        }

        curl_close($ch);

        if(empty($error_msg))
        {
            $authKey = null;
            if (!empty($authToken))
            {
                $authKey = $authToken['access_token'];
            }
            return $authKey;
        }else
        {
            $this->applogger->info('ERROR:'. print_r($error_msg,1));
        }
    }

}