<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

require_once(DEXMA_COMMONS . "/Workflow/ComponentManager.php");

class EventListener
{
	protected $db;
	protected $subdomainName;
	protected $applogger;
	
	protected $file_id;
	protected $action_type;
	protected $optional_data;
	
	function __construct($file_id,$action_type,$optional_data,$db,$subdomainName,$applogger){
		$this->file_id 			= $file_id;
		$this->action_type 		= $action_type;
		$this->db 				= $db;
		$this->applogger		= $applogger;
		$this->subdomainName	= $subdomainName;
		$this->optional_data	= $optional_data;
	}
	
	
	//CERCA NELLA TABELLA DOVE SONO STATI INSERITI GLI ID DEI COMPONENT CHE SI ASPETTATO UN EVENTO
	//SE LI TROVA, NE ISTANZIA LA CLASSE GENERALE E LI RISVEGLIA PASSANDOGLI IL JSON DELLA TABELLA-CHIAVE
	function trigger(){
		$components_id = $this->db->fetchCol("SELECT component_status_id FROM event_listeners WHERE file_id = ? AND component_type = ?", array($this->file_id, $this->action_type));
		
		foreach ($components_id as $component_id){
			$componentObj = new ComponentManager($component_id,$this->db,$this->subdomainName,$this->applogger);
			$componentObj->awakeEventTrigger($this->optional_data);
			unset($componentOBJ);
		}
	}
	
	
}


 ?>