<?php

namespace  Dex<PERSON><PERSON>ommons\Workflow;

use Zend_Registry;

class WorkflowManager
{
    //TABELLE
    const TABELLA_ISTANZE = 'component_status';
    const TABELLA_ISTANZE_WORKFLOW = 'workflow_pratica';
    const TABELLA_MODELLI = 'workflow_models';
    const TABELLA_STEPWAIT_INPUTS = 'stepwait_inputs';
    const TABELLA_EVENT_LISTENERS = 'event_listeners';
    const TABELLA_LOG = 'component_logs';

//STATI
    const WAIT = 1;
    const TODO = 2;
    const DOING = 3;
    const DONE = 4;
    const DISCARDED = 5;
    const OVERTIME = 6;
    const SYSTEM_ERROR = 7;

    /**
     * @var
     */
	protected $db;
	protected $subdomainName;
	protected $applogger;
	
	protected $workflow; //Rappresenta la riga dell'istanza del Workflow

    /**
     * WorkflowManager constructor.
     * @param $db
     * @param $subdomainName
     * @param $applogger
     * @param null $uniqueid
     */
	function __construct($db,$subdomainName,$applogger,$uniqueid = NULL){
		$this->db 					= $db;
		$this->subdomainName 		= $subdomainName;
		$this->applogger			= $applogger;
		
		if(!empty($uniqueid)){
			$this->workflow			= (object) 	$this->db->fetchRow("SELECT * FROM ".self::TABELLA_ISTANZE_WORKFLOW." WHERE uniqueid = ?", $uniqueid);
		}
        Zend_Registry::set('db', $db);
	}
	
	//RITORNA L'ATTUALE DATETIME

    /**
     * @return false|string
     */
	function now(){
		return date('Y-m-d H:i:s');
	}
	
	//SETTA A DONE IL WORKFLOW IMPEDENTO CHE PROSEGUA NON APPENA ESEGUE I COMPONENT GIà INIZIATI (N.B. SE UN COMPONENT NON TERMINA NELL'ULTIMA ITERAZIONE INIZIATA RIMARRà TALE)

    /**
     * @param null $userAction
     */
	function turnOffWorkflow($userAction = NULL){
		$fieldList = array(
				'stato' => self::DONE,
				'terminatoil' => $this->now(),
		);
		if (!empty($userAction)){
			$fieldList['modificatoil'] = $this->now(); //modificatoil viene usato per indicare se è stata un'azione dell'utente dalla gestione istanze
		}
		
		if ($this->workflow->stato != 4){
			$this->db->update(self::TABELLA_ISTANZE_WORKFLOW, $fieldList , array("id=?"=>$this->workflow->id));
		}
	}
	
	// METTE IN PAUSA IL WORKFLOW , DA EVITARE MA POTREBBE ESSERE NECESSARIO

    /**
     * @param null $userAction
     */
	function pauseWorkflow($userAction = NULL){
		$fieldList = array(
				'stato' => self::WAIT,
		);
		if (!empty($userAction)){
			$fieldList['modificatoil'] = $this->now(); //modificatoil viene usato per indicare se è stata un'azione dell'utente dalla gestione istanze
		}
		
		if ($this->workflow->stato == 3 ){
			$this->db->update(self::TABELLA_ISTANZE_WORKFLOW, $fieldList , array("id=?"=>$this->workflow->id));
		}
	}
	
	
	// RIPRENDE IL WORKFLOW , DA EVITARE MA POTREBBE ESSERE NECESSARIO

    /**
     * @param null $userAction
     */
	function resumeWorkflow($userAction = NULL){
		$fieldList = array(
				'stato' => self::DOING,
		);
		if (!empty($userAction)){
			$fieldList['modificatoil'] = $this->now(); //modificatoil viene usato per indicare se è stata un'azione dell'utente dalla gestione istanze
		}
		
		if ($this->workflow->stato == 1 ){
			$this->db->update(self::TABELLA_ISTANZE_WORKFLOW, $fieldList , array("id=?"=>$this->workflow->id));
		}
	}
	
	//funzione save riportata nel manager

    /**
     * @param $db
     * @param $table
     * @param $data
     * @param false $xss_protection
     * @return mixed|string
     */
	function save($db, $table, $data, $xss_protection = FALSE)
	{
		try
		{
			if (!empty($data['uniqueid']))
			{
				$uniqueid = $data['uniqueid'];
				$where['uniqueid = ?'] = $uniqueid;
				unset($data['uniqueid']);
				if($db->update($table, $data, $where, $xss_protection) >= 0)
				{
					return $uniqueid;
				}
			}
			else
			{
				$data['uniqueid'] = strtoupper(base_convert(microtime(true), 10, 16) . "-" . uniqid(rand()));
				if($db->insert($table, $data, $xss_protection) == 1)
				{
					return $data['uniqueid'];
				}
			}
		}
		catch (Zend_Db_Adapter_Exception $e)
		{
			throw $e;
		}
	}

    /**
     * @param $wf_uid
     * @param $actual
     * @param $next
     */
    function createComponents($workflow_uid, $actual, $next)
    {
        $istance        = $this->db->fetchRow("SELECT id,workflow_id FROM workflow_pratica WHERE uniqueid = ?", $workflow_uid);
        $flowJSON       = $this->db->fetchOne("SELECT json FROM workflow_models WHERE id = ?", $istance['workflow_id']);
        $flowArray      = json_decode($flowJSON, 1);


        $nodi = $flowArray['nodes'];

        foreach ($nodi as $node_id => $node_content) {

            $id = $node_content['id'];

            if ($next == NULL) {
                if ($node_content['name'] == 'Inizio Workflow') {

                    $next = $id;
                }
            }


            if (!empty($next) && $id == $next) {

                $data = $node_content['data'];
                $inputs = $node_content['inputs'];
                $outputs = $node_content['outputs'];
                $previous = array($actual);
                $in_the_future = array();

                foreach ($outputs as $output_key => $output_content) {
                    $connections = $output_content['connections'];
                    $way = array();
                    foreach ($connections as $connection) {
                        array_push($way, $connection['node']);
                    }
                    $in_the_future[$output_key] = $way;
                }

                $now = date('Y-m-d H:i:s');
                $fieldList = array(
                    'stato' => 3,
                    'previous' => json_encode($previous),
                    'next' => json_encode($in_the_future),
                    'input' => json_encode($inputs),
                    'output' => json_encode($outputs),
                    'data' => json_encode($data),
                    'workflow_id' => $istance['id'],
                    'node_id' => $id,
                    'component_id' => 0,
                    'immessoil' => $now,
                    'uniqueid' => strtoupper(base_convert(microtime(true), 10, 16) . "-" . uniqid(rand())),
                );
                $this->db->insert('component_status', $fieldList);

            }

        }
    }


    //
	
}

?>