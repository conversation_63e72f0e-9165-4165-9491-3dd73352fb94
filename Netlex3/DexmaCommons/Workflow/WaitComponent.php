<?php


namespace  Dex<PERSON><PERSON>om<PERSON>\Workflow;

use \DateInterval;
use \DateTime;

class WaitComponent extends ComponentManager{
	const FROM_BEGINNING = 1;
	const FROM_LAST_BLOCK = 2;
	
	protected $componentType = self::ATTESA;
	protected $startDate;
	protected $endDate;
	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		if (!empty($this->data->timeblockType) && $this->data->timeblockType == self::FROM_BEGINNING){
			$StartDate = $this->db->fetchOne("SELECT immessoil FROM ".self::TABELLA_ISTANZE_WORKFLOW." WHERE id = ? ", $this->workflow->id);
		}else{ //FROM_LAST_BLOCK
			//PICCOLA CORREZIONE RESA NECESSARIA DAL BLOCCO COUNTER, ma probabile anche da altri, CHE VENENDO ESEGUITI PIù VOLTE
			//LA DATA DI TERMINAZIONE DEL BLOCCO SI AGGIORNAVA MANDANDO IN STARVATION L'ATTUALE WaitComponent
			//$StartDate = $this->db->fetchOne("SELECT terminatoil FROM ".TABELLA_ISTANZE." WHERE id = ? ", (json_decode($this->istanza->previous,1))[0]);
			
			//utilizzo dunque del create invece del TERMINATOIL
			$StartDate = $this->db->fetchOne("SELECT immessoil FROM ".self::TABELLA_ISTANZE." WHERE id = ? ", ($this->istanza->id));
		}
		$this->startDate = new DateTime($StartDate);
		
		$this->endDate = $this->startDate;
		if (!empty($this->data->timeDayQuantity) && is_numeric($this->data->timeDayQuantity)){
			$this->endDate->add(new DateInterval('P'.$this->data->timeDayQuantity.'D'));
		}
		
		if (!empty($this->data->timeHourQuantity) && is_numeric($this->data->timeHourQuantity)){
			$this->endDate->add(new DateInterval('PT'.$this->data->timeHourQuantity.'H'));
		}
		
		if (!empty($this->data->timeMinQuantity) && is_numeric($this->data->timeMinQuantity)){
			$this->endDate->add(new DateInterval('PT'.$this->data->timeMinQuantity.'M'));
		}
	}
	
	
	

	function timeHasGone(){
		$actualDate = new DateTime(now());
		if ($actualDate->format('Y-m-d\TH:i:s') > $this->endDate->format('Y-m-d\TH:i:s')){
			return TRUE;
		}else{
			return FALSE;
		}	
	}
	


	
	
	function execute(){
		//non si setta iniziatoil fino a che non il tempo di attesa non è trascorso, iniziatoil a NULL viene settato da setDone
		if ($this->timeHasGone()){
			$this->setDone();
			if($this->hasNext()){
				$this->setResponse('response');
				$this->setOutput($this->istanza->obtained); //si limita a ritrasferire il proprio output obtained
				$this->setNextComponents();
			}
		}

	}
}

?>