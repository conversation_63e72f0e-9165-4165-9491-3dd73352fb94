<?php


namespace  <PERSON><PERSON><PERSON>om<PERSON>\Workflow;

class EventTriggerAddSubjectComponent extends ComponentManager{
	protected $componentType = self::EVENT_TRIGGER_ADDSUBJECT;
	protected $counter;
	protected $subjectId;

	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		$optional_data = $this->getOptionalDataArray();
		
		if (!empty($optional_data['counter'])){  //contatore giri sulla stessa e unica istanza di un event trigger
			$counter = $optional_data['counter']+1;
		}else{
			$counter = 1;
		}
		$this->counter 		= $counter;
		
		$this->subjectId 	= $optional_data['utente'];
		
	}
	
	



	function execute(){

		$this->begin();
			
		$this->db->update(self::TABELLA_ISTANZE, array("optional_data"=>json_encode(array('counter'=>$this->counter,'utente'=>$this->subjectId)), "terminatoil"=>$this->now(), "stato"=>WAIT), array("id=?" =>$this->istanza->id));

		if ($this->hasNext()){
			$this->setResponse('true');
			$this->setOutput(json_encode(array('utente'=>$this->subjectId)));
			$this->setNextComponents();
		}

	}
}

?>