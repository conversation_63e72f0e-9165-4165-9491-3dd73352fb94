<?php

namespace  Dex<PERSON><PERSON>om<PERSON>\Workflow;


class AddGroupComponent extends ComponentManager{
	protected $componentType = self::ADD_GROUP;
	protected $params;
	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$config,$params,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		$this->config = $config;
		$this->params = $params;
	}
	
	function addGroup($group_id){
		$file_id = $this->workflow->file_id;
		$is_already_reserved = $this->db->fetchOne("SELECT group_id FROM gruppo_pratica WHERE group_id = ? AND file_id = ?", array($group_id,$file_id));
		$group_name = $this->db->fetchOne("SELECT name FROM gruppo WHERE id =?", $group_id);
		
		$response = array(
			'response' => TRUE,
			'commento' => '',
			'gruppo' => $group_id,
		);
		
		$commento = "";
		
		
		$utenti = $this->db->fetchCol("SELECT id_utente FROM utente_gruppo WHERE id_gruppo = ?", $group_id);
		
		foreach($utenti as $user_id){
			$this->addRiserva($user_id);
			$this->handleOneDrivePermissionsAction($user_id, $this->workflow->file_id, 1, 1,$this->params);
		}
		
		if (!empty($is_already_reserved)){
			$commento .= " Il gruppo ".$group_name." risulta già presente tra le riserve della pratica $file_id. Verranno ripristinate tutte le riserve sugli utenti del gruppo.";
		}else{
			$this->db->insert("gruppo_pratica", array("group_id"=>$group_id, "file_id"=>$file_id));
			$commento .= " Il gruppo è stato aggiunto tra le riserve della pratica $file_id";
		}
		
		$response['commento'] = $commento;
		
		return $response;
	}
	
	
	function execute(){
		$this->begin();
		
		if (!empty($this->data->groupId)){
			$response = $this->addGroup($this->data->groupId);
		}else{
			$response['response'] = FALSE;
			$response['commento'] = 'Non è stato selezionato nessun gruppo da poter essere aggiunto';
			$response['gruppo'] = NULL;
		}
		
		$this->setDone();
		if ($this->hasNext()){
			$this->setResponse($response['response'] == TRUE ? 'true' : 'false');
			unset($response['response']);
			$this->setOutput(json_encode($response));
			$this->setNextComponents();
		}	
	}
	
}

?>