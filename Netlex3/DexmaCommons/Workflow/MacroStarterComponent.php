<?php

//require_once(DEXMA_COMMONS . '/TasksManager/TasksHandler.php');

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

use <PERSON>maCom<PERSON>\MacroManager\InstanceManager\InstanceManager;


class MacroStarterComponent extends ComponentManager
{
    protected $componentType = self::MACRO_STARTER;
    protected $dbShared;
    protected $macroId;
    protected $type;
    protected $assignedId = array();
    protected $now;
    protected $warningDays;
    protected $evadifestivi;
    protected $dbProvisioning;



    //AGGIORNARE DATE FESTIVITà
//	protected $holidays = array(
//		"2020-12-08",
//		"2020-12-24",
//		"2020-12-25",
//		"2020-12-26",
//		"2021-01-01",
//		"2021-01-06",
//		"2021-04-04",
//		"2021-05-04",
//		"2021-04-25",
//		"2021-05-01",
//		"2021-06-02",
//		"2021-08-15",
//		"2021-11-01",
//		"2021-12-08",
//		"2021-12-24",
//		"2021-12-25",
//		"2021-12-26"
//	);



    function __construct($component_status_id, $db, $dbShared, $dbProvisioning, $subdomainName, $applogger, $componentRow = NULL, $workflow = NULL)
    {
        parent::__construct($component_status_id, $db, $subdomainName, $applogger, $componentRow, $workflow);

        $this->dbShared = $dbShared;

        $type = explode('_', $this->data->macroId);

        $this->type = ($type[0] == 's') ? 1 : 0;

        $this->macroId = $type[1];

        $this->dbProvisioning = $dbProvisioning;

        $this->assignedId = $this->data->assignedId;

//        $objects = $this->getObtainedArray();
//        if (!empty($objects)) {
//            foreach ($objects as $key => $value) {
//
//                if ($key == 'utente') {
//                    if (is_array($value)) {  //PER PIù Intestatari
//                        foreach ($value as $id_user) {
//                            array_push($this->assignedId, $id_user);
//                        }
//                    } else {
//                        array_push($this->assignedId, $value);
//                    }
//                }
//            }
//        }
//

        $this->assignedId = array($this->assignedId);


        $this->now = $this->now();

        $this->warningDays = ! empty($this->data->timeDayQuantity) && is_numeric($this->data->timeDayQuantity) ? $this->data->timeDayQuantity : 10;

        $this->evadifestivi = !empty($this->data->evadifestivi) ? 1 : 0;

    }


    function execute()
    {
        $this->begin();

        $macroInstanceManager = new InstanceManager(
            null,
            $this->db,
            $this->dbShared,
            $this->dbProvisioning,
            $this->subdomainName,
            $this->applogger,
            null
        );

        list($macro, $details, $graph) = $macroInstanceManager->init(
            $this->now,
            $this->macroId,
            $this->type,
            true,
            false,
            $this->evadifestivi
        );


        $macroInstanceManager->run(
            $graph,
            $this->assignedId,
            $this->assignedId,
            null,
            $this->praticaUId,
            null,
            $this->macroId,
            $details,
            null,
            true,
            (int)$macro['dynamic'],
            null
        );
		
        $this->setDone();
		
		// $this->applogger->info($response);

			
		//gestione degli output del task
		// $taskId = $optional_data['task'];
		// $output_json = $this->db->fetchOne("SELECT output_type FROM tasks WHERE id =?", $taskId);
		// 
		// $output_array = json_decode($output_json,1);
		// 
		// $component_output = array();
		
		//di base la response prenderà il path del false
		// $response = "false";
		

		
		
		// $this->setDone();
		
		// if ($this->hasNext()){
		// 	$this->setOutput(json_encode($component_output));
		// 	$this->setResponse('done');
		// 	$this->setNextComponents();
		// 
		// 	if (!empty($response)){
		// 		//il task component segue due path di uscita, uno per il suo completamento e uno per il true false
		// 		//della risposta, di default false
		// 		$this->setResponse($response);
		// 		$this->setNextComponents();
		// 	}
		// }
		
	}
}

?>