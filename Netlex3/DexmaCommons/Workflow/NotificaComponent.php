<?php
//require_once "/home/<USER>/NetlexBot.php";

namespace  <PERSON>maCommons\Workflow;

use Netlex3\Software\patterns\Services\EmailService;

class NotificaComponent extends ComponentManager{
	
	const EMAIL_CHANNEL 	= 1;
	const NOTIFICA_CHANNEL	= 3;
	const TELEGRAM_CHANNEL 	= 2;
	
	protected $componentType = self::NOTIFICATION;
	protected $config;
	protected $startDate;
	protected $endDate;
	protected $destinatari = array();
	protected $utenti = array();
	protected $url_pratica_parziale;
	protected $url_pratica_completo;
	protected $destDir;
	protected $body;
    protected $emailService;
	
	function __construct($component_status_id,$subdomainName,$componentRow = NULL, $workflow = NULL, $Zend_Infrastructure){
		parent::__construct($component_status_id,$Zend_Infrastructure->getCustomerBucket(),$subdomainName,$Zend_Infrastructure->getLogger(),$componentRow, $workflow);
		$this->config = $Zend_Infrastructure->getConfig();
		$uniqueid_pratica = $this->db->fetchOne("SELECT uniqueid FROM archivio WHERE id = ?", $this->workflow->file_id);
		$this->url_pratica_parziale 	= "/archive/summary?uid=$uniqueid_pratica";
		$this->url_pratica_completo 	= "https://".$this->subdomainName.".netlex.cloud".$this->url_pratica_parziale;
        $this->emailService     = new EmailService($Zend_Infrastructure);
		
		if (!empty($this->data->userIdToNotify)){
			$utenti = $this->db->fetchCol("SELECT person_id FROM utente_pratica WHERE file_id = ? AND relazione = ?", array($this->workflow->file_id,$this->data->userIdToNotify));
			$this->utenti 				= $utenti;
			if (!empty($this->data->notificationChannelType) && ($this->data->notificationChannelType == self::EMAIL_CHANNEL || $this->data->notificationChannelType == self::TELEGRAM_CHANNEL)){
				foreach ($utenti as $utente){
					if ($this->data->notificationChannelType == self::EMAIL_CHANNEL){
						$email = $this->db->fetchOne("SELECT email FROM utente_email_account WHERE user_id = ? ORDER BY workflow DESC", $utente) ;
						if (!empty($email)){
							array_push($this->destinatari,$email);
						}
					}else{
						array_push($this->destinatari,$utente);
					}
				}
			}
		}
		
		$this->body 					= $this->data->messageBody;
		
		$this->destDir 					= $this->subdomainName . '/NotificationComponent/' . $this->istanza->uniqueid . '/';
	}

	


	function setNotification(){
		try{
			foreach ($this->utenti as $destinatario){
				$this->db->insert("notifiche", array("user_id"=>$destinatario, "file_id"=>$this->workflow->file_id, "text_message"=>$this->body, "url"=>$this->url_pratica_parziale, "creation_date" => $this->now(), "uniqueid" => $this->getUid()));
			}
		}catch(Exception $e){
			$this->applogger->info("*** Errore creazione notifica nella copia ***");
		}
	}

	function sendMail($to, $subject, $message) {
        $this->emailService->setMailValues("aws", $to, $subject, $message);
        $this->emailService->sendMail();
	}
	
	
	function execute(){
		$this->begin();
		if (!empty($this->data->notificationChannelType)){
			if ($this->data->notificationChannelType == self::EMAIL_CHANNEL){
				foreach ($this->destinatari as $to){
					$this->applogger->info("*** NOTIFICAZIONE EVENTO TRAMITE EMAIL $to ***");
					$this->sendMail( $to, 'Notifica Workflow '.$this->workflow->id, $this->body . " <br/>Riferimento alla pratica raggiungibile al seguente link: ". $this->url_pratica_completo);
				}
			}elseif ($this->data->notificationChannelType == self::NOTIFICA_CHANNEL){
				$this->body = 'Workflow '.$this->workflow->id.' '.$this->body;
				$this->applogger->info("##### NOTIFICAZIONE EVENTO TRAMITE NOTIFICA INTERNA");
				$this->setNotification();
			}elseif ($this->data->notificationChannelType == self::TELEGRAM_CHANNEL){
				//TELEGRAM AGGIUNGERE QUI
				$this->applogger->info("##### NOTIFICAZIONE EVENTO TRAMITE NOTIFICA TELEGRAM");
                //TODO: da implementare?
//				foreach ($this->destinatari as $to){
//					$body = 'Workflow '.$this->workflow->id.PHP_EOL.$this->body .PHP_EOL.'Riferimento pratica: '.$this->url_pratica_completo;
//					$bot = new NetlexBot();
//					$bot->initDB($this->subdomainName);
//					$bot->notificationUser($this->subdomainName, $to, $body );
//
//				}
			}
		}
		$this->setDone();
	}
	
	
}

?>