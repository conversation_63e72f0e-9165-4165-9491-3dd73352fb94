<?php

namespace  Dex<PERSON>Commons\Workflow;

class DeadlineListener extends <PERSON><PERSON><PERSON>ener
{
    protected $given_data;

    function __construct($file_id,$optional_data,$db,$subdomainName,$applogger,$given_data){
        parent::__construct($file_id, ComponentManager::EVENT_TRIGGER_EXPIRE_DEADLINE,$optional_data,$db,$subdomainName,$applogger);

        $this->given_data	= $given_data;
    }


    /**
     * @param $name
     * @param $arg
     *
     * @Overloading Trigger method in child class
     */
    function __call($name,$arg){

        /**
         * Launcher per impegni evasi di data tipologia
         */
        if ($name == 'trigger') {
            $components = $this->db->fetchAll("SELECT component_status_id FROM event_listeners WHERE file_id = ? AND component_type = ?", array($this->file_id, $this->action_type));

            foreach ($components as $position => $component) {
                if (!empty($components[$position])) {
                    $componentObj = new ComponentManager($component['id'],$this->db,$this->subdomainName,$this->applogger);
                    $componentObj->awakeEventTrigger($this->optional_data);
                    unset($componentOBJ);
                }
            }
        }

    }


}


?>