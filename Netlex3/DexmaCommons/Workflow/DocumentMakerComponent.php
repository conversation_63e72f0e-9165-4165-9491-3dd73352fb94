<?php

namespace  <PERSON><PERSON><PERSON>om<PERSON>\Workflow;

//require_once('tbs_class.php');
//require_once('tbs_plugin_opentbs.php');

use <PERSON>maCom<PERSON>\PrintsManager\PrintsHandler;

use Netlex3\Software\patterns\Classes\DocumentAbstract;
use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use Netlex3\Software\patterns\Repositories\ArchiveRepository;
use stdClass;


class DocumentMakerComponent extends ComponentManager{

	protected $componentType = self::DOCUMENT_MAKER;
	protected $s3Connector;
	protected $dbShared;
	protected $bucketName = "netlex-main-testing";
    private $infrastucture;

    function __construct($component_status_id,$db,$dbShared, $dbProvisioning,$subdomainName,$applogger, $generalConfig, $s3Connector,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		$this->s3Connector 			= $s3Connector;
		
		$this->dbShared 			= $dbShared;

        $this->infrastucture = new ZendInfrastructure($db, $dbProvisioning, $dbShared, $applogger, [], $subdomainName, $generalConfig);
    }


  function oldexecute(){
	  // $print = $this->db->fetchRow('SELECT * FROM stampe WHERE id = ?', $_REQUEST['docid']);
	  // 
	  // $folderPath = $subdomainName . '/' . self::$PRINTS. '/' .$print['id'] . '/' ;
	  // $filename = $print['filename'];

	  // $fileTemplate = $s3Connector->getBody($this->bucketName, $folderPath . $filename);
	  
	  $this->begin();
	  $fileName = "frontespizio_interno.docx";
	  $fileTemplate = $this->s3Connector->getBody($this->bucketName, $this->subdomainName.'/stampe/'. $fileName);
	  $tmpUid = $this->getUid();
	  $tmpDir = '/tmp' . '/' .  $this->subdomainName . '-' . $tmpUid;
	  if (!file_exists($tmpDir)) {
	   mkdir($tmpDir, 0777, true);
	  }
	  $filePath = $tmpDir . $fileName;

	  $handle = fopen($filePath, 'w');
	  fwrite($handle, $fileTemplate);
	  fclose($handle);
	  
	  $counter = 1;
	  $fileArray = explode('.', $fileName);
	  $ext = $fileArray[count($fileArray) - 1];
	  unset($fileArray[count($fileArray) - 1]);
	  $prefix = implode('.', $fileArray);
	  if (empty($prefix)) {
		  $prefix = "Documento";
		  $fileName = $prefix . "." . $ext;
	  }

	  while ($this->s3Connector->doesObjectExist($this->bucketName, $this->subdomainName.'/'.'documents'.'/'.$this->workflow->file_id.'/'.$fileName)) {
			$fileName = $prefix . '(' . $counter++ . ').' . $ext;
	  }
		
	  $this->s3Connector->putObject($this->bucketName, $this->subdomainName.'/'.'documents'.'/'.$this->workflow->file_id.'/'.$fileName, $filePath);
	  //include_once(DEXMA_COMMONS . '/PrintsManager/PrintsHandler.php');
	  $onlydate = explode(" " , now())[0];
	  $uid = $this->getUid();
	  $this->db->insert("documento", array('titolodocumento'=>$prefix, 'data' => $onlydate, 'immessoil' => $this->now(), 'modificatoil' => $this->now(), 'codicepratica' => $this->workflow->file_id, 'nomefile' => $fileName, 'uniqueid' => $uid ));
	  // 
	  // $PH = new stdClass();
	  // $PH->applogger = $applogger;
	  // $PH->db = $db;
	  // $PH->dbShared = $dbShared;
	
	  //DATA1
	  // if (!empty($data['documentType'])){
		//   $category_id = $data['documentType'];
	  // }
	
	
	  // $params = array(
		//   'aId' => $workflow['file_id'],
		//   'category' => $category_id,			
	  // )
	  // $archivioId = $params['aId'];
	  // $avvocato = $db->fetchRow("SELECT avvocato AS $subdomainName.avvocatoId FROM archivio WHERE id = ?", $archivioId);
	  // $params['soggetti'] = $db->fetchCol("SELECT an.id FROM $subdomainName.anagrafica_pratica ap INNER JOIN archivio a ON a.id = ap.file_id INNER JOIN anagrafiche an ON an.id = ap.person_id WHERE a.id = ?", $archivioId);
	  // 
	  // $printsHandler = new PrintsHandler($PH, array(
	  //  'labels' => array('pratica', 'avvocato', 'soggetti', 'generale'),
	  //  'request' => array_merge($params, $avvocato),
	  //  'filePath' => $filePath,
	  // ));
	  // 
	  //$printsHandler->print();
	  $output = json_encode(array(
		  'documento' => $this->db->fetchOne("SELECT id FROM documento WHERE uniqueid = ?", $uid),
	  ));
	  $this->setResponse('send_doc');
	  $this->setDone();
	  $this->setOutput($output);
	  if ($this->hasNext()){
		  $this->setNextComponents();
	  }
	
	  
  }
  
  function execute(){

	  $this->begin();

      try {

          $s3Connector = $this->s3Connector;

          if (!empty($this->data->documentType)) {
              //$print = $this->db->fetchRow('SELECT * FROM stampe WHERE category = ? AND default_print = 1', $this->data->documentType);
              $print = $this->db->fetchRow('SELECT * FROM stampe WHERE id = ? AND category = 3', $this->data->documentType);
          } else {
              //gestione errore
              $this->applogger->info("##### Errore: documentType non presente.");
          }

          $folderPath = $this->subdomainName . '/' . 'prints' . '/' . $print['id'] . '/';
          $filename = $print['filename'];
          $tmpUid = $this->getUid();
          $tmpDir = '/tmp' . '/' . $this->subdomainName . '-' . $tmpUid;
          $filePath = $tmpDir . '/' . $filename;

          $PH = new stdClass();
          $PH->applogger = $this->applogger;
          $PH->db = $this->db;
          $PH->dbShared = $this->dbShared;
          $PH->s3Connector = $s3Connector;

          $category = $this->data->documentType;

          $archivioId = $this->pratica;

          $params = array();
          $params['docid'] = $print['id'];
          $params['category'] = $category;
          $avvocato = $this->db->fetchRow('SELECT avvocato AS avvocatoId FROM archivio WHERE id = ?', $archivioId);
          $params['sectionid'] = $archivioId;
          $soggetti = $this->db->fetchCol('SELECT an.id FROM anagrafica_pratica ap INNER JOIN archivio a ON a.id = ap.file_id INNER JOIN anagrafiche an ON an.id = ap.person_id WHERE a.id = ?', $archivioId);
          $udienze = $this->db->fetchCol('SELECT ag.id FROM agenda ag INNER JOIN archivio a ON a.id = ag.pratica WHERE a.id = ?', $archivioId);
          $impegni = $this->db->fetchCol('SELECT s.id FROM scadenzario s WHERE s.pratica = ?', $archivioId);
          $documenti = $this->db->fetchCol('SELECT d.id FROM documento d WHERE d.codicepratica = ?', $archivioId);

          $labels = array('pratica', 'avvocato', 'generale');

          if (!empty($soggetti)) {
              $params['soggetti'] = $soggetti;
              $labels[] = 'soggetti';
          }
          if (!empty($udienze)) {
              $params['udienze'] = $udienze;
              $labels[] = 'udienze';
          }
          if (!empty($impegni)) {
              $params['impegni'] = $impegni;
              $labels[] = 'impegni';
          }
          if (!empty($documenti)) {
              $params['documenti'] = $documenti;
              $labels[] = 'documenti';
          }

          $datetime = implode("_", explode(" ", $this->now()));
          $datetime = implode("_", explode(":", $datetime));
          $filename = $print['filename'];
          $output = $tmpDir . '/' . $filename;

          if (!file_exists($tmpDir)) {
              mkdir($tmpDir, 0777, true);
          }

          $this->applogger->info("##### component creazione documento - labels:");
          $this->applogger->info(print_r($labels, 1));
          $this->applogger->info("##### component creazione documento - params:");
          $this->applogger->info(print_r($params, 1));

          $printsHandler = new PrintsHandler($PH, array(
              'labels' => $labels,
              'request' => array_merge($params, $avvocato),
              'filePath' => $filePath,
              'output' => $output,
              'sitekey' => $this->subdomainName,
          ));

          // download locally the template choosen

          $this->applogger->info("##### Get Template - labels:");

          $printsHandler->print();

            $onlydate = explode(" ", $this->now())[0];
            $archiveRepo = new ArchiveRepository($this->infrastucture);
            $document = DocumentAbstract::createHttpRequestBuilder(
                array(
                    "titolodocumento" => $filename,
                    "data" => $onlydate,
                    "immessoil" => $this->now(),
                    "modificatoil" => $this->now(),
                    "codicepratica" => $this->pratica,
                    "nomefile" => $filename,
                    "uploadSourceDir" => $this->subdomainName . '/' . 'documents' . '/' . $this->pratica . '/',
                    "file" => $output,
                    'documentale_data' => json_encode('')
                )
            )->build($archiveRepo->findArchiveById($this->pratica), $this->infrastucture);
            $document->setValidFilename($filename);
            $document->save();

//         exec('rm -rf ' . $tmpDir);

          $documento = $this->db->fetchOne("SELECT id FROM documento WHERE uniqueid = ?", $document->getUniqueid());

          $output = json_encode(array(
              'documento' => $documento,
          ));

          $this->setResponse('send_doc');
          $this->setDone();

          $this->setOutput($output);
      }catch (Exception $e){
          $this->applogger->info("##### component creazione documento - errore: $e");
          $this->setResponse('false');
          $this->setError();
      }

	  if ($this->hasNext()){
		  $this->setNextComponents();
	  }
  }

  
  // 
  // public function printAction(){
	//   $print = $this->db->fetchRow('SELECT * FROM stampe WHERE id = ?', $_REQUEST['docid']);
	//   $s3Connector = $this->s3Connector;
	//   $folderPath = SITEKEY . DIRECTORY_SEPARATOR . self::$PRINTS . DIRECTORY_SEPARATOR . $print['id'] . DIRECTORY_SEPARATOR;
	//   $filename = $print['filename'];
  // 
	//   $fileTemplate = $s3Connector->getBody(self::$MAIN_BUCKET, $folderPath . $filename);
	//   $tmpUid = $this->getUid();
	//   $tmpDir = '/tmp' . DIRECTORY_SEPARATOR . SITEKEY . '-' . $tmpUid;
	//   if (!file_exists($tmpDir)) {
	//    mkdir($tmpDir, 0777, true);
	//   }
	//   $filePath = $tmpDir . $filename;
  // 
	//   $handle = fopen($filePath, 'w');
	//   fwrite($handle, $fileTemplate);
	//   fclose($handle);
  // 
	// // $filePath = '/Users/<USER>/Downloads/frontespizio_interno.docx';
  // 
	//   include_once(DEXMA_COMMONS . '/PrintsManager/PrintsHandler.php');
  // 
	//   $PH = new stdClass();
	//   $PH->applogger = $this->applogger;
	//   $PH->db = $this->db;
	//   $PH->dbShared = $this->dbShared;
  // 
	//   $category = $this->dbShared->fetchOne('SELECT label FROM prints_categories WHERE id = ?', $_REQUEST['category']);
  // 
	//   $archivioId = $_REQUEST['sectionid'];
	//   $avvocato = $this->db->fetchRow('SELECT avvocato AS avvocatoId FROM archivio WHERE id = ?', $archivioId);
	//   $_REQUEST['soggetti'] = $this->db->fetchCol('SELECT an.id FROM anagrafica_pratica ap INNER JOIN archivio a ON a.id = ap.file_id INNER JOIN anagrafiche an ON an.id = ap.person_id WHERE a.id = ?', $archivioId);
	//   $_REQUEST['udienze'] = $this->db->fetchCol('SELECT ag.id FROM agenda ag INNER JOIN archivio a ON a.id = ag.pratica WHERE a.id = ?', $archivioId);
	//   $_REQUEST['impegni'] = $this->db->fetchCol('SELECT s.id FROM scadenzario s WHERE s.pratica = ?', $archivioId);
	//   $_REQUEST['documenti'] = $this->db->fetchCol('SELECT d.id FROM documento d WHERE d.codicepratica = ?', $archivioId);
  // 
	//   $printsHandler = new PrintsHandler($PH, array(
	//    'labels' => array('pratica', 'avvocato', 'soggetti', 'generale', 'udienze', 'impegni', 'documenti'),
	//    'request' => array_merge($_REQUEST, $avvocato),
	//    'filePath' => $filePath,
	//    'output' => 1,
	//   ));
  // 
	//   $this->applogger->info($printsHandler->print());
  // 
	//   exec('rm -rf ' .  $tmpDir);
 	// }
  //

}

?>