<?php

namespace  Dex<PERSON><PERSON>om<PERSON>\Workflow;

class EventTriggerChangeArchiveFieldsComponent extends ComponentManager {
    protected $componentType = self::EVENT_TRIGGER_CHANGE_ARCHIVE_FIELDS;
    protected $status_id;
    protected $now;

    function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
        parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
    }

    function execute(){

        $this->begin();

        $optionalData = $this->getOptionalDataArray();
        $data = $this->getDataArray();

        $fields = [
            "terminatoil" => $this->now(),
            "stato" => self::DONE,
        ];

        $archivioFieldChanged = false;
        if (isset($optionalData['archivioFields']) && isset($data['archivioField'])) {
            $archivioFieldChanged = in_array($data['archivioField'], $optionalData['archivioFields']);
        }

        if (! $archivioFieldChanged) {
            $fields = [
                "stato" => self::WAIT
            ];
        }

        $this->db->update(
            self::TABELLA_ISTANZE,
            $fields,
            ["id=?" => $this->istanza->id]
        );

        if ($archivioFieldChanged && $this->hasNext()){
            $this->setResponse('true');
            $this->setNextComponents();
        }

    }
}
