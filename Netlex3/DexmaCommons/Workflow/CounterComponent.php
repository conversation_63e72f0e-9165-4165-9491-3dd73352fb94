<?php


namespace  <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

class CounterComponent extends ComponentManager{
	protected $componentType = self::COUNTER;
	protected $counter;
	protected $giri;
	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		$optional_data = $this->getOptionalDataArray();
		
		if (!empty($optional_data)){  //contatore giri sulla stessa e unica istanza di un node counter
			$counter = $optional_data['counter']+1;
		}else{
			$counter = 1;
		}
		$this->counter 		= $counter;
		
		if (empty($this->data->giri)){
			$this->giri 	= 0;
		}else{
			$this->giri 	= $this->data->giri;
		}
	}
	
	



	function execute(){

		$this->begin();
			
		$this->db->update("component_status", array("optional_data"=>json_encode(array('counter'=>$this->counter)), "terminatoil"=>$this->now(), "stato"=>self::DONE), array("id=?" =>$this->istanza->id));

		if ($this->hasNext()){
			$this->setResponse(($this->counter >= $this->giri) ? 'true' : 'false');
			$this->setOutput($this->istanza->obtained);
			$this->setNextComponents();
		}

	}
}

?>