<?php

namespace  Dex<PERSON><PERSON>ommons\Workflow;

class AddSubjectComponent extends ComponentManager{
	protected $componentType = self::ADD_SUBJECTS;
	protected $params;
	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$config,$params,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		$this->config = $config;
		$this->params = $params;
	}
	
	function addCointestatario($user_id){
		$response = array(
			'response' => NULL,
			'commento' => '',
			'utente' => $user_id,
		);
		
		$file_id = $this->workflow->file_id;
		$commento = "";
		$intestatario = $this->db->fetchOne("SELECT avvocato FROM archivio WHERE id = ?", array($file_id));
		$utente = $this->db->fetchRow("SELECT codiceavvocato, nomeutente, tipoutente FROM utente WHERE id = ? ", $user_id);
		$is_already_cointestatario = $this->db->fetchOne("SELECT lawyer_id FROM cointestataripratica WHERE lawyer_id = ? AND file_id = ?", array($utente['codiceavvocato'], $file_id));
		$nomeutente = $utente['nomeutente'];
		if (empty($intestatario)){
			$commento .= "La pratica: $file_id non ha un intestatario";
			$response['commento'] = $commento;
			$response['response'] = FALSE;
			return $response;
		}elseif ($utente['tipoutente'] != 1){
			$commento .= "L'utente: '$nomeutente' non può è avvocato e non può essere cointestatario della pratica: $file_id";
			$response['commento'] = $commento;
			$response['response'] = FALSE;
			return $response;
		}elseif($intestatario == $utente['codiceavvocato']){
			$commento .= "L'utente: '$nomeutente' è già intestatario e non può essere cointestatario della pratica: $file_id";
			$response['commento'] = $commento;
			$response['response'] = FALSE;
			return $response;
		}elseif(!empty($is_already_cointestatario)){
			$commento .= "L'utente: '$nomeutente' è già cointestatario della pratica: $file_id";
			$response['commento'] = $commento;
			$response['response'] = FALSE;
			return $response;
		}else{
			$this->db->delete("utente_pratica", array("person_id=?"=>$user_id, "file_id=?"=>$file_id, "relazione"=>self::COINTESTATARIO)); //rimozione per sicurezza se vi fosse già l'utente nella nuova tabella utente_pratica ma non in quella dei cointestataripratica (in futuro si potrà togliere)
			$this->db->insert("cointestataripratica", array("file_id"=>$file_id, "lawyer_id"=>$utente['codiceavvocato']));
			$this->db->insert("utente_pratica", array("file_id"=>$file_id, "person_id"=>$user_id, "relazione"=>self::COINTESTATARIO));
			$commento .= "L'utente: '$nomeutente' è stato inserito tra i cointestatari della pratica: $file_id";
			$response['commento'] = $commento;
			$response['response'] = TRUE;
			return $response;
		}
		
		
	}
	
	
	
	

	function addResponsabile($user_id){
		$response = array(
			'response' => NULL,
			'commento' => '',
			'utente' => $user_id,
		);
		
		$file_id = $this->workflow->file_id;
		$commento = "";
		$actual_referent = $this->db->fetchOne("SELECT referent_id FROM archivio WHERE id = ?", array($file_id));
		$new_referent_name = $this->db->fetchOne("SELECT nomeutente FROM utente WHERE id = ?", $user_id);
		if (!empty($actual_referent)){
			$nomeutente_replaced = $this->db->fetchOne("SELECT nomeutente FROM utente WHERE id = ?", $actual_referent);
			if ($actual_referent == $user_id){
				$commento .= "L'utente: '$nomeutente_replaced' è già Responsabile della pratica. ";
				$response['response'] = FALSE;
				$response['commento'] = $commento;
				return $response;
			}
			$commento .= "L'utente: '$nomeutente_replaced' è stato rimosso dall'incarico di Responsabile ";
			$this->db->delete("utente_pratica", array("file_id=?"=>$file_id,"relazione"=>self::RESPONSABILE));
			$has_other_relation = $this->db->fetchOne("SELECT person_id FROM utente_pratica WHERE file_id = ? AND person_id = ? ", array($file_id,$actual_referent));
			
			if (empty($has_other_relation)){
				$this->db->insert("utente_pratica", array("person_id"=>$actual_referent,"file_id"=>$file_id,"relazione"=>self::UTENTE_INTERNO)); //Aggiunta relazione standard salva posizione del precedente responsabile
				$commento .= " ed é stato spostato come utente interno.";
			}else{
				$commento .= ". ";
			}
		}
		
		$this->db->update("archivio", array("referent_id"=>$user_id), array("id=?"=>$file_id));
		$this->db->insert("utente_pratica", array("person_id"=>$user_id,"file_id"=>$file_id,"relazione"=>self::RESPONSABILE));
		
		$commento .="L'utente: '$new_referent_name' è ora responsabile della pratica";
		$response['response'] = TRUE;
		$response['commento'] = $commento;
		
		return $response;
	}
	
	
	
	
	
	
	
	function addRelazione($user_id, $relazione){
		$response = array(
			'response' => NULL,
			'commento' => '',
			'utente' => $user_id,
		);
		
		$file_id = $this->workflow->file_id;
		$commento = "";
		$nomeutente = $this->db->fetchOne("SELECT nomeutente FROM utente WHERE id = ? ", $user_id);
		$relazione_nome = $this->db->fetchOne("SELECT nome FROM relazioni_utente WHERE id = ?", $relazione);
		$is_already_with_same_relation = $this->db->fetchOne("SELECT person_id FROM utente_pratica WHERE person_id = ? AND file_id = ? AND relazione = ?", array($user_id,$file_id,$relazione));
		
		if (!empty($is_already_with_same_relation)){
			$commento .= "L'utente: '$nomeutente' è già $relazione_nome della pratica: $file_id";
			$response['commento'] = $commento;
			$response['response'] = FALSE;
			return $response;
		}else{
			$this->db->insert("utente_pratica", array("person_id"=>$user_id, "file_id"=>$file_id, "relazione"=>$relazione));
			$commento .= "L'utente: '$nomeutente' è stato inserito come $relazione_nome della pratica: $file_id";
			$response['commento'] = $commento;
			$response['response'] = TRUE;
			return $response;
		}
		
	}



	
	
	function execute(){
		$this->begin();
			if (!empty($this->data->userId) && !empty($this->data->userRelation)){
				$user_id = $this->data->userId;
				$relazione = $this->data->userRelation;
				$response = array();
				if ($relazione == self::COINTESTATARIO){
					$response = $this->addCointestatario($user_id);
				}elseif ($relazione == self::RESPONSABILE){
					$response = $this->addResponsabile($user_id);
				}else{
					$response = $this->addRelazione($user_id, $relazione);
				}
				$riserva = empty($this->data->reserveFile) ? 0 : 1;
				
				if (!empty($this->data->reserveFile)){
					$response['commento'] .= $this->addRiserva($user_id);
				}
				
				$this->handleOneDrivePermissionsAction($user_id, $this->workflow->file_id, $riserva, 1,$this->params);
			}else{
				$response['response'] = FALSE;
				$response['commento'] = 'Non è stato selezionato nessun utente da poter essere aggiunto'.
				$response['utente'] = NULL;
			}
			
			$this->setDone();
			
			if ($response['response']){ //L'AGGIUNTA DI UN SOGGETTO UTENTE PUò TRIGGERARE GLI EVENT TRIGGER, VIENE DUNQUE TUTTO GESTITO DAL EVENTLISTENER
				$eventListener = new EventListener($this->pratica,self::EVENT_TRIGGER_ADDSUBJECT,json_encode(array('utente'=>$response['utente'])),$this->db,$this->subdomainName,$this->applogger);
				$eventListener->trigger();
			}
			
			if ($this->hasNext()){
				$this->setResponse($response['response'] == TRUE ? 'true' : 'false');
				unset($response['response']);
				$this->setOutput(json_encode($response));
				$this->setNextComponents();
				
			}			
	}


}

?>