<?php


namespace  Dex<PERSON><PERSON>ommons\Workflow;

class EventTriggerAddDocumentComponent extends ComponentManager{
	protected $componentType = self::EVENT_TRIGGER_ADD_DOCUMENT;
	protected $counter;
	protected $documentId;

	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		$optional_data = $this->getOptionalDataArray();
		
		if (!empty($optional_data['counter'])){  //contatore giri sulla stessa e unica istanza di un event trigger
			$counter = $optional_data['counter']+1;
		}else{
			$counter = 1;
		}
		$this->counter 		= $counter;
		
		$this->documentId 	= $optional_data['documento'];
		
	}
	
	



	function execute(){

		$this->begin();
			
		$this->db->update(self::TABELLA_ISTANZE, array("optional_data"=>json_encode(array('counter'=>$this->counter,'documento'=>$this->documentId)), "terminatoil"=>$this->now(), "stato"=>self::WAIT), array("id=?" =>$this->istanza->id));

		if ($this->hasNext()){
			$this->setResponse('true');
			$this->setOutput(json_encode(array('documento'=>$this->documentId)));
			$this->setNextComponents();
		}

	}
}

?>