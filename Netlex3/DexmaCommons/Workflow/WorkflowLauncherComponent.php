<?php


namespace  <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

class WorkflowLauncher<PERSON>omponent extends ComponentManager{

    protected $componentType = self::WORKFLOW_LAUNCHER;
    protected $workflow_id;
    protected $now;


    function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
        parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);

        $this->workflow_id  = $this->data->workflowId;
        $this->now          = $this->now();
    }


    function workflowLauncher(){
            $result = 'false';
        try{
            if (!empty($this->workflow_id)){
                $workflows_active = $this->db->fetchCol("SELECT workflow_id FROM workflow_pratica WHERE file_id = ? AND stato != 4", $this->workflow->file_id);
                //** blocco istanze multiple dello stesso modello */
                if (!in_array($this->workflow_id, $workflows_active)) {

                    $uid = $this->getUid();
                    $fieldList = array(
                        'workflow_id' => $this->workflow_id,
                        'file_id' => $this->workflow->file_id,
                        'stato' => 3,  //DOING
                        'immessoil' => $this->now,
                        'uniqueid' =>$uid,
                    );
                    $this->db->insert("workflow_pratica", $fieldList );
                    //GENERAZIONE COMPONENT
                    parent::createComponents($uid, NULL, NULL);
                    $result = 'true';
                }

            }
        }catch(Exception $e){
            $this->applogger->info("Component WorkflowLauncher: ". $e->getMessage());
        }

        return $result;
    }



    function execute(){
        $this->begin();

        $this->workflowLauncher();

        $this->setDone();

    }
}


?>