<?php


namespace  <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

class Step<PERSON>ait<PERSON>omponent extends ComponentManager{
	protected $componentType = self::STEPWAIT;
	protected $linkedChainName;
	protected $componentsArrived;
	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		$inputs = json_decode($this->istanza->input,1);
		$got_inputs = $inputs['obtained']['connections'];
		$ignored_inputs = $inputs['ignore']['connections'];

		$array_of_waiting_components_obtained = array();
		$array_of_waiting_components_ignored = array();
					
		foreach ($got_inputs as $input){
			$array_of_waiting_components_obtained[$input['node']]=$input['output'];
		}
		foreach ($ignored_inputs as $input){
			$array_of_waiting_components_ignored[$input['node']]=$input['output'];
		}

		array_merge($array_of_waiting_components_obtained,$array_of_waiting_components_ignored);
		
		$this->componentsArrived = $array_of_waiting_components_obtained;
		
		$this->linkedChainName = empty($this->data->linked_key) ? NULL : $this->data->linked_key;
	}

	
	
	
	function must_wait_a_little_bit_more(){
		foreach($this->componentsArrived as $nodeid => $noderesponse){
			$received_component = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_STEPWAIT_INPUTS." WHERE stepwait_id = ? AND ingresso_node_id = ? AND response = ?",array($this->istanza->id,$nodeid,$noderesponse));
			
			if (empty($received_component)){
				return TRUE;
			}
		}
		return FALSE;
	}
	

	function setTODOifAChainIsReady(){
		$linked_chains = $this->db->fetchOne("SELECT linked_chains FROM ".self::TABELLA_MODELLI." WHERE id=?", $this->workflow->workflow_id);
		
		$chain_rings = json_decode($linked_chains,1);
		$chain_rings = $chain_rings[$this->linkedChainName];
		$this->applogger->info(print_r($chain_rings,1));
		//aggiunto nella pila
		$this->db->insert("stepwait_linked_chain", array("stepwait_id" => $this->istanza->id, "stepwait_node_id"=>$this->istanza->node_id, "link_key"=>$this->linkedChainName,"stato"=>self::WAIT,"workflow_id"=>$this->workflow->id));
		//messo in pausa il component
		$this->db->update(self::TABELLA_ISTANZE, array("stato"=>self::WAIT), array("id=?"=>$this->istanza->id));
		//verifica che non ci sia una linked_chain annessa pronta
		$this->applogger->info("aggiunto alla pila della chain");
		
		$a_chain_is_rdy = TRUE;
		$chain_components_id = array();

		foreach ($chain_rings as $ring){
			$stepwait_ring = $this->db->fetchRow("SELECT stepwait_id,id FROM stepwait_linked_chain WHERE stepwait_node_id = ? AND workflow_id = ? ORDER BY id ASC", array($ring,$this->workflow->id));
		
			if (empty($stepwait_ring['stepwait_id'])){
				$a_chain_is_rdy = FALSE;
			}else{
				array_push($chain_components_id, $stepwait_ring);
			}
		}
		if ($a_chain_is_rdy){
			foreach ($chain_components_id as $chain_component_id){
				$this->applogger->info($chain_component_id['stepwait_id']);
				$this->db->update(self::TABELLA_ISTANZE, array("stato"=>self::TODO), array("id=?"=>$chain_component_id['stepwait_id']));
				$this->db->delete("stepwait_linked_chain", array("id=?"=>$chain_component_id['id']));
			}
		}	
	}



	function execute(){
		if (!$this->must_wait_a_little_bit_more() OR $this->istanza->stato==self::TODO){
			//PULITURA TABELLA DI PIVOT STEPWAIT INPUTS
			$this->db->delete(self::TABELLA_STEPWAIT_INPUTS, array("stepwait_id=?"=>$this->istanza->id));
			$this->applogger->info("PULITO STEPWAIT INPUT");

			if (!empty($this->linkedChainName) AND $this->istanza->stato!=self::TODO){
				
				$this->setTODOifAChainIsReady();
					
			}elseif(empty($this->linkedChainName) OR $this->istanza->stato==self::TODO){
				$this->setDone();
				
				if ($this->hasNext()){
					$this->setResponse('true');
					$this->setOutput($this->istanza->obtained);//si limita a ritrasferire il proprio output obtained
					$this->setNextComponents();
				}	
			}								
		}
	}





}

?>

