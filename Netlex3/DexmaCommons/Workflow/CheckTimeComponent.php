<?php


namespace  <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

class CheckTimeComponent extends ComponentManager{
	protected $componentType = self::CHECKTIME;
	protected $startDate;
	protected $inTimeDate;
	protected $endDate;
	
	function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		$optional_data = $this->getOptionalDataArray();
		$this->startDate = new DateTime($optional_data['start']);
		
		$inTimeDate = $this->startDate;
		
		if (!empty($this->data->timeDayQuantity) && is_numeric($this->data->timeDayQuantity)){
			$inTimeDate->add(new DateInterval('P'.$this->data->timeDayQuantity.'D'));
		}
		
		if (!empty($this->data->timeHourQuantity) && is_numeric($this->data->timeHourQuantity)){
			$inTimeDate->add(new DateInterval('PT'.$this->data->timeHourQuantity.'H'));
		}
		
		if (!empty($this->data->timeMinQuantity) && is_numeric($this->data->timeMinQuantity)){
			$inTimeDate->add(new DateInterval('PT'.$this->data->timeMinQuantity.'M'));
		}
		
		$this->inTimeDate = $inTimeDate;
		
		$this->endDate = empty($optional_data['end']) ? NULL : new DateTime($optional_data['end']);
	}
	
					
					
	function execute(){
		if (!empty($this->endDate)){ //Caso in cui è giunto il component della fine del checktime, bisogna comunque verificare che sia in tempo
			if ($this->endDate->format('Y-m-d\TH:i:s') > $this->inTimeDate->format('Y-m-d\TH:i:s')){
				$response = 'overtime';
			}else{
				$response = 'intime';
			}
		}else{ //caso in cui non è ancora giunto il component della fine ma bisogna verificare se stiamo in overtime
			$actualDate = new DateTime($this->now());
			if ($actualDate->format('Y-m-d\TH:i:s') > $this->inTimeDate->format('Y-m-d\TH:i:s')){
				$response = 'overtime';
			}
		}
		
		if(!empty($response)){
			$this->setDone();
			if ($this->hasNext()){
				$this->setResponse($response);
				$this->setNextComponents();
			}
		}

	}
}

?>