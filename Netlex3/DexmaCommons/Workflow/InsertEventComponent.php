<?php


namespace  DexmaCommons\Workflow;

use Exception;

class InsertEventComponent extends ComponentManager{
    const FROM_BEGINNING = 1;
    const FROM_THIS_BLOCK = 2;

    protected $componentType = self::INSERT_EVENT;
    protected $oggetto;
    protected $avvisaprima;
    protected $tipologiaImpegno;
    protected $deadlineEvasa;
    protected $deadlineNonevadere;
    protected $deadlineImportant;
    protected $deadlineVisible;
    protected $deadlineId;
    protected $relazioneIntestatario;

    protected $waitType;
    protected $days;
    protected $time;
    protected $last;

    protected $now;



    function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
        parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);

        $this->oggetto                  = $this->data->oggetto;
        $this->avvisaprima              = $this->data->avvisaprima;
        $this->tipologiaImpegno         = $this->data->tipologiaImpegno;
        $this->relazioneIntestatario    = $this->data->relazioneIntestatario;

        $this->deadlineEvasa            = property_exists($this->data, 'deadlineEvasa') && $this->data->deadlineEvasa == 'true';
        $this->deadlineNonevadere       = property_exists($this->data, 'deadlineNonevadere') && $this->data->deadlineNonevadere == 'true';
        $this->deadlineImportant        = property_exists($this->data, 'deadlineImportant') && $this->data->deadlineImportant == 'true';
        $this->deadlineVisible          = property_exists($this->data, 'deadlineVisible') && $this->data->deadlineVisible == 'true';
        $this->deadlineId               = property_exists($this->data, 'deadlineId') && $this->data->deadlineId == 'true';

        $this->waitType                 = $this->data->timeblockType;
        $this->days                     = $this->data->timeDayQuantity;
        $this->time                     = $this->data->timeHour;
        $this->last                     = $this->data->durata;

        $this->now                      = $this->now();
    }

    /**
     * @return array
     */
    function eventCreate(): array
    {
        $response = array(
            'response'  => FALSE,
            'commento'  => '',
            'impegno'   => ''
        );

        try{
            if (!empty($this->oggetto) && !empty($this->relazioneIntestatario) ){

                if ($this->relazioneIntestatario == "-1") {  /* Gestione automatica intestatari da component precedenti */
                    $objects = $this->getObtainedArray();

                    if (!empty($objects['utente'])) {
                        $intestatari = $objects['utente'];
                        $intestatari = is_array($intestatari) ? $intestatari : array($intestatari);
                    }else{
                        throw new Exception('Intestatari non ottenuti da componenti precedenti per la creazione impegno');
                    }
                }else{
                    $intestatari = $this->db->fetchCol("SELECT u.id FROM utente u INNER JOIN utente_pratica up ON up.person_id = u.id WHERE up.file_id = ? AND up.relazione = ?", array($this->workflow->file_id, $this->relazioneIntestatario));
                }


                $this->applogger->info(print_r($intestatari,1));

                if (empty($intestatari)){
                    throw new Exception('Intestatari non trovati per la creazione impegno');
                }


                if (!empty($this->data->timeblockType) && $this->data->timeblockType == self::FROM_BEGINNING){
                    $StartDate = $this->db->fetchOne("SELECT immessoil FROM ".self::TABELLA_ISTANZE_WORKFLOW." WHERE id = ? ", $this->workflow->id);
                }else{
                    $StartDate = $this->db->fetchOne("SELECT immessoil FROM ".self::TABELLA_ISTANZE." WHERE id = ? ", ($this->istanza->id));
                }

                $days = empty($this->days) ? 0 : $this->days;
                $date = date('Y-m-d 00:00:00', strtotime("$StartDate + $days days"));

                $avvisa = empty($this->avvisaprima) ? 0 : $this->avvisaprima;
                $dataavviso = date('Y-m-d 00:00:00', strtotime("$date - $avvisa days"));


                if($this->deadlineId) {
                    $praticaId = $this->workflow->file_id;
                    $oggetto = 'Pratica N.' . $praticaId . ' - ' . $this->oggetto;
                }

                    $impegnoUid = $this->getUid();
                    $impegno = array(
                        'testo'         => isset($oggetto)?$oggetto:$this->oggetto,
                        'data' 	        => $date,
                        'dataavviso'    => $dataavviso,
                        'durata'        => empty($this->last)? 0 : $this->last,
                        'tiposcadenza'  => empty($this->tipologiaImpegno)? 1 : $this->tipologiaImpegno ,
                        'evasa'         => empty($this->deadlineEvasa) ? 0 : 1,
                        'important'     => empty($this->deadlineImportant) ? 0 : 1,
                        'visible'       => empty($this->deadlineVisible) ? 0 : 1,
                        'nonevadere'    => empty($this->deadlineNonevadere) ? 0 : 1,
                        'ora'           => empty($this->time)? '09:00' : $this->time,
                        'immessoil'     => $this->now,
                        'modificatoil'  => $this->now,
                        'immessoda'     => -1,
                        'modificatoda'  => -1,
                        'annotazioni'   => 'Impegno generato automaticamente tramite Workflow ' . $this->workflow->id ,
                        'pratica'       => $this->workflow->file_id,
                        'linkuid'       => $this->getUid(),
                        'uniqueid'      => $impegnoUid
                    );

                    $this->db->insert('scadenzario', $impegno);

                    $impegnoId = $this->db->fetchOne("SELECT id FROM scadenzario WHERE uniqueid = ?", $impegnoUid);

                    foreach ($intestatari as $utente){
                        $this->db->insert('scadenzarioutente', array('id_utente' => $utente, 'id_scadenza'=> $impegnoId));
                    }

                    $response['response']       = TRUE;
                    $response['commento']       = "L'impegno è stato inserito correttamente";
                    $response['scadenzario']    = $impegnoId;


                    // ** todo: nella 3.8 uscirà associazione Impegno Documento, associare documenti in arrivo all'impegno * /

            }else{
                throw new Exception('Argomenti oggetto o relazione per creazione Impegno Mancanti');
            }
        }catch(Exception $e){
            $this->applogger->info("Insert Event Component: ". $e->getMessage());

            $response['response']       = FALSE;
            $response['commento']       = $e;
            $response['scadenzario']    = NULL;

        }

        return $response;
    }



    function execute(){
        $this->begin();

        $response = $this->eventCreate();

        $this->setDone();

        if ($this->hasNext()){
            $this->setResponse($response['response'] == TRUE ? 'true' : 'false');

            if(!$response['response']){
                $this->setError();
            }

            unset($response['response']);
            $this->setOutput(json_encode($response));
            $this->setNextComponents();

        }

    }
}


