<?php 

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

//COMPONENTS

use Carbon\Carbon;
use DexmaCom<PERSON>\OneDriveManager\OneDriveHandler;

class ComponentManager extends WorkflowManager
{
    const BEGINNING = 0;
    const ATTESA = 1;
    const NOTIFICATION = 2;
    const STEPWAIT = 3;
    const EMAILSENDER = 5;
    const DOCUMENT_MAKER = 6;
    const ADD_SUBJECTS = 7;
    const ADD_GROUP = 8;
    const COUNTER = 9;
    const CHECKTIME = 10;
    const TASK = 11;
    const MACRO_STARTER = 12;
    const WORKFLOW_LAUNCHER = 13;
    const STATUS_UPDATER = 14;
    const INSERT_EVENT = 15;
    const CREATE_MODEL = 16;
    const CREATE_WARNING = 17;
    const CHANGE_STATUS = 18;
    const CALC_INTERESTS = 19;
    const EVENT_TRIGGER_ADDSUBJECT = 100;
    const EVENT_TRIGGER_ADD_DOCUMENT = 101;
    const EVENT_TRIGGER_EXPIRE_DEADLINE = 102;
    const EVENT_TRIGGER_CHANGE_ARCHIVE_FIELDS = 103;
    const END = 1000;


//PARTS
    const RESPONSABILE = 2;
    const COINTESTATARIO = 1;
    const COLLABORATORE = 3;
    const COINTESTATARIO_PRATICANTE = 4;
    const UTENTE_INTERNO = 30;

    const CLIENTE = 1;
    const CONTROPARTE = 2;
    const AVVERSARIO = 3;
    const RUBRICA = 4;

    const AWAKE = 'TODO';
    const AWAKE_LISTENER = 'OVERTIME'; /** TODO: da modificare ENUM su DB */
    const SET_DONE = 'DONE';
    const SET_SLEEP = 'SLEEP';
    const BEGIN = 'DOING';
    const SET_ERROR = 'SYSTEM_ERROR';

    public static $MAIN_BUCKET = "netlex-main";
    public static $RECYCLE_BIN_BUCKET = 'netlex-recycle-bin';
    public static $DOCUMENTS = "documents";
    protected static $TEMPLATE_DIR = "templates";
    public static $LETTERHEADS = 'letterheads';

	protected $s3Connector;
	protected $config;
	
	protected $istanza; //La riga dell'istanza sul db
	
	protected $data; // Serve per le classi figlie, trasforma i parametri di input sul component di retejs in object
	
	protected $pratica; // Si memorizza per quale pratica sta lavorando
	protected $praticaUId; // Si memorizza per quale pratica sta lavorando

	protected $response; //La risposta che viene settata al completamento del component
	
	protected $output; //Se presente rappresenta l'output da passare al prossimo component durante la fase di setNextComponents
	
	
	
	//COSTRUTTORE DELLA CLASSE, PUò ESSERE INIZIALIZZATO PASSANDOGLI GIà LA RIGA DELL'ISTANZA ALTRIMENTI SE LA RECUPERA DA SOLO

    /**
     * ComponentManager constructor.
     * @param $component_status_id
     * @param $db
     * @param $subdomainName
     * @param $applogger
     * @param null $componentRow
     * @param null $workflow
     */
	public function __construct($component_status_id, $db, $subdomainName, $applogger, $componentRow = NULL, $workflow = NULL){

		parent::__construct($db,$subdomainName,$applogger);

		if (empty($componentRow)){
			$this->istanza 			= (object) $this->db->fetchRow("SELECT * FROM ".self::TABELLA_ISTANZE." WHERE id = ?", array($component_status_id));
		}else{
			$this->istanza 			= (object) $componentRow;
		}
		
		if (!empty($workflow)){
			$this->workflow			= (object) $workflow;
		}else{
			$this->workflow			= (object) $this->db->fetchRow("SELECT * FROM ".self::TABELLA_ISTANZE_WORKFLOW." WHERE id = ?", $this->istanza->workflow_id);
		}

		$this->data 				= (object) $this->getDataArray();

		$this->pratica				= $this->workflow->file_id;
        $this->praticaUId           = $this
                                      ->db
                                      ->fetchRow("SELECT uniqueid FROM archivio WHERE id = ?", $this->workflow->file_id)['uniqueid'];

	}
	

	
	//GENERA GLI UNIQUEID

    /**
     * @return string
     */
	function getUid(): string{
		return strtoupper(base_convert(microtime(true), 10, 16) . "-" . uniqid(rand()));
	}
	
	// SETTA IL COMPONENT SU DOING PRONTO PER ESSERE SEGUITO, SOLO SE ERA IN WAIT, E NE RESETTA LE VARIABILI TEMPORALI INIZIATOIL E TERMINATO IL, 
	// SI PUò INOLTRE PASSARE OPZIONALE UN JSON DI DATI CHIAVE VALORE

    /**
     * @param null $optional_data
     * @return bool
     */
	public function awake($optional_data = NULL): bool
    {
		try{
			if (!empty($this->istanza->stato) && $this->istanza->stato == WAIT){
				$fieldList = array(
					'stato'      	=> self::DOING,
					'iniziatoil' 	=> NULL,
					'terminatoil' 	=> NULL,
				);
				if (!empty($optional_data)){
					$fieldList['optional_data'] = $optional_data;
				}
				$this->db->update(self::TABELLA_ISTANZE, $fieldList, array('id=?'=>$this->istanza->id));

				/** LOG */
				$this->storeLog(self::AWAKE);

				return TRUE;
			}
            return FALSE;
        }catch(Exception $err){
			return FALSE;
		}
	}
	
	//L'AWAKE DELL'EVENT TRIGGER SI ASPETTA SEMPRE UN OPTIONAL DATA RISULTATO DI CIò CHE ASPETTA, NEL FORMATO JSON TABELLA-ID
	// INOLTRE DI BASE ESSENDO L'EVENT TRIGGER UNIVOCO NEL FLUSSO GLI SI DA UN COUNTER DI DEFAULT CHE SEGNERà IL N. DI ESECUZIONI

    /**
     * @param $optional_data
     * @return bool
     */
	function awakeEventTrigger($optional_data): bool
    {
		try{
            if ((int) $this->istanza->component_id === ComponentManager::EVENT_TRIGGER_CHANGE_ARCHIVE_FIELDS && in_array($this->istanza->stato, [self::WAIT, self::TODO, self::DOING])) {
                $newOptionalData = json_decode($optional_data,1);
                $newArchivioFields = [];
                if ($newOptionalData && isset($newOptionalData['archivioFields'])) {
                    $newArchivioFields = $newOptionalData['archivioFields'];
                }

                $oldOptionalData = $this->getOptionalDataArray();
                $oldArchivioFields = [];
                if ($oldOptionalData && isset($oldOptionalData['archivioFields'])) {
                    $oldArchivioFields = $oldOptionalData['archivioFields'];
                }
                $newArchivioFields = array_unique(array_merge($oldArchivioFields, $newArchivioFields));

                $newOptionalData['archivioFields'] = $newArchivioFields;
                $optional_data = json_encode($newOptionalData);

                $fieldList = ['optional_data' => json_encode($newOptionalData)];
                $this->db->update(self::TABELLA_ISTANZE, $fieldList, ['id=?' => $this->istanza->id]);
            }

			if (!empty($this->istanza->stato) && $this->istanza->stato == self::WAIT && !empty($optional_data)){
				$optional_data_new = json_decode($optional_data,1);
				$optional_data_old = $this->getOptionalDataArray();
				if (!empty($optional_data_old['counter'])){
					$optional_data_new['counter'] = $optional_data_old['counter'];
				}
				
				$fieldList = array(
					'stato'      	=> self::TODO,
					'iniziatoil' 	=> NULL,
					'terminatoil' 	=> NULL,
					'optional_data'	=> json_encode($optional_data_new),
				);
				$this->db->update(self::TABELLA_ISTANZE, $fieldList, array('id=?'=>$this->istanza->id));

                /** LOG */
                $this->storeLog(self::AWAKE_LISTENER);

				return TRUE;
			}

            return FALSE;
        }catch(Exception $err){
			return FALSE;
		}
	}
	
	// SETTA IL COMPONENT COME COMPLETATO SE su DOING, NE SETTA TERMINATOIL E IN CASO ANCHE INIZIATOIL SE FOSSE VUOTO,
	// POSSIBILE PASSARGLI UN JSON DI DATI DA SETTARGLI OPZIONALE

    /**
     * @param null $optional_data
     * @return bool
     */
	function setDone($optional_data = NULL): bool
    {
		try{
			if (!empty($this->istanza->stato) && ($this->istanza->stato == self::DOING || $this->istanza->stato == self::TODO) ){
				$now = $this->now();
				$fieldList = array(
					'stato' 		=> self::DONE,
					'terminatoil' 	=> $now,
				);
				if (!empty($optional_data)){
					$fieldList['optional_data'] = $optional_data;
				}
				if (empty($this->istanza->iniziatoil)){
					$fieldList['iniziatoil'] = $now;
				}
				$this->db->update(self::TABELLA_ISTANZE, $fieldList, array('id=?'=>$this->istanza->id));

                /** LOG */
                $this->storeLog(self::SET_DONE);


				return TRUE;
			}

            return FALSE;
        }catch(Exception $err){
			return FALSE;
		}
	}

    // SETTA IL COMPONENT COME SYSTEM ERROR, NE SETTA TERMINATOIL E IN CASO ANCHE INIZIATOIL SE FOSSE VUOTO,

    /**
     * @return bool
     */
    function setError(): bool
    {
        try{
            $now = $this->now();
            $fieldList = array(
                'stato' 		=> self::SYSTEM_ERROR,
                'terminatoil' 	=> $now,
            );

            if (empty($this->istanza->iniziatoil)){
                $fieldList['iniziatoil'] = $now;
            }

            $this->db->update(self::TABELLA_ISTANZE, $fieldList, array('id=?'=>$this->istanza->id));

            /** LOG */
            $this->storeLog(self::SET_ERROR);

            return TRUE;
        }catch(Exception $err){
            return FALSE;
        }
    }
	
	//ADDORMENTA L'ISTANZA PONENDOLA IN WAIT

    /**
     * @param null $optional_data
     * @return bool
     */
	function sleep($optional_data = NULL): bool
    {
		try{
			if (!empty($this->istanza->stato) && $this->istanza->stato == self::DOING){
				$fieldList = array(
					'stato'      	=> self::WAIT
				);
				if (!empty($optional_data)){
					$fieldList['optional_data'] = $optional_data;
				}
				$this->db->update(self::TABELLA_ISTANZE, $fieldList, array('id=?'=>$this->istanza->id));

                /** LOG */
                $this->storeLog(self::SET_SLEEP);


				return TRUE;
			}

            return FALSE;
        }catch(Exception $err){
			return FALSE;
		}
	}
	
	
	
	//PRENDE UN JSON CHIAVE VALORE O NULL E AGGIORNA IL CAMPO DELL'ISTANZA DEL COMPONENT

    /**
     * @param $optional_data
     * @return bool
     */
	function setOptionalData($optional_data): bool
    {
		try{
			$fieldList = array(
				'optional_data' => $optional_data,
			);
			$this->db->update(self::TABELLA_ISTANZE, $fieldList, array('id=?'=>$this->istanza->id));
			return TRUE;
		}catch(Exception $err){
			return FALSE;
		}
	}
	
	
	//SETTA IL VALORE DI INIZIATO BLOCCANDO LA POSSIBILITA' CHE VENGA FETCHIATO DA SCRIPT PARALLELI

	function begin(){
		$fieldList = array(
			'iniziatoil' => $this->now(),
		);
		$this->db->update(self::TABELLA_ISTANZE, $fieldList, array('id=?'=>$this->istanza->id));

		/** LOG */
        $this->storeLog(self::BEGIN);
	}
	
	//RITORNA L'ARRAY DI VARIABILI=>VALORE SETTATE SU RETE JS IN FASE DI CREAZIONE DEL MODELLO

    /**
     * @return mixed|null
     */
	function getDataArray(){
		if (!empty($this->istanza->data)){
			return json_decode($this->istanza->data,1);
		}else{
			return NULL;
		}		
	}
	
	//RITORNA L'ARRAY DI DATI CHIAVE:VALORE TRASFERITI DA UN COMPONENT AL SUCCESSIVO

    /**
     * @return mixed|null
     */
	function getObtainedArray(){
		if (!empty($this->istanza->obtained)){
			return json_decode($this->istanza->obtained,1);
		}else{
			return NULL;
		}	
	}
	
	//RITORNA L'ARRAY DEI DATI OPZIONALI (usati differentemente da ogni component, usato anch per comunicazioni SINCRONE / ASINCRONE per il passaggio dei dati)

    /**
     * @return mixed|null
     */
	function getOptionalDataArray(){
		if (!empty($this->istanza->optional_data)){
			return json_decode($this->istanza->optional_data,1);
		}else{
			return NULL;
		}	
	}
	
	// SETTA L'INSIEME DI CHIAVI:VALORE DA DARE AI COMPONENT FIGLI IN FASE DI CREAZIONE

    /**
     * @param $output
     */
	function setOutput($output){
		$this->output = $output;
	}
	
	//CONTROLLA SE HA BLOCCHI SUCCESSIVI CHE POSSONO ESSERE ESEGUITI

    /**
     * @return bool
     */
	function hasNext(): bool
    {
		if (!empty($this->istanza->next)){
			return TRUE;
		}else{
			return FALSE;
		}	
	}	

	//SETTA SE PRESENTE LA RISPOSTA DEL COMPONENT UNA VOLTA TERMINATO, RESPONSE è UNA STRINGA COINCIDENTE CON UNA CHIAVE PRESENTE NEL PROPRIO CAMPO NEXT

    /**
     * @param $response
     */
	function setResponse($response){
		$this->response = $response;
	}
	
	//SETTA I PROSSIMI COMPONENTI IN BASE AL RESPONSE E DOVREBBE ESSERE CHIAMATO SOLO DOPO HASNEXT

	function setNextComponents(){
		
		$flowJSON = $this->db->fetchOne("SELECT json FROM ".self::TABELLA_MODELLI." WHERE id = ?", $this->workflow->workflow_id);
		$flowArray = json_decode($flowJSON,1);
		
		if (empty($this->output)){
			$output = NULL;
		}else{
			$output = $this->output;
		}


		$nodi = $flowArray['nodes'];
		
		$next = json_decode($this->istanza->next,1);
		
		foreach ($nodi as $node_id => $node_content){
			
			$id = $node_content['id'];
			$skip_initialization = FALSE;
			
			
            if (in_array($id, $next[$this->response])){
				if($node_content['name'] == 'Attendi Ingressi'){
					$stepwait_Already_started = $this->db->fetchCol("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE node_id = ? AND stato = 3",$id);
					//VERIFICARE SE LO STEPWAIT IN ESECUZIONE HA GIA' RICEVUTO UN COLLEGAMENTO DAL NODO ATTUALE, ignore o obtained non fa differenza,  E DUNQUE è UNA SECONDA ATTIVAZIONE
					$actual_node_id = $this->istanza->node_id;
					foreach ($stepwait_Already_started as $id_stepwait){
						$already_associated = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_STEPWAIT_INPUTS." WHERE stepwait_id = ? AND ingresso_node_id = ? AND response = ?", array($id_stepwait, $actual_node_id, $this->response) );
						if (!$already_associated){
							$nodi_di_entrata = $node_content['inputs']['obtained']['connections'];
							
							$store_input = 0;
							foreach ($nodi_di_entrata as $nodo_di_entrata){ 
								if ($nodo_di_entrata['output'] == $this->response && $nodo_di_entrata['node'] == $actual_node_id){
									$store_input = 1;
								}
							}
							
							$stepwait_list = array(
								'stepwait_id' => $id_stepwait,
								'stepwait_node_id' => $id,
								'ingresso_id' => $this->istanza->id,
								'ingresso_node_id' => $actual_node_id,
								'obtained' => $store_input,
								'response' => $this->response,
							);
							$this->db->insert(self::TABELLA_STEPWAIT_INPUTS, $stepwait_list ); //AGGIUNTA ASSOCIAZIONE NODO E StepWait
							
							
							$ongoing_stepwait = $this->db->fetchRow("SELECT previous, obtained FROM ".self::TABELLA_ISTANZE." WHERE id = ?", $id_stepwait);

							//aggiunta se preso dell'output nello StepWait
							if ($store_input && !empty($output)){
								$already_obtained_data = $ongoing_stepwait['obtained'];
								if (!empty($already_obtained_data)){
									$already_obtained_data = json_decode($already_obtained_data,1);
								}
								
								$objects = json_decode($output,1);
//								$this->applogger->info("LOGGATA-> objects:".print_r($objects,1)."already_obtained:".print_r($already_obtained_data,1));
								array_merge($already_obtained_data, $objects);
								if (!empty($already_obtained_data)){
									foreach ($objects as $new_key => $new_value){
										$added = FALSE;
										foreach ($already_obtained_data as $key => $values){
											if ($key == $new_key){
												if (is_array($values)){
													array_push($values,$new_value);
												}else{
													$values = array($values, $new_value);
												}
												$already_obtained_data[$key] = $values;
												$added = TRUE;
											}
										}
										if (!$added){
											$already_obtained_data[$new_key] = $new_value;
										}	
									}
								}
								 
//								$this->applogger->info("MERGE:".print_r($already_obtained_data,1));
								$result_data_json =  json_encode($already_obtained_data);
								$this->db->update(self::TABELLA_ISTANZE, array('obtained'=> $result_data_json ), array('id=?'=>$id_stepwait));
							}
							
							//aggiunta nell'array dei component precedenti del component attuale che giunge allo stepwait
							$complex_previous = json_decode($ongoing_stepwait['previous'],1);
							array_push($complex_previous, $this->istanza->id);
							$this->db->update(self::TABELLA_ISTANZE, array('previous'=> json_encode($complex_previous) ), array('id=?'=>$id_stepwait));

							$skip_initialization = TRUE;
							break;
						}
					}
				}elseif($node_content['name'] == 'Verifica Overtime'){
					$actual_node_id = $this->db->fetchOne("SELECT node_id FROM ".self::TABELLA_ISTANZE." WHERE id = ?", $this->istanza->id);
					$is_checktime_running = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE node_id = ? AND stato = ?", array($node_id, DOING));
					if ($node_content['inputs']['begin']['connections']['0']['node'] == $actual_node_id && !empty($is_checktime_running)){
						$skip_initialization = TRUE;
					}elseif ($node_content['inputs']['begin']['connections']['0']['node'] == $actual_node_id && empty($is_checktime_running)){
						$optional_data = array("start"=>$this->now());
						$optional_data = json_encode($optional_data);
					}elseif ($node_content['inputs']['end']['connections']['0']['node'] == $actual_node_id && !empty($is_checktime_running)){
						$comp_row = $this->db->fetchRow("SELECT previous, optional_data FROM ".self::TABELLA_ISTANZE." WHERE id = ?", $is_checktime_running);
						//come optional data aggiungiamo allo start anche il valore end ottenuto in questo istante
						$optional_data = $comp_row['optional_data'];
						$optional_data = json_decode($optional_data,1);
						$optional_data['end'] = $this->now();
						$optional_data = json_encode($optional_data);
						//come precedenti id del component inseriamo sia lo start che l'end
						$start_end_previous = $comp_row['previous'];
						$start_end_previous = json_decode($start_end_previous,1);
						array_push($start_end_previous, $this->istanza->id);
						$start_end_previous = json_encode($start_end_previous);
						
						$this->db->update("component_status",array("previous"=>$start_end_previous,"optional_data"=>$optional_data),array("id=?"=>$is_checktime_running));
						$skip_initialization = TRUE;
					}elseif ($node_content['inputs']['end']['connections']['0']['node'] == $actual_node_id && empty($is_checktime_running)){
						$skip_initialization = TRUE; // E' arrivato un end prima che fosse inizializzato il nodo o già in overtime -> per adesso si ignora come caso semplice
					}
				}
				if (!$skip_initialization){
					$data = $node_content['data'];
					$inputs = $node_content['inputs'];
					$outputs = $node_content['outputs'];
					$previous = array($this->istanza->id);
					$in_the_future = array();
					
					foreach ($outputs as $output_key => $output_content){
						$connections = $output_content['connections'];	
						$way = array();
						foreach ($connections as $connection){
							array_push($way, $connection['node']);
						}
						$in_the_future[$output_key] = $way;
					}
			
					if ($node_content['name'] == 'Attesa'){
						$component_id = self::ATTESA;
						$obtained = $output;
					}elseif($node_content['name'] == 'Invio Email'){
						$component_id = self::EMAILSENDER;
						$obtained = $output;
					}elseif($node_content['name'] == 'Notifica'){
						$component_id = self::NOTIFICATION;
						$obtained = $output;
					}elseif($node_content['name'] == 'Genera Documento'){
						$component_id = self::DOCUMENT_MAKER;
						$obtained = $output;
					}elseif($node_content['name'] == 'Attendi Ingressi'){
						$component_id = self::STEPWAIT;
						$store_input = 0;
						$nodi_di_entrata = $inputs['obtained']['connections'];
						foreach ($nodi_di_entrata as $nodo_di_entrata){ 
							if ($nodo_di_entrata['output'] == $this->response && $nodo_di_entrata['node'] == $actual_node_id){
								$store_input = 1;
							}
						}
						if (!$store_input){
							$obtained = NULL;
						}else{
							$obtained = $output;
						}
						
					}elseif($node_content['name'] == 'Termina Workflow'){
						$component_id =self::END;
						$obtained = $output;
//                    }elseif($node_content['name'] == 'Inizio Workflow'){
//                        $component_id = BEGINNING;
//                        $obtained = NULL;
					}elseif($node_content['name'] == 'Aggiungi Utente'){
						$component_id = self::ADD_SUBJECTS;
						$obtained = $output;
					}elseif($node_content['name'] == 'Riserva Gruppo'){
						$component_id = self::ADD_GROUP;
						$obtained = $output;
					}elseif($node_content['name'] == 'Contatore'){
						$component_id = self::COUNTER;
						$obtained = $output;
						
						//VERIFICA CHE IL CONTATORE SIA GIà ISTANZIATO
						$id_counter_istance = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE node_id = ? AND workflow_id = ?", array($id,$this->workflow->id));

						if (!empty($id_counter_istance)){
							$updateList = array(
								'stato' => self::DOING,
								'previous' => json_encode($previous),
								'workflow_id' => $this->workflow->id,
								'obtained' => $obtained,
								'iniziatoil' => NULL,
								'terminatoil' => NULL,
							);
							
							$this->db->update(self::TABELLA_ISTANZE, $updateList, array("id=?"=>$id_counter_istance));
							continue; //uscita poichè non vi è necessità di istanziare nuovamente il counter				
						}				
					}elseif($node_content['name'] == 'Verifica Overtime'){
						$component_id = self::CHECKTIME;
						$obtained = $output;
					}elseif($node_content['name'] == 'Attendi Aggiunta Soggetto'){
						$component_id = self::EVENT_TRIGGER_ADDSUBJECT;
						$obtained = $output;
						
						//VERIFICA CHE IL TRIGGER EVENT SIA GIà ISTANZIATO
						$id_trigger_event_istance = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE node_id = ? AND workflow_id = ?", array($id,$this->workflow->id));

						if (!empty($id_trigger_event_istance)){
							continue; //uscita poichè non vi è necessità di istanziare nuovamente il TRIGGER EVVENT				
						}
					}elseif($node_content['name'] == 'Attendi Aggiunta Documento'){
						$component_id = self::EVENT_TRIGGER_ADD_DOCUMENT;
						$obtained = $output;
						
						//VERIFICA CHE IL TRIGGER EVENT SIA GIà ISTANZIATO
						$id_trigger_event_istance = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE node_id = ? AND workflow_id = ?", array($id,$this->workflow->id));

						if (!empty($id_trigger_event_istance)){
							continue; //uscita poichè non vi è necessità di istanziare nuovamente il TRIGGER EVVENT				
						}
					}elseif($node_content['name'] == 'Attendi Impegno Evaso'){
                        $component_id = self::EVENT_TRIGGER_EXPIRE_DEADLINE;
                        $obtained = $output;

                        //VERIFICA CHE IL TRIGGER EVENT SIA GIà ISTANZIATO
                        $id_trigger_event_istance = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE node_id = ? AND workflow_id = ?", array($id,$this->workflow->id));

                        if (!empty($id_trigger_event_istance)){
                            continue; //uscita poichè non vi è necessità di istanziare nuovamente il TRIGGER EVVENT
                        }
                    }elseif($node_content['name'] == 'Attendi Cambio Campo Pratica'){
                        $component_id = self::EVENT_TRIGGER_CHANGE_ARCHIVE_FIELDS;
                        $obtained = $output;

                         //VERIFICA CHE IL TRIGGER EVENT SIA GIà ISTANZIATO
                        $id_trigger_event_istance = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE node_id = ? AND workflow_id = ?", array($id,$this->workflow->id));

                        if (!empty($id_trigger_event_istance)){
                            continue; //uscita poichè non vi è necessità di istanziare nuovamente il TRIGGER EVVENT
                        }
                    }elseif($node_content['name'] == 'Crea Task'){
						$component_id = self::TASK;
						$obtained = $output;
					}elseif($node_content['name'] == 'Avvia Macro'){
						$component_id = self::MACRO_STARTER;
						$obtained = $output;
					}elseif($node_content['name'] == 'Avvia Workflow'){
                        $component_id = self::WORKFLOW_LAUNCHER;
                        $obtained = $output;
                    }elseif($node_content['name'] == 'Cambia Stato Pratica'){
                        $component_id = self::STATUS_UPDATER;
                        $obtained = $output;
                    }elseif($node_content['name'] == 'Sposta in Solleciti'){
                        $component_id = self::CHANGE_STATUS;
                        $obtained = $output;
                    }elseif($node_content['name'] == 'Calcola Interessi'){
                        $component_id = self::CALC_INTERESTS;
                        $obtained = $output;
                    }elseif($node_content['name'] == 'Inserisci Impegno'){
                        $component_id = self::INSERT_EVENT;
                        $obtained = $output;
                    }elseif($node_content['name'] == 'Genera Modello'){
                        $component_id = self::CREATE_MODEL;
                        $obtained = $output;
                    }elseif($node_content['name'] == 'Crea Sollecito'){
                        $component_id = self::CREATE_WARNING;
                        $obtained = $output;
                    }
			
			
					$now = $this->now();
					$uid = $this->getUid();


					$fieldList = array(
						'stato' => self::DOING,
						'previous' => json_encode($previous),
						'next' => json_encode($in_the_future),
						'input' => json_encode($inputs),
						'output' => json_encode($outputs),
						'data' => json_encode($data),
						'workflow_id' => $this->workflow->id,
						'node_id' => $id,
						'immessoil' => $now,
						'component_id' => $component_id,
						'obtained' => $obtained,
						'uniqueid' => $uid,
					);

					if ($component_id == self::CHECKTIME){
						$fieldList['optional_data'] = $optional_data;
					} elseif (
						$component_id == self::EVENT_TRIGGER_ADDSUBJECT
						|| $component_id == self::EVENT_TRIGGER_ADD_DOCUMENT
						|| $component_id == self::EVENT_TRIGGER_EXPIRE_DEADLINE
						|| $component_id == self::EVENT_TRIGGER_CHANGE_ARCHIVE_FIELDS
					) {
						$fieldList['stato'] = self::WAIT; //gli event trigger iniziato sempre in WAIT cioè attendono qualche azione per avviarsi
					}

					$this->db->insert(self::TABELLA_ISTANZE, $fieldList);
					
					
					if ($component_id == self::STEPWAIT){
						$created_id = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE uniqueid = ?", $uid);
						//CONSERVAZIONE ASSOCIAZIONI STEP WAIT
						$stepwait_list = array(
							'stepwait_id' => $created_id,
							'stepwait_node_id' => $id,
							'ingresso_id' => $this->istanza->id,
							'ingresso_node_id' => $actual_node_id,
							'obtained' => $store_input,
							'response' => $this->response,
						);
						$this->db->insert(self::TABELLA_STEPWAIT_INPUTS, $stepwait_list );
					}
					elseif($component_id == self::EVENT_TRIGGER_ADDSUBJECT){
						$created_id = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE uniqueid = ?", $uid);

						//CONSERVAZIONE NELLA TABELLA DEGLI EVENTI IN ATTESA DI QUALCOSA
						
						$event = array(
							'component_status_id' => $created_id,
							'component_type' => self::EVENT_TRIGGER_ADDSUBJECT,
							'file_id' => $this->workflow->file_id
						);
						
						$this->db->insert(self::TABELLA_EVENT_LISTENERS, $event );

					}
					elseif($component_id == self::EVENT_TRIGGER_ADD_DOCUMENT){
						$created_id = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE uniqueid = ?", $uid);

						//CONSERVAZIONE NELLA TABELLA DEGLI EVENTI IN ATTESA DI QUALCOSA
						
						$event = array(
							'component_status_id' => $created_id,
							'component_type' => self::EVENT_TRIGGER_ADD_DOCUMENT,
							'file_id' => $this->workflow->file_id
						);
						
						$this->db->insert(self::TABELLA_EVENT_LISTENERS, $event );

					}
                    elseif($component_id == self::EVENT_TRIGGER_EXPIRE_DEADLINE){
                        $created_id = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE uniqueid = ?", $uid);

                        //CONSERVAZIONE NELLA TABELLA DEGLI EVENTI IN ATTESA DI QUALCOSA

                        $event = array(
                            'component_status_id' => $created_id,
                            'component_type' => self::EVENT_TRIGGER_EXPIRE_DEADLINE,
                            'file_id' => $this->workflow->file_id,
                        );

                        $this->db->insert(self::TABELLA_EVENT_LISTENERS, $event );

                    }
                    elseif($component_id == self::EVENT_TRIGGER_CHANGE_ARCHIVE_FIELDS){
                        $created_id = $this->db->fetchOne("SELECT id FROM ".self::TABELLA_ISTANZE." WHERE uniqueid = ?", $uid);

                        //CONSERVAZIONE NELLA TABELLA DEGLI EVENTI IN ATTESA DI QUALCOSA

                        $event = array(
                            'component_status_id' => $created_id,
                            'component_type' => self::EVENT_TRIGGER_CHANGE_ARCHIVE_FIELDS,
                            'file_id' => $this->workflow->file_id,
                        );

                        $this->db->insert(self::TABELLA_EVENT_LISTENERS, $event);

                    }
				}
			}

		}
	}
	
	
	//GESTIONE PERMESSI ONEDRIVE CONDIVISA DAL COMPONENT AGGIUNTA SOGGETTI E GRUPPI
	
	function callOneDrive($data=array()){
		$content_type = "Content-Type: application/x-www-form-urlencoded";
		$curlHeader = array($content_type);
		if(isset($data['header'])){
			$curlHeader = $data['header'];
		}
		
		if(isset($data['bearer_token'])){
			array_push($curlHeader, $data['bearer_token']);
		}
		$url = $data['url'];
		$request_type = $data['request_type'];

		$receivedHeaders = [];

		$curlParams = array(
			CURLOPT_URL => $url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => $request_type,
			CURLOPT_HTTPHEADER => $curlHeader
		);
		
		if(in_array($request_type, ["POST","PUT"]) && isset($data['postParams'])){
			$curlParams[CURLOPT_POSTFIELDS] = $data['postParams'];
		}
		if(isset($data['follow_location'])){
			$curlParams[CURLOPT_FOLLOWLOCATION] = $data['follow_location'];
		}
		if(isset($data['is_copy']) && $data['is_copy']) {
			// Se chiamata per copia aggiungi parsing degli header per ottenere Location
			$curlParams[CURLOPT_HEADERFUNCTION] = function($curl, $header) use (&$receivedHeaders) {
													$len = strlen($header);
													$header = explode(':', $header, 2);

													// Ignora headers non validi
													if (count($header) < 2) {
														return $len;
													}
												
													$receivedHeaders[strtolower(trim($header[0]))][] = trim($header[1]);
												
													return $len;
												};
		}
		$curl = curl_init();
		curl_setopt_array($curl, $curlParams);
		$response = curl_exec($curl);
		$info = curl_getinfo($curl);
		curl_close($curl);

		//Aggiungo il redirect_url in caso di download del file
		if($info['redirect_url']){
			$response = json_decode($response);
			$response['download_url'] = $info['redirect_url'];
			$response = json_encode($response);
		}
		//per il logout restituisco solo http_code
		if(isset($data['is_logout'])){
			$response = $info['http_code'];
		} else if(isset($data['is_copy']) && $data['is_copy']) {
			// Se copia estrapolo drive-id dall'header "Location" (url monitoraggio stato avanzamento copia)

			if(!empty($receivedHeaders) && isset($receivedHeaders['location'])) {
				$response = substr(strrchr(rtrim($receivedHeaders['location'][0], '/'), '/'), 1);
			}
		}

		if(!isset($data['follow_location']) && !isset($data['is_logout'])){
			$this->applogger->info(print_r($response,1));
		}

		//DELETE, GET per il download e POST per copia documento restituiscono response vuota, casi limite
		if(empty($response) && !in_array($info['http_code'], [202, 204, 302])) {
			$this->applogger->info("*** OneDrive Error, empty response for " . SITEKEY . ", user_id: -1 ***");
		} else if(isset($response->error)){
			$this->applogger->info("*** OneDrive Error ***\nsubdomain: " . SITEKEY . "\nuser_id: -1 \nError: " . $response->error . "\nError Description: " . $response->error_description . "\n*********");
		}

		return $response;
	}
	
	
	function handleOneDrivePermissionsAction($personId, $file, $riserva, $type = 1, $params){
		$this->applogger->info("gestione onedrive");
		//type  0: edit, 1: aggiunta, 2: rimozione
		//$db, $config, $subdomain, $loggedUser
		$oneDriveHandler = new OneDriveHandler($this->db, $this->config, $this->subdomainName, null);
		$oneDriveFolderId = $this->db->fetchOne("SELECT one_drive_folder_id FROM archivio WHERE id = ?", $file);
		$permission = $this->db->fetchRow("SELECT id, permission_id FROM onedrive_permessi WHERE user_id = ? AND file_id = ?", array($personId, $file));
		$permissionUid = $permission['permission_id'];
		$accessToken = $oneDriveHandler->checkOneDriveToken();
		$one_drive_master_id = $this->db->fetchOne("SELECT one_drive_master_id FROM settings WHERE id = 1 ");
		//aggiunta dei permessi su one drive
		if($type == 1){
			$oneDriveEmail = $this->db->fetchAll(
				"SELECT u.one_drive_email as email FROM utente u WHERE u.id = ? AND (u.one_drive_uniqueid IS NULL OR u.one_drive_uniqueid != ?)",
				 array($personId, $one_drive_master_id));

			$postParams = array(
				"requireSignIn" => true,
				"sendInvitation" => true,
				"roles" => array('write'),
				"recipients" => $oneDriveEmail,
				"message" => "Here's the file that we're collaborating on."
			);
			$has_access = $this->db->fetchOne("SELECT id FROM utentipraticariservata WHERE person_id = ? AND file_id = ? ", array($personId,$file));
			$reserved = $this->db->fetchOne("SELECT riservata FROM archivio WHERE id = ?", $file);
			if($oneDriveFolderId && !$permissionUid && (($reserved && $has_access) || !$reserved)){
				$this->applogger->info("INVITO");
				$permissionData = array(
					"url" => "https://graph.microsoft.com/v1.0/me/drive/items/$oneDriveFolderId/invite",
					"request_type" => "POST",
					"header" => array(
						"Content-Type: application/json",
						"Authorization: Bearer " . $accessToken
					),
					"postParams" => json_encode($postParams)
				);
				$response = json_decode($this->callOneDrive($permissionData));
				if (!empty($response->value)){
					foreach($response->value as $value){
						$oneDriveEmail = $value->invitation->email;
						$permissionId = $value->id;
						$oneDriveUserUid = $value->grantedTo->user->id;

						$oneDriveUser = $this->db->fetchRow("SELECT u.id, u.one_drive_uniqueid FROM utente u WHERE u.one_drive_email = ?", $oneDriveEmail);
						if(empty($oneDriveUser['one_drive_uniqueid'])){
							$this->db->update("utente",
								array("one_drive_uniqueid" => $oneDriveUserUid),
								array("one_drive_email = ?" => $oneDriveEmail)
							);
						}
						$permissionExists = $this->db->fetchOne("SELECT id FROM onedrive_permessi WHERE user_id = ? AND file_id = ? AND permission_id IS NOT NULL", array($personId, $file));
						if(empty($permissionExists)){
							$permissionData = array(
								"user_id" => $oneDriveUser['id'],
								"file_id" => $file,
								"permission_id" => $permissionId,
								"created_at" => date("Y-m-d H:i:s")
							);
							$this->db->insert("onedrive_permessi", $permissionData);
						}
					}
				}
			}
			$oneDriveHandler->spreadOneDrivePermissions($file, $oneDriveFolderId, empty($reserved)? 'false' : 'true', $accessToken);
		//rimozione dei permessi su one drive
		} else if($type == 2){
			if($oneDriveFolderId && $permissionUid){
				$permissionData = array(
					"url" => "https://graph.microsoft.com/v1.0/me/drive/items/$oneDriveFolderId/permissions/$permissionUid",
					"request_type" => "DELETE",
					"header" => array(
						"Content-Type: application/json",
						"Authorization: Bearer " . $accessToken
						)
					);
				$response = json_decode($this->callOneDrive($permissionData));
				$this->db->delete("onedrive_permessi", array("id = ?" => $permission['id']));
			}
			$oneDriveHandler->spreadOneDrivePermissions($file, $oneDriveFolderId, $riserva, $accessToken);
		}
		//todo 
		// caso limite: rimozione utente dalla riserva ma la pratica è ancora riservata, bisogna cancellare il permesso
		else if($type == 0 && $riserva == 'false'){
			$countReservation = $this->db->fetchOne("SELECT COUNT(*) FROM utentipraticariservata where file_id = ?", $file);
			if($countReservation > 0){
				if($oneDriveFolderId && $permissionUid){
					$permissionData = array(
						"url" => "https://graph.microsoft.com/v1.0/me/drive/items/$oneDriveFolderId/permissions/$permissionUid",
						"request_type" => "DELETE",
						"header" => array(
							"Content-Type: application/json",
							"Authorization: Bearer " . $accessToken
							)
						);
					$response = json_decode($this->callOneDrive($permissionData));
					$this->db->delete("onedrive_permessi", array("id = ?" => $permission['id']));
				}
			} else {
				$oneDriveHandler->spreadOneDrivePermissions($file, $oneDriveFolderId, 'false', $accessToken, 1);
			}
		} else if($type == 0 && $riserva == 'true'){
			$oneDriveHandler->spreadOneDrivePermissions($file, $oneDriveFolderId, $riserva, $accessToken);
		}
	}
	
	
	
	//DA USARE SOLO PER AddSubjectComponent E addGroup AGGIUNGE LA RISERVA AD UN DATO UTENTE

    /**
     * @param $user_id
     * @return string
     */
	function addRiserva($user_id): string
    {
		$file_id = $this->workflow->file_id;
		$is_already_reserved = $this->db->fetchOne("SELECT id FROM utentipraticariservata WHERE person_id = ? AND file_id = ?", array($user_id,$file_id));
		$commento = "";
		if (!empty($is_already_reserved)){
			$commento .= " L'utente risulta già presente tra le riserve della pratica.";
        }else{
			$intestatario = $this->db->fetchOne("SELECT avvocato FROM archivio WHERE id = ?", array($file_id));
			if (empty($intestatario)){
				$commento .= " La pratica: $file_id non ha un intestatario";
				return $commento;
			}
			
			$intestatario_userRow = $this->db->fetchRow("SELECT id,nomeutente FROM utente WHERE codiceavvocato = ? AND tipoutente = 1", $intestatario);
			$archivio_reserved = $this->db->fetchOne("SELECT riservata FROM archivio WHERE id = ?", $file_id);
			if (empty($archivio_reserved)){
				$this->db->update("archivio", array("riservata"=>1), array("id=?"=>$file_id));
			}
			if ($intestatario_userRow['id'] == $user_id){
				$this->db->insert("utentipraticariservata" , array("person_id"=>$user_id , "file_id"=>$file_id));
				$commento .= " L'utente è stato aggiunto alle riserve della pratica";
			}else{
				$insteatario_is_reserved = $this->db->fetchOne("SELECT id FROM utentipraticariservata WHERE person_id = ? AND file_id = ?", array($intestatario_userRow['id'],$file_id));
				
				if (empty($insteatario_is_reserved)){
					$this->db->insert("utentipraticariservata" , array("person_id"=>$intestatario_userRow['id'] , "file_id"=>$file_id));
					$intestatario_has_relation = $this->db->fetchOne("SELECT person_id FROM utente_pratica WHERE person_id = ? AND file_id = ?", array($intestatario_userRow['id'],$file_id));
					if (empty($intestatario_has_relation)){
						$this->db->insert("utente_pratica", array("person_id"=>$intestatario_userRow['id'],"file_id"=>$file_id,"relazione"=>UTENTE_INTERNO));
					}
					$commento .= " E' stata aggiunta la riserva anche all'intestatario della pratica: " . $intestatario_userRow['nomeutente'];
				}
			
				$this->db->insert("utentipraticariservata" , array("person_id"=>$user_id , "file_id"=>$file_id));		
				$commento .= " E' stata aggiunta la riserva anche all'intestario della pratica: " . $intestatario_userRow['nomeutente'];

			}
        }
        return $commento;

    }

	static function getComponentName($componentId){
		switch($componentId){
			case self::EMAILSENDER:
				return "invio email";
			case self::ATTESA:
				return "attesa";
			case self::NOTIFICATION:
				return "notifica";
			case self::DOCUMENT_MAKER:
				return "creazione documento";
			case self::STEPWAIT:
				return "attesa ingressi";
			case self::ADD_SUBJECTS:
				return "aggiunta soggetto";
			case self::ADD_GROUP:
				return "riserva gruppo";
			case self::COUNTER:
				return "contatore";
			case self::CHECKTIME:
				return "verifica overtime";
			case self::TASK:
				return "task";
			case self::MACRO_STARTER:
				return "avvia macro";
			case self::WORKFLOW_LAUNCHER:
				return "avvia workflow";
			case self::STATUS_UPDATER:
				return "cambia stato";
			case self::INSERT_EVENT:
				return "aggiunta evento";
			case self::BEGINNING:
				return "inizio workflow";
			case self::CREATE_MODEL:
				return "genera modello";
            case self::CREATE_WARNING:
                return "crea sollecito";
            case self::CHANGE_STATUS:
                return "sposta in solleciti";
            case self::CALC_INTERESTS:
                return "calcola interessi";
			case self::EVENT_TRIGGER_ADDSUBJECT:
				return "evento aggiunta soggetto";
			case self::EVENT_TRIGGER_ADD_DOCUMENT:
				return "evento aggiunta documento";
			case self::EVENT_TRIGGER_EXPIRE_DEADLINE:
				return "evento impegno evaso";
			case self::END:
				return "termina workflow";
		}
	}

    /**
     * The following function is used to store workflow instance logs,
     * the function can be launched in any state modifier function
     *
     * @param string $action
     */
    public function storeLog(string $action): void
    {
        $component_name = self::getComponentName($this->istanza->component_id);

        $logrow =
                [
                    'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                    'workflow_id'=> $this->istanza->workflow_id,
                    'type'=>$action
                ];

        switch ($action)
        {
            case self::AWAKE:
                $log    = "Il componente $component_name ({$this->istanza->id}) è stato ridestato ed è pronto per l'esecuzione" ;

                break;

            case self::AWAKE_LISTENER:
                $log = "Il componente $component_name ({$this->istanza->id}) è stato innescato e rimane in ascolto.";

                break;

            case self::SET_DONE:
                $log = "Il componente $component_name ({$this->istanza->id}) è stato completato.";

                break;

            case self::SET_SLEEP:
                $log = "Il componente $component_name ({$this->istanza->id}) si è addormentato Z.z.z.";

                break;

            case self::BEGIN:
                $log = "Il componente $component_name ({$this->istanza->id}) è appena stato messo in elaborazione.";

                break;


            case self::SET_ERROR:
                $log = "Il componente $component_name ({$this->istanza->id}) ha avuto alcuni problemi :(";

                break;

            default:
                $logrow['type']   = 'UKNOWN';
                $log = "Il componente $component_name ({$this->istanza->id}) è in uno stato sconosciuto... Chi Sono io?";

                break;

        }

        
        $logrow['log']  = $log;

        $this->db->insert(self::TABELLA_LOG, $logrow);
    }

	
}


