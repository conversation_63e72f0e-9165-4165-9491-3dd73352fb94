<?php


namespace  DexmaCommons\Workflow;

use Netlex3\Software\patterns\Services\EmailService as EmailServiceOld;
use Netlex3\Software\patterns\Domains\Mailbox\Services\EmailService as EmailService;
use Netlex3\Software\patterns\Domains\Mailbox\Services\MailboxService;
use Exception;

class EmailSenderComponent extends ComponentManager{

    protected $componentType = self::EMAILSENDER;
    protected $destDir;
    protected $objects;

    private $emailServiceOld;
    private $infrastructure;
    private $mailboxService;

    function __construct($component_status_id,$subdomainName,$componentRow = NULL, $workflow = NULL, $Zend_Infrastructure){

        parent::__construct($component_status_id,$Zend_Infrastructure->getCustomerBucket(),$subdomainName,$Zend_Infrastructure->getLogger(),$componentRow, $workflow);

        $this->destDir 			= $this->subdomainName . '/EmailSenderComponent/' . $this->istanza->uniqueid . '/' ;
        $this->emailServiceOld  = new EmailServiceOld($Zend_Infrastructure);
        $this->objects          = $this->getObtainedArray();
        $this->infrastructure   = $Zend_Infrastructure;
        $this->mailboxService   = MailboxService::getInstance($Zend_Infrastructure);
    }

    function emailSender($destinatari, $mittenti, $title, $body){
        try{
            if (config('app.mailgateway')) {
                foreach ($mittenti as $id) {
                    foreach ($destinatari as $destinatario) {
                        try {
                            //$this->applogger->info('MITTENTE ID: ' . $id);
                            $emailAccount = $this->emailServiceOld->getUserEmailAccountData($this->emailServiceOld->getMailUid($id));
                            //$this->applogger->info('EMAIL ACCOUNT: ' . json_encode($emailAccount, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                            $mailAccountId = $this->mailboxService->getEmailAccountIdFromUid($emailAccount['uniqueid']);
                            //$this->applogger->info('ID: ' . json_encode($mailAccountId, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                            $emailService = EmailService::getInstance($this->infrastructure, $mailAccountId);
                            $emailService->sendEmail($destinatario, $title, $body, '', '', []);
                        } catch (Exception $e) {
                            if ($e->getCode() === 302) {
                                $this->applogger->err("MailGateway error: {$e->getMessage()}");
                                continue;
                            }
                            throw $e;
                        }
                    }
                }
            } else {
                if (!empty($mittenti) && !in_array("<EMAIL>", $mittenti)){

                    foreach ($mittenti as $id){

                        foreach ($destinatari as $destinatario){

                            $emailAccount = $this->emailServiceOld->getUserEmailAccountData($this->emailServiceOld->getMailUid($id));

                            if (!$emailAccount) {

                                $this->applogger->info("Nessun account email per inviare la mail");

                            } else {

                                $this->emailServiceOld->setMailValues(null, $destinatario, $title, $body);

                                $this->emailServiceOld->attachmentsWF($this->objects);

                                $this->emailServiceOld->sendMail();

                                $this->emailServiceOld->storeEml($id, $this->destDir);

                                $this->emailServiceOld->deleteTmpDir();

                                $this->applogger->info('Email inviata');

                            }
                        }
                    }
                }else{
                    foreach ($destinatari as $destinatario){

                        $this->emailServiceOld->setMailValues("aws", $destinatario, $title, $body);

                        $this->emailServiceOld->attachmentsWF($this->objects);

                        $this->emailServiceOld->sendMail();

                        $this->emailServiceOld->deleteTmpDir();

                    }
                }
            }

            return "true";

        }catch(Exception $e){
            $this->applogger->info($e);
            return "false";
        }
    }


    
    function execute(){
        $this->begin();

        $destinatari = $this->emailServiceOld->setDestinatari($this->pratica, $this->data->targetReceiverType);
        $mittenti = $this->emailServiceOld->setMittenti($this->pratica, isset($this->data->userEmailFrom) ? $this->data->userEmailFrom : '<EMAIL>');
        $title = $this->emailServiceOld->setTitle($this->data->messageTitle);
        $body = $this->emailServiceOld->setBody($this->data->messageBody, $this->data->modelId ?? null);

        $this->setResponse($this->emailSender($destinatari, $mittenti, $title, $body));
        $this->setDone();

        if ($this->hasNext()){
            $this->setNextComponents();
        }
    }
}


?>