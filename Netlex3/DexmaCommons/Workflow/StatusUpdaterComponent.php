<?php


namespace  <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

class StatusUpdaterComponent extends ComponentManager{

    protected $componentType = self::STATUS_UPDATER;
    protected $status_id;
    protected $now;


    function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
        parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);

        $this->status_id  = $this->data->statusId;
        $this->now          = $this->now();



    }


    function statusUpdate()
    {
        // ** todo: do we need an error exit option? * /
        $result = 'true';
        try{
            if (!empty($this->status_id) ){
                $this->db->update('archivio', array('stato'=>$this->status_id), array('id=?'=>$this->workflow->file_id));
            }
        }catch(Exception $e){
            $this->applogger->info("Status Updater Component: ". $e->getMessage());
        }

        return $result;
    }



    function execute(){
        $this->begin();

        $this->setResponse($this->statusUpdate());
        $this->setOutput($this->istanza->obtained); //si limita a ritrasferire il proprio output obtained

        $this->setDone();

        if ($this->hasNext()){
            $this->setNextComponents();
        }

    }
}


?>