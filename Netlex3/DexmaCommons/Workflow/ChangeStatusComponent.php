<?php


namespace  <PERSON><PERSON><PERSON>om<PERSON>\Workflow;

class ChangeStatusComponent extends ComponentManager{

    protected $componentType = self::CHANGE_STATUS;
    protected $status_id;
    protected $applogger;

    const SPOSTA = 1 ;
    const RIMUOVI = 2 ;

    function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
        parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);

        $this->status_id    =     $this->data->statusType;
        $this->now          =     $this->now();
        $this->applogger    =     $applogger;


    }

    function statusUpdate()
    {
        $result = 'true';

        try
        {
            if (!empty($this->status_id) )
            {
                $data = array();
                $sql = 'SELECT id FROM recuperocrediti_documenti WHERE file_id = ? AND step_reminder = 0';

                switch ($this->status_id)
                {
                    case self::RIMUOVI:
                        $where = ' AND status = 1';
                        $data['status'] = 0;
                        break;
                    case self::SPOSTA:
                        $where = ' AND status = 0';
                        $data['status'] = 1;
                        break;
                }

                if(isset($where)){
                    $sql .= $where;
                }

                $documents = $this->db->fetchCol($sql, $this->workflow->file_id);

                foreach ($documents as $document_id)
                {
                    $this->db->update('recuperocrediti_documenti', $data, ['id = ?' => $document_id]);
                }
            }
        }catch(Exception $e)
        {
            $this->applogger->info("Change Status Component: ". $e->getMessage());
        }

        return $result;
    }


    function execute(){
        $this->begin();
        $this->setResponse($this->statusUpdate());
        $this->setOutput($this->istanza->obtained); //si limita a ritrasferire il proprio output obtained

        $this->setDone();

        if ($this->hasNext()){
            $this->setNextComponents();
        }
    }

}

?>