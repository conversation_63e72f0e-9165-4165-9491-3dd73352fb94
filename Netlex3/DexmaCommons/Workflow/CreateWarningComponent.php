<?php

namespace  <PERSON><PERSON><PERSON>om<PERSON>\Workflow;

use DexmaCommons\PrintsManager\PrintsHandler;
use DexmaCom<PERSON>\RecoveryCredit\RecoveryCreditHandler;


//Libreria con le funzioni per utilizzare il recupero crediti



class CreateWarningComponent extends ComponentManager{


    protected $componentType = self::CREATE_WARNING;
    protected $s3Connector;
    protected $dbShared;
    protected $applogger;
    protected $recoveryHandler;
    protected $print_type;
    protected $actionType;
    protected $bucketName = "netlex-main";
    protected $user;

    const MORA = 1;
    const SOLLECITO_2 = 2;
    const SOLLECITO_3 = 3;
    const DIFFIDA = 4;
    const ELIMINA = 5;





    function __construct($component_status_id,$db,$dbShared,$subdomainName,$applogger,$s3Connector,$componentRow = NULL, $workflow = NULL, $Zend_Infrastructure){
        parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);

        $this->s3Connector 			=     $s3Connector;

        $this->dbShared 			=     $dbShared;

        $this->applogger            =     $applogger;

        $this->print_type           =     $this->data->printType;
        $this->actionType           =     $this->data->actionType;

        $this->user      =     $Zend_Infrastructure->getUser();
    }


    // genera i solleciti da stampare
    function generateWarning($file_id){

        $this->recoveryHandler      =     new RecoveryCreditHandler($this->db);

        $praticaId = $file_id;
        $sql ='SELECT id FROM recuperocrediti_documenti WHERE file_id = ? AND status = 1 AND step_reminder = ?';

        if($this->actionType !== self::ELIMINA) {
            switch ($this->actionType) {
                case self::MORA:
                    $value = '0';
                    break;
                case self::SOLLECITO_2:
                    $value = '1';
                    break;
                case self::SOLLECITO_3:
                    $value = '2';
                    break;
                case self::DIFFIDA:
                    $value = '3';
            }

            $documents = $this->db->fetchAll($sql, array($praticaId, $value));

            $warnings = array();
            $this->applogger->info('doc passati al generate: ' . print_r($documents, 1));

            foreach ($documents as $document) {
                $response = $this->recoveryHandler->generateWarning($document['id']);
                array_push($warnings, $response);
            }
            return $warnings;
        }
    }


    function printAction($warnings){


        $s3Connector = $this->s3Connector;
        $warningsData = $warnings;
        $uploadedDocuments = array();

        foreach ($warningsData as $warning){

            $sollecitoId = $warning;

            $sollecito = $this->db->fetchRow('SELECT id, recuperocrediti_doc_id, warning_type As status FROM recuperocrediti_solleciti WHERE id = ?', $sollecitoId);

            if(!empty($sollecito)  && empty($sollecito['workflow_printed'])) {

                $from = ' FROM recuperocrediti_settings';

                switch ($sollecito['status']) {
                    case self::MORA:
                        $select = 'SELECT id_mora_model';
                        $type = 'mora';
                        break;

                    case self::SOLLECITO_2:
                        $select = 'SELECT id_second_model';
                        $type = 'sollecito2';
                        break;

                    case self::SOLLECITO_3:
                        $select = 'SELECT id_third_model';
                        $type = 'sollecito3';
                        break;

                    case self::DIFFIDA:
                        $select = 'SELECT id_beware_model';
                        $type = 'diffida';
                        break;
                }

                $sql = $select . $from;

                $templateId = $this->db->fetchOne($sql);

                if (empty($templateId)) {
                    $print = $this->db->fetchRow('SELECT * FROM stampe WHERE category = 5 AND default_print = 1');
                } else {
                    $print = $this->db->fetchRow('SELECT * FROM stampe WHERE id =?', $templateId);
                }

                if (empty($print)) {
                    $this->applogger->info("##### Errore: nessun template trovato.");
                }


                $folderPath = $this->subdomainName . '/' . 'prints' . '/' . $print['id'] . '/';
                $filename = $print['filename'];

                $fileTemplate = $s3Connector->getBody($this->bucketName, $folderPath . $filename);
                $tmpUid = $this->getUid();


                $tmpDir = '/tmp' . '/' . $this->subdomainName . '-' . $tmpUid;

                if (!file_exists($tmpDir)) {
                    mkdir($tmpDir, 0777, true);
                }
                $filePath = $tmpDir . '/' . $filename;

                $handle = fopen($filePath, 'w');
                fwrite($handle, $fileTemplate);
                fclose($handle);


                $PH = new \stdClass();
                $PH->applogger = $this->applogger;
                $PH->db = $this->db;
                $PH->dbShared = $this->dbShared;
                $PH->s3Connector = $s3Connector;


                $archivioId = $this->workflow->file_id;

                $params = array();
                $params['docid'] = $print['id'];
                $params['feeId'] = $sollecito['recuperocrediti_doc_id'];
                $params['type'] = $sollecito['status'];

                $labels = array('recuperocrediti');


                $datetime = implode("_", explode(" ", $this->now()));
                $datetime = implode("_", explode(":", $datetime));
                $filename = $type . '_doc_' . $sollecito['id'] . '_'. $datetime . '.docx';
                $output = $tmpDir . '/' . $filename;

                $this->applogger->info("##### component creazione documento - labels:");
                $this->applogger->info(print_r($labels, 1));
                $this->applogger->info("##### component creazione documento - params:");
                $this->applogger->info(print_r($params, 1));

                $printsHandler = new PrintsHandler($PH, array(
                    'labels' => $labels,
                    'request' => $params,
                    'filePath' => $filePath,
                    'output' => $output,
                    'sitekey' => $this->subdomainName,
                ));

                $printsHandler->print($this->user['rollout_version']);

                $counter = 1;
                $fileArray = explode('.', $filename);
                $ext = $fileArray[count($fileArray) - 1];
                unset($fileArray[count($fileArray) - 1]);
                $prefix = implode('.', $fileArray);
                if (empty($prefix)) {
                    $prefix = "Documento";
                    $filename = $prefix . "." . $ext;
                }


                while ($s3Connector->doesObjectExist($this->bucketName, $this->subdomainName .  '/' . 'documents' . '/' . $this->workflow->file_id . '/' . $filename)) {
                    $filename = $prefix . '(' . $counter++ . ').' . $ext;
                }


                $s3Connector->putObject($this->bucketName, $this->subdomainName . '/' . 'documents' . '/' . $this->workflow->file_id . '/' . $filename, $output);
                $onlydate = explode(" ", $this->now())[0];
                $uid = $this->getUid();
                $this->db->insert("documento", array('titolodocumento' => $prefix, 'data' => $onlydate, 'immessoil' => $this->now(), 'modificatoil' => $this->now(), 'codicepratica' => $this->pratica, 'nomefile' => $filename, 'uniqueid' => $uid));


                exec('rm -rf ' . $tmpDir);

                // la colonna workflow_printed non esiste mai!
                // $this->db->update('recuperocrediti_solleciti', array('workflow_printed' => 1), array('id =?' => $sollecitoId));

                $documento = $this->db->fetchOne("SELECT id FROM documento WHERE uniqueid = ?", $uid);

                array_push($uploadedDocuments, $documento);
            }else{
                $this->applogger->info('nessun sollecito trovato!');
            }
        }


    }





    function execute(){
        $this->begin();


        //lancia la creazione del sollecito
        if($this->actionType !== self::ELIMINA){
            $warnings = $this->generateWarning($this->workflow->file_id);

            //lancia stampa se impostata
            if(isset($this->print_type) && $this->print_type == 1 && !empty($warnings) ){
                $this->printAction($warnings);
            }
        }



        $this->setResponse('true');
        $this->setOutput(json_encode($warnings));
        $this->setDone();



        if ($this->hasNext()){

            $this->setNextComponents();

        }
    }


}

?>