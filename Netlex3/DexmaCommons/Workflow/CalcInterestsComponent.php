<?php

namespace  <PERSON>ma<PERSON>om<PERSON>\Workflow;

use DexmaCommons\RecoveryCredit\RecoveryCreditHandler;
use Exception;

//Libreria con le funzioni per utilizzare il recupero crediti

class CalcInterestsComponent extends ComponentManager{

    protected $componentType = self::CALC_INTERESTS;
    protected $interestsType;
    protected $warningsType;
    protected $applogger;

    const MORA_SOLLECITI = 1 ;
    const DIFFIDA = 2 ;

    function __construct($component_status_id,$db,$subdomainName,$applogger,$componentRow = NULL, $workflow = NULL){
        parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);

        $this->interestsType    =     $this->data->interestsType;
        $this->warningsType    =     $this->data->warningsType;
        $this->applogger    =     $applogger;


    }

    function calcInterest($practice)
    {
        try
        {
            $this->recoveryHandler      =     new RecoveryCreditHandler($this->db);

            $file_id = $practice;
            $interests = array();

            if ($this->warningsType == self::MORA_SOLLECITI) {

                $documentsWarning = $this->db->fetchAll('SELECT * FROM recuperocrediti_documenti WHERE file_id = ? AND status = 1 AND step_reminder = 0', $file_id);

                if (!empty($documentsWarning)) {

                    foreach ($documentsWarning as $document) {
                        $interest = $this->recoveryHandler->calcInterests($document, 1);
                        array_push($interests, $interest);
                    }
                }

            } elseif ($this->warningsType == self::DIFFIDA) {

                $documentsBeware = $this->db->fetchAll('SELECT * FROM recuperocrediti_documenti WHERE file_id = ? AND status = 1 AND step_reminder = 3', $file_id);

                if (!empty($documentsBeware)) {
                    foreach ($documentsBeware as $document) {
                        $interest = $this->recoveryHandler->calcInterests($document, 2);
                        array_push($interests, $interest);
                    }
                }

            } else {

                $this->applogger->info('ERROR: non è stato impostato nessun tipo di sollecito');
            }
            $this->applogger->info('interessi: ' . print_r($interests, 1));
        }catch(Exception $e)
        {
            $this->applogger->info("Calc Interests Component: ". $e->getMessage());
        }
    }

    function execute(){
        $this->begin();

        $this->calcInterest($this->workflow->file_id);

        $this->setResponse('true');
        $this->setOutput($this->istanza->obtained); //si limita a ritrasferire il proprio output obtained

        $this->setDone();

        if ($this->hasNext()){
            $this->setNextComponents();
        }
    }

}

?>