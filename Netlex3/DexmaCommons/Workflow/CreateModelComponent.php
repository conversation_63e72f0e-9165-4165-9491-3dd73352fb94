<?php


namespace  <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use <PERSON>ma<PERSON>om<PERSON>\Modelli\Engine;

class CreateModelComponent extends ComponentManager
{

    protected $componentType = self::CREATE_MODEL;

    protected $model;
    protected $clienteRuolo;
    protected $controparteRuolo;
    protected $avversarioRuolo;
    protected $creditRecovery;
    protected $s3Connector;
    protected $dbShared;
    protected $dbProvisioning;
    protected $config;

    protected $now;


    function __construct($component_status_id, $db, $dbShared, $dbProvisioning, $subdomainName, $applogger, $s3Connector, $config, $componentRow = NULL, $workflow = NULL)
    {
        parent::__construct($component_status_id, $db, $subdomainName, $applogger, $componentRow, $workflow);

        $this->model = $this->data->model ?? null;
        $this->clienteRuolo = $this->data->ruoloCliente ?? null;
        $this->controparteRuolo = $this->data->ruoloControparte ?? null;
        $this->avversarioRuolo = $this->data->ruoloAvversario ?? null;
        $this->creditRecovery = $this->data->creditRecovery ?? null;

        $this->s3Connector = $s3Connector;
        $this->config = $config;

        $this->dbShared = $dbShared;
        $this->dbProvisioning = $dbProvisioning;

        $this->now = $this->now();
    }

    /**
     * @param $role
     * @param $relation
     * @return null
     */
    private function getSelectedPartsFromRoles($role, $relation)
    {
        $uid = null;
        if (!empty($role) && !empty($relation)){

            $andRole = '';

            if ($role == "-1"){
                $data = array($relation, $this->workflow->file_id);
            }else{
                $andRole = ' AND ap.role_id = ?';
                $data = array($relation, $this->workflow->file_id, $role);
            }


            $uid = $this->db->fetchOne("SELECT a.uniqueid 
                                        FROM anagrafiche a 
                                        INNER JOIN anagrafica_pratica ap ON ap.person_id = a.id
                                        WHERE ap.relazione = ?
                                        AND ap.file_id = ?
                                        $andRole", $data);
        }
        return $uid;
    }


    private function getLastCreditRecovery($fileId){
        return $this->db->fetchOne("SELECT id FROM recuperocrediti_documenti WHERE file_id = ? ORDER BY creation_date DESC LIMIT 1", array($fileId));
    }

    function execute()
    {
        $this->begin();

        $response = array(
            'response'  => FALSE,
            'commento'  => '',
            'documento' => ''
        );

        if (empty($this->model)){
            $response['commento'] = "Non è stato individuato un modello documento valido. ";
            $response['response'] = FALSE;
        }else{
            $params['fUid'] = $this->db->fetchOne('SELECT uniqueid FROM archivio WHERE id = ?', $this->workflow->file_id);
            $params['tUid'] = $this->dbProvisioning->fetchOne('SELECT uniqueid FROM templates WHERE id = ?', $this->model);


            $params['customers'] = $this->getSelectedPartsFromRoles($this->clienteRuolo, 1);
            $params['counterparts'] = $this->getSelectedPartsFromRoles($this->controparteRuolo, 2);
            $params['opponents'] = $this->getSelectedPartsFromRoles($this->avversarioRuolo, 3);
            if (!empty($this->creditRecovery)){
                $params['creditrecoveries'] = ($this->creditRecovery == "-1") ? $this->getLastCreditRecovery($this->workflow->file_id) : $this->creditRecovery;
            }

            /** TODO: manca recupero crediti, altri soggetti, clausole (non c'erano quando fatto componente) */

            $params["saveInDoc"] = true;

            //** I documenti per il momento vengono considerati solo quelli ricevuti in input, senza possibiltà di filtering o selezione */

            $params['documents'] = '';

            $objects = $this->getObtainedArray();

            if (!empty($objects)) {
                foreach ($objects as $key => $value) {

                    if ($key == 'documento') {
                        $array_of_documents = array();
                        if (is_array($value)) {  //PER PIù ALLEGATI
                            foreach ($value as $id_doc) {
                                array_push($array_of_documents, $this->db->fetchOne('SELECT uniqueid FROM documento WHERE id = ?',$id_doc ));
                            }
                        } else {
                            array_push($array_of_documents, $this->db->fetchOne('SELECT uniqueid FROM documento WHERE id = ?',$value ));
                        }
                        $params['documents'] = implode(",",$array_of_documents);
                    }
                }
            }



            $zend_infrastructure = new ZendInfrastructure( $this->db, $this->dbProvisioning, $this->dbShared, $this->applogger, ['id'=> -1, 'isExternal' => true] , $this->subdomainName, $this->config);

            $engine = new Engine($zend_infrastructure, $this->s3Connector);

            // $params['excludingColumns'] = [
            //     'note',
            //     'signature_points',
            //     'signers',
            //     'signature_status',
            // ];

            $document = $engine->work($params);

            if (!empty($document)) {
                $response['response'] = TRUE;
                $response['documento'] = $this->db->fetchOne('SELECT id FROM documento WHERE uniqueid = ?', $document['uniqueid']);
                $response['commento'] = "Documento generato con successo";
            } else {
                $response['response'] = FALSE;
                $response['commento'] = "Non è stato possibile generare il documento";
            }

        }


        $this->setDone();

        if ($this->hasNext()) {
            $this->setResponse($response['response'] == TRUE ? 'true' : 'false');
            unset($response['response']);
            $this->setOutput(json_encode($response));
            $this->setNextComponents();
        }

    }

}
