<?php



namespace  <PERSON><PERSON><PERSON><PERSON><PERSON>\Workflow;

use <PERSON><PERSON><PERSON>om<PERSON>\TasksManager\TasksHandler;

class TaskComponent extends ComponentManager{
	protected $componentType = self::TASK;

	protected $s3Connector;
	protected $dbShared;
	protected $bucketName = "netlex-main";
	function __construct($component_status_id,$db,$dbShared,$subdomainName,$applogger,$s3Connector,$componentRow = NULL, $workflow = NULL){
		parent::__construct($component_status_id,$db,$subdomainName,$applogger,$componentRow, $workflow);
		
		$this->s3Connector 			= $s3Connector;
		
		$this->dbShared 			= $dbShared;
	}
	
	// function createTask(){
	// 	$uid = $this->getUid();
	// 	$fieldList = array(
	// 		'title' 				=> $this->title,
	// 		'text'					=> $this->descrizione,
	// 		'status'				=> 1,
	// 		'created_at'			=> $this->now(),
	// 		'created_by'			=> -1,
	// 		'uniqueid'				=> $uid,
	// 		'output_type'			=> $this->modelId,
	// 		'file_id'				=> $this->workflow->file_id,
	// 		'component_status_id'	=> $this->istanza->id
	// 	);
	// 
	// 	$this->db->insert("taks", $fieldList);
	// 	$optional_data = json_encode(array('task_uid'=>$uid));
	// 	$this->db->update(TABELLA_ISTANZE, array('optional_data'=>$optional_data), array('id=?'=>$component_id));
	// }
	
	function execute(){
		$this->begin();
		$optional_data = $this->getOptionalDataArray();

		if(!empty($optional_data['task'])){
			
			//gestione degli output del task
			$taskId = $optional_data['task'];
			$output_json = $this->db->fetchOne("SELECT output_type FROM tasks WHERE id =?", $taskId);
			
			$output_array = json_decode($output_json,1);
			
			$component_output = array();
			
			//di base la response prenderà il path del false
			$response = "false";
			
			// gestione dell'output del task 
			foreach ($output_array as $key => $output){
				if (!empty($output['workflowOutput'])){
					
					switch($output['type']) {
						case 1:  //commento
							if (empty($component_output['commento'] )){
								$component_output['commento'] = $output['field'];
							}
							$component_output['commento'] .= $output['field'];
							break;
						case 4: //true_false
							//capire come viene fornito il true_false;
							
							//devono essere tutti true per anadare nell'uscita true
							
							//$response = "true" / "false";
							break;
						case 5: //documento
							if (!empty($output['idDocumento'])){
								if (empty($component_output['documento'] )){
									$component_output['documento'] = array($output['idDocumento']);
								}
								array_push($component_output['documento'] , $output['idDocumento']);
								break;
							}
	
						}
				}
			}
			
			
			$this->setDone();
			
			if ($this->hasNext()){
				$this->setOutput(json_encode($component_output));
				$this->setResponse('done');
				$this->setNextComponents();
				
				if (!empty($response)){
					//il task component segue due path di uscita, uno per il suo completamento e uno per il true false
					//della risposta, di default false
					$this->setResponse($response);
					$this->setNextComponents();
				}
			}
		}else{
			//DATA 1
			if (!empty($this->data->title)){
				$title = $this->data->title;
			}else{
				//di default inseriamo vari Id e la pratica
				$title = "Worfklow: ". $this->worklow->id. " Component: " . $this->istanza->id. " Pratica: ". $this->workflow->file_id;
			}
			//DATA 2
			
			$descrizione = empty($this->data->descrizione) ? 'Task generato in automatico tramite workflow' : $this->data->descrizione;
			
			//DATA 3
			if (!empty($this->data->modelloId)){
				$modello = $this->data->modelloId;
			}else{ //nel qual caso non venga selezionato un modello di tipi di output automaticamente verrà chiesto solo true false (completato o rifiutato) per poter procedere nel wf
				//$modello = '[{"type":"4","name":"Risultato","booloptions":{"true":"Completato","false":"Rifiutato"},"required":0,"counter":1}]';
			}
			
			if (!empty($this->data->assignedId)){
				$assigned = $this->data->assignedId;
			}
			
			$params = array(
				'title'=>$title,
				'text'=>$descrizione,
				'status'=>1,
				'outputtype'=>$modello,
				'assigned_to[]'=>$assigned	,
			);
			
			$files =  array(
					
			);
			
			$TH = new stdClass();
			$TH->applogger = $this->applogger;
			$TH->db = $this->db;
			$TH->dbShared = $this->dbShared;
			$TH->s3Connector = $this->s3Connector;  
			$TH->sitekey = $this->subdomainName;
			
			
			$obtained = $this->getObtainedArray();
			
			if (!empty($obtained['documento'])){
				$documenti = $obtained['documento'];

				if (!is_array($documenti)){
					$documenti = array($documenti);
				}
				
				$listOfDocs = array();
				
				$fname = array();
				$ftmpname = array();
				foreach ($documenti as $docId){
					$filename = $this->db->fetchOne("SELECT nomefile FROM documento WHERE id = ?", $docId);
				
					$s3object = $this->s3Connector->getObject($this->bucketName, $this->subdomainName.'/'.'documents'.'/'.$this->pratica.'/'.$filename);
							$content = $s3object['Body'];
					$tempUid = $this->getUid();
					$filePath = '/tmp/taskfiles/' . $tempUid . '/';
					mkdir($filePath, 0777, true);
					file_put_contents($filePath . $filename, $content);
					
					array_push($fname, $filename);
					array_push($ftmpname, $filePath . $filename);
				}
				
				$files['files'] = array(
					"name" => $fname,
					"tmp_name" =>$ftmpname,
				);
				
				array_push($listOfDocs, $docId);
			}

			$tasksHandler = new TasksHandler($TH, array(
				'post' => $params,
				'files' => $files,
				'loggedUserId' => 1,
				'sitekey' =>$this->subdomainName,
			));
			

			$id = $tasksHandler->save();

			//associazione task utente
			$this->db->insert("tasks_utente", array('task_id'=>$id,'assigned_to'=>$assigned));
			
			//associazione task pratica
			$this->db->update("tasks", array('file_id'=>$this->pratica), array('id=?'=>$id));
			
			//associazione task documento
			if (!empty($listOfDocs)){
				foreach ($listOfDocs as $docId){
					$this->db->insert("tasks_documento", array("tasks_id"=>$id,"documento_id"=>$docId));
				}
			}


			$this->sleep(json_encode(array("task"=>$id)));
			//createTask();
		}
	}
}

?>