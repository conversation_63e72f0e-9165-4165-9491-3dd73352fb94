<?php
class PoliswebManager_Polisweb
{
	public function requestError($db)
	{
		$updateComplete = false;
		if(!empty($_REQUEST['requestUid']))
		{
			$update = array('status' => $_REQUEST['status'],
							'error' => $_REQUEST['errorMessage']);
			$where = array('uniqueid = ?' => $_REQUEST['requestUid']);
			$db->update('polisweb_requests', $update, $where);
			$updateComplete = true;
		}
		return $updateComplete;
	}
	
	public function requestUpdate($db, $prewStatus, $status)
	{
		$updateComplete = false;
		if(!empty($_REQUEST['requestUid']))
		{
			$actualStatus = 'SELECT status
							 FROM polisweb_requests
							 WHERE uniqueid = ?';
			$actualStatus = $db->fetchOne($actualStatus, array($_REQUEST['requestUid']));
			
			if($actualStatus == $prewStatus)
			{
				$update = array('status' => $status);
				$where = array('uniqueid = ?' => $_REQUEST['requestUid']);
				$db->update('polisweb_requests', $update, $where);
				$updateComplete = true;
			}
		}
		return $updateComplete;
	}
}
?>