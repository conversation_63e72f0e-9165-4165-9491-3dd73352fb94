<?php
namespace DexmaCommons\MacroManager\InstanceManager;

interface InterfaceInstanceManager
{

    public function run($jsonGraph, $owner, $users, $hearingUniqueid, $fileUniqueid, $impegnoUid, $macroid, $macroDetails,
                        $macroAttivata,
                        $workingSuspension,
                        $dynamic,
                        $contractId);

    public function init($date, $macroId, $type, $workingSuspension, $_weekend, $holiday);

    public function remove($macroinstance, $id);

    public function previewUpdate($macroInstanceUniqueId, $deadlineUniqueId, $initDate, $workingSuspension,
                                  $updateDb, $weekend): array;

}