<?php

namespace <PERSON><PERSON><PERSON>om<PERSON>\MacroManager\InstanceManager;

use Beta\Microsoft\Graph\CallRecords\Model\FailureInfo;
use Carbon\Carbon;
use Fhaculty\Graph\Graph;
use ItemController;
use MacroManager\CarbonMacroHelper;
use Zend_Cache;
use Zend_Registry;

use Util\_Constants\Macro\Macro;

include_once("InterfaceInstanceManager.php");

class InstanceManager implements InterfaceInstanceManager
{

    private const allowMethods = [
        'calculateNewDate' => true
    ];

    private const holidays = array(
        "12-08",
        "12-24",
        "12-25",
        "12-26",
        "01-01",
        "01-06",
        "04-25",
        "05-01",
        "06-02",
        "08-15",
        "11-01",
    );

    protected $dbShared;
    protected $db;
    protected $subdomainName;
    protected $applogger;
    protected $type;
    protected $calendarClient;
    protected $user;
    protected $dbProvisioning;

    public function __construct($user, $db, $dbShared, $dbProvisioning, $subdomainName, $applogger, $calendarClient)
    {
        $this->user = $user;
        $this->db = $db;
        $this->dbShared = $dbShared;
        $this->dbProvisioning = $dbProvisioning;
        $this->subdomainName = $subdomainName;
        $this->applogger = $applogger;
        $this->calendarClient = $calendarClient;

        CarbonMacroHelper::registerCarbonMacros();

    }

    public function __call($method, $arguments)
    {
        if (self::allowMethods[$method]) {
            call_user_func_array([__CLASS__, $method], $arguments);
        } else {
            throw new BadMethodCallException("Undefined method $method");
        }
    }


    /**
     * @param $date
     * @param $macroId
     * @param $type
     * @param $_workingSuspension
     * @param $_weekend
     * @return array
     */
    public function init($date, $macroId, $type, $_workingSuspension, $_weekend, $holiday): array
    {
        $details = null;
        $carbonDate = new Carbon($date);
        $select = "SELECT id, data_decorrenza AS effectiveDate,
                         data_udienza AS hearingDate,
                         link, 
                         sospensione_feriale AS workingSuspension,
                         exclude_weekend AS excludeWeekend,
                         include_holidays AS includeHolidays,
                         tipo AS type";

        if ((int)$type === 0) {
            $select .= ", dinamica AS dynamic";
            $connection = $this->db;
        } else {
            $select .= ", 0 AS dynamic";
            $connection = $this->dbShared;
        }

        $macro = "$select
                  FROM macro
                  WHERE id = ?";

        $macro = $connection->fetchRow($macro, array($macroId));

        $macroDetail = $this->getDetails($macroId, (int)$type);
        $macro["workingSuspension"] = filter_var($_workingSuspension ?? $macro["workingSuspension"], FILTER_VALIDATE_BOOLEAN);
        $macro["excludeWeekend"] = filter_var($_weekend ?? $macro["excludeWeekend"], FILTER_VALIDATE_BOOLEAN);
        $macro["includeHolidays"] = filter_var($holiday ?? $macro["includeHolidays"], FILTER_VALIDATE_BOOLEAN);

        $graph = $this->constructGraph($macroDetail, $carbonDate,
            $macro["workingSuspension"],
            $macro["excludeWeekend"],
            !$macro["includeHolidays"]);


        $vertexs = $graph->getVertices()->getMap();

        $details = array_map(
            function ($item) use ($vertexs) {
                $item['date'] = self::getIsoFormat($vertexs[$item["detailOrder"]]->getAttribute('date'));
                return $item;
            }, $macroDetail);


        return [$macro, $details, $this->getJsonGraph($graph)];
    }

    function getUid(): string{
        return strtoupper(base_convert(microtime(true), 10, 16) . "-" . uniqid(rand()));
    }


    /**
     * @param $jsonGraph
     * @param $owner
     * @param $users
     * @param $hearingUniqueid
     * @param $fileUniqueid
     * @param $impegnoUid
     * @param $macroid
     * @param $macroDetails
     * @param $macroAttivata
     * @param $workingSuspension
     * @return null
     */
    public function run($jsonGraph,
                        $owner,
                        $users,
                        $hearingUniqueid,
                        $fileUniqueid,
                        $impegnoUid,
                        $macroid,
                        $macroDetails,
                        $macroAttivata,
                        $workingSuspension,
                        $dynamic,
                        $contractId
    )
    {
        $result = null;
        $today = date("Y-m-d H:i:s");
        $macroInstanceUid = $this->db->getUid();

        $fileId = null;
        $linkuid = null;

        if (!empty($hearingUniqueid)) {
            [$fileId, $linkuid] = $this->getHearing($hearingUniqueid);
            $result = $hearingUniqueid;
        } else if (!empty($fileUniqueid)) {
            [$fileId, $linkuid] = $this->getFile($fileUniqueid);
            $result = $fileUniqueid;
        }

        if (isset($macroAttivata, $impegnoUid)) {
            $this->updateMacroAttivata($macroAttivata, $impegnoUid);
        }

        // creao array di uniqueid
        $deadlineUniqueId = $this->getDeadlinesUniqueIdList($macroDetails);

        $graphJsonDecoded = json_decode($jsonGraph, false);
        $graph = $this->fromJson2Graph($graphJsonDecoded->vertices, $graphJsonDecoded->edges);

        $graph = $this->insertDeadlineUniqueId($graph, $deadlineUniqueId);

        $this->saveMacroInstance(
            $this->getJsonGraph($graph),
            $macroInstanceUid,
            $workingSuspension,
            $dynamic);

        $groupuid = $this->getUid();
        $macrospiegazioni = $this->db->fetchAll("
            SELECT ms.uniqueid, ms.fatturabile
            FROM macrospiegazioni ms 
            INNER JOIN macro m ON m.id = ms.macro_id 
            WHERE ms.macro_id = ?
            ORDER BY ms.id ASC", [$macroid]);
        $i = 0;
        foreach ($macroDetails as $key => $deadline) {
            $msUid = $macrospiegazioni[$i]['uniqueid'];
            $this->saveDeadline(
                $owner,
                $deadline,
                $today,
                $fileId,
                $macroInstanceUid,
                $linkuid,
                $deadlineUniqueId[$key],
                (int) $macrospiegazioni[$i]['fatturabile'],
                $fileUniqueid,
                $macroid,
                $groupuid,
                $msUid,
                $contractId);
            $i++;
        }
        return $result;
    }

    /**
     * @param $macroInstanceUniqueId
     * @param $deadlineUniqueId
     * @return void
     */
    public function remove($macroInstanceUniqueId, $deadlineUniqueId)
    {
        $model = $this->getModel($macroInstanceUniqueId);

        $graph = $this->fromJson2Graph($model->vertices, $model->edges);

        $vertex = array_values(
            array_filter($graph->getVertices()->getMap(),
                function ($item) use ($deadlineUniqueId) {
                    return $item->getAttribute('uniqueid') === $deadlineUniqueId;
                }
                , ARRAY_FILTER_USE_BOTH
            )
        );

        $graph = $this->removeTerm($graph, $vertex[0]->getId());

        $this->updateMacroModelDb($macroInstanceUniqueId, $graph);

    }

    /**
     * @param $macroInstanceUniqueId
     * @param $id
     * @param $initDate
     * @param $workingSuspension
     * @return array
     */
    public function previewUpdate($macroInstanceUniqueId, $deadlineUniqueId, $initDate, $workingSuspension,
                                  $updateDb, $weekend = false, $holiday = true): array{

        $result = [];
        $model = $this->getModel($macroInstanceUniqueId);
        $graph = $this->fromJson2Graph($model->vertices, $model->edges);

        $vertex = array_values(
            array_filter($graph->getVertices()->getMap(),
                function ($item) use ($deadlineUniqueId) {
                    return $item->getAttribute('uniqueid') === $deadlineUniqueId;
                }
                , ARRAY_FILTER_USE_BOTH
            )
        );

        $this->DFS(
            $graph->getVertex($vertex[0]->getId()),
            null,
            [],
            $result,
            function ($vertex, $_parent, &$result, $args) use ($deadlineUniqueId, $weekend, $holiday, $initDate) {
                $date = $args[0];

                if ($vertex->getAttribute('uniqueid') === $deadlineUniqueId) {
                    $vertex->setAttribute('date', new Carbon($date));
                } else {

                    if ($_parent !== null) {
                        $date = $_parent->getAttribute('date');
                    }

                    $calculateNewDateResult = self::calculateNewDate(
                        1,
                        (int)$vertex->getAttribute('days'),
                        $date,
                        $initDate,
                        $args[1],
                        $weekend,
                        $holiday
                    );
                    $vertex->setAttribute('date', $calculateNewDateResult['date']);

                    $result[$vertex->getAttribute('uniqueid')]['date'] =
                        self::getIsoFormat($vertex->getAttribute('date'));

                    if ($vertex->getAttribute('warningDays') !== null) {
                        $result[$vertex->getAttribute('uniqueid')]['warningDate'] = self::getIsoFormat(
                            $vertex->getAttribute('date')->addDays(-1 * $vertex->getAttribute('warningDays'))
                        );
                    } else {
                        $result[$vertex->getAttribute('uniqueid')]['warningDate'] =
                            self::getIsoFormat($vertex->getAttribute('date'));
                    }
                }
            }
            , $initDate, $workingSuspension);

        if ($updateDb) {
            $this->updateMacroModelDb($macroInstanceUniqueId, $graph);
        }

        return $result;
    }


    /**
     * @param $jsonGraph
     * @param $idElement
     * @param $adjustment
     * @return array
     */
    public function adjust($jsonGraph, $idElement, $adjustment): array
    {
        $result = [];

        $graph = json_decode($jsonGraph, false);

        $graph = $this->fromJson2Graph($graph->vertices, $graph->edges);

        $startTerm = $graph
            ->getVertex($idElement);

        $this->DFS($startTerm, null, [], $result,
            function ($vertex, $_parent, &$result, $args) {
                $date = new Carbon($vertex->getAttribute('date'));
                $newDate = $date->addDays($args[0]);
                $vertex->setAttribute('date', $newDate);
                $result[$vertex->getId()] = self::getIsoFormat($newDate);
            }, $adjustment);

        return [$result, $this->getJsonGraph($graph)] ;
    }


    /**
     * @param $unita
     * @param int $giorni
     * @param $date
     * @param $_workingSuspension
     * @param $_weekend
     * 
     * @return array
     */
    public static function calculateNewDate(int $unita, int $giorni, $date, $initDate, $_workingSuspension, $_weekend, $holiday): array
    {
        $carbonDate = new Carbon($date);
        $initDate = new Carbon($initDate);
        $termDate = $carbonDate;
        $hasWorkingSuspension = false;

        // add Easter dates
        $easterTimestamp = easter_date($termDate->year);
        $easter = new Carbon(date('Y-m-d', $easterTimestamp));
        $easterDates = [];
        $easterDates[] = $easter->format('m-d');
        $easterDates[] = $easter->addDay()->format('m-d');
        $holidaysDates = array_merge($easterDates, self::holidays);

        $termDate = $giorni > 0 ?
                $carbonDate->addWithCondition($unita, $giorni,  $_workingSuspension, $_weekend, $holiday, $holidaysDates) :
                $carbonDate->subWithCondition($unita, $giorni,  $_workingSuspension, $_weekend, $holiday, $holidaysDates);

        return [
            'date' => self::getIsoFormat($termDate),
            'hasWorkingSuspension' => $hasWorkingSuspension
        ];
    }

    /**
     * @param $macroId
     * @param $type
     * @return null
     */
    private function getDetails($macroId, $type)
    {
        $macroDetails = null;

        if ($type === 0) {
            $macroDetails = "SELECT m.ext_visible AS visible,
                                m.descrizione AS description,
								m.unitatempo AS tempo,
								m.giorni AS days,
								m.giorni_avviso AS warningDays,
								m.ordine AS detailOrder,
								m.riferimento_ordine AS reference,
								m.uniqueid,
								m.tiporelazione AS tiporelazione,
								m.utentisingoli AS utentisingoli,
                                m.impegno_id as deadlineType,	
       							m.relazione_id as relation,
                                m.impegno_id as impegno,
                                m.macro_associate,
                                m.templates,
                                m.importante, m.fatturabile, m.category_id, m.status_id
						 FROM macrospiegazioni m
                        /*LEFT JOIN scadenzario s on m.uniqueid = s.macrospiegazioniuid*/
						 WHERE m.macro_id = ?
						 ORDER BY m.ordine";
            $macroDetails = $this->db->fetchAll($macroDetails, array($macroId));
            foreach ($macroDetails as &$detail) {
                if ($detail['macro_associate']) {
                    $macroAssociateUids = json_decode($detail['macro_associate'], false);
                    foreach ($macroAssociateUids as &$macro) $macro = "'$macro'"; // serve per l'IN Clause
                    $macroDb = $this->db->fetchAll('SELECT uniqueid AS id, nome FROM macro WHERE uniqueid IN (' . implode(',', $macroAssociateUids) . ')');
                    $macroDbShared = $this->dbShared->fetchAll('SELECT uniqueid AS id, nome FROM macro WHERE uniqueid IN (' . implode(',', $macroAssociateUids) . ')');
                    $detail['macro_associate'] = json_encode((array_merge($macroDb, $macroDbShared)), JSON_HEX_APOS | JSON_HEX_AMP);
                }

                if ($detail['templates']) {
                    $inCondition = implode(',', json_decode($detail['templates'], false));
                    $templates = $this->dbProvisioning->fetchAll("SELECT id, title FROM templates WHERE id IN ( ? )" , $inCondition);
                    $detail['templates'] = json_encode($templates, JSON_HEX_APOS | JSON_HEX_AMP);
                }
            }
        } else if ($type === 1) {
            $macroDetails = "SELECT m.descrizione AS description,
								m.unitatempo AS tempo,
								m.giorni AS days,
								m.giorni_avviso AS warningDays,
								m.ordine AS detailOrder,
								m.riferimento_ordine AS reference,
								m.uniqueid,	
       							'-1' as relation,
                                '-1' as impegno
						 FROM macrospiegazioni m
						 WHERE m.macro_id = ?
						 ORDER BY m.ordine";

            $macroDetails = $this->dbShared->fetchAll($macroDetails, array($macroId));
            foreach ($macroDetails as &$detail) {
                $deadlineType = $this->db->fetchRow("SELECT tiposcadenza as deadlineType FROM scadenzario WHERE macrospiegazioniuid = ?", $detail['uniqueid']);
                $detail['deadlineType'] = $deadlineType['deadlineType'];
            }
        }
        return $macroDetails;
    }

    /**
     * @param $termDate
     * @return string
     */
    private function getIsoFormat($termDate)
    {
        return (new \Carbon\Carbon)->isoFormat($termDate);
    }

    /**
     * @return array
     */
    private function getHearing($hearingUniqueid): array
    {
        $hearing = "SELECT id, pratica, data, sezione, autorita, istruttore,
									   citta, linkuid
								FROM agenda
								WHERE uniqueid = ?";
        $hearing = $this->db->fetchRow($hearing, array($hearingUniqueid));
        $fileId = $hearing["pratica"];
        $linkuid = $hearing["linkuid"];
        return array($fileId, $linkuid);
    }

    /**
     * @return array
     */
    private function getFile($fileUniqueid): array
    {
        $file = "SELECT id
							 FROM archivio
							 WHERE uniqueid = ?";
        $fileId = $this->db->fetchOne($file, array($fileUniqueid));
        $linkuid = null;
        return array($fileId, $linkuid);
    }

    /**
     * @param $macroAttivata
     * @param $impegnoUid
     * @return void
     */
    private function updateMacroAttivata($macroAttivata, $impegnoUid): void
    {
        if ($macroAttivata->macro_attivata_type === 1) {
            $attivataUid = $this->dbShared->fetchOne("SELECT uniqueid FROM macro WHERE id = ?", $macroAttivata->macro_attivata);

        } else {
            $attivataUid = $this->db->fetchOne("SELECT uniqueid FROM macro WHERE id = ?", $macroAttivata->macro_attivata);
        }

        $macroAttivate = $this->db->fetchOne("SELECT macro_attivate FROM scadenzario WHERE uniqueid = ?", $impegnoUid);

        if ($macroAttivate) {
            $macroAttivate = json_decode($macroAttivate);
            $macroAttivate[] = $attivataUid;
            $macroAttivate = json_encode($macroAttivate);
        } else {
            $macroAttivate = json_encode([$attivataUid]);
        }

        $this->db->update('scadenzario', ['macro_attivate' => $macroAttivate], ['uniqueid = ?' => $impegnoUid]);
    }


    /**
     * @param $graph
     * @param $deadlineUniqueId
     * @return Graph
     */
    private function insertDeadlineUniqueId($graph, $deadlineUniqueId): Graph
    {
        foreach ($graph->getVertices() as $vertex) {
            $vertex->setAttribute('uniqueid', $deadlineUniqueId[$vertex->getId() - 1]);
        }
        return $graph;
    }

    /**
     * @param $macroDetails
     * @return array
     */
    private function getDeadlinesUniqueIdList($macroDetails): array
    {
        $deadlineUniqueId = [];
        foreach ($macroDetails as $item) {
            $deadlineUniqueId[] = $this->db->getUid();
        }

        return $deadlineUniqueId;
    }


    /**
     * @param $owner
     * @param $deadline
     * @param $today
     * @param $fileId
     * @param $macroInstanceUid
     * @param $linkuid
     * @param $uniqueid
     * @return void
     */
    private function saveDeadline($owner, $deadline, $today, $fileId, $macroInstanceUid, $linkuid, $uniqueid, $fatturabile, $fileUid, $macroid = '', $groupuid = '', $macrospiegazioniUid = '', $contractId): void
    {
        $deadlineAssociatedMacros = ! empty($deadline['macro']) ? json_encode(explode(',', $deadline['macro'])) : null;
        $deadlineWarningDate = null;
        $deadlineDate = date("Y-m-d H:i:s", strtotime($deadline["date"]));
        $deadlineWarningDays = $deadline["warning"] ?? 0;
        if ($deadlineWarningDays > 0) {
            $deadlineWarningDate = date("Y-m-d", strtotime("$deadlineDate -$deadlineWarningDays day"));
        }

        $templates = $deadline["templates"];
        $users = $deadline['users'] ?? null;
        $deadline = array(
            'testo' => $deadline['description'],
            'uniqueid' => $uniqueid,
            'data' => $deadlineDate,
            'ora' => '09:00',
            'durata' => 0,
            'annotazioni' => '',
            'evasa' => 0,
            'immessoil' => $today,
            'modificatoil' => $today,
            'immessoda' => $this->user ? $this->user->id : null,
            'modificatoda' => $this->user ? $this->user->id : null,
            'intestatario' => $owner[0],
            'pratica' => $fileId,
            'tiposcadenza' => (int)($deadline['deadlineType'] ?? -1) ,
            'dataavviso' => $deadlineWarningDate,
            'id_parcella' => null,
            'speseimponibili' => 0,
            'speseesenti' => 0,
            'onoraridiritti' => 0,
            'nonevadere' => 0,
            'important' => isset($deadline['important']) && $deadline['important'] == 1 ? 1 : 0,
            'visible' => $deadline['visible'] ?: true,
            'macroinstanceuniqueid' => $macroInstanceUid,
            'macro_id' => $macroid,
            'macrospiegazioniuid' => $macrospiegazioniUid,
            'macrogroupuid' => $groupuid,
            'macro_associate' => $deadlineAssociatedMacros,
            'templates' => !empty($templates) ? $templates : null,
            'category_id' => !empty($deadline['category_id']) ? (int) $deadline['category_id'] : NULL,
            'status_id' => !empty($deadline['status_id']) ? (int) $deadline['status_id'] : NULL,
            'contract_id' => $contractId,
        );


        if ($linkuid !== null) {
            $deadline["linkuid"] = $linkuid;
        } else {
            $deadline["linkuid"] = $this->db->getUid();
        }

        $this->db->insert("scadenzario", $deadline);

        if (\config('app.item_bool')) {
            $itemData = [
                "nome"         => $deadline['testo'],
                "tipo"         => ItemController::ITEM_IMPEGNO,
                "unita_misura" => 2,
                "valore"       => 0.00,
                "quantita"     => 0,
                "addebitabile" => $fatturabile,
                "fatturabile"  => $fatturabile,
                "externalUid"  => $deadline['uniqueid'],
                "externalDate" => $deadline['data'],
                "fileUid"      => $fileUid,
                "uniqueid"     => $this->getUid(),
                "immesso_il"   => date("Y-m-d H:i:s"),
                "immesso_da"   => $deadline['immessoda']
            ];

            $this->db->insert('item', $itemData);
        }

        if (!empty($uniqueid)) {
            $dId = $this->db->fetchOne("
							SELECT id
							FROM scadenzario
							WHERE uniqueid = ?",
                array($uniqueid)
            );

            if ($users !== null && count($users) > 0) {
                foreach ($users as $uId) {
                    $this->db->insert("scadenzarioutente", array("id_scadenza" => $dId, "id_utente" => $uId, "notifica_spedita" => 0));
                }
            } else {
                foreach ($owner as $uId) {
                    $this->db->insert("scadenzarioutente", array("id_scadenza" => (int)$dId, "id_utente" => $uId, "notifica_spedita" => 0));
                }
            }
            // TODO controllare che cavolo era questa cosa e cosa è la variabile $dUid
            if (!empty($this->calendarClient)) {
                // backwards compatibility
                $calendarsClient = is_array($this->calendarClient) ? $this->calendarClient : [$this->calendarClient];
                foreach ($calendarsClient as $calendarClient) {
                    $res = $calendarClient->saveEvent($this->db, $deadline['uniqueid']);
                }
            }

        }
    }


    /**
     * @param $model
     * @param $macroInstanceUid
     * @param $workingSuspension
     * @param $dynamic
     * @return void
     */
    private function saveMacroInstance($model, $macroInstanceUid, $workingSuspension, $dynamic): void
    {
        $macroIstanza = array(
            'uniqueid' => $macroInstanceUid,
            'working_suspension' => $workingSuspension,
            'dynamic' => $dynamic,
            'model' => $model,
        );

        $this->db->insert('macroinstance', $macroIstanza);
    }


    /**
     * @param $macroDetails
     * @param $initDate
     * @param $workingSuspension
     * @return Graph
     */
    private function constructGraph($macroDetails, $initDate, $workingSuspension, $_weekend = false, $holiday = true): Graph
    {
        $graph = new Graph();

        foreach ($macroDetails as $item) {
            if (!$graph->hasVertex($item['detailOrder'])) {
                $vertex = $graph->createVertex($item['detailOrder']);
                // if some parent has working suspension, children does not have working suspension
                $hasParentWorkingSuspension = $this->getHasWorkingSuspensionRecursiveFromItemReference($graph, $item['reference']);

                $calculateNewDateResult = self::calculateNewDate(
                    (int) $item['tempo'],
                    $item['days'],
                    $item["reference"] !== null ? $graph->getVertex($item["reference"])->getAttribute('date') : $initDate,
                    $initDate,
                    $hasParentWorkingSuspension ? false : $workingSuspension, // if some parent has working suspension, don't calculate working suspension for this item because it's already calculated
                    $_weekend,
                    $holiday
                );

                $vertex->setAttribute('date', new Carbon($calculateNewDateResult['date']));
                $vertex->setAttribute('hasWorkingSuspension', $calculateNewDateResult['hasWorkingSuspension']);
                $vertex->setAttribute('amount', $item['tempo']);
                $vertex->setAttribute('reference', $item['reference']);
                $vertex->setAttribute('days', $item['days']);
                $vertex->setAttribute('warning', $item['warningDays']);
            }
            if ($item["reference"] !== null) {
                $graph->getVertex($item["reference"])
                    ->createEdgeTo($graph->getVertex($item["detailOrder"]));
            }
        }
        return $graph;
    }

    private function getHasWorkingSuspensionRecursiveFromItemReference(Graph $graph, ?string $itemReference) : bool
    {
        if ($itemReference === null) {
            return false;
        }

        $item = $graph->getVertex($itemReference);
        $hasWorkingSuspension = $item->getAttribute('hasWorkingSuspension');

        if ($hasWorkingSuspension) {
            return true;
        }

        return $this->getHasWorkingSuspensionRecursiveFromItemReference($graph, $item->getAttribute('reference'));
    }

    /**
     * @param $vertex
     * @param $_parent
     * @param $visited
     * @param $result
     * @param callable $callback
     * @param $args
     * @return void
     */
    private function DFS($vertex, $_parent, $visited, &$result, callable $callback, ...$args): void
    {
        $visited[$vertex->getId()] = "";
        $callback($vertex, $_parent, $result, $args);

        foreach ($vertex->getVerticesEdgeTo() as $neighbour) {
            if (!isset($visited[$neighbour->getId()])) {
                $this->DFS($neighbour, $vertex, $visited, $result, $callback, ...$args);
            }
        }
    }

    private function getJsonGraph(Graph $graph)
    {
        $data = [];
        foreach ($graph->getVertices() as $vertex) {
            $data['vertices'][] = [
                'id' => $vertex->getId(),
                'uniqueid' => $vertex->getAttribute('uniqueid'),
                'amount' => $vertex->getAttribute('amount'),
                'date' => self::getIsoFormat($vertex->getAttribute('date')),
                'days' => $vertex->getAttribute('days'),
                'warningDays' => $vertex->getAttribute('warningDays')
            ];
        }

        foreach ($graph->getEdges() as $edge) {
            $data['edges'][] = [
                'from' => $edge->getVertexStart()->getId(),
                'to' => $edge->getVertexEnd()->getId()
            ];
        }

        return json_encode($data);
    }

    private function fromJson2Graph($vertices, $edges)
    {
        $graph = new Graph();
        foreach ($vertices as $vertexJson) {
            $vertex = $graph->createVertex($vertexJson->id);
            $vertex->setAttribute('date', $vertexJson->date);
            $vertex->setAttribute('uniqueid', $vertexJson->uniqueid);
            $vertex->setAttribute('amount', $vertexJson->amount);
            $vertex->setAttribute('days', $vertexJson->days);
            $vertex->setAttribute('warningDays', $vertexJson->warningDays);
        }

        foreach ($edges as $edge) {
            $graph
                ->getVertex($edge->from)
                ->createEdgeTo($graph->getVertex($edge->to));
        }

        return $graph;
    }

    private function removeTerm(Graph $graph, $id)
    {

        $edgesToRemove = $graph->getEdges()->getEdgesMatch(
            function ($edge) use ($id) {
                return (int)$edge->getVertexStart()->getId() === (int)$id
                    || (int)$edge->getVertexEnd()->getId() === (int)$id;
            }
        );

        foreach ($edgesToRemove as $edgeToRemove) {
            $graph->removeEdge($edgeToRemove);
        }

        $graph->removeVertex($graph->getVertex($id));
        return $graph;
    }

    private function updateMacroModelDb($macroInstanceUniqueId, $graph)
    {
        $this->db->update('macroinstance',
            ['model' => $this->getJsonGraph($graph)],
            ['uniqueid = ?' => $macroInstanceUniqueId]
        );
    }

    /**
     * @param $macroInstanceUniqueId
     * @return mixed
     */
    private function getModel($macroInstanceUniqueId)
    {
        $data[] = $macroInstanceUniqueId;
        $sql = 'select model from macroinstance where uniqueid = ?';
        $model = $this->db->fetchOne($sql, $data);
        return json_decode($model, false);
    }

    private function countAugustOccurrences(Carbon $startDate, Carbon $endDate)
    {
        $augustCount = 0;
    
        $currentDate = $startDate->copy();
        $copyEndDate = $endDate->copy();
        $copyStartDate = $startDate->copy();
    
        if ($startDate->lte($endDate)) {
            while ($currentDate->lte($endDate)) {
                if ($currentDate->month === 8) {
                    $augustCount++;
                    $currentDate = $copyStartDate->addYear();
                    continue;
                }
                $currentDate->addMonthNoOverflow();
            }
        } else {
            while ($currentDate->gte($endDate)) {
                if ($currentDate->month === 8) {
                    $augustCount--;
                    $currentDate = $copyStartDate->subYear();
                    continue;
                }
                $currentDate->subMonthNoOverflow();
            }
        }

        // check if the end date is in august, in that case add or subtract 1 to go after or before august
        $copyEndDate->addMonthsNoOverflow($augustCount);
        if ($copyEndDate->month === 8) {
            if ($startDate->lte($endDate)) {
                $augustCount++;
            } else {
                $augustCount--;
            }
        }
    
    
        return $augustCount;
    }
    

}