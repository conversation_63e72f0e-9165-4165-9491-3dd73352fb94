<?php

namespace MacroManager;

use Carbon\Carbon;
use Carbon\CarbonPeriod;

class CarbonMacroHelper
{
    public static function registerCarbonMacros(){
        self::macroAddDaysWithConditions();
        self::macroSubDaysWithConditions();
    }

    private static function macroAddDaysWithConditions(){
        Carbon::macro('addWithCondition', function (
            $unit,
            $days,
            $workingSuspension,
            $includeWeekends,
            $includeHolidays,
            $holidays) {

            return CarbonMacroHelper::modifyWithCondition($this, 'add', $unit, $days, $workingSuspension, $includeWeekends, $includeHolidays, $holidays);
        });
    }

    private static function macroSubDaysWithConditions(){
        Carbon::macro('subWithCondition', function (
            $unit,
            $days,
            $workingSuspension,
            $includeWeekends,
            $includeHolidays,
            $holidays) {

            return CarbonMacroHelper::modifyWithCondition($this, 'sub', $unit, $days, $workingSuspension, $includeWeekends, $includeHolidays, $holidays);
        });
    }

    public static function modifyWithCondition($date, $method, $unit, $days, $workingSuspension, $includeWeekends, $includeHolidays, $holidays)
    {
        /** @var Carbon $date */

         $count = abs(self::convertDaysTo($days, $unit));
         $dateEnd = null;

        // Gestisci la sospensione lavorativa di agosto
        if ($workingSuspension && $date->month === 8) {
            // if add it's start from 01-09 else 31-07
            $date = $method === 'add' ?
                $date->setDay(31)->setMonth(8) :
                $date->setDay(1)->setMonth(8);
        }

        switch ($unit) {
            case 1:
            case 0:
                $dateEnd = $method === 'add' ? $date->copy()->addDays($count) : $date->copy()->subDays($count);
                break;
            case 2 :
                $dateEnd = $method === 'add' ? $date->copy()->addMonthsNoOverflow($count) : $date->copy()->subMonthsNoOverflow($count);
                break;
            case 3 :
                $dateEnd = $method === 'add' ? $date->copy()->addYearsNoOverflow($count) : $date->copy()->subYearsNoOverflow($count);
                break;
        }

        if ($workingSuspension) {

            $dateStart = $date->copy();

            $countAugusts = $dateStart->greaterThan($dateEnd) ?
                self::countAugusts($dateEnd, $dateStart) :
                self::countAugusts($dateStart, $dateEnd);

            if ($dateStart->month === 8) {
                $countAugusts--;
            }

            $dateEnd->addDays($method === 'add' ? $countAugusts * 31 : -$countAugusts * 31);

            if ($dateEnd->month === 8) {
                $method === 'add' ?
                    $dateEnd->addDays(31 ) :
                    $dateEnd->addDays(-31);
            }
        }

        while (self::shouldSkipDay($dateEnd, $includeWeekends, $includeHolidays, $holidays)) {
            $daysToModify = $method === 'add' ? 1 : -1;
            $dateEnd->addDays($daysToModify);
        }


        return $dateEnd;
    }

    // Converte mesi o anni in giorni
    private static function convertDaysTo($days, $unit)
    {
        switch ($unit) {
            case 2: // Mesi
                return $days / 30;
            case 3: // Anni
                return $days / 365;
            default: // Giorni
                return $days;
        }
    }

    // Verifica se il giorno corrente deve essere saltato
    private static function shouldSkipDay($date, $includeWeekends, $includeHolidays, $holidays)
    {
        return
            ($includeHolidays && in_array($date->format('m-d'), $holidays)) ||
            ($includeWeekends &&
                (
                    $date->isDayOfWeek(Carbon::SUNDAY) ||
                    $date->isDayOfWeek(Carbon::SATURDAY)
                )
            );
    }

    /**
     * @param Carbon $dateStart
     * @param Carbon $dateEnd
     * @return int
     */
    private static function countAugusts(Carbon $dateStart, Carbon $dateEnd): int
    {
        return CarbonPeriod::create($dateStart->copy()->startOfMonth(),
            '1 month', $dateEnd->copy()->endOfMonth())
            ->filter(function ($date) {
                return $date->month === 8;
            })
            ->count();

    }
}
