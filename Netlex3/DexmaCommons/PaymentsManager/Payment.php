<?php
class PaymentsManager_Payment
{
	private static $CUSOTMER_NAME_LENGTH = 30;
	
	private $serialNumberColumnName;
	private $customerColumnName;
	private $amountColumnName;
	private $paidAmountColumnName;
	private $fileColumnName;
	private $externalColumnName;
	private $feeTypeColumnName;
	private $feesTableName;
	
	private $paymentsTableName;
	
	private $amountExplanationsColumnName;
	private $feeExplanationsColumnName;
	private $quickExplanationsExplanationsColumnName;
	private $paymentExplanationsColumnName;
	private $explanationsTableName;
	
	private $descriptionQuickExplanationsColumnName;
	private $typeQuickExplanationsColumnName;
	private $quickExplanationsTableName;
	
	private $db;
	private $applogger;
	
	public function __construct($serialNumberColumnName, $customerColumnName, $amountColumnName, $paidAmountColumnName, 
								$fileColumnName, $externalColumnName, $feeTypeColumnName, $feeTableName,
								$paymentsTableName, $amountExplanationsColumnName, $feeExplanationsColumnName,
								$quickExplanationsExplanationsColumnName, $paymentExplanationsColumnName, $explanationsTableName, 
								$descriptionQuickExplanationsColumnName, $typeQuickExplanationsColumnName, $quickExplanationsTableName,
								$db, $applogger)
	{
		$this->serialNumberColumnName = $serialNumberColumnName;
		$this->customerColumnName = $customerColumnName;
		$this->amountColumnName = $amountColumnName;
		$this->paidAmountColumnName = $paidAmountColumnName;
		$this->fileColumnName = $fileColumnName;
		$this->externalColumnName = $externalColumnName;
		$this->feeTypeColumnName = $feeTypeColumnName;
		$this->feesTableName = $feeTableName;
		
		$this->paymentsTableName = $paymentsTableName;
		
		$this->amountExplanationsColumnName = $amountExplanationsColumnName;
		$this->feeExplanationsColumnName = $feeExplanationsColumnName;
		$this->quickExplanationsExplanationsColumnName = $quickExplanationsExplanationsColumnName;
		$this->paymentExplanationsColumnName = $paymentExplanationsColumnName;
		$this->explanationsTableName = $explanationsTableName;
		
		$this->descriptionQuickExplanationsColumnName = $descriptionQuickExplanationsColumnName;
		$this->typeQuickExplanationsColumnName = $typeQuickExplanationsColumnName;
		$this->quickExplanationsTableName = $quickExplanationsTableName;
		
		$this->db = $db;
		$this->applogger = $applogger;
	}
	
	public function getPaymentDetail($resultsArray, $paymentId)
	{
		$selectFeesExplanations = $this->db->select()
										   ->from(array('e' => $this->explanationsTableName),
												  array('explanation' => "CONCAT(t.uniqueid, ',', e.$this->amountExplanationsColumnName, ',', t.$this->serialNumberColumnName,
																		 ',', CONCAT(SUBSTRING(t.$this->customerColumnName, 1, " . self::$CUSOTMER_NAME_LENGTH . "),
																				CASE WHEN(Length(t.$this->customerColumnName) > " . self::$CUSOTMER_NAME_LENGTH . ") THEN '...' ELSE '' END),
																		 ',', t.$this->amountColumnName - t.$this->paidAmountColumnName, ',', t.$this->feeTypeColumnName)"))
										   ->join(array('t' => $this->feesTableName),
														"e.$this->feeExplanationsColumnName = t.id",
														array())
										   ->where("e.$this->paymentExplanationsColumnName = ?", $paymentId)
										   ->where("e.$this->feeExplanationsColumnName <> 0")
										   ->order(array("t.$this->customerColumnName ASC", "t.$this->serialNumberColumnName ASC"));										   
		$feesExplanations = $this->db->fetchAll($selectFeesExplanations);

		$resultsArray['paymentDetail'] = $this->paymentDetailToString($feesExplanations, 'explanation');
		
		if(!empty($this->quickExplanationsTableName))
		{
			$selectQuickExplanations = $this->db->select()
											->from(array('e' => $this->explanationsTableName),
												   array('explanation' => "CONCAT(qe.uniqueid, ',', e.$this->amountExplanationsColumnName, ',', '',
																		  ',', CONCAT(SUBSTRING(qe.$this->descriptionQuickExplanationsColumnName, 1, " . self::$CUSOTMER_NAME_LENGTH . "),
																				CASE WHEN(Length(qe.$this->descriptionQuickExplanationsColumnName) > " . self::$CUSOTMER_NAME_LENGTH . ") THEN '...' ELSE '' END),
																		  ',', '')"))
										   ->join(array('qe' => $this->quickExplanationsTableName),
														"e.$this->quickExplanationsExplanationsColumnName = qe.id",
														array())
										   ->where("e.$this->paymentExplanationsColumnName = ?", $paymentId)
										   ->where("e.$this->quickExplanationsExplanationsColumnName <> 0")
										   ->order(array("qe.$this->descriptionQuickExplanationsColumnName ASC"));
			$quickExplanations = $this->db->fetchAll($selectQuickExplanations);
			$resultsArray['quickDetail'] = $this->paymentDetailToString($quickExplanations, 'explanation');
		}
		
		return $resultsArray;
	}
	
	private function paymentDetailToString($paymentDetailArray, $key)
	{
		$separator = '';
		$paymentDetail = '';
		foreach($paymentDetailArray as $detail)
		{	
			$paymentDetail .= $separator . $detail[$key];
			if(empty($separator))
			{
				$separator = ';';
			}
		}		
		
		return $paymentDetail;
	}
	
	public function getFee($feeUniqueid, $paymentUniqueid)
	{
		$result = null;
		try 
		{
			$select = $this->db->select()
							   ->from(array('t' => $this->feesTableName),
									  array('t.id',
											'feeNumber' => "t.$this->serialNumberColumnName",
											'customerName' => "CONCAT(SUBSTRING(t.$this->customerColumnName, 1, " . self::$CUSOTMER_NAME_LENGTH . "), 
															   CASE WHEN(Length(t.$this->customerColumnName) > " . self::$CUSOTMER_NAME_LENGTH . ") THEN '...' ELSE '' END)",
											'feeOverdrawn' => "(t.$this->amountColumnName - t.$this->paidAmountColumnName)",
											'feeAmount' => "t.$this->amountColumnName",
											'feePaidAmount' => "t.$this->paidAmountColumnName",
											'tipo' => "t.$this->feeTypeColumnName"))
							  ->where('t.uniqueid = ?', $feeUniqueid);
			$result = $this->db->fetchRow($select);
					
			if(!empty($paymentUniqueid))
			{
				
				$selectExplanations = $this->db->select()
											   ->from(array('e' => $this->explanationsTableName),
													  array('rowAmount' => "e.$this->amountExplanationsColumnName"))
											   ->join(array('p' => $this->paymentsTableName),
													  "e.$this->paymentExplanationsColumnName = p.id",
													  array())
											   ->where('p.uniqueid = ?', $paymentUniqueid)
											   ->where("e.$this->feeExplanationsColumnName = ?", $result['id']);
				
				$explanations = $this->db->fetchRow($selectExplanations);
				$result['rowAmount'] = $explanations['rowAmount'];
			}
			
			unset($result['id']);
		
		}
		catch(Exception $e)
		{
			$this->applogger->info($e->getMessage());
			$result = null;
		}
		
		return $result;
	}
	
	public function getFeesList($paymentId, $paymentType, $fileId, $feeType)
	{
		$selectFees = $this->db->select()
							   ->from(array('t' => $this->feesTableName),
									  array('feeNumber' => "t.$this->serialNumberColumnName",
											'customerName' => "CONCAT(SUBSTRING(t.$this->customerColumnName, 1, " . self::$CUSOTMER_NAME_LENGTH . "),
																		CASE WHEN(Length(t.$this->customerColumnName) > " . self::$CUSOTMER_NAME_LENGTH . ") THEN '...' ELSE '' END)",
											'feeOverdrawn' => "(t.$this->amountColumnName - t.$this->paidAmountColumnName)",
											'feeUniqueid' => 't.uniqueid'))
								->where("t.$this->paidAmountColumnName < t.$this->amountColumnName");
		
		if(!empty($this->feeTypeColumnName) && !is_null($feeType))
		{			
			$selectFees->where("t.$this->feeTypeColumnName = $feeType");
		}
		
		if(!empty($this->externalColumnName))
		{
			if(empty($paymentType))
			{
				$selectFees->where("t.$this->externalColumnName = 0");
			}
			else
			{
				$selectFees->where("t.$this->externalColumnName = 1");
			}
		}
		
		if(!empty($this->fileColumnName))
		{
			if(!empty($fileId))
			{
				$selectFees->where("t.$this->fileColumnName = ?", $fileId);
			}
			else
			{
				$selectFees->where("t.$this->fileColumnName IS NULL");
			}
		}
		
		if(!empty($paymentId))
		{
			$selectFees->where("t.id NOT IN (SELECT e.$this->feeExplanationsColumnName
											 FROM $this->explanationsTableName e
											 WHERE e.$this->paymentExplanationsColumnName = $paymentId)");
		}
		
		$selectFees->order(array("t.$this->customerColumnName ASC", "t.$this->serialNumberColumnName ASC"));
		
		$result = array();
		$result['fees'] = $this->db->fetchAll($selectFees);
		
		if(!empty($this->quickExplanationsTableName))
		{
			$selectQuickExplanations = $this->db->select()
												->from(array('qe' => $this->quickExplanationsTableName),
													   array('customerName' => "CONCAT(SUBSTRING(qe.$this->descriptionQuickExplanationsColumnName, 1, " . self::$CUSOTMER_NAME_LENGTH . "),
																					CASE WHEN(Length(qe.$this->descriptionQuickExplanationsColumnName) > " . self::$CUSOTMER_NAME_LENGTH . ") THEN '...' ELSE '' END)",
															 'feeUniqueid' => 'qe.uniqueid'))
												->where("qe.$this->typeQuickExplanationsColumnName = $paymentType")
												->where("qe.visible = 1");

			if(!empty($paymentId))
			{
				$selectQuickExplanations->where("qe.id NOT IN (SELECT e.$this->quickExplanationsExplanationsColumnName
															   FROM $this->explanationsTableName e
															   WHERE e.$this->paymentExplanationsColumnName = $paymentId)");
			}
			
			$selectQuickExplanations->order(array("qe.$this->descriptionQuickExplanationsColumnName ASC"));
			
			$result['quickExplanations'] = $this->db->fetchAll($selectQuickExplanations);
		}
		else
		{
			$result['quickExplanations'] = array();
		}
		
		return $result;
	}
	
	public function saveExplanations($paymentUniqueid, $paymentDetailField, $quickDetailField)
	{
		$selectPayment = $this->db->select()
								  ->from(array($this->paymentsTableName),
										 array('id'))
								  ->where('uniqueid = ?', $paymentUniqueid);
		
		$payment = $this->db->fetchRow($selectPayment);
		
		$paymentId = $payment['id'];
		
		$this->remove($paymentId);
		
		if(!empty($paymentDetailField))
		{
			$paymentDetail = explode(';', $paymentDetailField);
			
			for($i = 0; $i < count($paymentDetail); $i++) 
			{
				$detail = explode(',', $paymentDetail[$i]);
				
				$selectFee = $this->db->select()
									  ->from(array($this->feesTableName),
											 array('id', 'uniqueid'))
									  ->where('uniqueid = ?', array($detail[0]));
				
				$fee = $this->db->fetchRow($selectFee);
				$feeId = $fee['id'];
				$fieldsList = array();
				$fieldsList[$this->feeExplanationsColumnName] = $feeId;
				$fieldsList[$this->paymentExplanationsColumnName] = $paymentId;
				$fieldsList[$this->amountExplanationsColumnName] = $detail[1];
				$this->db->insert($this->explanationsTableName, $fieldsList);

				$this->updateFee($feeId, $detail[1], '+');
			}
		}
		if(!empty($quickDetailField))
		{
			$quickDetail = explode(';', $quickDetailField);
			
			for($i = 0; $i < count($quickDetail); $i++) 
			{
				$detail = explode(',', $quickDetail[$i]);
				
				$selectQuickExplanation = $this->db->select()
												   ->from(array($this->quickExplanationsTableName),
														  array('id'))
												   ->where('uniqueid = ?', array($detail[0]));

				$quickExplanation = $this->db->fetchRow($selectQuickExplanation);
				$quickExplanationId = $quickExplanation['id'];
				$fieldsList = array();
				$fieldsList[$this->quickExplanationsExplanationsColumnName] = $quickExplanationId;
				$fieldsList[$this->paymentExplanationsColumnName] = $paymentId;
				$fieldsList[$this->amountExplanationsColumnName] = $detail[1];
				$this->db->insert($this->explanationsTableName, $fieldsList);
			}
		}
	}	
	
	public function removeExplanations($paymentUniqueid)
	{
		$selectPayment = $this->db->select()
								  ->from(array($this->paymentsTableName),
										 array('id'))
								  ->where('uniqueid = ?', $paymentUniqueid);
		
		$payment = $this->db->fetchRow($selectPayment);
		
		$this->remove($payment['id']);
	}
	
	private function remove($paymentId)
	{
		$selectExplanations = $this->db->select()
									   ->from(array('e' => $this->explanationsTableName),
											  array("e.$this->feeExplanationsColumnName", "e.$this->amountExplanationsColumnName"))
									   ->where("e.$this->paymentExplanationsColumnName = ?", $paymentId)
									   ->where("e.$this->feeExplanationsColumnName <> 0");
		
		$explanations = $this->db->fetchAll($selectExplanations);
		
		foreach($explanations as $explanation)
		{
			$this->updateFee($explanation[$this->feeExplanationsColumnName], $explanation[$this->amountExplanationsColumnName], '-');
		}
		
		$where["$this->paymentExplanationsColumnName = ?"] = $paymentId;
		$this->db->delete($this->explanationsTableName, $where);
	}
	
	private function updateFee($id, $amount, $operator)
	{
		$fieldsList = array();
		$fieldsList[$this->paidAmountColumnName] =  new Zend_Db_Expr("$this->paidAmountColumnName $operator $amount");
		
		$where['id = ?'] = $id;
		
		$this->db->update($this->feesTableName, $fieldsList, $where);
	}
}
?>
