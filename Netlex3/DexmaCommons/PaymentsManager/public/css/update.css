#totalRowLabel { font-weight: bold; }
#totalRow { font-weight: bold; width: 68px;}
#paymentsContainer { height: 210px; overflow-y: scroll; }
#emptyMessage { font-weight: bold; margin-top: 102px; text-align: center; width: 99%; }
#fees { width: 300px; }
.displayInlineBlock { display: inline-block; }
.paymentBoxHeader {
	background-color: #F5F5F5; 
	-webkit-border-top-left-radius: 4px;
	-webkit-border-top-right-radius: 4px;
	-moz-border-radius-topleft: 4px;
	-moz-border-radius-topright: 4px;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	border: 1px solid #CCCCCC;
	border-bottom: none 0px;
	font-weight: bold;
	margin-left: 5px; 
	margin-right: 5px;
	padding: 10px;
}
.paymentBoxHeaderTitle {
	margin-top: 6px;
}
.paymentBox { 
	-webkit-border-bottom-right-radius: 4px;
	-webkit-border-bottom-left-radius: 4px;
	-moz-border-radius-bottomright: 4px;
	-moz-border-radius-bottomleft: 4px;
	border-bottom-right-radius: 4px;
	border-bottom-left-radius: 4px;
	border: 1px solid #CCCCCC; 
	margin-bottom: 20px;
	margin-left: 5px; 
	margin-right: 5px; 
	padding: 10px;
}
.paymentDetail { height: 20px; padding-bottom: 15px; }
.paymentLabel { padding-right: 10px; overflow: hidden; text-overflow: ellipsis; vertical-align: middle; white-space: nowrap; width: 505px; }
.paymentAmountLabel { width: 20px; }
.paymentTotalRowContainer { height: 20px; padding-top: 15px; }
