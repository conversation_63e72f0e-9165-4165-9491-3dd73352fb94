<link href="/public/PaymentsManager/css/update.css" rel="stylesheet" />
<script src="/public/PaymentsManager/js/update.js"></script>
<input type="hidden" id="paymentDetail" name="paymentDetail" value="" />
<input type="hidden" id="quickDetail" name="quickDetail" value="" />
<div id="paymentDetailContainer">
	<div class="paymentBoxHeader">
		<div class="pull-left paymentBoxHeaderTitle">{t}SPIEGAZIONE{/t}</div>
		<div class="pull-right">
			<select id="fees" name="fees"></select>
			<div id="newReasonDIV" style="display: none;">
				<input id="newReasonInp" type="text">
				<button id="saveReasonBtn" class="btn btn-primary btn-mini" onclick="saveReason(value)" type="button">Salva</button>
				<button id="cancelReasonBtn" class="btn btn-mini" onclick="cancelSaveReason()" type="button">{t}Annulla{/t}</button>
			</div>
			{if !$permissions || $permissions[43]['c']}
				<button id="insertReasonBtn" class="btn btn-primary btn-mini" type="button" onclick="insertReason()" title="{t}Aggiungi Spiegazione{/t}"><i class="icon-plus-sign icon-white"></i></button>
			{/if}
			<input id="associateReasonBtn" class="btn btn-primary" type="button" value="{t}Associa{/t}" onclick="addPayment()" />
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="paymentBox">
		<div id="paymentsContainer"></div>
		<div class="paymentTotalRowContainer">
			<div class="paymentLabel displayInlineBlock"></div><div id="totalRowLabel" class="paymentAmountLabel displayInlineBlock"></div><div id="totalRow" class="displayInlineBlock pagination-right"></div>
		</div>
	</div>
</div>
