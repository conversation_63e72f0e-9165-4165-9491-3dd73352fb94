var feesType = 'fees';
var quickType = 'quick';

function getPrimaryOptgroup()
{
	return $('<optgroup id="primaryOptgroup" label="' + gettext('Spiegazione...') + '"></optgroup>');
}

function getSecondaryOptgroup()
{
	return $('<optgroup id="secondaryOptgroup" label="' + gettext('Altre spiegazioni...') + '"></optgroup>');
}

function fillFees(list)
{
	var feesSelect = $('#fees');
	feesSelect.append('<option value="-1">' + gettext('Selezionare una spiegazione') + '...</option>');
	if(list.fees.length > 0)
	{
		var primaryOptgroup = getPrimaryOptgroup(); 
		$.each(list.fees, function(index, value) 
		{
			drawOption(primaryOptgroup, value.feeUniqueid, value.customerName, value.feeNumber, value.feeOverdrawn, feesType, value.tipo);
		});
		feesSelect.append(primaryOptgroup);
	}
	if(list.quickExplanations.length > 0)
	{
		var secondaryOptgroup = getSecondaryOptgroup();
		$.each(list.quickExplanations, function(index, value) 
		{
			drawOption(secondaryOptgroup, value.feeUniqueid, value.customerName, null, null, quickType, null);
		});
		feesSelect.append(secondaryOptgroup);
	}
}

function insertForDialog(list)
{
	fillFees(list);
	drawMessage(false);
}

function uploadForDialog(list)
{
	fillFees(list);
	drawPaymentDetail();
}

function closeForDialog()
{
	$('#fees').empty();
	$('#fees').popover('hide');
	drawMessage(true);
	resetTotal();
}

function drawPaymentDetail()
{
	var paymentDetailObj = $('#paymentDetail');
	var paymentDetail = paymentDetailObj.val();
	
	var quickDetailObj = $('#quickDetail');
	var quickDetail = quickDetailObj.val();
	
	var total = 0.00;
	if((paymentDetail != null && paymentDetail != '') ||
	   (quickDetail != null && quickDetail != ''))
	{
		if((paymentDetail != null && paymentDetail != ''))
		{
			paymentDetail = paymentDetail.split(';');
			for(var i = 0; i < paymentDetail.length; i++)
			{
				var row = paymentDetail[i].split(',');
			 	
				drawRow(row[0], row[1], row[2], row[3], row[4], feesType, row[5]);
				total += parseFloat(row[1]);
			}
		}
		
		if((quickDetail != null && quickDetail != ''))
		{
			quickDetail = quickDetail.split(';');
			for(var i = 0; i < quickDetail.length; i++)
			{
				var row = quickDetail[i].split(',');
				drawRow(row[0], row[1], null, row[3], null, quickType, null);
				total += parseFloat(row[1]);
			}
		}
		
		paymentDetailObj.val('');
		quickDetailObj.val('');
	}
	else
	{
		drawMessage(false);
		total = null;
	}
	
	drawTotal(total);
}

function validateDetail() {
	var result = true;
	var total = 0.00;

	$('#paymentsContainer :input[type=text]').each(function() {
		var inputValue = parseFloat($(this).val());
		if (!isNaN(inputValue)) {
			total += inputValue;
		}
	});

	total = parseFloat(total.toFixed(2));
	var amount = parseFloat($('#amount').val());

	if (isNaN(amount)) {
		alert(gettext('Il campo Importo del Pagamento non contiene un valore numerico valido.'));
		return false;
	}

	if ((amount >= 0 && total > amount) || (amount < 0 && total < amount)) {
		var message = amount >= 0
			? gettext('Attenzione! La somma dei singoli importi è superiore all\'importo del pagamento')
			: gettext('Attenzione! La somma dei singoli importi è inferiore all\'importo del pagamento');
		alert(message);
		result = false;
	}

	return result;
}

function preparePaymentDetail()
{
	var paymentDetail = new String();
	var quickDetail = new String();
	
    var separator = new String();
    $.each($('#paymentsContainer input[type=text][data-type=fees]'), function()
    {
        paymentDetail += separator + $(this).data('feeuid') + ',' + $(this).val();

        if(separator == '')
        {
            separator = ';';
        }
    });
    var separator = new String();
    $.each($('#paymentsContainer input[type=text][data-type=quick]'), function()
    {
        quickDetail += separator + $(this).data('feeuid') + ',' + $(this).val();

        if(separator == '')
        {
            separator = ';';
        }
    });
	$('#paymentDetail').val(paymentDetail);
	$('#quickDetail').val(quickDetail);
}

function checkAmount($this, type)
{
	var feeUniqueid = $($this).data('feeuid');
	var amount = parseFloat($('#amount').val());
	var total = 0.00;
	$.each($('#paymentsContainer :input[type=text]'), function() 
	{
		if($(this).data('feeuid') != feeUniqueid)
		{
			var singleAmount = parseFloat($(this).val());
			amount -= singleAmount;
			total += singleAmount;
		}
	});
	
	if(amount < 0)
	{
		amount = 0.00;
	}
	
	var rowAmount = $($this).val();
	if(rowAmount != null && rowAmount != '')
	{
		rowAmount = parseFloat(rowAmount);
		if(type == feesType)
		{
			var params = {feeUniqueid: feeUniqueid, paymentUniqueid: $('#uniqueid').val()};
			$.post('/' + controllerName + '/getfee', params, function(data)
			{
				if(data != null && data != false)
				{
					var feePaidAmount = data.feePaidAmount;
					if(data.rowAmount != null && data.rowAmount != '')
					{
						feePaidAmount = feePaidAmount - data.rowAmount;
					}
					
					var overdrawn = data.feeAmount - feePaidAmount;
					
					if(rowAmount > overdrawn && overdrawn < amount)
					{
						amount = parseFloat(overdrawn);
					}
					else if(rowAmount > overdrawn && overdrawn > amount)
					{
						amount = amount;
					}
					else if(rowAmount > amount)
					{
						amount = amount;
					}
					else
					{
						amount = rowAmount;
					}
					
					$($this).val(amount.toFixed(2));
					total += amount;
					overdrawn -= amount;
					
					$('#span' + feeUniqueid).html(overdrawn.toFixed(2));
					
					drawTotal(total);
				}
			}, 'json');
		}
		else if(type = quickType)
		{
			if(rowAmount > amount)
			{
				amount = amount;
			}
			else
			{
				amount = rowAmount;
			}
			$($this).val(amount.toFixed(2));
			total += amount;
			drawTotal(total);
		}
	}
	else
	{
		$($this).val(0.00);
		drawTotal(total);
	}
}

function drawMessage($clearContainer)
{
	if($clearContainer)
	{
		$('#paymentsContainer').empty();
	}
	else
	{
		var emptyMessage = '<div id="emptyMessage">' + gettext('NESSUNA SPIEGAZIONE PRESENTE') + '</div>';
		$('#paymentsContainer').html(emptyMessage);
	}
}

function drawRow(feeUniqueid, amount, serialNumber, customerName, overdrawn, type, tipo)
{
	feeLabel = 'fattura';
	if (3 == tipo) {
		feeLabel = 'fattura elettronica';
	}
	
	var paymentDetail = '<div class="paymentDetail" id="' + feeUniqueid + '"> ' +
							'<div class="paymentLabel displayInlineBlock">' + customerName;
							if(serialNumber !== null && overdrawn !== null)
							{
								paymentDetail += ' - ' + feeLabel + ' n&deg;' + serialNumber + ' (Scoperto &euro; <span id="span' + feeUniqueid + '">' + overdrawn + '</span>)';
							}
	paymentDetail += 		'</div>' +
							'<div class="paymentAmountLabel displayInlineBlock">&euro;</div>' +
							'<input class="input-mini pagination-right" type="text" value="' + amount + '" data-feeuid="' + feeUniqueid + '" data-type="' + type + '" onblur="checkAmount(this, \'' + type +'\')" onkeydown="return isKeyboardNumber(event, true)" /> ' +
							'<button class="btn btn-danger btn-mini" type="button" onclick="removePayment(\'' + feeUniqueid + '\', \'' + type + '\', \'' + escape(customerName) + '\')">' + gettext('Rimuovi') + '</button>' +
						'</div>';
	$('#paymentsContainer').append(paymentDetail);
}

function drawTotal(total)
{
	if(total == null)
	{
		resetTotal();
	}
	else
	{
		$('#totalRowLabel').html('&euro;');
		$('#totalRow').html(number_format(total.toString(), 2, ',', '.'));
	}
}

function drawOption(optgroup, feeUniqueid, customerName, feeNumber, feeOverdrawn, type, tipoFattura)
{
	var option = '<option value="' + feeUniqueid + '" data-type="' + type + '">' + customerName;
	
	if (tipoFattura && tipoFattura == '3') {
		feeLabel = 'fattura elettronica';
	} else feeLabel = 'fattura';
	
	if(feeNumber !== null && feeOverdrawn !== null)
	{
		option += ' - ' + feeLabel + ' n&deg;' + feeNumber + ' (' + gettext('Scoperto') + ': &euro; ' + feeOverdrawn + ')';
	}
	
	option += '</option>';
	optgroup.append(option);
}

function resetTotal()
{
	$('#totalRowLabel').empty();
	$('#totalRow').empty();
}

function addPayment()
{
	var option = $('#fees option:selected');
	var feeUniqueid = option.val();
	var type = option.data('type');
	if(feeUniqueid != -1)
	{
		var amount = parseFloat($('#amount').val());
		var total = 0.00;
		var paymentDetail = $('#paymentsContainer :input[type=text]');
		if(paymentDetail.length > 0)
		{
			$.each(paymentDetail, function()
			{
				var singleAmount = parseFloat($(this).val());
				amount -= singleAmount;
				total += singleAmount;
			});
			if(amount < 0)
			{
				amount = 0;
			}
		}
		else
		{
			drawMessage(true);
		}
		
		if(type == feesType)
		{
			var params = {feeUniqueid: feeUniqueid, paymentUniqueid: $('#uniqueid').val()};
			$.post('/' + controllerName + '/getfee', params, function(data)
			{
				if(data != null && data != false)
				{
					var overdrawn = data.feeOverdrawn;
					
					if(data.rowAmount != null && data.rowAmount != '')
					{
						var paidAmount = data.feePaidAmount - data.rowAmount;
						overdrawn = data.feeAmount - paidAmount;
					}
					
					if(parseFloat(overdrawn) < amount)
					{
						amount = parseFloat(overdrawn); 
					}
					
					overdrawn -= amount;

					drawRow(feeUniqueid, amount.toFixed(2), data.feeNumber, data.customerName, overdrawn.toFixed(2), feesType, data.tipo);
					
					total += amount;
					drawTotal(total);
					option.remove();
					
					var primaryOptgroup = $('#primaryOptgroup');
					if(primaryOptgroup.children().length == 0)
					{
						primaryOptgroup.remove();
					}
				}
			}, 'json');
		}
		else if(type = quickType)
		{
			drawRow(feeUniqueid, amount.toFixed(2), null, option.text(), null, quickType, null);
			
			total += amount;
			drawTotal(total);
			option.remove();
			
			var secondaryOptgroup = $('#secondaryOptgroup');
			if(secondaryOptgroup.children().length == 0)
			{
				secondaryOptgroup.remove();
			}
		}
	}
	else
	{
		alert(gettext('Attenzione! E\' necessario selezionare una spiegazione') + '.');
	}
}

function removePayment(feeUniqueid, type, customerName)
{
	var fees = $('#fees');
	var total = 0.00;
	var paymentDetail = $('#paymentsContainer :input[type=text]');
	if(paymentDetail.length > 0)
	{
		$.each(paymentDetail, function() 
		{
			if($(this).data('feeuid') != feeUniqueid)
			{
				total += parseFloat($(this).val());
			}
		});
	}
	else
	{
		drawMessage(false);
		total = null;
	}
	
	if(type == feesType)
	{
		var params = {feeUniqueid: feeUniqueid, paymentUniqueid: $('#uniqueid').val()};
		$.post('/' + controllerName + '/getfee', params, function(data)
		{
			if(data != null && data != false)
			{
				var overdrawn = data.feeOverdrawn;
				
				if(data.rowAmount != null && data.rowAmount != '')
				{
					var paidAmount = data.feePaidAmount - data.rowAmount;
					overdrawn = data.feeAmount - paidAmount;
				}
				
				overdrawn = parseFloat(overdrawn);
				var primaryOptgroup = $('#primaryOptgroup');
				if(primaryOptgroup.length == 0)
				{
					primaryOptgroup = getPrimaryOptgroup();
					//fees.prepend(primaryOptgroup);
					$('#fees option:first').after(primaryOptgroup);
				}
				drawOption(primaryOptgroup, feeUniqueid, data.customerName, data.feeNumber, overdrawn.toFixed(2), type, null);
				sortOptgroup(primaryOptgroup, fees);
				
				$('#' + feeUniqueid).remove();
				drawTotal(total);
			}
		}, 'json');
	}
	else if(type == quickType)
	{
		var secondaryOptgroup = $('#secondaryOptgroup');
		if(secondaryOptgroup.length == 0)
		{
			secondaryOptgroup = getSecondaryOptgroup();
			fees.append(secondaryOptgroup);
		}
		drawOption(secondaryOptgroup, feeUniqueid, customerName, null, null, type, null);
		sortOptgroup(secondaryOptgroup, fees);
		
		$('#' + feeUniqueid).remove();
		drawTotal(total);
	}
}

function sortOptgroup(optgroupObj, fees)
{
	var options = optgroupObj.find('option');
	optgroupObj.html(options.sort(function(prew, next)
	{
		return prew.text.toLowerCase() == next.text.toLowerCase() ? 0 : prew.text.toLowerCase() < next.text.toLowerCase() ? -1 : 1;
	}));
	
	fees.val($('#fees option:first').val());
}

function paymentsOnReady()
{
	$('#fees').popover({
		content: gettext('Selezionare una spiegazione e con "Associa" aggiungerla all\'elenco sottostante'),
		placement: 'top',
		title: gettext('Aggiungere spiegazione'),
		trigger: 'manual'
	})
	.on('show shown hide hidden', function(e){
		e.stopPropagation();
	});
}

function showPopover(element)
{
	if($(element).val() == 0)
	{
		$('#fees').popover('show');
	}
}

function hidePopover()
{
	$('#fees').popover('destroy');
}

function validateNewReason()
{
	return checkField('newReasonInp', gettext('Inserisci una spiegazione'));
}

function insertReason()
{
	$('#insertReasonBtn').hide();
	$('#associateReasonBtn').prop("disabled", true);
	$('#fees').hide();
	$('#newReasonDIV').show().css("display", "inline");
}

function saveReason(value)
{
	if(validateNewReason())
	{
		//Salvo la spiegazione
		$.ajax({
			url: '/spiegazioni/save',
			method: 'POST',
			data: {
				"description": $("#newReasonInp").val(),
				"type": 1
			},
			async: false
		});
		cancelSaveReason();
		//Aggiorno la select (solo se è una spesa)
		if($("#paymentDirectionLabel").children().text() == "Spesa")
		{
			var reasons = $.ajax({
				url: '/spiegazioni/getreasons',
				method: 'POST',
				async: false
			}).responseText;
			reasons = JSON.parse(reasons);
			$("#fees").text("");
			var html = "<option value='-1'>Selezionare una spiegazione...</option><optgroup id='secondaryOptgroup' label=gettext('Altre spiegazioni') + '...'>";
			reasons.forEach(row => {
				html += "<option value='" + row.uniqueid + "' data-type='quick'>" + row.description + "</option>";
			});
			html += "</optgroup>";
			$("#fees").append(html);
		}
	}
}

function cancelSaveReason()
{
	$("#newReasonDIV").hide();
	$('#insertReasonBtn').show();
	$('#fees').show();
	$('#associateReasonBtn').prop("disabled", false);
}
