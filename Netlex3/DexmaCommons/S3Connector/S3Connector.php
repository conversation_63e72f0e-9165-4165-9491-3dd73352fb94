<?php

require SITE_ROOT . "/vendor/autoload.php";

use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;
use Aws\Exception\AwsException;

class S3Connector_S3Connector
{
    //TODO sostituisce la costante CustomControllerAction::$DOCUMENTS
    public const  BASE_DOCUMENT_PATH = 'documents';
    public const  BASE_DOCUMENT_CONTRACT_PATH = 'contracts';

	private $s3;

	public function __construct($region, $key, $secret)
	{

		$this->s3 = new S3Client([
			'version' => 'latest',
			'region'  => $region,
			'credentials' => ['key' => $key, 'secret' => $secret]
		]);

	}

    public function storeAndPut($bucket, $uploadTmpDir,$amazonDir, $fileName, $fileContents){

        $fileTmp = $uploadTmpDir . $fileName;
        if (!file_exists($uploadTmpDir)) {
            mkdir($uploadTmpDir, 0777, true);
        }

        file_put_contents($fileTmp, $fileContents);
        $this->putObject($bucket, $amazonDir . $fileName, $fileTmp);
        return $fileTmp;
    }

	public function getObject($bucket, $filekey)
	{

		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));
		$result = $this->s3->getObject(array(
			'Bucket' => $bucket,
			'Key'    => $filekey
		));
		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));

		return $result;

	}

	public function getBody($bucket, $filekey)
	{
        $result = $this->getObject($bucket, $filekey);

        return $result["Body"];
	}

	public function getSize($bucket, $filekey)
	{

		$result = $this->getObject($bucket, $filekey);
		return $result["ContentLength"];
	}

	public function getObjectPrivateLink($bucket, $filekey)
	{

		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));
		$cmd = $this->s3->getCommand('GetObject', [
						'Bucket' => $bucket,
						'Key'    => $filekey
		]);
		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));
		$request = $this->s3->createPresignedRequest($cmd, '+15 minutes');

		// Get the actual presigned-url
		$presignedUrl = (string) $request->getUri();

		return $presignedUrl;

	}

	public function putObject($bucket, $filekey, $filepath)
	{

		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));
		$result = $this->s3->putObject([
			'Bucket' => $bucket,
			'Key'    => $filekey,
			'SourceFile'   => $filepath
		]);
		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));

		return $result;

	}

	public function saveStringAsFile($bucket, $filekey, $filecontent)
	{

		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));
		$result = $this->s3->putObject([
			'Bucket' => $bucket,
			'Key'    => $filekey,
			'Body'   => $filecontent
		]);
		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));

		return $result;

	}

	public function putFolder($folderpath, $bucket, $folderkey, $options = [])
	{

		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));
		$this->s3->uploadDirectory($folderpath, $bucket, $folderkey, $options);
		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));

	}

	public function getGIterator($bucket, $sitekey)
	{

		return $this->s3->getIterator("ListObjects", array("Bucket" => $bucket, "Prefix" => $sitekey));

	}

	public function copyObject($targetBucket, $targetKeyname, $sourceBucket, $sourceKeyname)
	{

		$result = array();
		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));

		try {

			$this->s3->copyObject(array(
				'Bucket' => $targetBucket,
				'Key' => $targetKeyname,
				'CopySource' => "{$sourceBucket}/{$sourceKeyname}",
			));

		} catch (S3Exception $e) {

			$result['error'] = $e->getMessage();

		}

		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));

		return $result;

	}

	public function deleteObject($bucket, $filekey)
	{

		$result = array();
		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));

		try {

			$this->s3->deleteObject(array(
				'Bucket' => $bucket,
				'Key' => $filekey,
			));

		} catch (S3Exception $e) {

			$result['error'] = $e->getMessage();

		}

		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));

		return $result;

	}

	public function doesObjectExist($bucket, $filekey)
	{

		$response = $this->s3->doesObjectExist($bucket, $filekey);
		return $response;

	}

	public function listObjects($bucket, $prefix)
	{

		spl_autoload_unregister(array("Zend_loader_autoloader", "autoload"));
		$results  = $this->s3->listObjects(array(
			'Bucket' => $bucket,
			'Prefix' => $prefix,
		));
		spl_autoload_register(array("Zend_loader_autoloader", "autoload"));

		return $results;

	}

    public function getDocumentsFileKey(?string $nomefile, ?int $archivioId = null, ?int $contractId = null): ?string
    {
        return $this->getFileKey($nomefile, self::BASE_DOCUMENT_PATH, $archivioId, $contractId);
    }

    public function getDocumentsPath(?int $archivioId = null, ?int $contractId = null): string
    {
        return $this->getPath(self::BASE_DOCUMENT_PATH, $archivioId, $contractId);
    }

    private function getFileKey(?string $nomefile, string $path, ?int $archivioId = null, ?int $contractId = null): ?string
    {
        if (! $nomefile) {
            return null;
        }

        $fileKey = $this->getPath($path, $archivioId, $contractId);

        $fileKey .= '/' . $nomefile;

        return $fileKey;
    }

    private function getPath(string $path, ?int $archivioId = null, ?int $contractId = null): string
    {
        $fileKey = SITEKEY . '/' . $path;

        if ($archivioId) {
            $fileKey .=  '/' . $archivioId;
        }

        if ($contractId) {
            $fileKey .= '/' . self::BASE_DOCUMENT_CONTRACT_PATH . '/' . $contractId;
        }

        return $fileKey;
    }

}
