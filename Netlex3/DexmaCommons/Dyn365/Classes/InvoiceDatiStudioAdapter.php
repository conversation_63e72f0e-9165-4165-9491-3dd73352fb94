<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Dyn365\Classes;

use <PERSON><PERSON><PERSON>om<PERSON>\Dyn365\Interfaces\InvoiceSubjectServiceInterface;
use Netlex3\Software\patterns\Domains\Anagrafiche\Services\AnagraficaService;
use Netlex3\Software\patterns\Domains\DatiStudio\Services\DatiStudioService;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;

class InvoiceDatiStudioAdapter implements InvoiceSubjectServiceInterface
{

    private $infrastructure;
    private $service;

    public function __construct(InfrastructureInterface $infrastructure)
    {
        $this->infrastructure = $infrastructure;
        $this->service = DatiStudioService::getInstance($this->infrastructure);
    }


    public function findById(int $id): InvoiceSubject
    {
        $data = $this->service->findDatiStudioByLawyerId($id);

//        $result['address'] = $defaultAddress['via'] ?? '';
//        $result['cap'] = $defaultAddress['cap'] ?? '';
//        $result['city'] = $defaultCity['nome'] ?? '';
//        $result['nation'] = $cityNationService->findNationById((int)$defaultCity['nazione_id'])['alpha3'] ?? '';
//        $result['province'] = $defaultCity['provincia'] ;
//        $result['region'] = $defaultAddress['regione'] ?? '';
//        $result['location'] = '';

        $result = [
            'type' => 'O',
            'company_name' => strlen($data['nome']) > 60 ?
                substr($data['nome'], 0, 60)
                : $data['nome'],
            'partitaiva' => $data['partitaiva'],
            'codicefiscale' => $data['codicefiscale'],
            'codiceb2b' =>  $data['codice_destinatario_af'],
            'address' =>  $data['indirizzo'],
            'cap' =>  $data['cap'],
            'city' =>  $data['citta'],
            'nation' =>  'ITA',
            'province' =>  '',
            'region' =>  '',
            'location' =>  '',
            'company_name_extension' => strlen($data['nome']) > 60 ?
                substr($data['nome'], 61, strlen($data['nome']))
                : ''
        ];

        return new InvoiceSubject($result);
    }


}