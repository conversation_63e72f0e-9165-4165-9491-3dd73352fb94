<?php

namespace <PERSON><PERSON><PERSON>om<PERSON>\Dyn365\Classes;

use Carbon\Carbon;
use DexmaCommons\Dyn365\Constants\InvoiceConst;

class Invoice
{

    private $movementList;
    private $subject;

    public function __construct(array $loggedUser, array $data , InvoiceSubject $subject , array $movements)
    {
        foreach($data as $key => $val) {
            // only accept keys that have explicitly been defined as class member variables
            //if(property_exists($this, $key)) {
            $this->{$key} = $val;
            //}
        }

        $this->logged_user = strtoupper(
            sprintf("%s %s",$loggedUser['nomepersonale'], $loggedUser['cognomepersonale'])
        );
        $this->data = (new Carbon($this->data))->format('d/m/Y');
        $this->movementList = $movements;
        $this->subject = $subject;
        $this->project = $data['archive_code'];

       // TODO capire perche' non c'e il totale_non_imponibile

        $this->total_ =  array_reduce($movements, function($acc, $item){  return $acc+=$item->total; }, 0);

        $this->paid = InvoiceConst::PAID;

        // FIXED FIELDS
        $this->type = InvoiceConst::TYPE_SALES;
        $this->is_vat_calculated = InvoiceConst::IS_VAT_CALCULATED;
        $this->causal_sale = InvoiceConst::CAUSAL_SALE;
        $this->responsibility_area = InvoiceConst::RESPONSIBILITY_AREA;
        $this->responsibility_centre = InvoiceConst::RESPONSIBILITY_CENTRE;
        $this->cost_center = InvoiceConst::COST_CENTER;
        $this->accounting_detail = InvoiceConst::ACCOUNTING_DETAIL;
        $this->activity = InvoiceConst::ACTIVITY;

    }

    public function convertTo($translator){
        return $translator->translate();
    }

    public function getProperties(){
         $result['invoice'] = (array)get_object_vars($this);
         unset($result['invoice']['movementList']);
         unset($result['invoice']['subject']);

         $result['subject'] = $this->subject->getProperties();

         foreach ($this->movementList as $movement){
             $result['movements'][] = $movement->getProperties();
         }

         return $result;
    }

}