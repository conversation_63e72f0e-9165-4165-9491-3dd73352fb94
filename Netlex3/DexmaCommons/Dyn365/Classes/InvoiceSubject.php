<?php

namespace Dexma<PERSON>ommons\Dyn365\Classes;

class InvoiceSubject
{
    public function __construct(array $data)
    {

        foreach($data as $key => $val) {
            // only accept keys that have explicitly been defined as class member variables
            //if(property_exists($this, $key)) {
            $this->{$key} = $val;
            //}
        }
    }

    public function getProperties() : array{
        return (array)get_object_vars($this);
    }
}