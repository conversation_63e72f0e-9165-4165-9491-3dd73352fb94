<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Dyn365\Classes;

use <PERSON><PERSON><PERSON>om<PERSON>\Dyn365\Interfaces\InvoiceSubjectServiceInterface;
use Netlex3\Software\patterns\Domains\Anagrafiche\Services\AnagraficaService;
use Netlex3\Software\patterns\Domains\CityNation\Services\CityNationService;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;

class InvoiceAnagraficaAdapter implements InvoiceSubjectServiceInterface
{

    private $infrastructure;
    private $service;

    public function __construct(InfrastructureInterface $infrastructure)
    {
        $this->infrastructure = $infrastructure;
        $this->service = AnagraficaService::getInstance($this->infrastructure);
    }

    public function findById(int $id): InvoiceSubject
    {
      $data =  $this->service->findAnagraficaById([$id])[0];

      if(!empty($data['external_sw_id'])){
          $result = [
              'external_sw_id'  => $data['external_sw_id'],
              'payment_method' => $data['modalita_pagamento']
          ];

          return new InvoiceSubject($result);
      }

      if($this->getType($data) === 'P'){
          $result = array_merge([
              'type' => $this->getType($data),
              'external_sw_id' => null,
              'codiceb2b' => $data['codiceb2b'],
              'nome' => $data['nome'],
              'cognome' => $data['cognome'],
              'partitaiva' => $data['partitaiva'],
              'codicefiscale' => $data['codicefiscale'],
              'payment_method' => $data['modalita_pagamento']
          ],
          $this->getAddress($data),
          $this->getContacts($data)
          );

      }

        if($this->getType($data) === 'O'){
            $result = array_merge([
                'type' => $this->getType($data),
                'external_sw_id' => null,
                'codiceb2b' => $data['codiceb2b'],
                'company_name' => strlen($data['denominazione']) > 60 ?
                    substr($data['denominazione'], 0, 60)
                    : $data['denominazione'],
                'partitaiva' => $data['partitaiva'],
                'codicefiscale' => $data['codicefiscale'],
                'payment_method' => $data['modalita_pagamento'],
                'company_name_extension' => strlen($data['denominazione']) > 60 ?
                    substr($data['denominazione'], 61, strlen($data['denominazione']))
                    : ''
            ],
                $this->getAddress($data),
                $this->getContacts($data)
            );

        }

        return new InvoiceSubject($result);
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    private function getType(array &$data): string
    {
        return (int)$data['tipo'] === 60 ? 'P' : 'O';
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    private function getContacts(array $data): array
    {
        $contatti = json_decode($data['contatti'], true);

        $result['email_pec'] = $contatti["7"] ?? null;
        $result["email"] = $contatti["6"] ?? null;
        return $result;
    }

    private function getAddress(array $data): array
    {
        $cityNationService = CityNationService::getInstance($this->infrastructure);

        $indirizzi = json_decode($data['indirizzi'], true);
        $defaultAddress = $indirizzi["9"] ?? null;

        $defaultCity = $cityNationService->findCityById((int)$defaultAddress['citta']);

        $result['address'] = $defaultAddress['via'] ?? '';
        $result['cap'] = $defaultAddress['cap'] ?? '';
        $result['nation'] = $cityNationService->findNationById((int)$defaultCity['nazione_id'])['alpha3'] ?? '';
        $result['province'] = $defaultCity['provincia'] ?? '';
        $result['region'] = $defaultAddress['regione'] ?? '';
        $result['location'] = $defaultCity['nome'] ?? '';

        return $result;
    }
}