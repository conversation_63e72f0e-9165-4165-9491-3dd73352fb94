<?php

namespace DexmaCommons\Dyn365\Classes;

class DynTranslate
{
    /**
     * @var string[][]
     */
    private $transcode;
    private $data;

    /**
     * @param Invoice $invoice
     * @param string[][] $TRANSCODE
     */
    public function __construct(Invoice $invoice, array $TRANSCODE)
    {
        $this->transcode = $TRANSCODE;
        $this->data = $invoice->getProperties();
    }

    public function translate() : array {
        $result['invoice'] = $this->translateSection($this->data['invoice'], 'invoice');
        $result['subject'] = $this->translateSection($this->data['subject'],'subject');

        foreach ($this->data['movements'] as $movement){
            $result['movements'][] = $this->translateSection($movement,'movement');
        }

        return $result;

    }

    private function translateSection(array $data, string $section)
    {
        $keys = array_keys($data);

        $result = [];
        foreach ($this->transcode[$section] as $key => $item){
            if(in_array($key, $keys)) {
                $result[$item] = strval($data[$key]) ?? '';
            }
        }

        return $result;
    }
}