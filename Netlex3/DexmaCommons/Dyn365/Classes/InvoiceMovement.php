<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Dyn365\Classes;

use <PERSON><PERSON><PERSON>om<PERSON>\Dyn365\Constants\InvoiceMovementsConst;

class InvoiceMovement
{

    public function __construct(int $rowNumber, array $request_params)
    {
        foreach($request_params as $key => $val) {
            // only accept keys that have explicitly been defined as class member variables
            //if(property_exists($this, $key)) {
            $this->{$key} = $val;
            //}
        }


        $this->numberRow = $rowNumber;
        $this->unita_prezzo = 1;
        $this->total = ((float)$this->importo * (float)$this->quantita);
        $this->total_iva = (float)$this->iva > 0.00 ?
            sprintf("%.2f", round(((float)$this->importo * (float)$this->quantita) * ((float)$this->iva/100),2)) :
            "0.00";

        $this->iva = (int)$this->iva === 0 ? $this->vat_code : $this->iva;

        // FIXED FIELDS

        $this->iva_scorporato = InvoiceMovementsConst::IVA_SCORPORATO;
        $this->responsibility_area = InvoiceMovementsConst::RESPONSIBILITY_AREA;
        $this->responsibility_centre = InvoiceMovementsConst::RESPONSIBILITY_CENTRE;
        $this->cost_center = InvoiceMovementsConst::COST_CENTER;
        $this->budget_category = InvoiceMovementsConst::BUDGET_CATEGORY;
        $this->activity = InvoiceMovementsConst::ACTIVITY;


    }

    public function getProperties() : array{
        return get_object_vars($this);
    }
}