<?php

namespace <PERSON><PERSON><PERSON>om<PERSON>\Dyn365\Classes;

use <PERSON><PERSON><PERSON>om<PERSON>\Dyn365\Interfaces\InvoiceSubjectServiceInterface;
use Netlex3\Software\patterns\Domains\EntiPa\Services\EntiPaService;
use Netlex3\Software\patterns\Interfaces\InfrastructureInterface;

class InvoicePaSubjectAdapter implements InvoiceSubjectServiceInterface
{

    private $infrastructure;

    public function __construct(InfrastructureInterface $infrastructure)
    {
        $this->infrastructure = $infrastructure;
    }

    public function findById(int $id): InvoiceSubject
    {
        $service = EntiPaService::getInstance($this->infrastructure);

        $data = $service->findEntePaById($id);
        $dataEntePa = $service->findPaEntePaById((int)$data['ente_id']);

        $result = [
            'type' => 'O',
            'company_name' => strlen($dataEntePa['denominazione']) > 60 ?
                substr($dataEntePa['denominazione'], 0, 60)
                : $dataEntePa['denominazione'],
            'partitaiva' => $data['partitaiva'] ?? '',
            'codicefiscale' => $data['codice_fiscale'],
            'codiceb2b' =>  $dataEntePa['codice_ipa'],
            'address' =>  $data['indirizzo'],
            'cap' =>  $data['cap'],
            'city' =>  $data['comune'],
            'nation' =>  'ITA',
            'province' =>  $data['provincia'],
            'region' =>  $data['regione'],
            'location' =>  $data['comune'],
            'company_name_extension' => strlen($dataEntePa['denominazione']) > 60 ?
                substr($dataEntePa['denominazione'], 61, strlen($dataEntePa['denominazione']))
                : ''
        ];

        return new InvoiceSubject($result);

    }
}