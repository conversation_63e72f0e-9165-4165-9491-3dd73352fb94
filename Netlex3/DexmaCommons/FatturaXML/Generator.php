<?php
class FatturaXML_Generator
{
	private $xml;
	private $localErrors = array();
	private $structuredArray  = array();
	private $newSchema;

	public function __construct($newSchema=false)
	{
		putenv('XML_CATALOG_FILES=' . __DIR__ . DIRECTORY_SEPARATOR . 'catalog.xml');
		$this->newSchema = $newSchema;
	}

	private function setDatiTrasmissione2($IdPaese,$IdCodice,$ProgressivoInvio,$FormatoTrasmissione,$CodiceDestinatario) {					// Function that save the problems raised when Generating/Checking the XML file in the proper Array
		$temp["DatiTrasmissione"] = array(
				"IdTrasmittente"=> array(
						"IdPaese" => $IdPaese,
						"IdCodice" => $IdCodice
				),
				"ProgressivoInvio" => $ProgressivoInvio,
				"FormatoTrasmissione" => $FormatoTrasmissione,
				"CodiceDestinatario" => $CodiceDestinatario,
		);
	}

	private function setDatiTrasmissione($IdPaese,$IdCodice,$ProgressivoInvio,$FormatoTrasmissione,$CodiceDestinatario) {					// Function that save the problems raised when Generating/Checking the XML file in the proper Array
		$temp["DatiAnagrafici"] = array(
				"IdFiscaleIVA" => array(
						"IdPaese" => "IT",
						"IdCodice" => '01234567890'
				),
				"Anagrafica" => array(
						"Denominazione" => "Societa' alpha S.r.l.",		// o questa
						//"Nome" => '01234567890',						// o queste due
						//"Cognome" => "IT",
				),
				"RegimeFiscale" => "RF01"
		);
		$temp["Sede"] = array(
				"Indirizzo" => "Via Roma",
					"NumeroCivico" => '42',
					"CAP" => "00166",
					"Comune" => 'Roma',
					"Nazione" => "IT",
		);

	}

	private function composeXML($args,$node){		// Recursive function to create the XML - Input: current data to insert, current node of the XML
		foreach($args as $key => $value) {
			if(is_array($value)) {
				if(!is_numeric($key)){
					$temp = $this->xml->createElement($key);
					$subnode = $node->appendChild($temp);
					$this->composeXML($value,$subnode);
				}
				else{
					if($key==0)
						$this->composeXML($value,$node);
					else
					{
						$temp = $this->xml->createElement($node->nodeName);
						$node = $node->parentNode->appendChild($temp);
						$this->composeXML($value,$node);
					}

				}
			}
			else {
				$temp = $this->xml->createElement($key);
				$subnode = $this->xml->createTextNode($value);
				$temp->appendChild($subnode);
				$node->appendChild($temp);
			}
		}
	}

	private function saveErrors() {					// Function that save the problems raised when Generating/Checking the XML file in the proper Array
		$errors = libxml_get_errors();
		foreach ($errors as $error) {
			$tok = strtok($error->message, "'");
			$tok = strtok("'");
			$this->localErrors[$tok] = $error->code.': '.$error->message;
		}
		libxml_clear_errors();
	}

	public function getErrors(){
		return $this->localErrors;
	}

	public function generateXML($array, $version="FPA12")
	{

		// setting Error Array and XML
		$this->localErrors = array();
		$this->xml =  new DOMDocument("1.0", 'UTF-8');

		$root = $this->xml->createElement("p:FatturaElettronica");
		$this->xml->appendChild($root);
		$root->setAttribute( 'xmlns:ds', 'http://www.w3.org/2000/09/xmldsig#' );
		$root->setAttribute('xmlns:p', 'http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2');
		$root->setAttribute( 'xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance' );
		$root->setAttribute('versione', $version);

		$this->composeXML($array, $root);

		// Enable user error handling
		libxml_use_internal_errors(true);
		// Set User Agent
		$opts = array('http' => array('user_agent' => 'PHP libxml agent'));
		$context = stream_context_create($opts);
		libxml_set_streams_context($context);

		// This is a workaround to a problem that seems to have the Validator, saving and reloading the XML will fix this
		$doc = new DOMDocument();
		$doc->loadXML( $this->xml->saveXML() );
		$doc->formatOutput = true;

		// Validate the XML using the XSD schema ******** CHANGE NAME WITH THE FINAL ONE
		$schema = $this->newSchema ? 'fatturapa_v1.2.3.xsd' : 'fatturapa_v1.2.xsd';
		if (!$doc->schemaValidate(__DIR__ . DIRECTORY_SEPARATOR . $schema)) {
			$this->saveErrors();
			return false;
		} else {
			// If no errors, print the XML
			return $doc->saveXML();
		}
	}
}
