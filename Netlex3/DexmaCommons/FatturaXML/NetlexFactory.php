<?php

use Util\_Constants\Fees\Fees as FeesConstants;

class FatturaXML_NetlexFactory
{
	private $XML;
	private $db;
	private $dbShared;
	private $s3Connector;
	private $oneDriveHandler;
	private $openText;
	private $provisioningRow;
	private $loggedUser;
	private $netlexSettings;
	private $applogger;
	private $sitekey;

	protected $movimentoBolloVirtualeDescrizione =  'Imposta di bollo';
	protected $movimentoForfettarioDescrizione = 'Importo a forfait come da contratto';
	protected $movimentoBolloVirtualeImporto = '2.00';

	private $datiDocumentiCorrelati = array(
		'DatiOrdineAcquisto' => 'dati_ordine_',
		'DatiContratto' => 'dati_contratto_',
		'DatiConvenzione' => 'dati_convenzione_',
		'DatiRicezione' => 'dati_ricezione_',
		'DatiFattureCollegate' => 'dati_fatture_'
	);

	const MAIN_BUCKET = "netlex-main";
	//const MAIN_BUCKET = "netlex-main-testing";
	const DOCUMENTS = 'documents';
	const INVOICESPA = "invoicespa";
	const CREDITNOTESPA = 'creditnotespa';
	const DEBITNOTESPA = 'debitnotespa';

    const ARRAY_DOC_TYPE_AUTO_BILL = [
        'TD16',
        'TD17',
        'TD18',
        'TD19',
        'TD20',
        'TD28',
    ];

	protected $cordafix = array(
		'E25E4E17020-16172104515CB871E7CC5A7',
		'E25E4E8C0DC-19182906035CB87217BE30E',
		'E25E4EC81A3-7277304105CB872305B86C',
		'E25E4F109ED-397379725CB8724E131AF',
		'E25E4F4FB39-15814844505CB87267DFB2C',
		'E25E4F92B6A-12018080105CB8728358F6B',
		'16A307FA769-14605412665CB872996DA95');

	protected $avvalaiafix = array(
		'E29B07BBDA5-12726825005CD15166B6FD5',
		'16A91A6728D-10572961735CD15185BCC6C',
		'E29B088A0F3-20113707825CD151BB33AB0',
		'E29B084C73E-16029632605CD151A1EEAE4');


	public function __construct($constants, $db, $dbShared, $provisioningRow, $s3Connector, $loggedUser, $netlexSettings, $applogger, $oneDriveHandler=null, $openText=null)
	{
		$this->db = $db;
		$this->dbShared = $dbShared;
		$this->provisioningRow = $provisioningRow;
		$this->s3Connector = $s3Connector;
		$this->oneDriveHandler = $oneDriveHandler;
		$this->openText = $openText;
		$this->loggedUser = $loggedUser;
		$this->netlexSettings = $netlexSettings;
		$this->applogger = $applogger;
		$this->officeSettings = $this->db->fetchRow("SELECT * FROM office_settings");

		$this->sitekey = $constants['SITEKEY'];
		foreach($constants as $name => $value) {
			if (!defined($name)) {
				define($name, $value);
			}
		}
	}

	// FIX numerazione in stampa a e xml
//	public function setSezionale($fee, $getDefault = false)
//    {
//        if ( !empty($fee['sezionale']) && $fee['sezionale'] != -1 ) {
//            $datiSezionale = $this->db->fetchRow('SELECT tipo, valore FROM tabellasezionalifatture WHERE id=?', $fee['sezionale']);
//            switch ($datiSezionale['tipo']) {
//                // case 1:
//                // 	$fee['sezionale'] = '-' . $fee['anno'];
//                // 	break;
//                case 2:
//					if (!empty($fee['numerazione'])) {
//						$fee['sezionale'] = '-'.$fee['numerazione']. '/' . $fee['anno'] . '-Bis';
//					}else{
//						$fee['sezionale'] = '/' . $fee['anno'] . '-Bis';
//					}
//                break;
//                case 3:
//					if (!empty($fee['numerazione'])) {
//						$fee['sezionale'] = '-'.$fee['numerazione']. '/' . $fee['anno'] . '/' . $datiSezionale['valore'];
//					}else{
//						$fee['sezionale'] = '/' . $fee['anno'] . '/' . $datiSezionale['valore'];
//					}
//                break;
//                case 4:
//					if (!empty($fee['numerazione'])) {
//						$fee['sezionale'] = '-'.$fee['numerazione'].'/' . substr($fee['anno'], -2) . $datiSezionale['valore'];
//					}else{
//						$fee['sezionale'] = '/' . substr($fee['anno'], -2) . $datiSezionale['valore'];
//					}
//                break;
//            }
//
//       	} else {
//			if (!empty($getDefault)) {
//				if (!empty($fee['numerazione'])) {
//					$fee['sezionale'] ='-'. $fee['numerazione'].  '/' . $fee['anno'];
//				} else {
//					$fee['sezionale'] = '/' . $fee['anno'];
//				}
//			} else {
//				if (!empty($fee['numerazione'])) {
//					$fee['sezionale'] ='-'. $fee['numerazione'].  '/' . $fee['anno'];
//				} else {
//					unset($fee['sezionale']);
//				}
//			}
//		}
//
//		return $fee;
//	}

	 public function setSezionale($fee, $getDefault = false)
     {
         if ( !empty($fee['sezionale']) && $fee['sezionale'] != -1 ) {
             $datiSezionale = $this->db->fetchRow('SELECT tipo, valore FROM tabellasezionalifatture WHERE id=?', $fee['sezionale']);
             switch ($datiSezionale['tipo']) {
                 // case 1:
                 // 	$fee['sezionale'] = '-' . $fee['anno'];
                 // 	break;
                 case 2:
                     $fee['sezionale'] = '/' . $fee['anno'] . '-Bis';
                     break;
                 case 3:
                     $fee['sezionale'] = '/' . $fee['anno'] . '/' . $datiSezionale['valore'];
                     break;
                 case 4:
                     $fee['sezionale'] = '/' . substr($fee['anno'], -2) . $datiSezionale['valore'];
                     break;
             }

             $fee['sezionale'] = !empty($fee['numerazione']) ? '-' . $fee['numerazione'] . $fee['sezionale'] : $fee['sezionale'];

         } else if ( !empty($fee['numerazione']) ) {
             $fee['sezionale'] = '-' . $fee['numerazione'] . '/' . $fee['anno'];
         } else {
	 		if (!empty($getDefault)) {
	 			$fee['sezionale'] = '/' . $fee['anno'];
	 		} else {
	 			unset($fee['sezionale']);
	 		}
	 	}

	 	return $fee;
	 }

	public function generateXml($uid, $PAid = NULL, $invioUid = NULL, $groupVoices = NULL, $isDemo = false)
	{
		$result = array();
		$columns = array(
				'p.*',
				'DATE(p.data) as data',
			'CASE WHEN p.esigibilita_iva = "I" THEN "IVA ad esigibilità immediata"
					WHEN p.esigibilita_iva = "D" THEN "IVA ad esigibilità differita"
					WHEN p.esigibilita_iva = "S" THEN "Scissione dei pagamenti (D.L. n. 50 del 24 aprile 2017)" END AS esigibilitaIvaLabel',
		);

		$invoice = $this->db->fetchRow(
			'SELECT ' . implode(', ', $columns) . ' FROM parcella p WHERE p.uniqueid = ?',
			$uid
		);

		$invoice = $this->setSezionale($invoice, true);

		$emittenteId = $invoice['avvocatoemittente'];
		if($invoice['office']) {
		    if($invoice['id_officesettings']){
                $studioAssociato = $this->db->fetchRow('SELECT * FROM office_settings WHERE id='.$invoice['id_officesettings']);
            }else{
                $studioAssociato = $this->officeSettings;
            }
			//$this->officeSettings;
			$tipoRitenuta = 2;
			$referenceType = $invoice['tipo'];
			$paIdCodice = $studioAssociato['codice_fiscale'];
			$paIdCodiceDatiAnagrafici = $studioAssociato['partita_iva'];
			$codiceFiscaleDatiAnagrafici = $studioAssociato['codice_fiscale'];

			if($invoice['office_iban']) {
				$user['iban'] = $invoice['office_iban'];
				//aggiunge abi, cab e bic se l'iban selezionato fa parte degli stessi dati di pagamento
				$ibanList = json_decode($studioAssociato['iban'], true);
				if(!empty($ibanList)){
					foreach($ibanList as $entry){
						if($entry['iban'] == $invoice['office_iban']){
							if(!empty($entry['abi'])){
								$user['abi'] = $entry['abi'];
							}
							if(!empty($entry['cab'])){
								$user['cab'] = $entry['cab'];
							}
							if(!empty($entry['bic'])){
								$user['bic'] = $entry['bic'];
							}
							break;
						}
					}
				}
			} else {
				/**
				 * Se nella creazione della parcella non è stato specificato l'iban, prende il default impostato in office_settings
				 * Utile solo per retrocompatibilità
				 */
				$ibanList = json_decode($studioAssociato['iban'], true);
				if (!empty($ibanList)) {
					foreach($ibanList as $entry) {
						if($entry['isDefault']) {
							$user['iban'] = $entry['iban'];
							if(!empty($entry['abi'])){
								$user['abi'] = $entry['abi'];
							}
							if(!empty($entry['cab'])){
								$user['cab'] = $entry['cab'];
							}
							if(!empty($entry['bic'])){
								$user['bic'] = $entry['bic'];
							}
							break;
						}
					}
				}
			}

			$tipoCassa = $this->dbShared->fetchOne('SELECT codice FROM tipo_cassa WHERE id = ?', $studioAssociato['tipo_cassa']);
			$tipoCassa2 = $this->dbShared->fetchOne('SELECT codice FROM tipo_cassa WHERE id = ?', $studioAssociato['tipo_cassa_2']);
			$regimeFiscale = $this->dbShared->fetchOne('SELECT codice FROM regime_fiscale WHERE id = ?', $studioAssociato['regime_fiscale']);
		} else {
			$user = $this->db->fetchRow(ltrim('
				SELECT u.id, u.pa_tipo_ritenuta, u.pa_id_codice, u.pa_id_codice_da, u.pa_codice_fiscale_da, d.regime_fiscale_id, d.payments_data, d.tipo_cassa_id, d.nascondi_dati_pagamento_xml, d.tipo_cassa_2
				FROM utente u
				INNER JOIN datistudio d ON d.avvocato = u.codiceavvocato
				WHERE u.tipoutente = 1 AND u.codiceavvocato = ?'),
				$emittenteId
			);
            $bankAccountsDetails = json_decode($user['payments_data'], true);

            if((int)$invoice['office'] === 0) {
                $result = array_values(array_filter($bankAccountsDetails, function ($item) use ($invoice) {
                    return $item['iban'] === $invoice['office_iban'];
                }))[0];

                $user['iban'] = $result['iban'];
                $user['abi'] = $result['abi'];
                $user['cab'] = $result['cab'];
                $user['bic'] = $result['bic'];
            }


			$referenceType = $invoice['tipo'];
			$tipoRitenuta = $user['pa_tipo_ritenuta'];
			$paIdCodice = $user['pa_id_codice'];
			$paIdCodiceDatiAnagrafici = $user['pa_id_codice_da'];
			$codiceFiscaleDatiAnagrafici = $user['pa_codice_fiscale_da'];
			$tipoCassa = $this->dbShared->fetchOne('SELECT codice FROM tipo_cassa WHERE id = ?', $user['tipo_cassa_id']);
			$tipoCassa2 = $this->dbShared->fetchOne('SELECT codice FROM tipo_cassa WHERE id = ?', $user['tipo_cassa_2']);

			$regimeFiscale = $this->dbShared->fetchOne('SELECT codice FROM regime_fiscale WHERE id = ?', $user['regime_fiscale_id']);
		}
		if ($invoice['tipo_documento']) {
			$tipoDocumento = $invoice['tipo_documento'];
		} else {
			if ($invoice['tipo'] == TYPE_PA_INVOICE || $invoice['tipo'] == TYPE_PR_INVOICE) {
				$tipoDocumento = 'TD01';
			} else if ($invoice['tipo'] == TYPE_PA_CREDIT_NOTE || $invoice['tipo'] == TYPE_PR_CREDIT_NOTE) {
				$tipoDocumento = 'TD04';
			}
		}

		if ($tipoRitenuta == 0) {
			$result['errors']['tipoRitenuta'] = true;
		}

		if (! $this->isFEEnabled()) {
            $result['errors']['notEnabled'] = true;
            if ($isDemo) {
                $result['errors']['isDemo'] = true;
            }
		} else {
			$freezed = $invoice;
			$freezed['movimenti'] = array();

			$personaFisica = (1 == $tipoRitenuta);

			if ($PAid) {
				$where = array('ip.id', $PAid);
			} else {
				if (TIPO_INTESTATARIO_PA == $invoice['tipointestatario']) {
					$where = array('ip.id', $invoice['codiceintestatario']);
				} else {
					$table = TIPO_INTESTATARIO_CONTROPARTE == $invoice['tipointestatario']? 'controparte' : 'cliente';
					$codicefiscalePA = $this->db->fetchRow(
						"SELECT p.codicefiscale, p.partitaiva
						FROM anagrafiche p
						WHERE p.id = ?",
						$invoice['codiceintestatario']
					);
					$where = array('ip.codice_fiscale', strtoupper($codicefiscalePA['codicefiscale']) ?: $codicefiscalePA['partitaiva']);
				}
			}

			$fakeXML = FALSE;
			if($referenceType == TYPE_PA_INVOICE || $referenceType == TYPE_PA_CREDIT_NOTE || ($referenceType == TYPE_E_DEBIT_NOTE && TIPO_INTESTATARIO_PA == $invoice['tipointestatario'])){
				$cessionarioCommittente = $this->dbShared->fetchAll(
					"SELECT ip.id, ip.codice_fiscale, ip.codice_pa, CONCAT(ipe.denominazione, ' ', ip.denominazione) AS denominazione, ip.regione, ip.comune, ip.provincia, ip.indirizzo, ip.cap, ip.id_fiscale_iva
					FROM indice_pa ip
                    LEFT JOIN indice_pa_enti ipe ON ipe.id = ip.ente_id
					WHERE " . $where[0] . ' = ?',
					$where[1]
				);

				if (1 === count($cessionarioCommittente)) {
					$cessionarioCommittente = $cessionarioCommittente[0];
				} else {
					// Nel caso in cui la PA venga ricercata per codice fiscale e ce ne sia + di una, allora non generiamo l'xml, l'ufficio verrà scelto dopo
					$fakeXML = TRUE;
					$cessionarioCommittente = array(
						'id' => NULL,
						'codice_pa' => NULL,
						'codice_fiscale' => NULL,
						'denominazione' => NULL,
						'indirizzo' => NULL,
						'cap' => NULL,
						'comune' => NULL,
						'provincia' => NULL,
					);
				}
			}
			else if ($referenceType == TYPE_PR_INVOICE || $referenceType == TYPE_PR_CREDIT_NOTE || ($referenceType == TYPE_E_DEBIT_NOTE && in_array($invoice['tipointestatario'], array(TIPO_INTESTATARIO_CONTROPARTE, TIPO_INTESTATARIO_CLIENTE, TIPO_INTESTATARIO_AUTOFATTURA)))) {
                $table = TIPO_INTESTATARIO_CONTROPARTE == $invoice['tipointestatario'] ? 'controparte' : 'cliente';


                if (TIPO_INTESTATARIO_AUTOFATTURA == $invoice['tipointestatario'] && $invoice['tipo_cessionario_af'] == 1) {
                    $idStudio = !empty($invoice['id_officesettings']) ? $invoice['id_officesettings'] : "";
                    if($invoice['office']){
                        $resultcommittente = $this->db->fetchRow('SELECT nome AS denominazione, CONCAT(indirizzo," ", civico) as indirizzo, cap, citta AS comune, provincia, 
                                                    codice_fiscale, partita_iva AS piva, codice_destinatario_af AS codice_pa
                                            FROM office_settings WHERE id = ?', $idStudio);
                    }else{
                        $resultcommittente = $this->db->fetchRow('SELECT nome AS denominazione, indirizzo, cap, citta AS comune, provincia, 
                                                    codicefiscale AS codice_fiscale, partitaiva AS piva, codice_destinatario_af AS codice_pa
                                            FROM datistudio WHERE avvocato = ?', $invoice['avvocatoemittente']);
                    }
                    /*$resultcommittente = $this->db->fetchROW("SELECT id, fiscalcode_af as codice_fiscale, cod_destinatario_af as codice_pa, denominazione_af as denominazione, indirizzo_af as indirizzo, partita_iva_af as piva, comune_af as comune, provincia_af as provincia, cap_af as cap, nazione_af as nazione
                                                                FROM tabellaavvocati
                                                                WHERE id=?", $invoice['immessoda']);*/
                } else {
                    $resultcommittente = $this->db->fetchRow(
                        "SELECT c.id, c.nome, c.cognome, c.codicefiscale as codice_fiscale, c.codiceb2b as codice_pa, c.denominazione, c.indirizzi, c.partitaiva as piva, c.contatti, c.banca
                                        FROM anagrafiche c 
                                        WHERE c.id = ?", $invoice['codiceintestatario']);
                }

				if(!empty($resultcommittente['banca'])){
					$datipagamento = $this->db->fetchOne("SELECT datipagamento FROM bancacassa WHERE id = ?", $resultcommittente['banca']);
					if(!empty($datipagamento)){
						$datipagamento = json_decode($datipagamento, 1);
						$user['iban'] = $datipagamento['iban'] ? $datipagamento['iban'] : '';
						$user['abi'] = $datipagamento['abi'] ? $datipagamento['abi'] : '';
						$user['cab'] = $datipagamento['cab'] ? $datipagamento['cab'] : '';
						$user['bic'] = $datipagamento['bic'] ? $datipagamento['bic'] : '';
					}
				}


				$contatti = json_decode($resultcommittente['contatti'],1);
				if (!empty($contatti['7'])) {
					$resultcommittente['email_pec'] = $contatti['7'];
				}else{
					$resultcommittente['email_pec'] = '';
				}

				$indirizzi = json_decode($resultcommittente['indirizzi'], 1);
                if (!empty($indirizzi['9'])) {
                    $resultcommittente['indirizzo'] = $indirizzi['9']['via'];
                    $resultcommittente['cap'] = $indirizzi['9']['cap'];
                    if (!empty($indirizzi['9']['citta'])) {
                        $cittarow = $this->db->fetchRow('SELECT * FROM tabellacitta WHERE id = ?', array($indirizzi['9']['citta']));
                        $resultcommittente['comune'] = $cittarow['nome'];
                        //$resultcommittente['citta_id'] = $cittarow['id'];
                        $resultcommittente['provincia'] = $cittarow['provincia'];
                    }
                    if (!empty($indirizzi['9']['nazione'])) {
                        $resultcommittente['nazione'] = $indirizzi['9']['nazione'];
                    }
                }


				unset($resultcommittente['contatti']);
				unset($resultcommittente['indirizzi']);

				//
				// "SELECT c.id, c.codicefiscale as codice_fiscale, c.codiceb2b as codice_pa, c.nome as denominazione, c.indirizzo, c.cap, tc.nome as comune, c.partitaiva as piva, tc.provincia, c.email_pec, c.nazione
				// FROM anagrafiche c LEFT JOIN tabellacitta tc ON tc.id = c.citta
				// WHERE c.id = ?", $invoice['codiceintestatario']

				$cessionarioCommittente = $resultcommittente;
				unset($resultcommittente);
                $nazioneCommittente = $this->dbShared->fetchOne("SELECT codice FROM nazioni WHERE id = ?", $cessionarioCommittente['nazione']);
				$cessionarioCommittente['nazione'] = $invoice['tipointestatario'] == 5 && $invoice['tipo_cessionario_af'] == 1 ? 'IT' : $nazioneCommittente;
				if($invoice['target_type'] == 1 || $invoice['target_type'] == 0)
					$committenteTarget = $cessionarioCommittente['codice_fiscale'];
				elseif($invoice['target_type'] == 2)
					$committenteTarget = $cessionarioCommittente['piva'];

				if(empty($cessionarioCommittente['codice_pa'])) {
					$cessionarioCommittente['codice_pa'] = '0000000';
                    if($invoice['target_mode'] == 2 || $invoice['target_mode'] == 0){
                        $cessionarioCommittente['codice_pa'] = '0000000';
                    } elseif($invoice['target_mode'] == 1){
                        $cessionarioCommittente['codice_pa'] = $cessionarioCommittente['codice_pa'];
                    }
                }
                if($invoice['target_mode'] == 5){
                    if(strpos($invoice['intestatario'],'San Marino')){
                        $cessionarioCommittente['codice_pa'] = '2R4GTO8';
                    }else{
                        $cessionarioCommittente['codice_pa'] = 'XXXXXXX';
                    }
                }
			}

			if (!$cessionarioCommittente) {
				$this->applogger->info("ERRORE CESSIONARIO COMMITTENTE");
			} else {
				try {
					$this->db->beginTransaction();

					$data = array(
						'parcella_id' => $invoice['id'],
						'pa_id' => $referenceType == TYPE_PR_INVOICE || $referenceType == TYPE_PR_CREDIT_NOTE || ($referenceType == TYPE_E_DEBIT_NOTE && in_array($invoice['tipointestatario'], array(TIPO_INTESTATARIO_CONTROPARTE, TIPO_INTESTATARIO_CLIENTE, TIPO_INTESTATARIO_AUTOFATTURA))) ? null : $cessionarioCommittente['id'],
						'progressivo' => $invoice['progressivo'],
						'anno' => $invoice['anno'],
						'sezionale' => $invoice['sezionale'],
						'avvocatoemittente' => $invoice['avvocatoemittente'],
						'created_by' => $this->loggedUser->id,
						'created_at' => date('Y-m-d H:i:s'),
						'tipo' => $referenceType,
						'agyo' => $invoice['office'] ? 4 : 1,
						'group_voices' => $groupVoices ? true : false
					);

					if ($invioUid) {
						$data['uniqueid'] = $invioUid;
					}

					$invioUid = $this->db->save('parcelle_pa', $data);
					$invio = $this->db->fetchRow('SELECT id, riferimento_amministrazione, modalita_pagamento FROM parcelle_pa WHERE uniqueid = ?', $invioUid);
					$progressivoInvio = $invio['id'];
					$riferimentoAmministrazione = $invio['riferimento_amministrazione'];

					if($invoice['office']){
						$cedentePrestatore = $this->db->fetchRow(
							'SELECT nome AS denominazione, indirizzo, cap, citta AS comune, provincia, codice_fiscale AS codicefiscale, partita_iva AS partitaiva, civico, codice_destinatario_af AS codice_pa, agyo_id AS company_cf
							FROM office_settings');

						$cedentePrestatore['indirizzo'] = $cedentePrestatore['indirizzo'] . " " . $cedentePrestatore['civico'];
					} else {
						$cedentePrestatore = $this->db->fetchRow(
							'SELECT d.nome AS denominazione, d.codicefiscale, d.partitaiva, d.indirizzo, d.cap, d.citta AS comune, d.provincia,codice_destinatario_af AS codice_pa, agyo_id AS company_cf
							FROM datistudio d
							WHERE d.avvocato = ?',
							$emittenteId
						);
					}

					if ($personaFisica) {
// 						$nomeAvvocato = $this->db->fetchOne('SELECT nomeutente FROM utente WHERE tipoutente = 1 AND codiceavvocato = ?', $emittenteId);
// 						$cedentePrestatore['denominazione'] = $nomeAvvocato;
						$datiPersonaliAvvocato = $this->db->fetchRow('SELECT nomepersonale, cognomepersonale FROM utente WHERE tipoutente = 1 AND codiceavvocato = ?', $emittenteId);
						$cedentePrestatore['nome'] = $datiPersonaliAvvocato['nomepersonale'];
						$cedentePrestatore['cognome'] = $datiPersonaliAvvocato['cognomepersonale'];
					}

					$importoSpeseEsenti = $invoice['importospesee'];
					$importoSpeseEscluse = $invoice['importo_speseescluse'];
					$importoInversioneContabile = $invoice['importo_inversione_contabile'];
					$importoRegimeMargine = $invoice['importo_regime_margine'];
					$speseGenerali = (float)$invoice['importo_spesegenerali'];
					$naturaSpese = $invoice['natura_spese'];
					$naturaCassa = $invoice['natura_cassa'];
					$naturaCassa2 = $invoice['natura_cassa_2'];
					$riferimentoNormativoCassa = $invoice['riferimento_normativo_cassa'];
					$riferimentoNormativoCassa2 = $invoice['riferimento_normativo_cassa_2'];
					$importo = $invoice['importo'];
					$importoSpeseImponibili = $invoice['importospesei'];
					$importoCassa = $invoice['importo_cassa'];
					$importoCassa2 = $invoice['importo_cassa_2'];
					$importoRitenuta = $invoice['importo_ritenuta'];
					$importoBolloVirtuale = $invoice['importo_bollovirtuale'];
					$esigibilitaIva = $invoice['esigibilita_iva'];
					$isSplitPayment = $esigibilitaIva == 'S'? true : false;
					$imponibileCassa = $invoice['totale_imponibile'] - $invoice['importo_cassa'];
					$imponibileCassa2 = $invoice['totale_imponibile'] - $invoice['importo_cassa_2'];
					$i = 1;
					$i_iva = 1;
					$movimenti = array();
					// TODO: verificare tipologia natura se iva 0
					$naturaIvaZeroNonEsente = in_array($regimeFiscale, ['RF19', 'RF20']) ? "N2.2" : "N2.1";
					$naturaEsenzioneBollo = in_array($regimeFiscale, ['RF19', 'RF20']) ? "N2.2" : "N1";
					
					// riepilogoIva: (AliquotaIVA, Natura) => (TotaleImponibile)
					// Se Natura == NULL, utilizzare 0
					$riepilogoIva = array(); // MAP: (AliquotaIVA, Natura) => (ImponibileImporto)
					$esenzione = 0;
					$esclusione = 0;

                    if ($groupVoices) {
                        $voci_xml = json_decode($this->db->fetchOne("SELECT voci_xml FROM settings"), true);
                        $movements = $this->db->fetchAll(ltrim("
							SELECT type, iva AS AliquotaIVA, importo AS importo, 'num' as UnitaMisura, 1.00 AS Quantita,
                                    DATE_FORMAT(data_inizio_periodo, '%Y-%m-%d') as DataInizioPeriodo, DATE_FORMAT(data_fine_periodo, '%Y-%m-%d')   as DataFinePeriodo,
                                    NULL AS NumeroLinea, descrizione AS Descrizione, NULL AS Ritenuta, riferimento_amministrazione, applica_cassa, applica_ritenuta, riferimento_normativo, natura
                            FROM parcellamovimenti
                            WHERE parcella_id = ?
                                AND excluded = 0
                            "),
                            $invoice['id']
                        );
                        $voiceType2 = $voci_xml[FeesConstants::VOICES[2]];
                        $orderedMovements = [];
                        foreach ($movements as $movement) {
                            $voices = [];
                            $type = $movement['type'];
                            $voice_type = FeesConstants::VOICES[$type];
                            $voices[] = $voci_xml[$voice_type];
                            $iva = $movement['AliquotaIVA'];
                            $natura = $movement['natura'];
                            /*if ($voices[0] == $voiceType2 && $speseGenerali > 0) {
                                $voices[] = $voci_xml['forfettarie'];
                            }*/
                            foreach ($voices as $voice) {
                                if (!isset($orderedMovements[$voice][$iva][$natura])) {
                                    $orderedMovements[$voice][$iva][$natura] = [];
                                    $orderedMovements[$voice][$iva][$natura]['importo'] = 0;
                                }
                                $orderedMovements[$voice][$iva][$natura]['Descrizione'] = $movement['Descrizione'];
                                $orderedMovements[$voice][$iva][$natura]['Quantita'] = $movement['Quantita'];
                                $orderedMovements[$voice][$iva][$natura]['AliquotaIVA'] = $movement['AliquotaIVA'];
                                $orderedMovements[$voice][$iva][$natura]['Descrizione'] = $movement['Descrizione'];
                                $importo = (float)$movement['importo'];
                               /* if ($voice == $voci_xml['forfettarie']) {
                                    $importo = $importo * ($speseGenerali / 100);
                                }*/
                                $orderedMovements[$voice][$iva][$natura]['importo'] += $importo;
                                if ($orderedMovements[$voice][$iva][$natura]['AliquotaIVA'] == 0) $orderedMovements[$voice][$iva][$natura]['Natura'] = $natura;
                                if (isset($movement['riferimento_normativo'])) $orderedMovements[$voice][$iva][$natura]['riferimento_normativo'] = $movement['riferimento_normativo'];
                                $orderedMovements[$voice][$iva][$natura]['type'] = $movement['type'];
                            }
                        }

                        $ivaMedia = 0;
                        foreach ($orderedMovements as $voice => $array) {
                            foreach ($array as $ivaKey => $item) {
                                foreach ($item as $natura => $movement) {
                                    $ivaRow = $movement['AliquotaIVA'];
                                    if (isset($movement['type']) && $movement['type'] <> 3) {
                                        $ivaMedia += $ivaRow;
                                    }
                                    unset($movement['type']);
                                    $ivaFormatted = number_format($ivaRow, 2, '.', '');
                                    $row = [];
                                    $row['NumeroLinea'] = $i++;
                                    $row['Descrizione'] = $voice;
                                    $row['Quantita'] = $movement['Quantita'];
                                    $row['PrezzoUnitario'] = number_format($movement['importo'], 2, '.', '');
                                    $row['PrezzoTotale'] = number_format($movement['importo'], 2, '.', '');
                                    $row['AliquotaIVA'] = $ivaFormatted;
                                    if ($movement['AliquotaIVA'] == 0) $row['Natura'] = $natura;
                                    $riferimento = !empty($movement['riferimento_normativo']) ? $movement['riferimento_normativo'] : 0;
                                    $natura = isset($row['Natura']) ? $row['Natura'] : 0;
                                    if (!isset($riepilogoIva["${ivaFormatted}"][$natura])) {
                                        $riepilogoIva["${ivaFormatted}"][$natura][$riferimento] = array('ImponibileImporto' => 0);
                                    }
                                    unset($movement['riferimento_normativo']);
                                    //if (!isset($riepilogoIva["${ivaFormatted}"][$natura][$riferimento])) $riepilogoIva["${ivaFormatted}"][$natura][$riferimento] = array('ImponibileImporto' => 0);
                                    $riepilogoIva["${ivaFormatted}"][$natura][$riferimento]['ImponibileImporto'] += $movement['importo'];
                                    $movimenti[] = $row;
                                }
                            }
                        }
                        if($ivaMedia == 0)
                            $ivaMedia = 0.00;
                        else
                            $ivaMedia = 22.00;

                        $ivaMedia = $this->fixfattureiva($uid, $ivaMedia);

                        $iva = $ivaMedia;

                    } else if (COMPLEX_FORMAT == $invoice['formato_parcella']) {
						$ivaMedia = 0;
						$stmt = $this->db->query(ltrim("
							SELECT id, NULL AS NumeroLinea, descrizione AS Descrizione, quantita AS Quantita, unita_misura as UnitaMisura, DATE_FORMAT(data_inizio_periodo,'%Y-%m-%d') as DataInizioPeriodo, DATE_FORMAT(data_fine_periodo,'%Y-%m-%d') as DataFinePeriodo, importo AS PrezzoUnitario, (importo * quantita) AS PrezzoTotale, iva AS AliquotaIVA, type, NULL AS Ritenuta, riferimento_amministrazione, applica_cassa, applica_ritenuta, riferimento_normativo, natura
							FROM parcellamovimenti
							WHERE parcella_id = ? AND excluded = 0
							ORDER BY date, id ASC"),
							$invoice['id']
						);

						while ($row = $stmt->fetch()) {
							$row['Quantita'] = number_format($row['Quantita'], 2, '.', '');
							$ivaRiga = $row['AliquotaIVA'];
							$ivaFormatted = number_format($ivaRiga, 2, '.', '');
							if ($row['type'] <> 3) {
								$ivaMedia += $ivaRiga;
							}
							$row['NumeroLinea'] = $i++;
							$row['AliquotaIVA'] = $ivaFormatted;
							/*
							Se c'è la ritenuta, abbiamo due casi:
								1) Movimenti di tipo "compensi" e "imponibile" a cui viene calcolata in automatico se spuntata in edit fattura
								2) Movimenti di tipo diverso a cui è stata spuntata l'opzione di "applica ritenuta" in altri dati gestionali
							Il controllo tiene conto sempre di importoRitenuta della fattura per evitare casi in cui viene unflaggata l'opzione dalla fattura ma
							restano ancora movimenti con applica_ritenuta
							*/
							if($importoRitenuta > 0 && (in_array($row['type'], [1,2]) || $row['applica_ritenuta'] == 1)) {
								$row['Ritenuta'] = 'SI';
							} else {
								unset($row['Ritenuta']);
							}

							unset($row['applica_ritenuta']);

							//gestione natura nuovo schema
							if(!empty($row['natura'])){
								$row['Natura'] = $row['natura'];
								if($row['natura'] == 'N1'){
									$esclusione = 1;
								} else if($row['natura'] == 'N4'){
									$esenzione = 1;
								}else if($row['natura'] == 'N2.2'){
                                    $esclusione = 1;
                                }
							} else {
								if ($row['type'] != 3 && $row['type'] != 4 && $row['type'] != 5 && $row['type'] != 6 && $row['type'] != 7 && $ivaRiga == 0) {
									//$row['Natura'] = ($row['type'] != 3? 'N4' : $naturaIvaZeroNonEsente);
									$row['Natura'] = $naturaIvaZeroNonEsente;
								} else if($row['type'] == 3) {
									$row['Natura'] = 'N4';
									$esenzione = 1;
								} else if ($row['type'] == 4) {
									$row['Natura'] = 'N1';
									$esclusione = 1;
								} else if ($row['type'] == 5) {
									$row['Natura'] = $invoice['tracciato'] ? 'N3.1' : 'N3';
								} else if ($row['type'] == 6) {
									$row['Natura'] = 'N6';
								} else if ($row['type'] == 7) {
									$row['Natura'] = 'N5';
								}
							}

							unset($row['natura']);
							unset($row['type']);
							$natura = isset($row['Natura'])? $row['Natura'] : 0;

							if($row['applica_cassa']){
								$imponibileCassa += $row['PrezzoTotale'];
								$imponibileCassa2 += $row['PrezzoTotale'];
							}
							unset($row['applica_cassa']);

							$riferimento_amministrazione = $row['riferimento_amministrazione'];

							if(!empty($riferimento_amministrazione)){
								$row['RiferimentoAmministrazione'] = $riferimento_amministrazione;
							}
							unset($row['riferimento_amministrazione']);

							if(empty($row['UnitaMisura']) || $row['UnitaMisura']==' ')
								unset($row['UnitaMisura']);

							if(empty($row['DataInizioPeriodo']))
								unset($row['DataInizioPeriodo']);

							if(empty($row['DataFinePeriodo']))
								unset($row['DataFinePeriodo']);

							$row['AltriDatiGestionali'] = $this->db->fetchAll("SELECT tipo_dato as TipoDato, riferimento_testo as RiferimentoTesto, riferimento_numero as RiferimentoNumero, riferimento_data as RiferimentoData FROM campi_aggiuntivi WHERE movimento_id = ?", $row['id']);

							foreach (array_keys($row['AltriDatiGestionali']) as $key) {
								if(empty($row['AltriDatiGestionali'][$key]['RiferimentoTesto'])){
									unset($row['AltriDatiGestionali'][$key]['RiferimentoTesto']);
								}
								if(empty($row['AltriDatiGestionali'][$key]['RiferimentoNumero'])){
									unset($row['AltriDatiGestionali'][$key]['RiferimentoNumero']);
								}
								if(empty($row['AltriDatiGestionali'][$key]['RiferimentoData'])){
									unset($row['AltriDatiGestionali'][$key]['RiferimentoData']);
								}
							}

							unset($row['id']);

							if(empty($row['AltriDatiGestionali']))
								unset($row['AltriDatiGestionali']);

							$riferimento = !empty($row['riferimento_normativo']) ? $row['riferimento_normativo'] : 0;
							if (!isset($riepilogoIva["${ivaFormatted}"][$natura])) {
								$riepilogoIva["${ivaFormatted}"][$natura][$riferimento] = array('ImponibileImporto' => 0);
							}
							unset($row['riferimento_normativo']);
							$riepilogoIva["${ivaFormatted}"][$natura][$riferimento]['ImponibileImporto'] += $row['PrezzoTotale'];

							$movimenti[] = $row;
							$freezed['movimenti'][] = $row;
						}

						if($ivaMedia == 0)
							$ivaMedia = 0.00;
						else
							$ivaMedia = 22.00;

						$ivaMedia = $this->fixfattureiva($uid, $ivaMedia);

						$iva = $ivaMedia;

						unset($row, $ivaRiga, $ivaMedia);
					} else {
						$iva = $invoice['iva'];
						$ivaFormatted = number_format($iva, 2, '.', '');
						$natura = (0 == $iva? $naturaIvaZeroNonEsente : 0);

						if($natura == 0){
							$riepilogoIva["${ivaFormatted}"][$natura][0] = array('ImponibileImporto' => 0);
						}

						if ($importo > 0) {
							$row = array(
								'NumeroLinea' => $i++,
								'Descrizione' => 'Compensi professionali',
								'PrezzoUnitario' => $importo,
								'PrezzoTotale' => $importo,
								'AliquotaIVA' => $ivaFormatted,
							);

							if($importoRitenuta > 0) {
								$row['Ritenuta'] = 'SI';
							}

							$row['Natura'] = NULL;

							if (0 == $iva) {
								$row['Natura'] = $naturaIvaZeroNonEsente;
								$riepilogoIva["${ivaFormatted}"][$natura][0]['ImponibileImporto'] += $importo;
							} else {
								unset($row['Natura']);
								$riepilogoIva["${ivaFormatted}"][$natura][0]['ImponibileImporto'] += $importo;
							}

							$movimenti[] = $row;
							$freezed['movimenti'][] = $row;
						}

						if ($importoSpeseImponibili > 0) {
							$row = array(
								'NumeroLinea' => $i++,
								'Descrizione' => 'Spese imponibili',
								'PrezzoUnitario' => $importoSpeseImponibili,
								'PrezzoTotale' => $importoSpeseImponibili,
								'AliquotaIVA' => $ivaFormatted,
							);

							if($importoRitenuta > 0) {
								$row['Ritenuta'] = 'SI';
							}

							$row['Natura'] = NULL;

							if (0 == $iva) {
								$row['Natura'] = $naturaIvaZeroNonEsente;
							} else {
								unset($row['Natura']);
							}

							$movimenti[] = $row;
							if(isset($riepilogoIva["${ivaFormatted}"][$natura][0]['ImponibileImporto'])){
								$riepilogoIva["${ivaFormatted}"][$natura][0]['ImponibileImporto'] += $importoSpeseImponibili;
							} else {
								$riepilogoIva["${ivaFormatted}"][$natura][0]['ImponibileImporto'] += $importoSpeseImponibili;
							}
							$freezed['movimenti'][] = $row;
						}
					}

					if ($importoBolloVirtuale > 0 && $invoice["contabilizza_bollo"] == true) {
						$movimenti[] = array(
							'NumeroLinea' => $i++,
							'Descrizione' => $this->movimentoBolloVirtualeDescrizione,
							'Quantita' => '1.00',
							'PrezzoUnitario' => $this->movimentoBolloVirtualeImporto,
							'PrezzoTotale' => $this->movimentoBolloVirtualeImporto,
							'AliquotaIVA' => '0.00',
							'Natura' => $naturaEsenzioneBollo,
						);

						$riferimentoNormativoBollo = !empty($invoice['riferimento_normativo_bollo']) ? $invoice['riferimento_normativo_bollo'] : 0;
						if (!isset($riepilogoIva['0.00'][$naturaEsenzioneBollo])) {
							$riepilogoIva['0.00'][$naturaEsenzioneBollo][$riferimentoNormativoBollo] = array('ImponibileImporto' => 0);
						}
						$riepilogoIva['0.00'][$naturaEsenzioneBollo][$riferimentoNormativoBollo]['ImponibileImporto'] += $this->movimentoBolloVirtualeImporto;
					}

					$ivaFormatted = number_format($iva, 2, '.', '');

					if (SIMPLE_FORMAT == $invoice['formato_parcella'] && ($importoSpeseEscluse > 0 || $importoSpeseEsenti > 0) && $importoSpeseImponibili <= 0) {
						/* togliere riga di DatiRiepilogo
							fatta così:
							<DatiRiepilogo>
								<AliquotaIVA>0.00</AliquotaIVA>
								<Natura>N2</Natura>
								<ImponibileImporto>0.00</ImponibileImporto>
								<Imposta>0.00</Imposta>
							</DatiRiepilogo>
							che sta solo nelle sintetiche, in caso di fattura di sole spese esenti o spese escluse
						*/
						$nat = $invoice['tracciato'] ? 'N2.1' : 'N2';
						if ($riepilogoIva['0.00'][$nat][0]['ImponibileImporto'] == 0) {
							unset($riepilogoIva['0.00'][$nat]);
						}
					}

                    $riferimentoNormativoSpese = !empty($invoice['riferimento_normativo_spese']) ? $invoice['riferimento_normativo_spese'] : 0;

                    $speseGeneraliInParcellaPratiche    = $this->db->fetchOne("SELECT SUM(importo_spese_generali) FROM parcella_pratiche WHERE id_parcella = ? AND importo_spese_generali IS NOT NULL AND spese_generali IS NOT NULL", $invoice['id']); // check spese generali già indicate in mutlipratica
                    if ( $speseGenerali > 0 && empty($speseGeneraliInParcellaPratiche)) {

						$row = array(
							'NumeroLinea' => $i++,
							'Descrizione' => ($groupVoices) ? $voci_xml['forfettarie']: 'Spese generali',
							'PrezzoUnitario' => number_format($speseGenerali, 2, '.', ''),
							'PrezzoTotale' => number_format($speseGenerali, 2, '.', ''),
							'AliquotaIVA' => number_format($ivaFormatted, 2, '.', '')
						);

						if($importoRitenuta > 0) {
							$row['Ritenuta'] = 'SI';
						}

						if(in_array($uid, $this->avvalaiafix)){
							unset($row['Ritenuta']);
						}

						$row['Natura'] = NULL;

						if ($iva == 0) {
							$row['Natura'] = $naturaIvaZeroNonEsente;
						} else {
							unset($row['Natura']);
						}

						if(!empty($naturaSpese)){
							$ivaFormatted = number_format(0, 2, '.', '');
							$row['Natura'] = $naturaSpese;
							$row['AliquotaIVA'] = $ivaFormatted;
						}

						$movimenti[] = $row;
						$natura = isset($row['Natura'])? $row['Natura'] : 0;

						if (!isset($riepilogoIva["${ivaFormatted}"][$natura])) {
							$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoSpese] = array('ImponibileImporto' => 0);
						}

                        //if (!isset($riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoSpese])) $riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoSpese] = array('ImponibileImporto' => 0);
						$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoSpese]['ImponibileImporto'] += $speseGenerali;


						$row['Descrizione'] .= ' (' . number_format($invoice['spesegeneriche'], 2, ',', '.') . '%)';
						$ivaFormatted = number_format($iva, 2, '.', '');
						$freezed['movimenti'][] = $row;
					}

					//gestione totali divisi per fattura multipratica
					$multiFiles = $this->db->fetchAll("SELECT * FROM parcella_pratiche WHERE id_parcella = ?", $invoice['id']);
					if(!empty($multiFiles)){
						foreach($multiFiles as $file){
							if(!empty($file['spese_generali']) && !empty($file['importo_spese_generali'])) {

							    $percSpeseGeneraliPratica = number_format($file['spese_generali'], 2, ',', '.');

								$row = array(
									'NumeroLinea'       => $i++,
									'Descrizione'       => 'Spese generali' . ' (' . $percSpeseGeneraliPratica . '%)',
									'PrezzoUnitario'    => $file['importo_spese_generali'],
									'PrezzoTotale'      => $file['importo_spese_generali'],
									'AliquotaIVA'       => $ivaFormatted,
								);

								$row['Natura'] = NULL;

								if ($iva == 0) {
									$row['Natura'] = $naturaIvaZeroNonEsente;
								} else {
									unset($row['Natura']);
								}

                                if($importoRitenuta > 0){
                                    $row['Ritenuta'] = 'SI';
                                }

								$movimenti[] = $row;
								$natura = isset($row['Natura'])? $row['Natura'] : 0;
								if (!isset($riepilogoIva["${ivaFormatted}"][$natura])) {
									$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoSpese] = array('ImponibileImporto' => 0);
								}

								// TODO: DANIELE qua è corretto che usa $speseGenerali invece di $file['importo_spese_generali'] ?
								$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoSpese]['ImponibileImporto'] += $file['importo_spese_generali'];
								$freezed['movimenti'][] = $row;
							}
						}
					}

                    $idPaese = 'IT';
					$autofattura = false;
					if(TIPO_INTESTATARIO_AUTOFATTURA == $invoice['tipointestatario'] && in_array($invoice['tipo_documento'], self::ARRAY_DOC_TYPE_AUTO_BILL))
					{
					    $arrayAppoggio = $cessionarioCommittente;
					    $cessionarioCommittente = $cedentePrestatore;
					    $cedentePrestatore = $arrayAppoggio;

					    $cessionarioCommittente['codice_fiscale'] = $cessionarioCommittente['codicefiscale'];
					    unset($cessionarioCommittente['codicefiscale']);
					    $cessionarioCommittente['piva'] = $cessionarioCommittente['partitaiva'];
					    unset($cessionarioCommittente['partitaiva']);

					    $cedentePrestatore['codicefiscale'] = $cedentePrestatore['codice_fiscale'];
					    unset($cedentePrestatore['codice_fiscale']);
					    $cedentePrestatore['partitaiva'] = $cedentePrestatore['piva'];
                        unset($cedentePrestatore['partita_iva']);

                        $personaFisica = $this->db->fetchOne("SELECT tipo FROM anagrafiche WHERE id = ? ",$cedentePrestatore['id']) == 60 ?? 0;
                        $idPaese = $cedentePrestatore['nazione'];

                        $paIdCodice = $personaFisica ? $cessionarioCommittente['codice_fiscale'] : $cessionarioCommittente['piva'] ;
                        $paIdCodiceDatiAnagrafici = "";
                        $committenteTarget = !empty($cessionarioCommittente['piva']) ? $cessionarioCommittente['piva'] : $cessionarioCommittente['codice_fiscale'];

                        $autofattura = true;
                    }

					$datiAnagraficiCessionarioCommittente = array();

					if (!empty($cessionarioCommittente['id_fiscale_iva'])) {
						$datiAnagraficiCessionarioCommittente['IdFiscaleIVA']['IdPaese'] = 'IT';
						$datiAnagraficiCessionarioCommittente['IdFiscaleIVA']['IdCodice'] = $cessionarioCommittente['id_fiscale_iva'];

						if(!empty($cessionarioCommittente['codice_fiscale'])){
							$datiAnagraficiCessionarioCommittente['CodiceFiscale'] = $cessionarioCommittente['codice_fiscale'];
						}
					}
					else{
						if($invoice['target_type'] == 2){
							$datiAnagraficiCessionarioCommittente['IdFiscaleIVA']['IdPaese'] = isset($cessionarioCommittente['nazione']) ? $cessionarioCommittente['nazione'] : 'IT';
							$datiAnagraficiCessionarioCommittente['IdFiscaleIVA']['IdCodice'] = isset($committenteTarget) ? $committenteTarget : $cessionarioCommittente['piva'];

                            /**
                             * Nelle autofatture vi è la necessità del blocco codicefiscale del commitente
                             * che può contenere PIVA o CF l'importante è che corrisponda alla company
                             * per finire nel recipient id
                             */
                            if(!empty($cessionarioCommittente['company_cf']) && $autofattura && $this->sitekey != 'bellimarchetti'){
                                $datiAnagraficiCessionarioCommittente['CodiceFiscale'] = $cessionarioCommittente['company_cf'];
                            }elseif(!empty($cessionarioCommittente['company_cf']) && $autofattura){
                                $datiAnagraficiCessionarioCommittente['CodiceFiscale'] = $cessionarioCommittente['codice_fiscale'];
                            }
						}
						elseif($invoice['target_type'] == 4){
							$datiAnagraficiCessionarioCommittente['IdFiscaleIVA']['IdPaese'] = isset($cessionarioCommittente['nazione']) ? $cessionarioCommittente['nazione'] : 'IT';
							if(!empty($cessionarioCommittente['piva']))
								$datiAnagraficiCessionarioCommittente['IdFiscaleIVA']['IdCodice'] = $cessionarioCommittente['piva'];
							if(!empty($cessionarioCommittente['codice_fiscale']))
								$datiAnagraficiCessionarioCommittente['CodiceFiscale'] = $cessionarioCommittente['codice_fiscale'];
						}
						else{
							$datiAnagraficiCessionarioCommittente['CodiceFiscale'] = isset($committenteTarget) ? $committenteTarget : $cessionarioCommittente['codice_fiscale'];
						}
					}

					$datiAnagraficiCessionarioCommittente['Anagrafica'] = array(
						'Denominazione' => substr($cessionarioCommittente['denominazione'], 0, 80),
					);

					//// Costruzione
					$fattura["FatturaElettronicaHeader"] = array(  //capire se il trasmittente sia netlex o no
						"DatiTrasmissione"=> array(
							"IdTrasmittente"=> array(
								"IdPaese" => "IT",
// 								"IdCodice" => $personaFisica? $cedentePrestatore["codicefiscale"] : $cedentePrestatore["partitaiva"],
								"IdCodice" => !empty($paIdCodice)? $paIdCodice : $cedentePrestatore["codicefiscale"],
							),
							"ProgressivoInvio" => $progressivoInvio, // sempre e solo una fattura alla volta
							"FormatoTrasmissione" => $referenceType == 6 || $referenceType == 7 || ($referenceType == 10 && in_array($invoice['tipointestatario'], array(TIPO_INTESTATARIO_CONTROPARTE, TIPO_INTESTATARIO_CLIENTE, TIPO_INTESTATARIO_AUTOFATTURA))) ? 'FPR12' : 'FPA12',
							"CodiceDestinatario" => $cessionarioCommittente['codice_pa'],
						),
						"CedentePrestatore" => array(
							"DatiAnagrafici" => array(
								"IdFiscaleIVA" => array(
									"IdPaese" => $idPaese,
									"IdCodice" => !empty($paIdCodiceDatiAnagrafici)? $paIdCodiceDatiAnagrafici : $cedentePrestatore["partitaiva"],
								),
								"CodiceFiscale" => null,
								"Anagrafica" => ( !$personaFisica ? array("Denominazione" => $cedentePrestatore["denominazione"])
                                                                    : array(
                                                                            'Nome' => $cedentePrestatore['nome'],
                                                                            'Cognome' => $cedentePrestatore['cognome'],
									                                    )
								),
								"RegimeFiscale" => $regimeFiscale
							),
							"Sede" => array(
								"Indirizzo" => $cedentePrestatore["indirizzo"],
								"CAP" => $cedentePrestatore["cap"],
								"Comune" => $cedentePrestatore["comune"],
								"Provincia" => $cedentePrestatore["provincia"],
								"Nazione" => $idPaese,
							),
							"RiferimentoAmministrazione" => $riferimentoAmministrazione,
						),
						"CessionarioCommittente" => array(
							"DatiAnagrafici" => $datiAnagraficiCessionarioCommittente,
							"Sede" => array(
								"Indirizzo" => $cessionarioCommittente["indirizzo"],
								"CAP" => $cessionarioCommittente["cap"],
								"Comune" => $cessionarioCommittente["comune"],
								"Provincia" => $cessionarioCommittente["provincia"],
								"Nazione" => isset($cessionarioCommittente['nazione']) ? $cessionarioCommittente['nazione'] : 'IT',
							),
						),
					);

					$codiceFiscaleDA = $personaFisica? $cedentePrestatore["codicefiscale"] : null;
					$codiceFiscale = !empty($codiceFiscaleDatiAnagrafici) ? $codiceFiscaleDatiAnagrafici : null;
					$codiceFiscale = !empty($codiceFiscale)? $codiceFiscale : $codiceFiscaleDA;

					$showFiscalCode = !empty($codiceFiscale) ? 1 : 0;
					if(isset($studioAssociato) && $studioAssociato['nascondi_codice_fiscale_xml']){
						$showFiscalCode = 0;
					}

					if ($showFiscalCode && !$autofattura) {
						$fattura["FatturaElettronicaHeader"]["CedentePrestatore"]["DatiAnagrafici"]["CodiceFiscale"] = $codiceFiscale;
					} else {
						unset($fattura["FatturaElettronicaHeader"]["CedentePrestatore"]["DatiAnagrafici"]["CodiceFiscale"]);
					}

					if(!empty($cessionarioCommittente['email_pec']) && $invoice['target_mode'] == 2){
						$fattura["FatturaElettronicaHeader"]['DatiTrasmissione']['PECDestinatario'] = $cessionarioCommittente['email_pec'];
					}

					if (!$riferimentoAmministrazione) {
						unset($fattura['FatturaElettronicaHeader']['CedentePrestatore']['RiferimentoAmministrazione']);
					}

					$fattura['FatturaElettronicaBody'] = array(  //capire se il trasmittente sia netlex o no
						"DatiGenerali" => array(
							"DatiGeneraliDocumento" => array(
								"TipoDocumento" => $tipoDocumento,
								"Divisa" => 'EUR',
								"Data" => $invoice['data'],
								"Numero" => ($invoice['numero_fattura']),
							),
						),
						"DatiBeniServizi" => array(
							"DettaglioLinee" => $movimenti,
						),
					);

					if ($importoCassa > 0) { // L'elemento verrà inserito più avanti ma bisogna fare il riepilogo degli importi prima

						$DatiCassaPrevidenziale = array(
							'TipoCassa' => $tipoCassa,
							'AlCassa' => $invoice['cassa'],
							'ImportoContributoCassa' => $importoCassa,
							'ImponibileCassa' => number_format($imponibileCassa, 2, '.', ''),
							'AliquotaIVA' => $ivaFormatted,
							'Natura' => NULL,
						);


						if (0 == $iva) {
							$DatiCassaPrevidenziale['Natura'] = $naturaIvaZeroNonEsente;
						} else {
							unset($DatiCassaPrevidenziale['Natura']);
						}

						if(!empty($naturaCassa) && date('Y-m-d') >= date('2019-05-06')){
							$ivaFormatted = number_format(0, 2, '.', '');
							$DatiCassaPrevidenziale['Natura'] = $naturaCassa;
							$DatiCassaPrevidenziale['AliquotaIVA'] = $ivaFormatted;
						}

						#solo se è gestione separata inps
// 						if($importoRitenuta > 0) {
// 							$DatiCassaPrevidenziale['Ritenuta'] = 'SI';
// 						}
						$natura = isset($DatiCassaPrevidenziale['Natura'])? $DatiCassaPrevidenziale['Natura'] : 0;
						// errore: Codice errore 00429 => ******* Natura non presente a fronte di ******* AliquotaIVA pari a zero
						// corretto con MODIFICA del 21/03/2017 Per regimi fiscali: Contribuenti minimi e regime forfettario la natura della linea della cassa previdenziale non può non esserci,
						// quindi ho introdotto questo controllo per mettere la natura
						//if (in_array($regimeFiscale, array('RF02', 'RF19')) && $natura === 0) {
						//	$natura = 'N4';
						//}
						if(in_array($uid, $this->cordafix) && $this->sitekey=="francescocorda"){
							$natura = 'N4';
						}
						$riferimentoNormativoCassa = !empty($riferimentoNormativoCassa) ? $riferimentoNormativoCassa : 0;
						if (!isset($riepilogoIva["${ivaFormatted}"][$natura])) {
							$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoCassa] = array('ImponibileImporto' => 0);
						}
						$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoCassa]['ImponibileImporto'] += $importoCassa;
						$ivaFormatted = number_format($iva, 2, '.', '');

					}

					if ($importoCassa2 > 0) {

						$DatiCassaPrevidenziale2 = array(
							'TipoCassa' => $tipoCassa2,
							'AlCassa' => $invoice['cassa_2'],
							'ImportoContributoCassa' => $importoCassa2,
							'ImponibileCassa' => number_format($imponibileCassa2, 2, '.', ''),
							'AliquotaIVA' => $ivaFormatted,
							'Natura' => NULL,
						);


						if (0 == $iva) {
							$DatiCassaPrevidenziale2['Natura'] = $naturaIvaZeroNonEsente;
						} else {
							unset($DatiCassaPrevidenziale2['Natura']);
						}

						if(!empty($naturaCassa2)){
							$ivaFormatted = number_format(0, 2, '.', '');
							$DatiCassaPrevidenziale2['Natura'] = $naturaCassa2;
							$DatiCassaPrevidenziale2['AliquotaIVA'] = $ivaFormatted;
						}

						$natura = isset($DatiCassaPrevidenziale2['Natura'])? $DatiCassaPrevidenziale2['Natura'] : 0;

						$riferimentoNormativoCassa2 = !empty($riferimentoNormativoCassa2) ? $riferimentoNormativoCassa2 : 0;
						if (!isset($riepilogoIva["${ivaFormatted}"][$natura])) {
							$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoCassa2] = array('ImponibileImporto' => 0);
						}
						$riepilogoIva["${ivaFormatted}"][$natura][$riferimentoNormativoCassa2]['ImponibileImporto'] += $importoCassa2;
						$ivaFormatted = number_format($iva, 2, '.', '');

						$datiCassaPrevidenziale[] = $DatiCassaPrevidenziale2;
					}

					foreach ($riepilogoIva as $vat => $nature) {
						foreach ($nature as $natura => $rif) {
							foreach ($rif as $rif_norm => $dati){
								$riferimento = $this->dbShared->fetchOne("SELECT nome FROM riferimento_normativo WHERE id = ?", $rif_norm);

								$riepilogo = array(
									'AliquotaIVA' => $vat,
									'Natura' => NULL,
									'ImponibileImporto' => number_format($dati['ImponibileImporto'], 2, '.', ''),
									'Imposta' => number_format($dati['ImponibileImporto'] * $vat / 100, 2, '.', ''),
								);
								if(!empty($riferimento)){
									$riepilogo['RiferimentoNormativo'] = $riferimento;
								}
								if (!empty($natura)) {
									$riepilogo['Natura'] = $natura;
								} else {
									unset($riepilogo['Natura']);
								}
								// la seconda condizione è stata aggiunta il 10/10/2017 dopo richiesta di avvvalente per fatture verso INAIL che non dovevano avere il campo
								// esigibilità iva nei DatiRiepilogo della riga corrispondente alle spese esenti
								if (!empty($esigibilitaIva) && $vat > 0){
									$riepilogo['EsigibilitaIVA'] = $esigibilitaIva;
								}

								$fattura['FatturaElettronicaBody']['DatiBeniServizi']['DatiRiepilogo'][] = $riepilogo;
							}
						}
					}
					unset($vat);

					$freezed['data'] = strftime('%d %B %Y', DateTime::createFromFormat('Y-m-d', $invoice['data'])->getTimestamp());

					if ($importoRitenuta > 0) {
						$tipoRitenutaDefault = $personaFisica? 'RT01' : 'RT02'; // RT01 Fisiche, RT02 Giuridiche
						$DatiRitenuta = array(
							'TipoRitenuta' => !empty($invoice['tipo_ritenuta']) ? $invoice['tipo_ritenuta'] : $tipoRitenutaDefault,
							'ImportoRitenuta' => $importoRitenuta,
							'AliquotaRitenuta' => $invoice['ritenuta'],
							'CausalePagamento' => 'A',
						);
						$fattura["FatturaElettronicaBody"]["DatiGenerali"]["DatiGeneraliDocumento"]["DatiRitenuta"] = $DatiRitenuta;
					}

					if($importoBolloVirtuale > 0) {
						$DatiBollo = array(
							'BolloVirtuale' => 'SI',
							'ImportoBollo' => $importoBolloVirtuale,
						);
						$fattura["FatturaElettronicaBody"]["DatiGenerali"]["DatiGeneraliDocumento"]["DatiBollo"] = $DatiBollo;
					}

					if ($importoCassa > 0) {
						$cassaLabel = 'Cassa';
						switch ($tipoCassa) {
							case 1:
								$cassaLabel .= ' avvocati';
								break;
							case 2:
								$cassaLabel .= ' commercialisti';
								break;
						}
						$fattura["FatturaElettronicaBody"]["DatiGenerali"]["DatiGeneraliDocumento"]["DatiCassaPrevidenziale"][] = $DatiCassaPrevidenziale;
						$freezed['movimenti'][] = array(
							'Descrizione' => $cassaLabel . ' (' . number_format($invoice['cassa'], 2, ',', '.') . '%)',
							'PrezzoTotale' => $importoCassa,
						);
					}

					if ($importoCassa2 > 0) {
						$cassaLabel = 'Cassa';
						$fattura["FatturaElettronicaBody"]["DatiGenerali"]["DatiGeneraliDocumento"]["DatiCassaPrevidenziale"][] = $DatiCassaPrevidenziale2;
						$freezed['movimenti'][] = array(
							'Descrizione' => $cassaLabel . ' (' . number_format($invoice['cassa_2'], 2, ',', '.') . '%)',
							'PrezzoTotale' => $importoCassa2,
						);
					}
					$bollo = in_array($regimeFiscale, ['RF19', 'RF20']) ? 0 : ($importoBolloVirtuale * $invoice['contabilizza_bollo']);
					$importoTotaleDocumento = number_format($invoice['totale_imponibile'] + $invoice['totale_non_imponibile'] + $invoice['totale_iva'] + $importoSpeseEsenti + $importoSpeseEscluse + $importoInversioneContabile + $bollo, 2, '.', '');

					$fattura["FatturaElettronicaBody"]["DatiGenerali"]["DatiGeneraliDocumento"]["ImportoTotaleDocumento"] = $importoTotaleDocumento;

					if ($causale = trim($invoice['motivo'])) {
						$fattura['FatturaElettronicaBody']['DatiGenerali']['DatiGeneraliDocumento']['Causale'] = substr($causale, 0, 200);
						unset($causale);
					}

                    $doc_correlati = !empty(json_decode($invoice['documenti_correlati_JSON'])) ? json_decode($invoice['documenti_correlati_JSON']) : [];
                    foreach($doc_correlati as $key => $value)
                    {
                        $tipoDocumento = '';
                        // fix doppio case - lo switch non considerava un numero di documenti maggiore di 9
                        switch(substr($key, 5, (strlen($key) - 7)))
                        {
                            case 'ordine':
                            case 'ordine_':
                                $tipoDocumento = 'DatiOrdineAcquisto';
                                break;
                            case 'contratto':
                            case 'contratto_':
                                $tipoDocumento = 'DatiContratto';
                                break;
                            case 'convenzione':
                            case 'convenzione_':
                                $tipoDocumento = 'DatiConvenzione';
                                break;
                            case 'ricezione':
                            case 'ricezione_':
                                $tipoDocumento = 'DatiRicezione';
                                break;
                            case 'fatture':
                            case 'fatture_':
                                $tipoDocumento = 'DatiFattureCollegate';
                                break;
                        }
                        if(!isset($fattura['FatturaElettronicaBody']['DatiGenerali'][$tipoDocumento]))
                            $fattura['FatturaElettronicaBody']['DatiGenerali'][$tipoDocumento] = array();

                        $docArray =	array(
                            "IdDocumento" => $value->id_documento,
                            "Data" => date('Y-m-d', strtotime(str_replace("/", "-", $value->date))),
                            "NumItem" => $value->num_item,
                            "CodiceCommessaConvenzione" => $value->codice_commessa,
                            "CodiceCUP"  => $value->codice_cup,
                            "CodiceCIG"  => $value->codice_cig
                        );

                        if(empty($value->id_documento))
                            unset($docArray["IdDocumento"]);
                        if(empty($value->date))
                            unset($docArray["Data"]);
                        if(empty($value->num_item))
                            unset($docArray["NumItem"]);
                        if(empty($value->codice_commessa))
                            unset($docArray["CodiceCommessaConvenzione"]);
                        if(empty($value->codice_cup))
                            unset($docArray["CodiceCUP"]);
                        if(empty($value->codice_cig))
                            unset($docArray["CodiceCIG"]);

                        array_push($fattura['FatturaElettronicaBody']['DatiGenerali'][$tipoDocumento], $docArray);
                    }

					$modalitaPagamento = (empty($invio['modalita_pagamento'])? (empty($user['iban'])? 'MP01' : 'MP05') : $invio['modalita_pagamento']);
					$importoPagamento = !empty($esigibilitaIva) && $isSplitPayment? number_format($invoice['totale_imponibile'] + $importoSpeseEsenti - $importoRitenuta, 2, '.', '') : $invoice['totale'];
                    $importoPagamento = !empty($esigibilitaIva) && $isSplitPayment? number_format($invoice['totale'] - $invoice['totale_iva'], 2, '.', '') : $invoice['totale'];
                    //$fattura.totale - $fattura.totale_iva

					// Inserisci elemento "DatiPagamento" se non richiesto che venga nascosto
					$hideDatiPagamento = $invoice['office'] ? $studioAssociato['nascondi_dati_pagamento_xml'] : $user['nascondi_dati_pagamento_xml'];
					if(!$hideDatiPagamento) {
						$fattura['FatturaElettronicaBody']['DatiPagamento'] = array(
							'CondizioniPagamento' => 'TP02',
							'DettaglioPagamento' => array(
								array(
									'ModalitaPagamento' => $modalitaPagamento,
									'DataScadenzaPagamento' => $invoice['data_scadenza'],
									'ImportoPagamento' => $importoPagamento,
                                    'IBAN' => $user['iban'],
                                    'ABI' => $user['abi'],
                                    'CAB' => $user['cab'],
                                    'BIC' => $user['bic'],
								),

							)
						);


                        if (empty($invoice['data_scadenza'])) {
                                unset($fattura['FatturaElettronicaBody']['DatiPagamento']['DettaglioPagamento'][0]['DataScadenzaPagamento']);
                        }


                        foreach (array('iban', 'abi', 'cab', 'bic') as $key) {
                            if (empty($user[$key])) {
                                unset($fattura['FatturaElettronicaBody']['DatiPagamento']['DettaglioPagamento'][0][strtoupper($key)]);
                            }
                        }
					}

					$allegati = array();
					$allegati = $this->db->fetchAll('
						SELECT pd.parcella_id, pd.documento_id, d.codicepratica, d.nomefile, d.titolodocumento, d.one_drive_uniqueid, d.opentext_uniqueid
						FROM parcella_documento pd
						INNER JOIN documento d ON d.id = pd.documento_id
						INNER JOIN archivio a ON a.id = d.codicepratica
						WHERE pd.is_email=0 AND parcella_id = ?',
						$invoice['id']
					);
					$emails = $this->db->fetchAll('
						SELECT pd.parcella_id, pd.documento_id, es.file_id AS codicepratica, es.filename AS nomefile, es.subject AS titolodocumento
						FROM parcella_documento pd
						INNER JOIN emails_storage es ON es.id = pd.documento_id
						INNER JOIN archivio a ON a.id = es.file_id
						WHERE pd.is_email=1 AND parcella_id = ?',
							$invoice['id']
					);
					$allegati = array_merge($allegati, $emails);

					if(count($allegati) > 0) {
						$fattura['FatturaElettronicaBody']['Allegati'] = array();
						foreach($allegati as $a) {
							$fileName = $a['nomefile'];
							$sourceDir = $this->sitekey . DIRECTORY_SEPARATOR . 'documents' . DIRECTORY_SEPARATOR . $a['codicepratica'] . DIRECTORY_SEPARATOR;
							if(!empty($a['one_drive_uniqueid']) && !empty($this->oneDriveHandler)){
								$oneDriveFile = $this->oneDriveHandler->download($a['one_drive_uniqueid']);
								$fileContents = file_get_contents($oneDriveFile->download_url);
							} else if (!empty($a['opentext_uniqueid']) && !empty($this->openText)){
								$openTextFile = $this->openText->download($a['opentext_uniqueid']);
								$fileContents = $openTextFile['document'];
							} else {
								$fileContents = $this->s3Connector->getBody(self::MAIN_BUCKET, $sourceDir . $fileName);
							}
							$fileContents = base64_encode($fileContents);
							$f = explode('.', $fileName);
							$ext = $f[count($f) - 1];
							$allegato = array(
								'NomeAttachment' => $fileName,
								'FormatoAttachment' => strtoupper($ext),
								'DescrizioneAttachment' => $a['titolodocumento'],
								'Attachment' => $fileContents
							);
							if (in_array($ext, array('zip', 'rar'))) {
								$allegato['AlgoritmoCompressione'] = strtoupper($ext);
							}
							$fattura['FatturaElettronicaBody']['Allegati'][] = $allegato;
						}
					}

					$tracciato = $invoice['tracciato'];

					$generator = new FatturaXML_Generator($tracciato);
					if (!$fakeXML) {
						if($referenceType == 6 || $referenceType == 7 || ($referenceType == 10 && in_array($invoice['tipointestatario'], array(TIPO_INTESTATARIO_CONTROPARTE, TIPO_INTESTATARIO_CLIENTE,TIPO_INTESTATARIO_AUTOFATTURA)))){
							$XML = $generator->generateXML($fattura, "FPR12");
						} else {
							$XML = $generator->generateXML($fattura, "FPA12");
						}
					}
					if ($fakeXML || $XML) {

                        $namefileCForIVA = $cedentePrestatore['codicefiscale'];

                        /**
                         * In caso di autofatture il CF estero non è valorizzato ed in ogni
                         * caso l'emittente risulta essere il committente non il prestatore
                         */
                        if ($autofattura)
                        {
                            $namefileCForIVA = $cessionarioCommittente['codice_fiscale'];
                        }

						$fileName = 'IT' . $namefileCForIVA . '_n' . str_pad(base_convert($progressivoInvio, 10, 32), 4, 0, STR_PAD_LEFT) . '.xml';

						switch($referenceType) {
							case '3':
							case '6':
								$folderName = self::INVOICESPA;
								break;
							case '4':
							case '7':
								$folderName = self::CREDITNOTESPA;
								break;
							case '10':
								$folderName = self::DEBITNOTESPA;
								break;
						}

						if (!$fakeXML) {
							$filePath = $this->sitekey . DIRECTORY_SEPARATOR . $folderName . DIRECTORY_SEPARATOR;
							$filePath .= $progressivoInvio . DIRECTORY_SEPARATOR . $fileName;
							$this->s3Connector->saveStringAsFile(self::MAIN_BUCKET, $filePath, $XML);
                            //--se si vuole disabilatare S3 e salvare il file in DexmaCommons/FatturaXML:--
                            //file_put_contents(__DIR__ . "/".$fileName, $XML);
						}

						$result['invioUid'] = $invioUid;
						$result["documentNames"] = $fileName;
						$result["bucketName"] = self::MAIN_BUCKET;
						$result["customerFolder"] = $this->sitekey;
						$result["folderName"] = $folderName;
						$result['xml'] = $XML;
						$result["itemId"] = $progressivoInvio;
						$result['tipo'] = $referenceType;
						$result['cessionarioCommittente'] = $cessionarioCommittente;

						if (!$invioUid && !$this->getDefaultPEC($user['id'])) {
							$result['errors']['pecNotFound'] = TRUE;
						}

						$this->db->update('parcelle_pa', array(
							'modalita_pagamento' => $modalitaPagamento,
							'filename' => $fileName,
							'parcella_json' => json_encode($freezed),
						), array('id = ?' => $progressivoInvio));

					} else {
						$errors = $generator->getErrors();
						$this->applogger->err(print_r($errors, 1));
						$result['errors'] = $errors;
					}

					$this->db->commit();
				} catch (Exception $e) {
					$this->db->rollBack();
					$this->applogger->err($e->getMessage());
				}
			}
		}

		return $result;
	}

	private function isFEEnabled()
	{
		if ($this->loggedUser->agyo && isset($this->loggedUser->agyo_access_token)) {
			return true;
		}

		if($this->officeSettings['agyo'] && isset($this->officeSettings['agyo_access_token'])){
			return true;
		}
		return false;
	}

	private function fixfattureiva($uid, $ivaMedia){

		$notarangelo_11_00 = ['168E66C5353-1096619885C63F2C5701CA'];
		$notarangelo_14_67 = ['E1900636D75-1369031475C63F3CCB3798'];
		$notarangelo_7_33 = ['E19007E3572-8548235645C63F47C3CA19'];

		$bottino_11_00 = ['E193C316A89-17667523055C657BBC43733'];

		$larocca_11_00 = ['E19FF1A11A2-18396899705C6A7916EF26A'];

		$nardi_7_33 = ['E19FDAC7A01-17046220525C6A6FBB08475'];

		$sciorilli_11_00 = ['16906AFFFA4-14003441435C6C353F60BA8'];

		$riva_11_00 = ['E1B679B5CE3-18982091595C73B3B2178C0'];

		$astrua_11_00 = ['E1B9B2D11BA-20107001915C7505A8C336C'];

		$bellinzoni_7_33 = ['E1E4371D797-21220172785C866FDE3045E'];

		$buzzi_11_00 = ['E1E4948A549-13186521545C869622844D2'];

		$salvetti_11_00 = ['E1E6A97F7B0-6093080655C87706F3073E', 'E1E6AA04958-10706559205C8770A5AF3C9', 'E1E6AA42674-14366047605C8770BF09850', 'E1E6AA82626-5121665955C8770D93BF84', 'E1E6AACF0DD-8104688295C8770F89E91D', 'E1E6AB1869E-17358304375C877116AA4F9', 'E1E6AB59FE2-11686629745C8771318869C', 'E1E6AB8CD11-9335359445C8771465BDE2'];

		$lucaperilli_11_00 = ['E22A1D5863B-14575567625CA31158369BF'];

		$lucantoni_11_00 = ['E22A5E8E342-18720055405CA32C0E02704', 'E22A5ED7639-10949858445CA32C2BF0E24'];

		$laward_11_00 = ['E22A82D41BD-8274704465CA33AE97E238'];

		$morara_11_00 = ['E26DB21C622-13968508395CBEBFBCEFF23'];

		$scagliola_11_00 = ['E28EF2C78B7-11022138875CCC5E86EFA91'];

		$avvalaia_11_00 = ['E29B07BBDA5-12726825005CD15166B6FD5', '16A91A6728D-10572961735CD15185BCC6C', 'E29B088A0F3-20113707825CD151BB33AB0'];

		$avvalaia_13_20 = ['E29B084C73E-16029632605CD151A1EEAE4'];

		$pezzino_11_00 = ['E2B1A4BCAE9-6533645305CDA9493B3933'];

		$avvpadovano_11_00 = ['E52B89FD60B-770425895DDE3956F1B02'];
		$avvpadovano_13_20 = ['E52B8A37B94-13759216265DDE396ED8E73'];

		//$manzo_11_00 = [''];

		if($this->sitekey == 'notarangelo'){
			if(in_array($uid, $notarangelo_11_00)){
				$ivaMedia = 11.00;
			}
			elseif (in_array($uid, $notarangelo_14_67)) {
				$ivaMedia = 14.67;
			}
			elseif (in_array($uid, $notarangelo_7_33)) {
				$ivaMedia = 7.33;
			}
		}
		elseif($this->sitekey == 'avvbottino'){
			if(in_array($uid, $bottino_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'larocca72'){
			if(in_array($uid, $larocca_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'avvocatonardi'){
			if(in_array($uid, $nardi_7_33)){
				$ivaMedia = 7.33;
			}
		}
		elseif($this->sitekey == 'cecilia'){
			if(in_array($uid, $sciorilli_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'studiolegaleriva'){
			if(in_array($uid, $riva_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'astrua'){
			if(in_array($uid, $astrua_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'studiobellinzoni'){
			if(in_array($uid, $bellinzoni_7_33)){
				$ivaMedia = 7.33;
			}
		}
		elseif($this->sitekey == 'avvocatobuzzi'){
			if(in_array($uid, $buzzi_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'avvsalvetti'){
			if(in_array($uid, $salvetti_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'avvdelucaperilli'){
			if(in_array($uid, $lucaperilli_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'lucantoni'){
			if(in_array($uid, $lucantoni_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'laward'){
			if(in_array($uid, $laward_11_00)){
				$ivaMedia = 11.00;
			}
		}
		elseif($this->sitekey == 'avvmorara'){
   			if(in_array($uid, $morara_11_00)){
    			$ivaMedia = 11.00;
   			}
  		}
  		elseif($this->sitekey == 'giorgioscagliola'){
  			if(in_array($uid, $scagliola_11_00)){
  				$ivaMedia = 11.00;
  			}
  		}
  		elseif($this->sitekey == 'avvalaia'){
  			if(in_array($uid, $avvalaia_11_00)){
  				$ivaMedia = 11.00;
  			}
  			elseif(in_array($uid, $avvalaia_13_20)){
  				$ivaMedia = 13.20;
  			}
  		}
  		elseif($this->sitekey == 'studiopezzino'){
  			if(in_array($uid, $pezzino_11_00)){
  				$ivaMedia = 11.00;
  			}
  		}
		elseif($this->sitekey == 'avvpadovano'){
			if(in_array($uid, $avvpadovano_11_00)){
				$ivaMedia = 11.00;
			} else if (in_array($uid, $avvpadovano_13_20)){
				$ivaMedia = 13.20;
			}
		}

		return $ivaMedia;
	}
}
