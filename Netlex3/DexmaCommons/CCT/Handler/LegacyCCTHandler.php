<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Handler;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes\AbstractPdIBase;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes\CCTUploadResponse;
use <PERSON>ma<PERSON>om<PERSON>\CCT\Interfaces\CCTHandlerInterface;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\commcrypt\CommcryptClass;
use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use Netlex3\Software\patterns\Domains\Users\Repositories\UsersRepository;
use Netlex3\Software\patterns\Repositories\Settings\SettingsRepository;
use SoapClient;
use SoapFault;
use SoapVar;
use stdClass;

/**
 *
 */
class LegacyCCTHandler implements CCTHandlerInterface
{
    const PDI_BASE_NAME_SPACE = "http://www.teamsystem.com/CS/2015/11";
    private $fileTransferWSDL;
    private $userAuthenticationWSDL;

    private $infrastructure;
    private $settingsRepository;
    private $userRepository;
    /**
     * @var void|null
     */
    private $digestAuthentication;


    /**
     * PHP 5 allows developers to declare constructor methods for classes.
     * Classes which have a constructor method call this method on each newly-created object,
     * so it is suitable for any initialization that the object may need before it is used.
     *
     * Note: Parent constructors are not called implicitly if the child class defines a constructor.
     * In order to run a parent constructor, a call to parent::__construct() within the child constructor is required.
     *
     * param [ mixed $args [, $... ]]
     * @link https://php.net/manual/en/language.oop5.decon.php
     * @throws SoapFault
     */
    public function __construct(ZendInfrastructure $infrastructure)
    {
        $this->infrastructure = $infrastructure;
        $this->settingsRepository = new SettingsRepository();

        $isTesting = (bool)$this->infrastructure->getConfig()->cct->isTest;
        $cct = $this->infrastructure->getConfig()->cct;

        $this->fileTransferWSDL =
            $isTesting ? $cct->wsdlFileTransferTest : $cct->wsdlFileTransfer;
        $this->userAuthenticationWSDL =
            $isTesting ? $cct->wsdlUserAuthenticationTest : $cct->wsdlUserAuthentication;
        try {
            $this->digestAuthentication = $this->getDigestAuthentication();
        }
        catch(SoapFault $e){
            throw $e;
        }
    }

    /**
     * @param object $pdiDocument
     * @param object $soggettoTitolare
     * @return mixed
     * @throws SoapFault
     */
    public function uploadFile(string $documentType, $pdiDocument, $soggettoTitolare) : CCTUploadResponse
    {
        $documentType = sprintf('PdI%sDC', $documentType);
        $pdIBase =
            new SoapVar($pdiDocument, SOAP_ENC_OBJECT, $documentType, self::PDI_BASE_NAME_SPACE);

        try{

			$fileTransferResult = $this->getSoapClient($this->fileTransferWSDL)->UploadTypedPdI(
				array(
					"UserIdentity" => $this->getUserIdentity(),
					"UploadPdI" => array(
						"Canale" => "WCF",
						"PdIBase" => $pdIBase,
						"SoggettoTitolare" => $soggettoTitolare
					)
				)
			);

            return new CCTUploadResponse(
                $fileTransferResult->UploadTypedPdIResult->IdDoc,
                $fileTransferResult->UploadTypedPdIResult->IdPdI
            );

		} catch (SoapFault $e) {
			throw $e;
		}
    }


    /**
     * @throws SoapFault
     * @return void
     */
    private function getDigestAuthentication() {
        try {
            $cc = new CommcryptClass();

            $cct_username = $this->infrastructure->getUser()['cct_username'];
            $encrypt_password = $this->infrastructure->getUser()['cct_password'];
            $pecPassKey = $this->infrastructure->getConfig()->pecPassKey;
            $subdomain = $this->infrastructure->getUser()['subdomain_name'];

            $password =  $cc->comm_decrypt_password($encrypt_password, $pecPassKey, $subdomain);
            $digestAuthenticationResult = null;

            $userAuthentication = $this->getSoapClient($this->userAuthenticationWSDL);

            $baseUserIdentity = new StdClass();
            $baseUserIdentity->Username = $cct_username;

            $getTicketResult = $userAuthentication->GetTicket(
                array("BaseUserIdentity" => $baseUserIdentity)
            );

            if (!empty($getTicketResult)) {
                $key = hash("sha1", strtolower($cct_username) . strtolower($password));
                $digest = $this->digest($cct_username, $getTicketResult, $key);
                $digestAuthentication = new StdClass();
                $digestAuthentication->UserName = $cct_username;
                $digestAuthentication->Ticket = $getTicketResult->GetTicketResult->Ticket;
                $digestAuthentication->Digest = $digest;

                $digestAuthenticationResult = $userAuthentication->DigestAuthentication(
                    array("DigestAuthentication" => $digestAuthentication)
                );
            }
            return $digestAuthenticationResult;


        } catch (SoapFault $e) {
            throw $e;
        }
    }

//    public function downloadFile(string $idObject, string $idDoc) {
//        try {
//            $downloadFileResult = null;
//            $downloadFile = new StdClass();
//            $downloadFile->IdObject = $idObject;
//            $downloadFile->IdDoc = $idDoc;
//
//            $downloadFileResult = $this->getSoapClient($this->fileTransferWSDL)->DownloadFile(
//                array(
//                    "UserIdentity" => $this->getUserIdentity(),
//                    "DownloadFile" => $downloadFile
//                )
//            );
//
//            return $downloadFileResult;
//
//        } catch (SoapFault $e) {
//            throw $e;
//        }
//    }

    /**
     * @param $wsdl
     * @return SoapClient
     */
    private function getSoapClient($wsdl): SoapClient
    {
        return new SoapClient(
            $wsdl,
            array("soap_version" => SOAP_1_2, "trace" => 1)
        );
    }

    private function getUserIdentity() {

        $username = $this->infrastructure->getUser()['cct_username'];
        $passwordExpirationDate =
            strtotime($this->digestAuthentication->DigestAuthenticationResult->UserIdentity->PasswordExpirationDate);
        $now = strtotime(date("Y-m-d\TH:i:s"));

        if ($passwordExpirationDate <= $now) {
            $this->digestAuthentication = $this->getDigestAuthentication();
        }

        $userIdentity = new StdClass();
        $userIdentity->SecurityToken = $this->digestAuthentication->DigestAuthenticationResult->UserIdentity->SecurityToken;
        $userIdentity->Username = $username;

        return $userIdentity;
    }

    /**
     * @param $username
     * @param $getTicketResult
     * @param string $key
     * @return string
     */
    private function digest($username, $getTicketResult, string $key): string
    {
        return strtoupper(
            hash("sha1",
                strtolower($username) . $getTicketResult->GetTicketResult->ClientIPAddress . strtoupper($key) . $getTicketResult->GetTicketResult->Ticket));
    }
}