<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Handler;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes\CCTUploadResponse;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Interfaces\CCTHandlerInterface;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Request;
use Netlex3\Software\patterns\Classes\ZendInfrastructure;

class Digital<PERSON>THandler implements CCTHandlerInterface
{
    private $handlerVersion = '1.0';
    private $handlerName = 'TSLEGAL';

    private $infrastructure;
    private $restUploadBaseUrl;
//    private $restDownloadBaseUrl;
    private $digitalM2MToken;

    public function __construct(ZendInfrastructure $infrastructure)
    {
        $this->infrastructure = $infrastructure;
        $isTesting = (bool)$this->infrastructure->getConfig()->cct->isTest;
        $cct = $this->infrastructure->getConfig()->cct;

        $this->digitalM2MToken = $cct->digitalM2MToken;

        $this->restUploadBaseUrl =
            $isTesting ? $cct->restUploadBaseUrlTest : $cct->restUploadBaseUrl;
        $this->restDownloadBaseUrl =
            $isTesting ? $cct->restDownloadBaseUrlTest : $cct->restDownloadBaseUrl;
        $this->configBaseUrl =
            $isTesting ? $cct->configBaseUrlTest : $cct->configBaseUrl;
    }

    /**
     * @param string $documentType
     * @param $pdiDocument
     * @param $soggettoTitolare
     * @return CCTUploadResponse
     * @throws GuzzleException
     */
    public function uploadFile(string $documentType, $pdiDocument, $soggettoTitolare = null): CCTUploadResponse
    {
        try {

            $submitterUuid = $this->infrastructure->getUser()['submitterUuid'];
            $holderUuid = $this->infrastructure->getUser()['holderUuid'];

            if (empty($submitterUuid) && empty($holderUuid)) {
                $this->retriveCoordinates();
            }

            $agyo_id = $this->infrastructure->getUser()['agyo_id'] ?? '1';

            list($client, $headers) = $this->getClient($agyo_id);
            $options = [
                'multipart' => [
                    [
                        'name' => 'fileMetadata',
                        'contents' => json_encode([
                            'hash_256' => $pdiDocument->file->HashValue,
                            'name' => $pdiDocument->file->FileName,
                            'externalApplicationId' => $this->handlerName,

                        ]),
                        'headers' => ['Content-Type' => 'application/json']
                    ],
                    [
                        'name' => 'details',
                        'contents' => json_encode(
                            $pdiDocument->details
                        ),
                        'headers' => ['Content-Type' => 'application/json']
                    ],
                    [
                        'name' => 'holderUuid',
                        'contents' => $holderUuid
                    ],
                    [
                        'name' => 'submitterUuid',
                        'contents' => $submitterUuid
                    ],
                    [
                        'name' => 'file',
                        'contents' => $pdiDocument->file->FileContent,
                        'filename' => $pdiDocument->file->FileName
                    ]
                ]];

            $request = new Request('POST', sprintf('%s%s',
                $this->restUploadBaseUrl,
                '/digital/v1/backoffice/input-package'
            ), $headers);

            $res = $client->sendAsync($request, $options)->wait();
            $decodedResponse = json_decode($res->getBody(), false);

            return new CCTUploadResponse(null, $decodedResponse->id);
        } catch (GuzzleException $e) {
            throw $e;
        }


    }


    /**
     * @param $submitterUuid
     * @param $holderUuid
     * @return bool
     */
    public function isDigital($submitterUuid, $holderUuid)
    {
        $agyo_id = $this->infrastructure->getUser()['agyo_id'] ?? '1';
        list($client, $headers) = $this->getClient($agyo_id);

        $request = new Request(
            'GET',
            sprintf(
                '%s/api/v1/config/%s/%s/config-status-report',
                $this->configBaseUrl,
                $submitterUuid,
                $holderUuid
            ),
            $headers
        );

        $response = $client->sendAsync($request)->wait();
        $decodeResponse = json_decode($response->getBody()->getContents(), false);

        return $decodeResponse->configType === 'DIGITAL';

    }

    /**
     * @param string $agyo_id
     * @return array
     */
    private function getClient(string $agyo_id): array
    {
        $client = new Client();
        $headers = [
            'accept' => 'application/json',
            'X-App-Name' => $this->handlerName,
            'X-App-Version' => $this->handlerVersion,
            'X-Request-ID' => $this->infrastructure->getCustomerBucket()->getUid(),
            'X-Correlation-ID' => $this->infrastructure->getCustomerBucket()->getUid(),
            'X-User-ID' => $agyo_id,
            'X-Item-ID' => $agyo_id,
            'Authorization' => 'Bearer ' . $this->digitalM2MToken
        ];
        return array($client, $headers);
    }

//    private function retriveCoordinates()
//    {
//        $agyo_id = $this->infrastructure->getUser()['agyo_id'] ?? '1';
//        list($client, $headers) = $this->getClient($agyo_id);
//
//        $request = new Request(
//            'GET',
//            sprintf(
//                '%s/api/v1/config/search-preservation-info',
//                $this->configBaseUrl
//            ),
//            $headers
//        );
//
//        $options = [
//            'body' => [
//                "holderVatNumber" => '',
//                "holderTaxId" => '',
//                "holderGovCode" => ''
//            ]
//        ];
//
//        $client->send($request, $options);
//    }


}