<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes;

use Carbon\Carbon;
use stdClass;

class PdI2056DC extends AbstractPdIBase
{
    const DOCUMENT_TYPE_ID="2056";

    public function __construct($isLegacy, $fileName, $fileSize, $fileContent, $data)
    {
        $this->fileName = $fileName;
        $this->fileSize = $fileSize;
        $this->fileContent = $fileContent;
        $this->fileType = AbstractPdIBase::EML;
        $this->data = $data;
        $this->isLegacy = $isLegacy;
    }


    public function build(): array
    {

        [$pdiBase, $soggettoTitolare] = parent::build();

        if ($this->isLegacy) {

            return [
                'pdiBase' => array_merge((array)$pdiBase, $this->buildLegacy()),
                'soggettoTitolare' => $soggettoTitolare
            ];
        }
        return [
            'pdiBase' => ['file' => $pdiBase, 'details' => $this->buildDigital()],
            'soggettoTitolare' => null
        ];
    }


    /**
     * @return mixed
     */
    public function getDocumentTypeId()
    {
        return self::DOCUMENT_TYPE_ID;
    }

    private function buildLegacy(): array
    {
        $pdIBase = new StdClass();
        $pdIBase->AnnoMessaggio = $this->data["email"]["year"];
        $pdIBase->CasellaMail_Pec = $this->data["email"]["email_address"];
        $pdIBase->DataMessaggio = parent::getDate($this->data["email"]["date"]);
        $pdIBase->OggettoMessaggio = $this->data["email"]["subject"];
        $pdIBase->IdMessaggio = $this->data["email"]["message_uid"];
        $pdIBase->MittenteMessaggio = $this->data["email"]["from"];
        $pdIBase->DestinatarioMessaggio = $this->data["email"]["to"];

        return (array)$pdIBase;
    }

    private function buildDigital(): array
    {
        return [
                'tipologia_documentale' => $this->getDocumentTypeId(),
                'anno_riferimento' => (int)$this->data["email"]["year"],
                'dati_registrazione' => [
                    'numero' => $this->data["email"]["message_uid"],
                    'data' => (new Carbon($this->data["email"]["date"]))->toJSON()
                ],
                'custom_metadata' => [
                    'casella_pec' => $this->data["email"]["email_address"]
                ],
                'soggetti' => [
//                        'destinatario' => [
//                            'cognome' => $this->data["email"]["to"]
//                        ],
//                        'mittente' => [
//                             'cognome' => $this->data["email"]["from"]
//                        ]
                ],
                'chiave_descrittiva' => [
                    'oggetto' => $this->data["email"]["subject"]
                ]
        ];
    }
}

?>
