<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes;

use Carbon\Carbon;
use stdClass;

class PdI2068DC extends AbstractPdIBase{

    const DocumentType = "Registro unico";
    const DOCUMENT_TYPE_ID="2056";


    public function __construct($isLegacy, $fileName, $fileSize, $fileContent, $data)
    {
        $this->fileName = $fileName;
        $this->fileSize = $fileSize;
        $this->fileContent = $fileContent;
        $this->data = $data;
        $this->isLegacy = $isLegacy;
    }

    public function build(): array
    {

        [$pdiBase, $soggettoTitolare] = parent::build();

        if ($this->isLegacy) {

            return [
                'pdiBase' => array_merge((array)$pdiBase, $this->buildLegacy()),
                'soggettoTitolare' => $soggettoTitolare
            ];
        }
        return [
            'pdiBase' => ['file' => $pdiBase, 'details' => $this->buildDigital()],
            'soggettoTitolare' => null
        ];
    }

    public function isLegacy() : bool{
        return $this->isLegacy;
    }

    /**
     * @return mixed
     */
    public function getDocumentTypeId()
    {
        return self::DOCUMENT_TYPE_ID;
    }


	public function buildLegacy() {

			$pdIBase = new StdClass();
			$pdIBase->PeriodoRiferimentoAnno = $this->data["antiriciclaggio"]["year"];
			$pdIBase->DataDocumento = parent::getDate($this->data["antiriciclaggio"]["date"]);
			$pdIBase->TipoDocumento = self::DocumentType;

            return (array)$pdIBase;
	}


    public function buildDigital()
    {
        return [
                'tipologia_documentale' => $this->getDocumentTypeId(),
                'anno_riferimento' => (int)$this->data["antiriciclaggio"]["year"],
                'dati_registrazione' => [
                    'data' => (new Carbon($this->data["antiriciclaggio"]["date"]))->toJSON()
                ],
                'custom_metadata' => [
                    'tipo' => self::DocumentType
                ],
                "soggetti" => []
        ];
    }
}
?>