<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes;

use Carbon\Carbon;
use stdClass;

class PdI1001DC extends AbstractPdIBase
{

    const DOCUMENT_TYPE_ID = "1001";

    public function __construct($isLegacy, $fileName, $fileSize, $fileContent, $data)
    {
        $this->fileName = $fileName;
        $this->fileSize = $fileSize;
        $this->fileContent = $fileContent;
        $this->data = $data;
        $this->isLegacy = $isLegacy;
    }


    public function build(): array
    {

        [$pdiBase, $soggettoTitolare] = parent::build();

        if ($this->isLegacy) {

            return [
                'pdiBase' => array_merge((array)$pdiBase, $this->buildLegacy()),
                'soggettoTitolare' => $soggettoTitolare
            ];
        }
        return [
            'pdiBase' => ['file' => $pdiBase, 'details' => $this->buildDigital()],
            'soggettoTitolare' => null
        ];
    }

    /**
     * @return mixed
     */
    public function getDocumentTypeId()
    {
        return self::DOCUMENT_TYPE_ID;
    }

    public function buildLegacy()
    {
        $pdIBase = new StdClass();
        $pdIBase->SoggettoDestinatario = new StdClass();
        $pdIBase->SoggettoDestinatario->IdPaese = $this->data["bill"]["addresseeCountry"];
        $pdIBase->SoggettoDestinatario->PartitaIVA = $this->data["bill"]["addresseeVatNumber"];
        $pdIBase->SoggettoDestinatario->Denominazione = $this->data["bill"]["addresseeName"];
        $pdIBase->PeriodoImposta = $this->data["bill"]["year"];
        $pdIBase->DataEmissione = parent::getDate($this->data["bill"]["sent_at"]);
        $pdIBase->NumeroFattura = $this->data["bill"]["number"];
        $pdIBase->NomeFileOriginale = $this->data["bill"]["filename"];
        $pdIBase->CodiceIPA = $this->data["bill"]["IPACode"];
        $pdIBase->CodiceSdI = $this->data["bill"]["SdICode"];
        $pdIBase->TipoFormato = $this->data["bill"]["type"];

        return (array)$pdIBase;

    }

    public function buildDigital() : array
    {
        return [
                'tipologia_documentale' => $this->getDocumentTypeId(),
                'anno_riferimento' => (int)$this->data["bill"]["year"],
                'dati_registrazione' => [
                    'data' => (new Carbon($this->data["bill"]["sent_at"]))->toJSON(),
                    'numero' => $this->data["bill"]["number"]
                ],
                'soggeti' => [
                    'destinatario' => [
                        'codiceIpa' => $this->data["bill"]["IPACode"]
                    ]
                ],
                'custom_metadata' => [
                    'identificativo_sdi' => $this->data["bill"]["SdICode"],
                    'tipo_formato' => $this->data["bill"]["type"]
                ]
        ];
    }
}

?>