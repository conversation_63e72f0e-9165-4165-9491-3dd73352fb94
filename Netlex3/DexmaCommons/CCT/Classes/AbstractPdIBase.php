<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes;

use Dexma<PERSON>om<PERSON>\CCT\Interfaces\PdIBaseInterface;
use stdClass;

abstract class AbstractPdIBase implements PdIBaseInterface {
	const SHA_256 = "SHA256";
	const EML = "eml";
	const XML_P7M = "xml_p7m";
	const PDF = "pdf";

    protected $fileName;
    protected $fileSize;
    protected $fileContent;
    protected $fileType;
    protected $data;
    protected $pdIBase;
    protected $isLegacy;

	function build() : array
    {
        $contents = $this->fileContent->getContents();

        $pdIBase = (object)[];
        $pdIBase->FileType = $this->fileType;
        $pdIBase->HashValue = hash(self::SHA_256, $contents);
        $pdIBase->HashType = self::SHA_256;
        $pdIBase->FileName = $this->fileName;
        $pdIBase->FileSize = $this->fileSize;
        $pdIBase->FileContent = $contents;

        $soggettoTitolare = new StdClass();
        $soggettoTitolare->IdPaese = $this->data['owner']["country"];
        $soggettoTitolare->PartitaIVA = $this->data['owner']["vatNumber"];
        $soggettoTitolare->CodiceFiscale = $this->data['owner']["fiscalCode"];
        $soggettoTitolare->Denominazione = $this->data['owner']["description"];

        return [$pdIBase, $soggettoTitolare];

	}

    public function isLegacy() : bool{
        return $this->isLegacy;
    }

    protected function getDate($d) {
        return date("c", strtotime($d));
    }

    abstract public function getDocumentTypeId();

}
?>