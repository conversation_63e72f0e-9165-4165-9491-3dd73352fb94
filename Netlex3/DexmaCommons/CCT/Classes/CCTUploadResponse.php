<?php

namespace <PERSON>ma<PERSON>om<PERSON>\CCT\Classes;

class CCTUploadResponse
{

    private $idDoc;
    private $idPdI;

    /**
     * @return mixed
     */
    public function getIdDoc()
    {
        return $this->idDoc;
    }

    /**
     * @return mixed
     */
    public function getIdPdI()
    {
        return $this->idPdI;
    }

    public function __construct($idDoc, $idPdI)
    {
        $this->idDoc = $idDoc;
        $this->idPdI = $idPdI;
    }
}