<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Services;

//use <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Handler\DigitalCCTHandler;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CCT\Classes\CCTUploadResponse;
use DexmaCom<PERSON>\CCT\Handler\DigitalCCTHandler;
use <PERSON>maCom<PERSON>\CCT\Handler\LegacyCCTHandler;
use <PERSON><PERSON><PERSON>om<PERSON>\CCT\Interfaces\PdIBaseInterface;
use Exception;
use Netlex3\Software\patterns\Classes\ZendInfrastructure;
use Netlex3\Software\patterns\Domains\Users\Repositories\UsersRepository;

class CCTService
{

    private static $instance;
    private $infrastructure;
    private $userRepository;

    private function __construct(ZendInfrastructure $infrastructure)
    {
        $this->infrastructure = $infrastructure;
        $this->userRepository = new UsersRepository($infrastructure);

    }

    public static function getInstance(ZendInfrastructure $infrastructure) : CCTService {
        if(self::$instance === null){
            self::$instance = new self($infrastructure);
        }

        return self::$instance;
    }

    /**
     * Returns if the customer is LEGACY or DIGITAL
     * @return bool
     */
    public function isLegacy() : bool{
        $cctCredential = $this->userRepository->findUserById($this->infrastructure->getUser()['id']);

        if(!empty($cctCredential['submitterUuid']) && !empty($cctCredential['holderUuid']) ){
            $digitalCCTHandler = new DigitalCCTHandler($this->infrastructure);

            //TODO quando il cliente ottine credenziali digital non può piu tornare legacy
            $isDigital = $digitalCCTHandler->isDigital(
                $cctCredential['submitterUuid'],
                $cctCredential['holderUuid']
            );

            return !$isDigital;
        }

        return true;
    }

    public function uploadFile(PdIBaseInterface $document) : ?CCTUploadResponse{
        try {
            $build = $document->build();
            if ($document->isLegacy()) {
                $legacyCCTHandler = new LegacyCCTHandler($this->infrastructure);
                return $legacyCCTHandler->uploadFile(
                    $document->getDocumentTypeId(),
                    $build['pdiBase'],
                    $build['soggettoTitolare']
                );
            }

            $digitalCCTHandler = new DigitalCCTHandler($this->infrastructure);
            return $digitalCCTHandler->uploadFile(
                $document->getDocumentTypeId(),
                (object)$build['pdiBase']
            );
        }
        catch(Exception $e){
            return null;
        }

    }

    public function downloadFile($idObject, $idDoc){
        if($this->isLegacy()){
            $legacyCCTHandler = new LegacyCCTHandler($this->infrastructure);
            return $legacyCCTHandler->downloadFile($idObject, $idDoc);
        }

        $digitalCCTHandler = new DigitalCCTHandler($this->infrastructure);
        return $digitalCCTHandler->downloadFile($idObject, $idDoc);

    }

    private function gettCCTStatus(){

    }



}