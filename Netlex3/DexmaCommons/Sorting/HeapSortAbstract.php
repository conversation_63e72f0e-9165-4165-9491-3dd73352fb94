<?php

namespace Dex<PERSON><PERSON>ommons\Sorting;

abstract class HeapSortAbstract {

    private $sortDirection = 'ASC';

    /**
     * get largest value according to sort direction and sort field
     *
     * @param array $array
     * @param int $size
     * @param int $left
     * @param int $right
     * @param int $largest
     * @return int
     */
    abstract protected function getLargest(array &$array, int $size, int $left, int $right, int $largest) : int ;

    /**
     * @param string $direction
     * @return void
     */
    public function setSortDirection(string $direction = 'ASC') : void {
        $this->sortDirection = $direction;
    }

    /**
     * Make heap and execute HeapSort
     *
     * @param array $array
     * @return void
     */
    public function heapSort(array &$array) : void {
        $size = count($array);
        $i = 0;
        $j = 0;
        $temp = null;

        for ($i = floor($size / 2) - 1; $i >= 0; $i--) {
            $this->heapify($array, $size, $i);
        }

        for ($j = $size - 1; $j >= 0; $j--) {
            $temp = $array[0];
            $array[0] = $array[$j];
            $array[$j] = $temp;
            $this->heapify($array, $j, 0);
        }
    }

    /**
     * Heapsort algorithm
     *
     * @param array $array
     * @param int $size
     * @param int $root
     * @return void
     */
    protected function heapify(array &$array, int $size, int $root) : void {
        $largest = $root;
        $left = 2 * $root + 1;
        $right = 2 * $root + 2;
        $temp = null;
        $largest = $this->getLargest($array, $size, $left, $right, $largest);

        if ($largest !== $root) {
            $temp = $array[$root];
            $array[$root] = $array[$largest];
            $array[$largest] = $temp;
            $this->heapify($array, $size, $largest);
        }
    }
}