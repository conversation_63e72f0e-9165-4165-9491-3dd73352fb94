<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Sorting\HeapSort;

require_once DEXMA_COMMONS . '/Sorting/HeapSortAbstract.php';

use <PERSON>maCommons\Sorting\HeapSortAbstract;

class HeapSortSimple extends HeapSortAbstract {

    private $sortDirection = 'ASC';

    protected function getLargest(array &$array, int $size, int $left, int $right, int $largest) : int {
        if ($this->sortDirection == 'DESC') {
            if ($left < $size && $array[$left] < $array[$largest]) {
                $largest = $left;
            }
            if ($right < $size && $array[$right] < $array[$largest]) {
                $largest = $right;
            }
        } else {
            if ($left < $size && $array[$left] > $array[$largest]) {
                $largest = $left;
            }
            if ($right < $size && $array[$right] > $array[$largest]) {
                $largest = $right;
            }
        }
        return $largest;
    }

}
