<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Sorting\HeapSort;

require_once DEXMA_COMMONS . '/Sorting/HeapSortAbstract.php';

use <PERSON>maCom<PERSON>\Sorting\HeapSortAbstract;

class HeapSortMultilevel extends HeapSortAbstract
{

    private $sortField = null;
    private $sortDirection = 'ASC';

    /**
     * Set sort field
     *
     * @param string $field
     * @return void
     */
    public function setSortField(string $field) : void {
        $this->sortField = $field;
    }

    protected function getLargest(array &$array, int $size, int $left, int $right, int $largest) : int {
        if ($this->sortDirection == 'DESC') {
            if ($left < $size && $array[$left][$this->sortField] < $array[$largest][$this->sortField]) {
                $largest = $left;
            }
            if ($right < $size && $array[$right][$this->sortField] < $array[$largest][$this->sortField]) {
                $largest = $right;
            }
        } else {
            if ($left < $size && $array[$left][$this->sortField] > $array[$largest][$this->sortField]) {
                $largest = $left;
            }
            if ($right < $size && $array[$right][$this->sortField] > $array[$largest][$this->sortField]) {
                $largest = $right;
            }
        }
        return $largest;
    }
}
