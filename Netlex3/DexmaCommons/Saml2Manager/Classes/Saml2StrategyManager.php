<?php

namespace <PERSON>ma<PERSON>om<PERSON>\Saml2Manager\Classes;

use DexmaCommons\Saml2Manager\Interfaces\Saml2StrategyInterface;

class Saml2StrategyManager
{
    private $strategy;
    private $clientId;
    private $settings;


    /**
     * @param $settings
     * @param Saml2StrategyInterface $strategy
     */
    public function __construct($settings, Saml2StrategyInterface $strategy)
    {
        $this->settings = $settings;
        $this->strategy = $strategy;
    }

    /**
     * @param $params
     * @param bool $isProxed
     * @param bool $forceLogin if true it will force the login even if the user is already logged in
     * @return void
     */
    public function login($params, bool $isProxed = false, bool  $forceLogin = false): void
    {
        $this->strategy->login($this->settings, $params, $isProxed, $forceLogin);
    }







}