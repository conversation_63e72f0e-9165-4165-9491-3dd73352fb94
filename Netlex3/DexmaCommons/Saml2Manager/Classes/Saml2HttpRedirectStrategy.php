<?php

namespace DexmaCommons\Saml2Manager\classes;

use DexmaCommons\Saml2Manager\Interfaces\Saml2StrategyInterface;
use OneLogin_Saml2_Auth;

class Saml2HttpRedirectStrategy implements Saml2StrategyInterface
{

    public function login($settings, $params, bool $isProxed = false, bool $forceLogin = false)
    {
        $auth = new OneLogin_Saml2_Auth($settings->getSettings());
        $auth->login('', $params, $forceLogin);
    }

    public function logout(array $setting)
    {
        // TODO: Implement logout() method.
    }
}