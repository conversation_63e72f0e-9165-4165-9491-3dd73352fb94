<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Saml2Manager\Classes;

require_once(SITE_ROOT . '/vendor/onelogin/php-saml/_toolkit_loader.php');


class Saml2Settings
{
    private $defaultSettings;
    private $clientId;

    public function __construct()
    {

        $x509certDir = realpath(dirname(dirname((__DIR__))) . '/..') . '/Software/data/x509certs/';
        $netlexSamlCertName = 'saml_cert.pem';
        $netlexSamlKeyName = 'saml_cert.key';

        $this->defaultSettings = [
            // If 'strict' is True, then the PHP Toolkit will reject unsigned
            // or unencrypted messages if it expects them signed or encrypted
            // Also will reject the messages if not strictly follow the SAML
            // standard: Destination, NameId, Conditions ... are validated too.
            'strict' => true,

            // Enable debug mode (to print errors]
            'debug' => false,

            // Set a BaseURL to be used instead of try to guess
            // the BaseURL of the view that process the SAML Message.
            // Ex. http://sp.example.com/
            //     http://example.com/sp/
            'baseurl' => null,

            // Service Provider Data that we are deploying
            'sp' => [
                // Identifier of the SP entity  (must be a URI]
                'entityId' => 'https://subdomain.netlex.cloud/',
                // Specifies info about where and how the <AuthnResponse> message MUST be
                // returned to the requester, in this case our SP.
                'assertionConsumerService' => [
                    // URL Location where the <Response> from the IdP will be returned
                    'url' => 'https://subdomain.netlex.cloud/saml/idp-auth-return',
                    // SAML protocol binding to be used when returning the <Response>
                    // message.  SAML Toolkit supports for this endpoint the
                    // HTTP-POST binding only
                    'binding' => 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST',
                ],
                // If you need to specify requested attributes, set a
                // attributeConsumingService. nameFormat, attributeValue and
                // friendlyName can be omitted. Otherwise remove this section.
                "attributeConsumingService" => [
                    "serviceName" => "SP test",
                    "serviceDescription" => "Test Service",
                    "requestedAttributes" => [
                        [
                            "isRequired" => true,
                            "nameFormat" => "urn:oasis:names:tc:SAML:2.0:attrname-format:basic",
                            "friendlyName" => "email",
                        ],
                        [
                            "isRequired" => true,
                            "name" => "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name",
                            "nameFormat" => "urn:oasis:names:tc:SAML:2.0:attrname-format:basic",
                            "friendlyName" => "emailaddress",
                        ]
                    ]
                ],
                // Specifies info about where and how the <Logout Response> message MUST be
                // returned to the requester, in this case our SP.
								'singleLogoutService' => [
                    // URL Location where the <Response> from the IdP will be returned
                    'url' => 'https://subdomain.netlex.cloud/saml/logout',
                    // SAML protocol binding to be used when returning the <Response>
                    // message.  SAML Toolkit supports for this endpoint the
                    // HTTP-Redirect binding only
                    'binding' => 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST'
                ],
                // Specifies constraints on the name identifier to be used to
                // represent the requested subject.
                // Take a look on lib/Saml2/Constants.php to see the NameIdFormat supported
                'NameIDFormat' => 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',

                // Usually x509cert and privateKey of the SP are provided by files placed at
                // the certs folder. But we can also provide them with the following parameters
                'x509cert' =>
                    $this->extrapolate(
                        file_get_contents($x509certDir . 'netlexcerts/' . $netlexSamlCertName,
                            FILE_USE_INCLUDE_PATH),
                        "CERTIFICATE"),

                'privateKey' => $this->extrapolate(
                    file_get_contents($x509certDir . 'netlexcerts/' . $netlexSamlKeyName,
                        FILE_USE_INCLUDE_PATH),
                    "PRIVATE KEY")

                /*
                 * Key rollover
                 * If you plan to update the SP x509cert and privateKey
                 * you can define here the new x509cert and it will be
                 * published on the SP metadata so Identity Providers can
                 * read them and get ready for rollover.
                 */
                // 'x509certNew' => '',
            ],

//     Identity Provider Data that we want connect with our SP
            'idp' => [
                // Identifier of the IdP entity  (must be a URI]
                'entityId' => '',
                // SSO endpoint info of the IdP. (Authentication Request protocol]
                'singleSignOnService' => [
                    // URL Target of the IdP where the SP will send the Authentication Request Message
                    'url' => '',
                    // SAML protocol binding to be used when returning the <Response>
                    // message.  SAML Toolkit supports for this endpoint the
                    // HTTP-Redirect binding only
                    'binding' => 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect',
                ],
                // SLO endpoint info of the IdP.
                'singleLogoutService' => [
                    // URL Location of the IdP where the SP will send the SLO Request
                    'url' => '',
                    // URL location of the IdP where the SP will send the SLO Response (ResponseLocation]
                    // if not set, url for the SLO Request will be used
                    'responseUrl' => '',
                    // SAML protocol binding to be used when returning the <Response>
                    // message.  SAML Toolkit supports for this endpoint the
                    // HTTP-Redirect binding only
                    'binding' => 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect',
                ],
                // Public x509 certificate of the IdP
                'x509cert' => '',
                /*
                 *  Instead of use the whole x509cert you can use a fingerprint in
                 *  order to validate the SAMLResponse, but we don't recommend to use
                 *  that method on production since is exploitable by a collision
                 *  attack.
                 *  (openssl x509 -noout -fingerprint -in "idp.crt" to generate it,
                 *   or add for example the -sha256 , -sha384 or -sha512 parameter]
                 *
                 *  If a fingerprint is provided, then the certFingerprintAlgorithm is required in order to
                 *  let the toolkit know which Algorithm was used. Possible values: sha1, sha256, sha384 or sha512
                 *  'sha1' is the default value.
                 */
                // 'certFingerprint' => '',
                // 'certFingerprintAlgorithm' => 'sha1',

                /* In some scenarios the IdP uses different certificates for
                 * signing/encryption, or is under key rollover phase and more
                 * than one certificate is published on IdP metadata.
                 * In order to handle that the toolkit offers that parameter.
                 * (when used, 'x509cert' and 'certFingerprint' values are
                 * ignored].
                 */
                // 'x509certMulti' => [
                //      'signing' => [
                //          0 => '<cert1-string>',
                //      ],
                //      'encryption' => [
                //          0 => '<cert2-string>',
                //      ]
                // ],
            ],
            'compress' => array(
                'requests' => true,
                'responses' => false
            ),
            'security' => array(
                'wantAssertionsSigned' => true,
                'authnRequestsSigned' => true,
                'signatureAlgorithm' => 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256',
                'digestAlgorithm' => 'http://www.w3.org/2001/04/xmlenc#sha256',
                'signMetadata' => true,
				'requestedAuthnContext' => false,
                'wantAssertionsEncrypted' => false,
            ),
        ];
    }


    public function setIdpSettings($sitekey, $data)
    {

        $samlData = (array)json_decode($data);

        if (json_last_error() === JSON_ERROR_SYNTAX) {
            $re = '/\/([-\w\d]+)\//';
            $IdPSettings = (\OneLogin_Saml2_IdPMetadataParser::parseRemoteXML($data))['idp'];

            if(str_contains($IdPSettings['entityId'], 'sts.windows.net')){
                preg_match_all($re, $IdPSettings['entityId'], $matches, PREG_SET_ORDER, 0);
                $this->clientId = $matches[0][1];
            }else{
                $this->clientId = sprintf("%s.netlex.cloud", $sitekey);
            }
        } else {
            if (!empty($samlData['reply_url'])) {
                $this->setAssertionConsumerServiceURL($samlData['reply_url']);
            }
            $this->initializeJSONIdP($samlData, $IdPSettings);
            $this->clientId = $samlData['client_id'];
        }

        $this->defaultSettings['idp'] = $IdPSettings;
    }

    public function setSPSettings($sitekey)
    {
        $this->replace_in_array('subdomain', $sitekey, $this->defaultSettings['sp']);
    }

    public function setDeflate(bool $deflate)
    {
        $this->defaultSettings['compress']['requests'] = $deflate;
    }

    public function setAssertionConsumerServiceURL(string $url)
    {
        $this->
        defaultSettings['sp']['assertionConsumerService']['url'] =
            $url;
    }

    public function getSettings()
    {
        return $this->defaultSettings;
    }

    public function getClientId()
    {
        return $this->clientId;
    }


    private function initializeJSONIdP($idpJsonData, &$idp)
    {
        $x509certDir = realpath(dirname(dirname((__DIR__))) . '/..') . '/Software/data/x509certs/';

        $idp['entityId'] = $idpJsonData['idp_identifier'];
        $idp['singleSignOnService']['url'] = $idpJsonData['login_url'];
        $idp['singleLogoutService']['url'] = $idpJsonData['logout_url'];
        $idp['x509cert'] =
            file_get_contents(
                $x509certDir . ($idpJsonData['x509cert_name'] ?? ''),
                FILE_USE_INCLUDE_PATH);
    }

    private function replace_in_array($find, $replace, &$array)
    {
        array_walk_recursive($array, function (&$array) use ($find, $replace) {
            if (str_contains($array, $find)) {
                $array = preg_replace('/' . $find . '/', $replace, $array);
            }
        });
        return $array;
    }

    private function extrapolate($certificate, $type)
    {
        $certificate = preg_replace("/[\r\n]*/", "", $certificate);
        $re = '/-----BEGIN '.$type.'-----(.*?)-----END '.$type.'-----$/';
        $subst = "$1";

        return preg_replace($re, $subst, $certificate);

    }
}

