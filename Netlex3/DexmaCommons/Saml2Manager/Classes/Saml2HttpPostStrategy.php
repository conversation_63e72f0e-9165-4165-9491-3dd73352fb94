<?php

namespace <PERSON>maCom<PERSON>\Saml2Manager\Classes;

use <PERSON>maCom<PERSON>\Saml2Manager\Interfaces\Saml2StrategyInterface;
use OneLogin_Saml2_AuthnRequest;
use OneLogin_Saml2_Settings;
use OneLogin_Saml2_Utils;
use XMLSecurityDSig;

class Saml2HttpPostStrategy implements Saml2StrategyInterface
{

    public function login($settings, $params, bool $isProxed = false, bool $forceLogin = false)
    {
        OneLogin_Saml2_Utils::setProxyVars($isProxed);

        $settings = new OneLogin_Saml2_Settings($settings->getSettings());

        $security = $settings->getSecurityData();

        $authNRequest = new OneLogin_Saml2_AuthnRequest($settings, $forceLogin);
        $authNRequestXML = $authNRequest->getXML();

        $key = $settings->getSPkey();
        $cert = $settings->getSPcert();

        $signatureAlgorithm = $security['signatureAlgorithm'];
        $digestAlgorithm = XMLSecurityDSig::SHA1;
        $signedAuthNRequestXML = OneLogin_Saml2_Utils::addSign($authNRequestXML, $key, $cert, $signatureAlgorithm, $digestAlgorithm);
        $encodedAuthNRequest = base64_encode($signedAuthNRequestXML);

        $request = $this->requestConstructor(
            $settings->getIdPSSOUrl(),
            $encodedAuthNRequest,
            '');
        echo $request;
        exit();

    }

    public function logout(array $setting)
    {
        // TODO: Implement logout() method.
    }
    /**
     * @param $location
     * @param $authnReq
     * @param bool $forceLogin
     * @param string $relayState
     * @return string
     */
    private function requestConstructor($location, $authnReq, $relayState = '')
    {
        return "
                <html>
                <body onload='document.forms[0].submit()'>
                    <form method='post' action='".$location."'>
                        <input type='hidden' name='SAMLRequest'
                            value='" . $authnReq . "'>
                        <input type='hidden' name='RelayState' value='" . $relayState . "'>
                        <input style='opacity:0' type='submit' value='Invia'/>
                    </form>
                </body>
            </html>";
    }
}