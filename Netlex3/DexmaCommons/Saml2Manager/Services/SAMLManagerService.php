<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Saml2Manager\Services;

use <PERSON><PERSON><PERSON>om<PERSON>\Saml2Manager\Classes\Saml2HttpPostStrategy;
use <PERSON>maCom<PERSON>\Saml2Manager\Classes\Saml2HttpRedirectStrategy;
use <PERSON>maCom<PERSON>\Saml2Manager\Classes\Saml2StrategyManager;
use <PERSON>ma<PERSON>om<PERSON>\Saml2Manager\Classes\Saml2Settings;
use OneLogin_Saml2_Constants;
use OneLogin_Saml2_IdPMetadataParser;

class SAMLManagerService
{

    private static $instance;
    private $samlObject;
    private $settings;
    private $sitekey;

    private function __construct($samlObject, $sitekey)
    {
        $this->samlObject = $samlObject;
        $this->sitekey = $sitekey;

        $settings = new Saml2Settings();
        $settings->setIdpSettings($this->sitekey, $this->samlObject);
        $settings->setSPSettings($this->sitekey);
        $this->settings = $settings;
    }

    public static function getInstance($samlObject, $sitekey){
        if(empty(self::$instance)){
            self::$instance = new SAMLManagerService($samlObject, $sitekey);
        }
        return self::$instance;
    }

    /**
     * @param $samlObject
     * @return Saml2StrategyManager
     * @throws Exception
     */
    public function processLoginSSO(): Saml2StrategyManager
    {
        $contextStrategy = null;


        $method = OneLogin_Saml2_Constants::BINDING_HTTP_REDIRECT;
        if (json_last_error() === JSON_ERROR_SYNTAX) {
            $result = OneLogin_Saml2_IdPMetadataParser::parseRemoteXML($this->samlObject);

            $method = $result['idp']['singleSignOnService']['binding']
            === OneLogin_Saml2_Constants::BINDING_HTTP_REDIRECT ? OneLogin_Saml2_Constants::BINDING_HTTP_REDIRECT
                : OneLogin_Saml2_Constants::BINDING_HTTP_POST;
        }

        if ($method === OneLogin_Saml2_Constants::BINDING_HTTP_REDIRECT) {
            $this->settings->setDeflate(true);
            $contextStrategy = new Saml2StrategyManager(
                $this->settings, new Saml2HttpRedirectStrategy());
        }

        if ($method === OneLogin_Saml2_Constants::BINDING_HTTP_POST) {
            $this->settings->setDeflate(false);
            $contextStrategy = new Saml2StrategyManager(
                $this->settings, new Saml2HttpPostStrategy());
        }

        return $contextStrategy;
    }

    public function getSettings(){
        return $this->settings;
    }
}