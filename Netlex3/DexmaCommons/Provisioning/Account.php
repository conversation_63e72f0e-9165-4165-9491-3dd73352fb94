<?php
namespace Provisioning;

use DateTime;
use DateInterval;
use Zend_Db;

class Account
{
	private $dbProvisioning;
	private $uniqueid;
	private $customer;

	private $expirationDate;

	public static $_var = 'RIVENDITORE';
	public static $_diretta = 'DIRETTA';
	public static $_convenzione = 'CONVENZIONI';

	public static $_var_tollerance = 10000;


	public function __construct($db, $uniqueid)
	{
		if (!$db || !$uniqueid) {
			throw new \Exception('Atteso db e uniqueid');
		}

		$this->uniqueid = $uniqueid;
		$this->dbProvisioning = $db;

		$this->customer = $this->dbProvisioning->fetchRow('SELECT c.*, r.report_type FROM customers c LEFT JOIN retailers r ON r.retailer_code = c.retailer  WHERE c.uniqueid = ?', $uniqueid, Zend_Db::FETCH_OBJ);

		if ($this->customer) {
			$this->product = $this->dbProvisioning->fetchRow(ltrim('
				SELECT tolerance, days_before
				FROM products
				WHERE id = ?'),
				$this->customer->product_id,
				Zend_Db::FETCH_OBJ
			);

			$this->init();
		}
	}

	private function init()
	{
		$this->expirationDate = DateTime::createFromFormat('Y-m-d H:i:s', $this->customer->expiration_date);
		$this->expirationDate->setTime(23, 59, 59);
	}

	public function customerExists()
	{
		return null != $this->customer;
	}

	public function getCreationDate()
	{
		return DateTime::createFromFormat('Y-m-d H:i:s', $this->customer->creation_date);
	}

	public function getExpirationDate()
	{
		return $this->expirationDate;
	}

	public function getValidityDays()
	{
		$expirationDate = clone $this->expirationDate;
		$expirationDate->setTime(0, 0);
		$today = new DateTime;
		$today->setTime(0, 0);
		return (int) $today->diff($expirationDate)->format('%r%a');
	}

	/**
	 * Returns true if customer has a reseller
	 *
	 * @return bool
	 */
	public function isVar(): bool
	{
		return $this->customer->report_type === self::$_var;
	}


	/**
	 * Returns an integer representing the customer's tolerance buffer in days
	 *
	 * @return int
	 */
	public function getToleranceDays(): int
	{
		if ($this->product) {
			return $this->isVar() ? self::$_var_tollerance : (int) $this->product->tolerance;
		}

		return 0;
	}

	/**
	 * This function return Tollerance Days for Trials
	 *
	 * @return int
	 */
	public function getToleranceDaysTrial(): int
	{
		if ($this->product) {
			//* TODO: inserire parametro in provisioning products
			return 0;
		}

		return 0;
	}



	public function getDaysBefore()
	{
		if ($this->product) {
			return (int) $this->product->days_before;
		}

		return 0;
	}

	public function getSubscriptionType() {
		return $this->customer->subscription_type;
	}

	public function isDeleted()
	{
		return in_array($this->customer->status, array(-2, -3));
	}

	public function isDemo()
	{
		if ($this->customer->subscription_type == 88) {
			return false;
		} else return 0 === (int) $this->customer->payment_type;
	}

	public function isDisabled()
	{
		return -4 === (int) $this->customer->status;
	}

	public function isExpired()
	{
		return $this->expirationDate < new DateTime;
	}

	public function isExpiring()
	{
		if ($this->product) {
			$daysBeforeDate = clone $this->expirationDate;
			$daysBeforeDate->setTime(0, 0);
			$today = new DateTime;
			$today->setTime(0, 0);
			$days = (int) $today->diff($daysBeforeDate)->format('%r%a');

			return $days >= 0 && $days <= $this->getDaysBefore();
		}

		return false;
	}

	/**
	 * Return if a copy is expired an optional argument can be passed true/false
	 * to get different expirations based on payment type to define
	 * if a copy is a trial or not
	 *
	 * @param $payment_type
	 */
	public function toleranceIsExpired($payment_type_is_empty = false)
	{
		if ($this->product) {
			/*
			 * Added an optional param to get different tollarance expiration based on Trials or not
			 */
			$toleranceDays = $payment_type_is_empty ?  $this->getToleranceDaysTrial() : $this->getToleranceDays();
			$toleranceDate = clone $this->expirationDate;
			$toleranceDate->setTime(0, 0);
			$toleranceDate->add(new DateInterval("P{$toleranceDays}D"));
			$today = new DateTime;
			$today->setTime(0, 0);

			return (int) $today->diff($toleranceDate)->format('%r%a') < 0;
		}

		return true;
	}



	public function IsParticularCaseANDIsExpired()
	{
		if ($this->product) {

			$toleranceDate = clone $this->expirationDate;
			$toleranceDate->setTime(0, 0);


			if (!empty($this->customer->migration_time)){
				$toleranceDays = 15;
				$zeroDate = DateTime::createFromFormat('Y-m-d H:i:s',  $this->customer->migration_time);
				$zeroDate->setTime(0, 0);

				if ($toleranceDate->diff($zeroDate)->format('%r%a') > 0 ){
					$toleranceDate = clone ($zeroDate);
				}
			}else{
				$toleranceDays = 15;//modificabile in caso si decida di seguire logiche diverse per le scadenze
			}


			$toleranceDate->add(new DateInterval("P{$toleranceDays}D"));

			$today = new DateTime;
			$today->setTime(0, 0);

			return (int) $today->diff($toleranceDate)->format('%r%a') < 0;
		}

		return true;
	}

	public function getSpecialToleranceDays()
	{
		if ($this->product) {

			if (!empty($this->customer->migration_time)){
//				$toleranceDays = 7;
				$toleranceDays = 15;
				$zeroDate = DateTime::createFromFormat('Y-m-d H:i:s',  $this->customer->migration_time);
				$zeroDate->setTime(0, 0);

				$toleranceDate = clone $this->expirationDate;
				$toleranceDate->setTime(0, 0);

				$today = new DateTime;
				$today->setTime(0, 0);

				if ($toleranceDate->diff($zeroDate)->format('%r%a') > 0 ){
					$toleranceDate = clone $zeroDate;


				}else{
					$toleranceDate = clone $this->expirationDate;

				}

				$toleranceDate->add(new DateInterval("P{$toleranceDays}D"));



				return (int) $today->diff($toleranceDate)->format('%r%a');

			}else{
				return (int) $this->product->tolerance;
			}


		}

		return 0;
	}

}