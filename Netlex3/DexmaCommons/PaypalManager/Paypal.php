<?php
class PaypalManager_Paypal {

	private $PayPalMode,
			$PayPalApiUsername,
			$PayPalApiPassword,
			$PayPalApiSignature,
			$PayPalCurrencyCode,
			$PayPalReturnURL,
			$PayPalCancelURL,
			$successRedirect,
			$errorRedirect;

	public function __construct($PayPalMode, $PayPalApiUsername, $PayPalApiPassword, $PayPalApiSignature, $PayPalCurrencyCode, $PayPalReturnURL, $PayPalCancelURL, $successRedirect, $errorRedirect)
	{
		$this->PayPalMode = $PayPalMode;
		$this->PayPalApiUsername = $PayPalApiUsername;
		$this->PayPalApiPassword = $PayPalApiPassword;
		$this->PayPalApiSignature = $PayPalApiSignature;
		$this->PayPalCurrencyCode = $PayPalCurrencyCode;
		$this->PayPalReturnURL = $PayPalReturnURL;
		$this->PayPalCancelURL = $PayPalCancelURL;
		$this->successRedirect = $successRedirect;
		$this->errorRedirect = $errorRedirect;
	}

    function PPHttpPost($methodName_, $nvpStr_, $PayPalApiUsername, $PayPalApiPassword, $PayPalApiSignature, $PayPalMode) {
		// Set up your API credentials, PayPal end point, and API version.
        $API_UserName = urlencode($PayPalApiUsername);
        $API_Password = urlencode($PayPalApiPassword);
        $API_Signature = urlencode($PayPalApiSignature);

        if($PayPalMode=='sandbox')
        {
        	$paypalmode     =   '.sandbox';
        }
        else
        {
        	$paypalmode     =   '';
        }

        $API_Endpoint = "https://api-3t".$paypalmode.".paypal.com/nvp";
        $version = urlencode('76.0');

        // Set the curl parameters.
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $API_Endpoint);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);

        // Turn off the server and peer verification (TrustManager Concept).
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);

        // Set the API operation, version, and API signature in the request.
        $nvpreq = "METHOD=$methodName_&VERSION=$version&PWD=$API_Password&USER=$API_UserName&SIGNATURE=$API_Signature$nvpStr_";

        // Set the request as a POST FIELD for curl.
        curl_setopt($ch, CURLOPT_POSTFIELDS, $nvpreq);

        // Get response from the server.
        $httpResponse = curl_exec($ch);

        if(!$httpResponse) {
        	exit("$methodName_ failed: ".curl_error($ch).'('.curl_errno($ch).')');
        }

        // Extract the response details.
        $httpResponseAr = explode("&", $httpResponse);

        $httpParsedResponseAr = array();
        foreach ($httpResponseAr as $i => $value) {
           $tmpAr = explode("=", $value);
           if(sizeof($tmpAr) > 1) {
               $httpParsedResponseAr[$tmpAr[0]] = $tmpAr[1];
           }
       	}

        if((0 == sizeof($httpParsedResponseAr)) || !array_key_exists('ACK', $httpParsedResponseAr)) {
            exit("Invalid HTTP Response for POST request($nvpreq) to $API_Endpoint.");
        }

        return $httpParsedResponseAr;
    }

    public function process()
    {
	    if($_POST) //Post Data received from product list page.
	    {
	    	//Mainly we need 4 variables from an item, Item Name, Item Price, Item Number and Item Quantity.
	    	$ItemName = $_POST["itemname"]; //Item Name
	    	$ItemPrice = $_POST["itemprice"]; //Item Price
	    	$ItemNumber = $_POST["itemnumber"]; //Item Number
	    	$ItemQty = $_POST["itemQty"]; // Item Quantity
	    	$ItemTotalPrice = ($ItemPrice*$ItemQty); //(Item Price x Quantity = Total) Get total amount of product;

			$ItemTotalPrice = number_format($ItemTotalPrice, 2, '.', ',');

	    	//Data to be sent to paypal
	    	$padata =   '&CURRENCYCODE='.urlencode($this->PayPalCurrencyCode).
				    	'&PAYMENTACTION=Sale'.
				    	'&ALLOWNOTE=1'.
				    	'&PAYMENTREQUEST_0_CURRENCYCODE='.urlencode($this->PayPalCurrencyCode).
				    	'&PAYMENTREQUEST_0_AMT='.urlencode($ItemTotalPrice).
				    	'&PAYMENTREQUEST_0_ITEMAMT='.urlencode($ItemTotalPrice).
				    	'&L_PAYMENTREQUEST_0_QTY0='. urlencode($ItemQty).
				    	'&L_PAYMENTREQUEST_0_AMT0='.urlencode($ItemPrice).
				    	'&L_PAYMENTREQUEST_0_NAME0='.urlencode($ItemName).
				    	'&L_PAYMENTREQUEST_0_NUMBER0='.urlencode($ItemNumber).
				    	'&AMT='.urlencode($ItemTotalPrice).
				    	'&RETURNURL='.urlencode($this->PayPalReturnURL ).
				    	'&CANCELURL='.urlencode($this->PayPalCancelURL).
				    	'&SOLUTIONTYPE=Sole'.
				    	'&LANDINGPAGE=Billing';

	    	//We need to execute the "SetExpressCheckOut" method to obtain paypal token
	    	$paypal= new PaypalManager_Paypal($this->PayPalMode, $this->PayPalApiUsername, $this->PayPalApiPassword, $this->PayPalApiSignature, $this->PayPalCurrencyCode,
	    									  $this->PayPalReturnURL, $this->PayPalCancelURL, $this->successRedirect, $this->errorRedirect);
	    	$httpParsedResponseAr = $paypal->PPHttpPost('SetExpressCheckout', $padata, $this->PayPalApiUsername, $this->PayPalApiPassword, $this->PayPalApiSignature, $this->PayPalMode);

	    	//Respond according to message we receive from Paypal
	    	if("SUCCESS" == strtoupper($httpParsedResponseAr["ACK"]) || "SUCCESSWITHWARNING" == strtoupper($httpParsedResponseAr["ACK"]))
	    	{
	    		// If successful set some session variable we need later when user is redirected back to page from paypal.
	    		$_SESSION['itemprice'] =  $ItemPrice;
	    		$_SESSION['totalamount'] = $ItemTotalPrice;
	    		$_SESSION['itemName'] =  $ItemName;
	    		$_SESSION['itemNo'] =  $ItemNumber;
	    		$_SESSION['itemQTY'] =  $ItemQty;

	    		if($this->PayPalMode=='sandbox')
	    		{
	    			$paypalmode     =   '.sandbox';
	    		}
	    		else
	    		{
	    			$paypalmode     =   '';
	    		}
	    		//Redirect user to PayPal store with Token received.
	    		$paypalurl ='https://www'.$paypalmode.'.paypal.com/cgi-bin/webscr?cmd=_express-checkout&token='.$httpParsedResponseAr["TOKEN"].'';
	    		header('Location: '.$paypalurl);

	    	}else{
	    		//Show error message
	    		echo '<div style="color:red"><b>Error : </b>'.urldecode($httpParsedResponseAr["L_LONGMESSAGE0"]). 'item:'.$ItemTotalPrice .'</div>';
	    		echo '<pre>';
	    		print_r($httpParsedResponseAr);
	    		echo '</pre>';
	    	}
	    }

	    //Paypal redirects back to this page using ReturnURL, We should receive TOKEN and Payer ID
	    if(isset($_GET["token"]) && isset($_GET["PayerID"]))
	    {
	    	//we will be using these two variables to execute the "DoExpressCheckoutPayment"
	    	//Note: we haven't received any payment yet.

	    	$token = $_GET["token"];
	    	$playerid = $_GET["PayerID"];

	    	//get session variables
	    	$ItemPrice      = $_SESSION['itemprice'];
	    	$ItemTotalPrice = $_SESSION['totalamount'];
	    	$ItemName       = $_SESSION['itemName'];
	    	$ItemNumber     = $_SESSION['itemNo'];
	    	$ItemQTY        =$_SESSION['itemQTY'];

	    	$padata =   '&TOKEN='.urlencode($token).
				    	'&PAYERID='.urlencode($playerid).
				    	'&PAYMENTACTION='.urlencode("SALE").
				    	'&AMT='.urlencode($ItemTotalPrice).
				    	'&CURRENCYCODE='.urlencode($this->PayPalCurrencyCode);

	    	//We need to execute the "DoExpressCheckoutPayment" at this point to Receive payment from user.
	    	$paypal= new PaypalManager_Paypal($this->PayPalMode, $this->PayPalApiUsername, $this->PayPalApiPassword, $this->PayPalApiSignature, $this->PayPalCurrencyCode,
	    									  $this->PayPalReturnURL, $this->PayPalCancelURL, $this->successRedirect, $this->errorRedirect);
	    	$httpParsedResponseAr = $paypal->PPHttpPost('DoExpressCheckoutPayment', $padata, $this->PayPalApiUsername, $this->PayPalApiPassword, $this->PayPalApiSignature, $this->PayPalMode);

	    	//Check if everything went ok..
	    	if("SUCCESS" == strtoupper($httpParsedResponseAr["ACK"]) || "SUCCESSWITHWARNING" == strtoupper($httpParsedResponseAr["ACK"]))
	    	{
	    		header("location: $this->successRedirect");

	    		//             echo '<h2>Success</h2>';
	    		//             echo 'Your Transaction ID :'.urldecode($httpParsedResponseAr["TRANSACTIONID"]);
	    
	    		//                 /*
	    		//                 //Sometimes Payment are kept pending even when transaction is complete.
	    		//                 //because of Currency change, user choose other payment option or its pending review etc.
	    		//                 //hence we need to notify user about it and ask him manually approve the transiction
	    		//                 */
	    
	    		//                 if('Completed' == $httpParsedResponseAr["PAYMENTSTATUS"])
	    		//                 {
	    		//                     echo '<div style="color:green">Payment Received! Your product will be sent to you very soon!</div>';
	    		//                 }
	    		//                 elseif('Pending' == $httpParsedResponseAr["PAYMENTSTATUS"])
	    		//                 {
	    		//                     echo '<div style="color:red">Transaction Complete, but payment is still pending! You need to manually authorize this payment in your <a target="_new" href="http://www.paypal.com">Paypal Account</a></div>';
	    		//                 }
	    
	    		//             echo '<br /><b>Stuff to store in database :</b><br /><pre>';
	    
	    		//                 $transactionID = urlencode($httpParsedResponseAr["TRANSACTIONID"]);
	    		//                 $nvpStr = "&TRANSACTIONID=".$transactionID;
	    		//                 $paypal= new PaypalManager_Paypal();
	    		//                 $httpParsedResponseAr = $paypal->PPHttpPost('GetTransactionDetails', $nvpStr, $PayPalApiUsername, $PayPalApiPassword, $PayPalApiSignature, $PayPalMode);
	    
	    		//                 if("SUCCESS" == strtoupper($httpParsedResponseAr["ACK"]) || "SUCCESSWITHWARNING" == strtoupper($httpParsedResponseAr["ACK"])) {
	    
	    		//                     /*
	    		//                     #### SAVE BUYER INFORMATION IN DATABASE ###
	    		//                     $buyerName = $httpParsedResponseAr["FIRSTNAME"].' '.$httpParsedResponseAr["LASTNAME"];
	    		//                     $buyerEmail = $httpParsedResponseAr["EMAIL"];
	    		//                     $paymentStatus = $httpParsedResponseAr["PAYMENTSTATUS"];
	    
	    		//                     $conn = mysql_connect("localhost","MySQLUsername","MySQLPassword");
	    		//                     if (!$conn)
	    		//                     {
	    		//                      die('Could not connect: ' . mysql_error());
	    		//                     }
	    
	    		//                     mysql_select_db("Database_Name", $conn);
	    
	    		//                     mysql_query("INSERT INTO BuyerTable
	    		//                     (BuyerName,BuyerEmail,TransactionID,ItemName,ItemNumber, ItemAmount,ItemQTY,PaymentStatus)
	    		//                     VALUES
	    		//                     ('$buyerName','$buyerEmail','$transactionID','$ItemName',$ItemNumber, $ItemTotalPrice,$ItemQTY,$paymentStatus)");
	    
	    		//                     mysql_close($con);
	    		//                     */
	    
	    		//                     echo '<pre>';
	    		//                     print_r($httpParsedResponseAr);
	    		//                     echo '</pre>';
	    		//                 } else  {
	    		//                     echo '<div style="color:red"><b>GetTransactionDetails failed:</b>'.urldecode($httpParsedResponseAr["L_LONGMESSAGE0"]).'</div>';
	    		//                     echo '<pre>';
	    		//                     print_r($httpParsedResponseAr);
	    		//                     echo '</pre>';
	    
	    		//                 }
	    
	    	}else{
	    		$httpResponse = $httpParsedResponseAr["L_LONGMESSAGE0"];
	    		header("location: $this->errorRedirect?httpResponse=$httpResponse");
	    		//             echo '<div style="color:red"><b>Error : </b>'.urldecode($httpParsedResponseAr["L_LONGMESSAGE0"]).'</div>';
	    		//             echo '<pre>';
	    		//             print_r($httpParsedResponseAr);
	    		//             echo '</pre>';
	    	}
	    }
    }
}