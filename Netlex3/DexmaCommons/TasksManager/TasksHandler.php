<?php

//require(DEXMA_COMMONS . '/TasksManager/Entity/Task.php');
//require_once(DEXMA_COMMONS . '/Workflow/ComponentManager.php');


use <PERSON><PERSON><PERSON>om<PERSON>\Workflow\ComponentManager;
use TaskManager\Task;

class TasksHandler
{
    private $TH;
    private $s3Connector;
    private $sitekey;
    private static $MAIN_BUCKET = Task::MAIN_BUCKET;
    private static $OUTPUTFILE = Task::OUTPUTFILE;
    private static $ATTACHMENTS = Task::ATTACHMENTS;
    private static $FILETASK = Task::FILETASK;
    private static $TASKOUTPUT = Task::TASKOUTPUT;
    private static $DOCUMENTS = Task::DOCUMENTS;

    protected $error_messages = array(
        'upload_error' => 'Errore caricamento file nei task',
        'save_error' => 'Errore salvataggio task',
    );

    public function __construct($TH, $options = null, $error_messages = null)
	{
        $this->TH = $TH;
		if ($options) {
            $this->options = $options;
		}
		if ($error_messages) {
			$this->error_messages = $error_messages;
        }

        $this->s3Connector = $this->TH->s3Connector;

        $this->sitekey = $this->options && $this->options['sitekey']? $this->options['sitekey'] : SITEKEY;
    }

    public function save()
	{
        $post = $this->options['post'];
        $files = $this->options['files'];

        $task = array_map('trim', array(
            'title' => $post['title'],
            'text' => $post['text'],
            'status' => $post['status'],
            'created_at' => date('Y-m-d H:i:s')
        ));

        if (empty($post['id'])) {
            unset($task['id']);
        }

        // if (!empty($post['created_at'])) {
        //     $task['created_at'] = $this->getDateInSQLFormat($post['created_at'], '/');
        // }
        if (!empty($post['started_at'])) {
            $task['started_at'] = $this->getDateInSQLFormat($post['started_at'], '/');
        }
        if (!empty($post['done_at'])) {
            $task['done_at'] = $this->getDateInSQLFormat($post['done_at'], '/');
        }

        // associazione task a pratica
        if (!empty($post['fileUid'])) {
            $task['file_id'] = $this->TH->db->fetchOne('SELECT id FROM archivio WHERE uniqueid = ?', $post['fileUid']);
        }

        try {
            if (empty($post['id'])) {
                $task['created_by'] = $this->options['loggedUserId'];
                $this->TH->db->insert('tasks', $task);
                $id = $this->TH->db->lastInsertId();
            } else {
                $id = $post['id'];
                $this->TH->db->update('tasks', $task, 'id = ' . $id);
            }

            if (!empty($post['outputtype'])) {
                $tasksoutput = $this->TH->db->fetchRow('SELECT id, output_type FROM tasks_output WHERE id = ?', $post['outputtype']);
                $outputType = $tasksoutput['output_type'];
                if (!empty($outputType)) {
                    // $outputTypeDecoded = json_decode($outputType, true);
                    // foreach($outputTypeDecoded as $key => $value) {
                        // if ($value['type'] == 5) { // controllare se output è di tipo allegato ed in questo caso salva l'allegato su S3 copiandolo dal modello di output scelto per il task
                            // $outputtypeId = $value['counter'];
                            // $source = $this->sitekey . DIRECTORY_SEPARATOR . self::$TASKOUTPUT . DIRECTORY_SEPARATOR . $tasksoutput['id'] . DIRECTORY_SEPARATOR . $outputtypeId . DIRECTORY_SEPARATOR;
                            // $targetPath = $this->sitekey . DIRECTORY_SEPARATOR . self::$FILETASK . DIRECTORY_SEPARATOR . $id . DIRECTORY_SEPARATOR . self::$OUTPUTFILE . DIRECTORY_SEPARATOR . $outputtypeId . DIRECTORY_SEPARATOR;
                            // $iterator = $this->s3Connector->getGIterator(self::$MAIN_BUCKET, $source);
                            // foreach ($iterator as $object) {
                            //     $this->s3Connector->copyObject(self::$MAIN_BUCKET, $targetPath . $value['field'], self::$MAIN_BUCKET, $object['Key']);
                            // }
                        // }
                    // }
                    $this->TH->db->update('tasks', array('output_type' => $outputType), 'id = ' . $id);
                }
            }

            // salvataggio allegati del task
            if (!empty($files)) {
                foreach($files['files']['name'] as $index => $name) {
                    $fileName = $name;
                    $this->uploadFile($id, $name, null, $index);
                }
            }

            // assegnazione task
            if (!empty($post['assigned_to'])) {
                $exists = $this->TH->db->fetchCol('SELECT assigned_to FROM tasks_utente WHERE task_id = ?', $id);

                if ($post['assigned_to'] != $exists) {
                    try {
                        $this->TH->db->beginTransaction();
                        $this->TH->db->delete('tasks_utente', array('id = ?' => $id));
                        foreach($post['assigned_to'] as $assign) {
                            $this->TH->db->insert('tasks_utente', array('assigned_to' => $assign, 'task_id' => $id));
                        }
                        $this->TH->db->commit();
                    } catch (Exception $e) {
                        $this->TH->db->rollBack();
                        $this->TH->applogger->info($e->getMessage());
                    }
                }
            }

            $componentId = $this->TH->db->fetchOne('SELECT component_status_id FROM tasks WHERE id = ?', $id);

            if ($post['status'] == 4 && $componentId) {
                // chiamata al trigger del workflow medodo awake
                $componentObj = new ComponentManager($componentId, $this->TH->db, $this->sitekey, $this->TH->applogger);
			    $componentObj->awake();
            }

            return $id;
        } catch (Exception $e) {
// 				$this->TH->db->rollBack();
            $this->TH->applogger->info($e->getMessage());
            $this->TH->applogger->info($this->getErrorMessage('upload_error'));
            return false;
        }
    }

    public function uploadFile($taskId = null, $fileName, $outputtypeId = null, $attachmentId = null, $outputId = null)
    {
        Zend_Session::writeClose();
        $now = new DateTime();
        $files = $this->options['files'];
        $idDocumento = null;
        $codicepratica = $taskId? $this->TH->db->fetchOne('SELECT file_id FROM tasks WHERE id = ?', $taskId) : null;

        if ($outputtypeId) { // caso modello output
			$uploadFolder = $this->sitekey . DIRECTORY_SEPARATOR . self::$DOCUMENTS . DIRECTORY_SEPARATOR;
            $sourceFile = $files['outputtype']['tmp_name'][$outputtypeId]['field'];
        } else { // caso allegati del task
            if ($taskId) {
                $folderName = $this->getFilePath($taskId, $this->TH->db);
            }
            $uploadFolder = $this->sitekey . DIRECTORY_SEPARATOR . self::$DOCUMENTS . DIRECTORY_SEPARATOR . $folderName;
            $sourceFile = $files['files']['tmp_name'][$attachmentId];
        }

        $tmpUid = $this->getUid();
        $uploadSourceDir = '/tmp/' . $tmpUid . DIRECTORY_SEPARATOR . $uploadFolder;

        try {
            $fileName = preg_replace('/[^\w.]/', '_', trim($fileName));
            $fileName = preg_replace('/_+/', '_', $fileName);

            $counter = 1;

            if (empty(strpos($fileName, '.'))) {
                $prefix = $fileName;
                $ext = 00;
            } else {
                $f = explode('.', $fileName);
                $ext = '.' .$f[count($f) - 1];
                unset($f[count($f) - 1]);
                if ($ext == '.p7m' && count($f)>1) {
                    $ext = '.' .$f[count($f) - 1] . $ext;
                    unset($f[count($f) - 1]);
                }
                $prefix = implode('.', $f);
            }

            while ($this->s3Connector->doesObjectExist(self::$MAIN_BUCKET, $uploadFolder . $fileName)) {
                $fileName = $prefix . '_' . $counter .  $ext;
                $counter++;
            }

            if (is_uploaded_file($sourceFile)) {
                if (!file_exists($uploadSourceDir)) {
                    mkdir($uploadSourceDir, 0777, true);
                }
                $filePath = $sourceFile . $fileName;
                move_uploaded_file($sourceFile, $filePath);

                if(!$this->s3Connector->putObject(self::$MAIN_BUCKET, $uploadFolder . $fileName, $filePath)) {
                    $this->TH->applogger->info($this->getErrorMessage('upload_error'));
                }

                $newDocument = array(
                    'titolodocumento' => $fileName . ' - allegato task',
                    'folder_id' => null,
                    'data' => $now->format('Y-m-d'),
                    'immessoil' => $now->format('Y-m-d H:i:s'),
                    'modificatoil' => $now->format('Y-m-d H:i:s'),
                    'codicepratica' => $codicepratica? $codicepratica : null,
                    'immessoda' => $this->options['loggedUserId'],
                    'modificatoda' => $this->options['loggedUserId'],
                    'nomefile' => $fileName,
                    'uniqueid' => $this->getUid(),
                );
                $this->TH->db->insert('documento', $newDocument);
                $idDocumento = $this->TH->db->lastInsertId();

                if ($taskId) {
                    $this->TH->db->insert('tasks_documento', array('tasks_id' => $taskId, 'documento_id' => $idDocumento));
                }

                $this->rmdir_nw($uploadSourceDir);

                return $idDocumento;
            }
        } catch (Exception $e) {
            $this->TH->applogger->err($this->getErrorMessage('upload_error') . '\n ' . $e->getMessage());
            if (file_exists('/tmp/' . $tmpUid)) {
                $this->rmdir_nw('/tmp/' . $tmpUid);
            }
        }
    }

    private function rmdir_nw($dir)
    {
		if (is_dir($dir)) {
			$objects = scandir($dir);
			foreach ($objects as $object) {
				if ($object != "." && $object != "..") {
					if (filetype($dir."/".$object) == "dir") {
						$this->rmdir_nw($dir."/".$object);
					} else {
						unlink($dir."/".$object);
					}
				}
			}
			reset($objects);
			rmdir($dir);
		}
    }

    private function getErrorMessage($error)
    {
        return array_key_exists($error, $this->error_messages)? $this->error_messages[$error] : $error;
    }

    private function getFilePath($taskId = null, $db)
    {
		$folderName = '';
		if(!empty($taskId)) {
			$folderName = $db->fetchOne('SELECT file_id FROM tasks WHERE id = ?', $taskId);
			if(!empty($folderName)) {
				$folderName .= '/';
			}
		}
		return $folderName;
    }

    private function getUid()
    {
        return strtoupper(base_convert(microtime(true), 10, 16) . '-' . uniqid(rand()));
    }
}