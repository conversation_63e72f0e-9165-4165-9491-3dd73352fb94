1 - creare l'alias nel file di configurazione di apache aggiungendo la riga: 
	 		Alias /public /home/<USER>/DexmaCommons/public

2 - nella pagina dove viene creata la lista importare la pagina "listManager.tpl" aggiungendo la riga:
    	{include file="$dexmaCommonsPath/public/ListsManager/listManager.tpl"}

3 - creare il div che conterrà la lista:
		<div id="divname" class="grid" data-activesortfield="{$activeSortField}" data-activesortdirection="{$activeSortDirection}" 
						                   data-activepage="{$activePage}">
	 gli attributi "data" sono necessari per gestire ordinamento e numero di pagina attiva della lista;	

4 - realizzare la funzione di creazione della lista aggiungendo e personalizzando il seguente codice:
	 	$(function()
		{
   		var divnameDIV = $('#divname');
   		divnameDIV.simplePagingGrid({
			columnNames: ['columnName1', 'columnName2', '...', 'columnNameN'],
			columnKeys: ['columnKey1', 'columnKey2', '...', 'columnKeyN'],
			columnWidths: ['columnWidth1%', 'columnWidth2%', '...%', 'columnWidth3%'],
			dataUrl: '/controllername/list',
			sortable: [true/false, true/false, true/false, true/false],
			currentPage: divnameDIV.data('activepage'),
			initialSortColumn: divnameDIV.data('activesortfield'),
			sortOrder: divnameDIV.data('activesortdirection'),
			pageSize: pagesize,
			minimumVisibleRows: pagesize,
			/* Funzione di callback invocata dopo il caricamento della lista, gli viene passato l'oggetto json come parametro */
			pageRenderedEvent: null,
			/* definisce il template di ogni singola cella: la prima cella contiene un link html, la seconda e la terza non hanno nessun templete
				mentre la quarta ha un templete che viene definito nel metodo esposto al punto 5.
			*/
			cellTemplates:["<a href='/controllerName/update?uid={{uniqueid}}'>{{columnKey}}</a>", null, null, '{{columnKeyN}}'],
			/* Funzione eseguita al click su una riga, come argomento viene passato l'intero oggetto json relativo alla riga
			/*  */			
			rowClickHandler: function(row){
				myClickFunction(row.uniqueid);
			},
			/* Campi per la ricerca se NON utilizzati non devono essere dichiarati */			
			searchFormName: 'searchFormName',
			/* Default "false" impedisce il submit automatico (alla pressione del tasto invio) per i form di ricerca in cui è presente un solo 
				campo input */
			disableAutoSubmitForm: true/false,
			searchButtonName: 'searchButtonName',
			showAllButtonName: 'showAllButtonName',
			tableClass: 'cssClassName'
   	});


5 - Nel caso in cui si abbia la necessità di inserire in una cella un particolare template si deve utilizzare il seguente metodo, personalizzandolo
	 secondo le esigenze:  
	 	Handlebars.registerHelper('columnKeyN', function() 
		{
			if(this.columnKeyN == 0) 
  			{
				return new Handlebars.SafeString('<img src="/images/bulletGreen.png" alt="alt1" /> text1');
  			} 
  			else if(this.columnKeyN == 1)
  			{
  				return new Handlebars.SafeString('<img src="/images/bulletWhite.png" alt="alt2" /> text2');
  			}
  			else id(this.columnKeyN == N)
  			{
  				return new Handlebars.SafeString('<img src="/images/bulletRed.png" alt="altN" /> textN');
  			}
		});
	 	

6 - implementare il metodo "list" nel controller:
		public function listAction() 
    	{
    		$whereCondition = 'WHERE 1=1';
    		$data = array();
			/** INIZIO SEZIONE RICERCA **/
	    	if(!empty($_REQUEST['searchName']))
			{
				$searchName = $_REQUEST['searchName'];
				parent::setAttribute($this->name, $searchName);
				$whereCondition .= ' AND u.personName LIKE ?';
				array_push($data, "%$searchName%");
			}
			else 
			{
				parent::setAttribute($this->name, null);
			}
			
			if(!empty($_REQUEST['searchEmail']))
			{
				$searchEmail = $_REQUEST['searchEmail'];
				parent::setAttribute($this->email, $searchEmail);
				$whereCondition .= ' AND u.email LIKE ?';
				array_push($data, "%$searchEmail%");
			}
			else
			{
				parent::setAttribute($this->email, null);
			}
	
			if(isset($_REQUEST['searchType']))
			{
				$searchType = $_REQUEST['searchType'];
				parent::setAttribute($this->type, $searchType);
				if ($searchType != -1)
				{
					$whereCondition .= ' AND u.adminLevel = ?';
					array_push($data, $searchType);
				}
			}
			
			if(isset($_REQUEST['searchStatus']))
			{
				$searchStatus = $_REQUEST['searchStatus'];
				parent::setAttribute($this->status, $searchStatus);
				if ($searchStatus != -1)
				{
					$whereCondition .= ' AND u.status = ?';
					array_push($data, $searchStatus);
				}
			}
			/** FINE SEZIONE RICERCA **/
	      $sql = "SELECT COUNT(id) 
	        		  FROM users u 
	        		  $whereCondition";
	      $totalRows = $this->db->fetchOne($sql, $data);	      
			/** PARAMETRI PASSATI DALLA GRIGLIA **/      	
      	$page = $_REQUEST['page'];
	      $sortField = $_REQUEST['sortColumn'];
	      $sortDir = $_REQUEST['sortOrder'];
	      $pageSize = $_REQUEST['pageSize'];
	        
	      parent::saveGridParameters($totalRows, $pageSize, $page, $sortField, $sortDir);
	        
	      $sql = "SELECT u.id, u.uniqueid, u.username, u.personName, u.email, u.status,
	        	     CASE u.adminLevel
	        	     		WHEN 1 THEN 'Operatore'
	        	    			WHEN 2 THEN 'Cliente in abbonamento' ELSE 'Cliente standard' END AS adminLevel
	        	    	FROM users u
	        	    	$whereCondition
	        	    	ORDER BY $sortField $sortDir
	        	    	LIMIT " . $page * $pageSize . ',' . $pageSize;
	
	      $results = $this->db->fetchAll($sql, $data);
			$results = json_encode($results);
			/** LA STRING JSON DEVE AVERE IL SEGUENTE FORMATO **/		
			echo '{"currentPage":' . $results . ', "totalRows":' . $totalRows . '}';
		
			$this->_helper->viewRenderer->setNoRender(true);    		
    }    		
    N.B. il metodo sopra è da prendere come esempio per la realizzazione del proprio metodo list;

7 - se nel metodo "update" si ha necessità di impostare uno specifico numero di pagina si può utilizzare la seguente riga di codice:
		parent::setAttribute(parent::getActivePageKey(), pageNumber);
    
8 - per gestire la cancellazione in maniera ottimale, per l'utente che utilizza la lista, nel metodo "delete" utilizzare:
		parent::updateActivePageAfterDelete();		

9 - nel caso in cui la lista deve essere integrata con la ricerca si possono utilizzare le seguenti funzionalità:
	 
	 a - <form id="searchFormName">
	 			<input id="searchButtonName" type="button" value="Cerca" />
		  		<input id="showAllButtonName" type="button" value="Mostra tutti" />
						
		  definire i pulsanti "Cerca" e "Mostra" con gli id uguali a quelli dichiarati nel momento di creazione della lista(punto 4)
	
	 b - <div>Label</div><input id="searchField1" name="searchField1" value="{$searchField1}" type="text" data-autosearch /></div>
		  
		  se è presente l'attributo "data-autosearch" avviene quanto segue:
		  		- se l'INPUT è di tipo "TEXT" alla pressione del tasto invio viene avviata la ricerca
		  		- se l'INPUT è di tipo "CHECKBOX" o "DEXMADATE"(tipo di riferimento per i campi data) oppure si tratta si una SELECT la 
		  		  ricerca viene avviata ad un qualunque cambio di stato del componente;
	 
	 c - 		<select id="searchField2" name="searchField2" class="dexmaSelect" data-resetvalue>  
		  		<input id="searchField3" name="searchField3" data-resetvalue='topolino'> 
		  		...				
				...
		  </form>
		  
		  attributo "data-resetvalue":
		  		- se INPUT di tipo "TEXT" o "DEXMADATE" deve essere presente e valorizzato, se e solo se, al momento del click sul pulsante "Mostra tutti"
		  		  si desidera impostare un determinato valore di default all'interno del campo(es. nel campo searchField3 verrà impostato il valore 
		  		  "topolino"), l'assenza di tale attributo imposterà il campo con valore vuoto;
		  		- se SELECT deve essere presente e valorizzato con l'id della OPTION da selezionare, se e solo se, al momento del click sul pulsante 
		  		 "Mostra tutti" si desidera impostare tale OPTION come selezionata, l'assenza dell'attributo determinerà la selezione della prima OPTION
		  		  disponibile;
		  		- se CHECKBOX la presenza dell'attributo(senza nessuna valorizzazione) imposta la CHECKBOX come spuntata al momento del click sul 
		  		  pulsante "Mostra tutti", l'assenza di tale attributo determinerà la deselezione della CHECKBOX;

10 - importare come primi files css, quelli di Bootstrap, aggiungendo le righe di codice:
	<link href="/public/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="/public/bootstrap/css/bootstrap-responsive.min.css" rel="stylesheet">

	<style type="text/css">
	 body {
    	  padding-top: 60px;
          padding-bottom: 40px;
    	 }
    	 legend {
    	  margin-bottom: 0px;
    	 }
	</style>

11 - versione jquery: da 1.7.2 in poi.

12 - Per ricaricare la lista richiamare la funzione "reloadGrid(myGridObject)"
