(function ($) {
	function dataPage(data, currentPage, pageSize) {
		return data.slice(currentPage * pageSize, currentPage * pageSize + pageSize);
	}

	$.fn.simplePagingGrid = function (options) {
		var templates = $.extend({
			buttonBarTemplate: '<div class="datagrid-actions"><button class="btn pull-left margin">&laquo; ' + gettext('Prima') + '</button><button class="btn pull-left">&lsaquo; ' + gettext('Indietro') + '</button><button class="btn pull-right margin">' + gettext('Ultima') + ' &raquo;</button><button class="btn pull-right">' + gettext('Avanti') + ' &rsaquo;</button><div class="page-numbers"></div><div class="clearfix"></div>',
			tableTemplate: '<table><thead></thead><tbody></tbody></table>',
			headerTemplate: '<th width="{{width}}">{{title}}</th>',
			sortableHeaderTemplate: '<th width="{{width}}">{{title}}<ul class="sort"><li class="sort-ascending"/><li class="sort-descending"/></ul></th>',
			emptyCellTemplate: '<td>&nbsp;</td>',
			loadingOverlayTemplate: '<div class="loading"></div>',
			currentPageTemplate: '<span class="page-number active-page-number">{{pageNumber}}</span>',
			pageLinkTemplate: '<a class="page-number" href="#">{{pageNumber}}</a>',
			infoBarTemplate: '<div><div class="info-bar"></div></div>',
			legendTemplate: '<div>' + gettext('Elementi') + ' {{startElement}} - {{endElement}} ' + gettext('Di') + ' {{numberOfRows}}</div>',
			pageLabel: gettext('Pagina'),
			ofLabel: gettext('Di')
		}, options.templates);

		var settings = $.extend({
			pageSize: 10,
			columnWidths: [],
			cellTemplates: null,
			headerTemplates: null,
			sortable: [],
			currentPage: null,
			sortOrder: "asc",
			initialSortColumn: null,
			tableClass: "table",
			dataFunction: null,
			dataUrl: null,
			data: null,
			minimumVisibleRows: 10,
			showLoadingOverlay: true,
			showPageNumbers: true,
			showInfoBar: true,
			numberOfPageLinks: 10,
			pageRenderedEvent: null,
			searchFormName: null,
			searchButtonName: null,
			showAllButtonName: null,
			showHeader: true
		}, options);
		settings.templates = templates;
		this.settings = settings;

		return this.each(function () {
			var table;
			var tbody;
			var thead;
			var headerRow;
			var data;
			var currentPage = (settings.currentPage != null) ? settings.currentPage : 0;
			var infoBar;
			var buttonBar;
			var firstButton;
			var previousButton;
			var lastButton;
			var nextButton;
			var pageData = (settings.data !== null)? settings.data : null;
			var numberOfRows = null;
			var fetchingData = false;
			var sortOrder = settings.sortOrder.toLowerCase();
			var sortedColumn = settings.initialSortColumn;
			var sortElement = null;
			var loadingOverlay = null;
			var gridElement = this;
			var searchForm = (settings.searchFormName != null) ? $('#' + settings.searchFormName) : null;
			var activeXHR = null;

			if (searchForm != null) {
				searchForm.attr('onsubmit', 'return false');
			}

			var searchParams = new Array();

			var $this = $(this);

			function numberOfPages() {
				return Math.ceil(numberOfRows / settings.pageSize);
			}

			function configureButtons() {
				if (fetchingData) {
					firstButton.prop('disabled', true);
					previousButton.prop('disabled', true);
					nextButton.prop('disabled', true);
				}
				else {
					if (currentPage === 0) {
						firstButton.prop('disabled', true);
						previousButton.prop('disabled', true);
					}
					else {
						firstButton.prop('disabled', false);
						previousButton.prop('disabled', false);
					}

					if ((numberOfRows === null && pageData.length < settings.pageSize) ||
						(numberOfRows !== null && currentPage >= (numberOfPages()-1))) {
						nextButton.prop('disabled', true);
						lastButton.prop('disabled', true);
					}
					else {
						nextButton.prop('disabled', false);
						lastButton.prop('disabled', false);
					}
				}
			}

			function configurePageNumbers() {
				function createPageNumberClickHandler(pageNumber) {
					return function(ev) {
						ev.preventDefault();
						currentPage = pageNumber-1;
						refreshData(null, false);
					};
				}
				if (settings.showPageNumbers && numberOfRows !== null) {
					var firstPage;
					var lastPage;
					var totalPages = numberOfPages();
					var pages = [];
					var index;
					var pageNumberElement;

					firstPage = (currentPage+1) - settings.numberOfPageLinks/2;
					if (firstPage < 1) {
						firstPage = 1;
						lastPage = settings.numberOfPageLinks;
						if (lastPage > totalPages) {
							lastPage = totalPages;
						}
					}
					else
					{
						lastPage = (currentPage+1) + settings.numberOfPageLinks/2 - 1;
						if (lastPage > totalPages) {
							lastPage = totalPages;
							firstPage = lastPage - settings.numberOfPageLinks + 1;
							if (firstPage < 1) firstPage=1;
						}
					}

					var pageNumberContainer = buttonBar.find(".page-numbers");
					pageNumberContainer.empty();
					pageNumberContainer.append(settings.templates.pageLabel + '&nbsp;');
					var ofLabel = '&nbsp;' + settings.templates.ofLabel + '&nbsp;';
					if(numberOfRows == 0) {
						pageNumberElement= $(Handlebars.compile(settings.templates.currentPageTemplate)({ pageNumber: 1} ));
						pageNumberContainer.append(pageNumberElement);
						pageNumberContainer.append(ofLabel);
						pageNumberContainer.append(pageNumberElement.clone());
					}
					else {
						for (index = firstPage; index <= lastPage; index++) {
							if(index == (currentPage+1)) {
								pageNumberElement = $(Handlebars.compile(settings.templates.currentPageTemplate)({ pageNumber: index} ));
							}
							else {
								pageNumberElement = $(Handlebars.compile(settings.templates.pageLinkTemplate)({ pageNumber: index} ));
								pageNumberElement.click(createPageNumberClickHandler(index));
							}
							pageNumberContainer.append(pageNumberElement);
							if(index == lastPage) {
								pageNumberContainer.append(ofLabel);
								var lastPageIndex = Math.ceil(numberOfRows/settings.pageSize);
								if(lastPageIndex == (currentPage+1)) {
									pageNumberElement = $(Handlebars.compile(settings.templates.currentPageTemplate)({ pageNumber: index} ));
								} else {
									pageNumberElement = $(Handlebars.compile(settings.templates.pageLinkTemplate)({ pageNumber: lastPageIndex} ));
									pageNumberElement.click(createPageNumberClickHandler(lastPageIndex));
								}
								pageNumberContainer.append(pageNumberElement);
							}
						}
					}
				}
			}

			function configureLegend() {
				if(settings.showInfoBar && numberOfRows !== null) {
					var infoBarContainer = infoBar.find(".info-bar");
					infoBarContainer.empty();
					var startElement = currentPage * settings.pageSize;
					var endElement = null;
					if(pageData.length === settings.pageSize) {
						endElement = (currentPage + 1) * settings.pageSize;
					}
					else {
						endElement = startElement + pageData.length;
					}
					if(numberOfRows > 0) {
						startElement++;
					}
					var legendElement = $(Handlebars.compile(settings.templates.legendTemplate)({startElement: startElement, endElement: endElement, numberOfRows: numberOfRows}));
					infoBarContainer.append(legendElement);
				}
			}

			function sizeLoadingOverlay() {
				if (loadingOverlay != null) {
					loadingOverlay.width($this.width());
					loadingOverlay.height($this.height());
				}
			}

			function showLoading() {
				if (settings.showLoadingOverlay) {
					loadingOverlay = $(settings.templates.loadingOverlayTemplate)
					sizeLoadingOverlay();
					$this.prepend(loadingOverlay);
				}
			}

			function hideLoading() {
				if (loadingOverlay !== null) {
					loadingOverlay.remove();
					loadingOverlay = null;
				}
			}

			function getPageDataFromSource(sourceData) {
				if ($.isArray(sourceData)) {
					pageData = sourceData;
				}
				else if ($.isPlainObject(sourceData)) {
					pageData = sourceData.currentPage;
					numberOfRows = sourceData.totalRows;
				}
			}

			function refreshDataRemote(currentPage) {
				currentPage = parseInt(currentPage);
				refreshData(null, false, currentPage);
			}

			function refreshData(newDataUrl, refreshStore, activePage) {
				var sortedData;
				var aVal;
				var bVal;
				var dataToSort;

				if (newDataUrl !== undefined && newDataUrl !== null) {
					settings.dataUrl = newDataUrl;
					currentPage = 0;
				}

				if(refreshStore === true) {
					currentPage = 0;
				}

				if(activePage !== undefined && activePage !== null) {
					currentPage = activePage;
				}

				if (settings.data !== null) {
					if ($.isArray(settings.data)) {
						pageData = settings.data;
					}
					else if ($.isPlainObject(settings.data)) {
						pageData = settings.data.currentPage;
						numberOfRows = settings.data.totalRows;
					}
					gridElement.currentData = pageData;
					loadData();
					configureLegend();
					configureButtons();
					configurePageNumbers();
				}
				else if (settings.dataUrl !== null) {
					fetchingData = true;
					configureButtons();

					if (activeXHR != null) {
						activeXHR.abort();
					}
					else {
						showLoading();
					}

					if (searchForm != null) { // && refreshStore !== false
						searchParams = new Array();
						$.each(searchForm.serializeArray(), function(i, field) {
							if (searchParams[field.name]) {
								if (!$.isArray(searchParams[field.name])) {
									searchParams[field.name] = [searchParams[field.name], field.value];
								}
								else searchParams[field.name].push(field.value);
							}
							else searchParams[field.name] = field.value;
						});
					}

					var params = { page: currentPage, pageSize: settings.pageSize, sortColumn: sortedColumn, sortOrder: sortOrder };

					for(var index in searchParams) {
						params[index] = searchParams[index];
					}

					activeXHR = $.getJSON(settings.dataUrl, params, function (jsonData) {
						getPageDataFromSource(jsonData);
						gridElement.currentData = pageData;
						loadData();
						fetchingData = false;
						activeXHR = null;
						configureLegend();
						configureButtons();
						configurePageNumbers();
						hideLoading();
						if (settings.pageRenderedEvent !== null) settings.pageRenderedEvent(jsonData);
					});

					return activeXHR;
				}
				else if (settings.dataFunction !== null) {
					getPageDataFromSource(settings.dataFunction(currentPage, settings.pageSize, sortedColumn, sortOrder));
					gridElement.currentData = pageData;
					loadData();
					configureLegend();
					configureButtons();
					configurePageNumbers();
					if (settings.pageRenderedEvent !== null) settings.pageRenderedEvent(pageData);
				}
			}

			function loadData() {
				var firstTime = true;
				tbody.empty();

				$.each(pageData, function (rowIndex, rowData) {
					const {tableName} = rowData;
					var tr = $(`<tr class="soggetti-row${tableName}">`);

					if (settings.rowClickHandler) {
						tr.click(function(){
							settings.rowClickHandler( rowData);
						});
					}
					$.each(settings.columnKeys, function (index, propertyName) {
						var td;

						if (!settings.showHeader && settings.columnWidths[index] && firstTime) {
							td = $('<td width="' + settings.columnWidths[index] + '">');
						}
						else {
							td = $('<td>');
						}

						if (settings.cellTemplates !== null && index < settings.cellTemplates.length && settings.cellTemplates[index] !== null) {
							td.html(Handlebars.compile(settings.cellTemplates[index])(rowData));
						}
						else {
							td.text((rowData[propertyName] == null)? '' : rowData[propertyName]);
						}
						td.prop('title', td.text());
						tr.append(td);
					});

					firstTime = false;
					tbody.append(tr);
				});

				if (pageData.length < settings.minimumVisibleRows) {
					var emptyRowIndex;
					var emptyRow;
					for (emptyRowIndex = pageData.length; emptyRowIndex < settings.minimumVisibleRows; emptyRowIndex++) {
						emptyRow = $('<tr>');
						$.each(settings.columnKeys, function() {
							emptyRow.append(Handlebars.compile(settings.templates.emptyCellTemplate));
						});
						tbody.append(emptyRow);
					}
				}
			}

			table = $(settings.templates.tableTemplate);
			tbody = table.find("tbody");
			if (settings.showHeader) {
				thead = table.find("thead");
				headerRow = $("<tr>").appendTo(thead);
				$.each(settings.columnNames, function (index, columnName) {
					var sortEnabled = settings.sortable[index];
					var sortAscending;
					var sortDescending;
					var columnKey = settings.columnKeys[index];
					var width;
					var headerCell = null;

					width = settings.columnWidths.length > index ?settings.columnWidths[index] : "";
					if (settings.headerTemplates !== null && index < settings.headerTemplates.length && settings.headerTemplates[index] != null) {
						headerCell = $(Handlebars.compile(settings.headerTemplates[index])({ width: width, title: columnName }));
					}

					if (sortEnabled) {
						if (headerCell === null) {
							headerCell = $(Handlebars.compile(settings.templates.sortableHeaderTemplate)({width:width, title:columnName}));
						}
						sortAscending = headerCell.find(".sort-ascending");
						sortDescending = headerCell.find(".sort-descending");

						if(columnKey == sortedColumn)
						{
							sortElement = sortOrder === "asc" ? sortAscending : sortDescending;
							sortElement.addClass(sortOrder === "asc" ? "sort-ascending-active" : "sort-descending-active");
						}

						function sort(event) {
							event.preventDefault();
							if (sortedColumn === columnKey) {
								sortOrder = sortOrder === "asc" ? "desc" : "asc";
							}
							sortedColumn = columnKey;
							if (sortElement != null) {
								sortElement.removeClass("sort-ascending-active");
								sortElement.removeClass("sort-descending-active");
							}
							sortElement = sortOrder === "asc" ? sortAscending : sortDescending;
							sortElement.addClass(sortOrder === "asc" ? "sort-ascending-active" : "sort-descending-active");
							refreshData(null, false, null);
						};

						sortAscending.click(function (event) {
							sort(event);
						});

						sortDescending.click(function (event) {
							sort(event);
						});
					}
					else {
						if (headerCell === null) {
							headerCell = $(Handlebars.compile(settings.templates.headerTemplate)({ width:width, title:columnName }));
						}
					}
					headerRow.append(headerCell);
				});
			}

			table.addClass(settings.tableClass);

			infoBar = $(settings.templates.infoBarTemplate);

			buttonBarHtml = settings.templates.buttonBarTemplate;
			buttonBar = $(buttonBarHtml);
			firstButton = buttonBar.find('button:nth-child(1)');//.first();
			previousButton = buttonBar.find('button:nth-child(2)');//.first();
			nextButton = buttonBar.find('button:nth-child(4)');//.last();
			lastButton = buttonBar.find('button:nth-child(3)');//.last();
			firstButton.click(function (event) {
				event.preventDefault();
				if (currentPage > 0) {
					currentPage = 0;
					refreshData(null, false, null);
				}
			});
			previousButton.click(function (event) {
				event.preventDefault();
				if (currentPage > 0) {
					currentPage--;
					refreshData(null, false, null);
				}
			});

			nextButton.click(function (event) {
				event.preventDefault();
				if (pageData.length === settings.pageSize) {
					currentPage++;
					refreshData(null, false, null);
				}
			});
			lastButton.click(function (event) {
				event.preventDefault();
				if(currentPage < numberOfPages() - 1) {
					currentPage = numberOfPages() - 1;
				}
				refreshData(null, false, null);
			});
			/** BEGIN CUSTOM METHODS **/
			//Search button
			if(settings.searchButtonName != null && settings.searchButtonName != '') {
				$('#' + settings.searchButtonName).click(function (event) {
					refreshData(null, true, null);
				});
			}
			//Show all button
			if(settings.showAllButtonName != null && settings.showAllButtonName != '') {
				$('#' + settings.showAllButtonName).click(function (event) {
					if(searchForm != null) {
						searchForm.find('input, select').each(function() {
							var $this = $(this);
							if(this.tagName.toUpperCase() == 'INPUT') {
								var type = $this.attr('type');
								if (type == 'button' || type == 'submit' || type == 'reset' || type == 'radio') {
									return true;
								}
								else if(type == 'checkbox') {
									if($this.data('resetvalue') !== undefined) {
										this.checked = true;
									}
									else {
										this.checked = null;
									}
								}
								else {
									if($this.data('resetvalue') !== undefined) {
										this.value = $(this).data('resetvalue');
									}
									else {
										this.value = '';
									}
								}
							}
							else if(this.tagName.toUpperCase() == 'SELECT') {
								if($this.prop('multiple')) {
									$this.find('option').prop('selected', false);
								}

								if($this.data('resetvalue') !== undefined) {
									$this.find('option[value="' + $this.data('resetvalue') + '"]').prop('selected', true);
								}
								else {
									$this.find('option:first').prop('selected', true);
								}
							}
						});
					}
					refreshData(null, true, null);
				});
			}
			if(searchForm != null) {
				searchForm.find('input, select').each(function() {
					var $this = $(this);
					if(this.tagName.toUpperCase() == 'INPUT') {
						var type = $this.attr('type');
						if (type == 'button' || type == 'submit' || type == 'reset') {
							return true;
						}
						else if(type == 'checkbox' || $this.hasClass('jqueryCalendar')) {
							if($this.data('autosearch') !== undefined) {
								$this.change(function (event) {
									refreshData(null, true, null);
								});
							}
						}
						else {
							if($this.data('autosearch') !== undefined) {
								$this.keypress(function (event) {
									if(((event.which) ? event.which : event.keyCode) == 13) {
										refreshData(null, true, null);
									}
								});
							}
						}
					}
					else if(this.tagName.toUpperCase() == 'SELECT') {
						if($this.data('autosearch') !== undefined) {
							$this.change(function (event) {
								refreshData(null, true, null);
							});
						}
					}
				});
			}
			/** END CUSTOM METHODS **/
			refreshData();
			$this.append(table);
			$this.append(infoBar);
			$this.append(buttonBar);
			if (settings.data !== null && settings.pageRenderedEvent !== null) {
				settings.pageRenderedEvent(settings.data);
			}
			$(window).resize(sizeLoadingOverlay);
			gridElement.refreshData = refreshData;
			gridElement.refreshDataRemote = refreshDataRemote;
			gridElement.loadData = loadData;
			return this;
		});
	};
})(jQuery);
