﻿ul.sort {
	display: inline-block;
	list-style: none;
	margin: 0 0 0 8px;
	padding: 0;
	vertical-align: middle;
}

li.sort-ascending
{
	border-color: transparent transparent #CCCCCC transparent;
	border-style: solid;
	border-width: 7px;
	width: 0;
	height: 0;
	padding-top: 0px;
	margin-top: -8px;
	margin-bottom: 3px;
}

li.sort-ascending-active
{
	border-color: transparent transparent #3F3F3F transparent;
}

li.sort-descending
{
	border-color: #CCCCCC transparent transparent transparent;
	border-style: solid;
	border-width: 7px;
	width: 0;
	height: 0;
	margin-bottom: -8px;
	padding: 0;
}

li.sort-descending-active
{
	border-color: #3F3F3F transparent transparent transparent;
}

div.loading
{
	position: absolute;
	height: 100%;
	width: 100%;
	z-index: 999;
	border: 5px;
	background-color: white;
	background-image: url(../images/loader.gif);
	background-repeat: no-repeat;
	background-position: center;
}

.page-numbers  {
	font-size: 14px;
	padding-top: 5px;
	text-align: center;
	min-width: 100%;
}

.page-number {
	margin-left: 0.5em;
	margin-right: 0.5em;
}

.active-page-number {
	font-weight: bold;
	border: 1px solid #3F3F3F;
	padding: 0 5px;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
}

.info-bar{font-size:14px;margin:10px 0;text-align:right}
.table{font-size:14px;table-layout:fixed}
.table thead th{background-color:#F4F4F4}
.table th,.table td{padding:10px;border-top:0 none;border-bottom:1px solid #ddd;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}
.table th.pagination-right { text-align: right; }
.table th.pagination-centered { text-align: center; }
.table td,.table .sort{cursor:pointer}
.pull-left.margin{margin-right:5px}
.pull-right.margin{margin-left:5px}