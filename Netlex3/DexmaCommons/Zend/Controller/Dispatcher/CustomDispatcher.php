<?php


class Zend_Controller_Dispatcher_CustomDispatcher extends Zend_Controller_Dispatcher_Standard
{

	protected $_curModule;
	protected $_defaultModule = 'default';
	protected $_controllerName;
	protected $_classPath;

	public function __construct( array $params = array() )
	{
		parent::__construct( $params );
	}

	public function classToFilename($class)
	{

		if ( $this->_classPath ) {

			return $this->_classPath . DIRECTORY_SEPARATOR . $class . '.php';

		}

		return str_replace( '_', DIRECTORY_SEPARATOR, $class ) . '.php';

	}

	public function getControllerClass( Zend_Controller_Request_Abstract $request )
	{

		$controllerName = $request->getControllerName();
		if (empty($controllerName)) {
			if (!$this->getParam('useDefaultControllerAlways')) {
				return false;
			}
			$controllerName = $this->getDefaultControllerName();
			$request->setControllerName($controllerName);
		}

		/** Set classPath and controllerName to use in loadClass function */
		$this->_classPath = $this->getClassDir($controllerName);
		$controllerName = str_replace( [ "/" ], "_", $controllerName );
		$this->_controllerName = $controllerName;

		$className = $this->formatControllerName($controllerName);

		$controllerDirs      = $this->getControllerDirectory();
		$module = $request->getModuleName();
		if ($this->isValidModule($module)) {
			$this->_curModule    = $module;
			$this->_curDirectory = $controllerDirs[$module];
		} elseif ($this->isValidModule($this->_defaultModule)) {
			$request->setModuleName($this->_defaultModule);
			$this->_curModule    = $this->_defaultModule;
			$this->_curDirectory = $controllerDirs[$this->_defaultModule];
		} else {
			require_once 'Zend/Controller/Exception.php';
			throw new Zend_Controller_Exception('No default module defined for this application');
		}

		return $className;

	}

	public function getClassDir( $controllerName )
	{

		$parts = explode("/", $controllerName);

		if ( count($parts) > 1 ) {
			array_splice( $parts, count($parts) - 1 );
			$classDir = implode( "/", $parts );
			if ( is_dir( $this->getControllerDirectory( $this->_defaultModule ) . DIRECTORY_SEPARATOR . $classDir ) ) {
				return implode( "/", $parts );
			}

		}

		return NULL;

	}

	public function loadClass( $className )
	{

		$finalClass  = $className;
		if (($this->_defaultModule != $this->_curModule)
			|| $this->getParam('prefixDefaultModule'))
		{
			$finalClass = $this->formatClassName($this->_curModule, $className);
		}
		if (class_exists($finalClass, false)) {
			return $finalClass;
		}

		$dispatchDir = $this->getDispatchDirectory();
		$loadFile    = $dispatchDir . DIRECTORY_SEPARATOR . $this->classToFilename($className);

		if (Zend_Loader::isReadable($loadFile)) {
			include_once $loadFile;
		} else {
			require_once 'Zend/Controller/Dispatcher/Exception.php';
			throw new Zend_Controller_Dispatcher_Exception('Cannot load controller class "' . $className . '" from file "' . $loadFile . "'");
		}

		if (!class_exists($finalClass, false)) {
			require_once 'Zend/Controller/Dispatcher/Exception.php';
			throw new Zend_Controller_Dispatcher_Exception('Invalid controller class ("' . $finalClass . '")');
		}

		return $finalClass;

	}


}