<?php

namespace <PERSON>ma<PERSON>om<PERSON>\RecoveryCredit;

use Carbon\Carbon;
use Zend_Log;
use Zend_Log_Filter_Priority;
use Zend_Log_Writer_Stream;

require_once SITE_ROOT . "/vendor/autoload.php";

class RecoveryCreditHandler
{

    protected $db;
    protected $applogger;
    protected $subdomain;

    const NESSUNO_STATUS = '0';
    const IN_FLUSSO_SOLLECITI= '1';
    const TERMINATO_FLUSSO_SOLLECITI = '2';
    const MORA = '1';
    const SOLLECITO_2 = '2';
    const SOLLECITO_3 = '3';
    const DIFFIDA = '4';


    public function __construct($db)
    {
        $applogger = new Zend_Log(new Zend_Log_Writer_Stream(SITE_ROOT .  "/Software/data/logs/recoverycredit.log"));
        $filter = new Zend_Log_Filter_Priority(6);
        $applogger->addFilter($filter);
        $this->applogger = $applogger;


        $this->db = $db;

        if(!empty($subdomain)) {
            $this->subdomain = $subdomain;
        }

    }

    function calcInterests($document, $warning_type)
    {

        $type = $warning_type;
        $fee = $document;

        $mora_solleciti = 1;
        $diffida = 2;
        $data = array();


        if($type == $mora_solleciti){
            $settings = $this->db->fetchRow('SELECT warnings_interests AS interests, warnings_charges AS charges,
                                                warnings_forfait AS forfait, warnings_range AS soglia 
                                                FROM recuperocrediti_settings');
            $data['interests_type'] = 3;
        }

        if($type == $diffida){
            $settings = $this->db->fetchRow('SELECT beware_interests AS interests, beware_charges AS charges, 
                                                beware_forfait AS forfait, beware_range AS soglia 
                                                FROM recuperocrediti_settings');
            $data['interests_type'] = 4;
        }

        $startDate = Carbon::createFromDate($fee['deadline_date']);
        $endDate = Carbon::now()->startOfDay();
        
        $data['start_date'] = $startDate->format('Y-m-d');

        $data['end_date'] = $endDate->format('Y-m-d');
        $data['total_days'] = $endDate->diffInDays($startDate);


        if(isset($settings)){
            $data['total_interests'] = number_format($settings['interests'] / 36500 * $fee['remaining'] * $data['total_days'],2,'.','');
            $data['forfait_applied'] = number_format(($settings['forfait'] /100) * $fee['remaining'],2,'.','');
            $data['charges_applied'] = $settings['charges'];

            if(!empty($settings['soglia'])){
                $range = json_decode($settings['soglia'],1);
                $importRange=0;


                foreach($range as $value){
                    if(($value['from'] <= $fee['remaining']) && ($fee['remaining'] <= $value['to'])){
                        $importRange = $value['amount'];
                    }
                }

                $data['range_applied'] = $importRange;
            }


        }

        $data['capital'] = $fee['remaining'];
        $data['file_id'] = $this->db->fetchOne('SELECT id FROM archivio WHERE uniqueid =?',$fee['file_id']);
        $data['recuperocrediti_doc_id'] = $fee['id'];
        $data['creation_date']= date('Y-m-d H:i:s');
        $data['file_id'] = $fee['file_id'];




        $this->db->insert('recuperocrediti_interessi', $data);

        $calculatedDocs = $this->db->fetchRow("SELECT id, recuperocrediti_doc_id FROM recuperocrediti_interessi ORDER BY id desc LIMIT 1");

        return $calculatedDocs;
    }

    public function generateWarning($document){
        $feeId = $document;

        $errors = array();

        if(!empty($feeId))
        {
            $fee = $this->db->fetchRow('SELECT * FROM recuperocrediti_documenti WHERE id =?',$feeId);
            $expiration_date = $this->db->fetchOne('SELECT deadline_date FROM recuperocrediti_solleciti WHERE recuperocrediti_doc_id = ? ORDER BY id DESC', $feeId);

            if(!empty($expiration_date) && date("Y-m-d") >= $expiration_date){
                $isExpired = true;
            }else if($fee['step_reminder'] == self::NESSUNO_STATUS){
                $isExpired = true;
            }
        }


        if(!empty($fee) && $fee['status'] !== self::TERMINATO_FLUSSO_SOLLECITI && isset($isExpired))
        {

            $step = $fee['step_reminder'];



            $sql = "SELECT * FROM recuperocrediti_interessi WHERE recuperocrediti_doc_id = ?";
            $orderBy =' ORDER BY id DESC';
            if($step < self::SOLLECITO_3)
            {
                $sql.=' AND interests_type = 3';
            }else{
                $sql.=' AND interests_type = 4';
            }

            $sql.= $orderBy;
            $feeInterest = $this->db->fetchRow($sql,$feeId);

            if(empty($feeInterest))
            {
                $errors['feeInterests'] = "1";
            }else{
                $data['interest_applied'] = $feeInterest['id'];
            }

            $data['recuperocrediti_doc_id'] = $feeId;

            if($step !== NULL)
            {
                $from = 'FROM recuperocrediti_settings';

                switch($step)
                {
                    case self::NESSUNO_STATUS:
                        $select = 'SELECT id_mora_model ';
                        $data['warning_type'] = self::MORA;
                        $deadlineSelect = 'SELECT mora_expire ';
                        $dataFee['step_reminder'] = self::MORA;
                        break;

                    case self::MORA:

                        $select = 'SELECT id_second_model ';
                        $data['warning_type'] = self::SOLLECITO_2;
                        $deadlineSelect = 'SELECT second_expire ';
                        $dataFee['step_reminder'] = self::SOLLECITO_2;
                        break;

                    case self::SOLLECITO_2:
                        $select = 'SELECT id_third_model ';
                        $data['warning_type'] = self::SOLLECITO_3;
                        $deadlineSelect = 'SELECT third_expire ';
                        $dataFee['step_reminder'] = self::SOLLECITO_3;
                        break;

                    case self::SOLLECITO_3:
                        $select = 'SELECT id_beware_model ';
                        $data['warning_type'] = self::DIFFIDA;
                        $deadlineSelect = 'SELECT beware_expire ';
                        $dataFee['step_reminder'] = self::DIFFIDA;
                        break;
                }


                $queryModel = $select . $from;
                $queryExpire = $deadlineSelect . 'FROM recuperocrediti_settings';

                $deadline = $this->db->fetchOne($queryExpire);
                if(empty($deadline))
                {
                    $deadline= '10';
                }

                $printId = ($this->db->fetchOne($queryModel)) ? $this->db->fetchOne($queryModel) : $this->db->fetchOne('SELECT id FROM stampe WHERE category = 5 AND default_print = 1') ;
                if(!empty($printId))
                {
                    $data['print_id']= $printId;
                }else{
                    $errors['print_id'] = '1';
                }

            }


            if(empty($errors['print_id']))
            {
                $data['creation_date']= date('Y-m-d');
                $data['deadline_date']=date('Y-m-d', strtotime($data['creation_date'] . ' +' . $deadline . ' days'));

                $data['amount'] = $fee['remaining'];

                if(empty($errors['feeInterest']))
                {
                    $data['total_interests'] =  $feeInterest['total_interests'];

                    $charges = floatval($feeInterest['charges_applied']);
                    $forfait = floatval($feeInterest['forfait_applied']);
                    $range = floatval($feeInterest['range_applied']);
                    if(!empty($charges) || !empty($forfait) || !empty($range) )
                    {
                        $data['other_costs'] = $charges + $forfait + $range;
                    }else{
                        $data['other_costs'] = 0.00;
                    }

                    $data['total_amount'] = $data['amount'] + $data['other_costs'] + $data['total_interests'];

                    if($dataFee['step_reminder'] == self::DIFFIDA)
                    {
                        $dataFee['status'] = self::TERMINATO_FLUSSO_SOLLECITI;
                    }
                }
            }
            if(empty($errors))
            {

                $this->db->insert('recuperocrediti_solleciti', $data);
                $this->db->update('recuperocrediti_documenti', $dataFee, array('id = ?'=> $feeId));
                $idSolleciti = $this->db->fetchOne('SELECT id FROM recuperocrediti_solleciti ORDER BY id DESC');

                return $idSolleciti;

            }else
            {
                return $errors;
            }
        }else{
            return 'worked';
        }
    }


}