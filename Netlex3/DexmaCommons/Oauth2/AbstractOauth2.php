<?php

namespace DexmaCommons\Oauth2;

use \Exception;

abstract class AbstractOauth2 {

    protected $accessToken = '';
    protected $refreshToken = '';
    protected $tokenTime = null;

    abstract protected function getExpiresIn() : int;
    abstract protected function getReceiveTenantUrl() : string;
    abstract protected function getAuthorizeRequestUrl() : string;
    abstract public function getUserTokens(string $code = '') : ?array;

    /**
     * Get access token
     *
     * @return string|null
     */
    protected function getAccessToken() : ?string {
        return (isset($this->accessToken)) ? $this->accessToken : null;
    }

    /**
     * Get token expiration time
     *
     * @return string|null
     */
    protected function getTokenTime() : ?string {
        return (isset($this->tokenTime)) ? $this->tokenTime : null;
    }

    /**
     * Get refresh token
     *
     * @return string|null
     */
    protected function getRefreshToken() : ?string {
        return (isset($this->refreshToken)) ? $this->refreshToken : null;
    }

    /**
     * Check if token expired
     *
     * @return bool
     */
    protected function isTokenExpired() : bool {
        return (time() - $this->getTokenTime() >= $this->getExpiresIn());
    }

    /**
     * Manage user token, if it exists in session return it, if not, try to refresh it, if it fails, redirect to log in
     *
     * @param string $code
     * @return array
     */
    public function getUserToken(string $code = '') : ?array {
        $accessToken = $this->getAccessToken();
        $expired = $this->isTokenExpired();
        $refreshToken = $this->getRefreshToken();
        if (!is_null($accessToken) && !$expired) {
            return [
                'access_token' => $accessToken,
                'refresh_token' => $this->getRefreshToken(),
                'access_token_time' => $this->getTokenTime()
            ];
        } else if ($expired || !empty($code)) {
            try {
                $tokens = $this->getUserTokens($code);
            } catch (Exception $e) {
                return $this->authorize();
            }
            return $tokens;
        }
        if (!empty($refreshToken)) {
            return $this->getUserTokens();
        }
    }

    /**
     * set instance url as cookie and redirect to Microsoft log in
     *
     * @return void
     */
    protected function authorize() : array {
        setcookie('oauthCallback', $this->getReceiveTenantUrl(), time()+60*60*24*30, "/", "netlex.cloud");
        if ($_REQUEST['isN4']) {
            setcookie('isN4', $_REQUEST['isN4'], time()+60*60*24*30, "/", "netlex.cloud");
            $result = array(
                'authorization' => true,
                'url' => $this->getAuthorizeRequestUrl()	
			);
            echo json_encode($result);
            return $result;
        } else {
            setcookie('isN4', null, -1, "/", "netlex.cloud");
            header('Location: ' . $this->getAuthorizeRequestUrl());
            $error = [];
            $error['error'] = 'true';
            $error['error_description'] = 'User not logged in';
            $error['tenanturl'] = $this->getAuthorizeRequestUrl();
            echo json_encode($error);
        }

    }
}