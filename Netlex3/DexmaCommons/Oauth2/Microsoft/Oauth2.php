<?php

namespace <PERSON><PERSON><PERSON>om<PERSON>\Oauth2\Microsoft;

use \DexmaCommons\Oauth2\AbstractOauth2;
use GuzzleHttp\Client;
use Microsoft\Graph\Graph;
use \Exception;

class Oauth2 extends AbstractOauth2 {

    private const EXPIRATION_TIME = 3600; //seconds

    private $client;
    private $clientId = '';
    private $authTenant = '';
    private $clientSecret = '';
    private $graphUserScopes = '';
    private $userClient;

    private $redirectUrl  = '';
    private $authorizeRequestUrl = '';
    private $receiveTenantUrl = '';

    public function __construct($clientId, $authTenant, $clientSecret, $graphUserScopes, $redirectUrl, $accessToken, $refreshToken, $tokenTime = null) {
        // App credentials
        $this->client = new Client();
        $this->clientId = $clientId;
        $this->authTenant = $authTenant;
        $this->clientSecret = $clientSecret;
        $this->graphUserScopes = $graphUserScopes;
        $this->userClient = new Graph();
        // tokens
        $this->accessToken = $accessToken;
        $this->refreshToken = $refreshToken;
        $this->tokenTime = $tokenTime;
        // urls
        $this->redirectUrl = $redirectUrl;
        $this->authorizeRequestUrl = 'https://login.microsoftonline.com/' . $authTenant . '/oauth2/v2.0/authorize' . '?client_id=' . $clientId . '&response_type=code&redirect_uri=' . urlencode($this->redirectUrl) . '&response_mode=query&scope=' . urlencode($graphUserScopes);
        $this->receiveTenantUrl = 'https://' .SITEKEY. '.netlex.cloud/mailbox/receivetenant';
    }

    public function getUserClient(): Graph {
        return $this->userClient;
    }

    protected function getExpiresIn() : int {
        return self::EXPIRATION_TIME;
    }

    protected function getReceiveTenantUrl() : string {
        return $this->receiveTenantUrl;
    }

    protected function getAuthorizeRequestUrl() : string {
        return $this->authorizeRequestUrl;
    }

    /**
     * Get user tokens or refresh access token
     *
     * @param string $code
     * @return array
     * @throws Exception
     */
    public function getUserTokens(string $code = '') : array {
        $tokenRequestUrl = 'https://login.microsoftonline.com/' . $this->authTenant . '/oauth2/v2.0/token';
        $tokenRequestBody = [
            'client_id' => $this->clientId,
            'scope' => $this->graphUserScopes,
            'client_secret' => $this->clientSecret,
        ];
        if (!is_null($this->getRefreshToken()) && empty($code)) { //!is_null($this->getAccessToken())
            $tokenRequestBody['grant_type'] = 'refresh_token';
            $tokenRequestBody['refresh_token'] = $this->getRefreshToken();
        } else if (!empty($code)) {
            $tokenRequestBody['grant_type'] = 'authorization_code';
            $tokenRequestBody['redirect_uri'] = $this->redirectUrl;
            $tokenRequestBody['code'] = $code;
        } else {
            throw new Exception('Invalid token request');
        }
        $tokenResponse = $this->client->post($tokenRequestUrl, [
            'form_params' => $tokenRequestBody,
            'http_errors' => false,
            'curl' => [
                CURLOPT_FAILONERROR => false
            ]
        ]);
        if ($tokenResponse->getStatusCode() == 200) {
            // Return the access_token
            $responseBody = json_decode($tokenResponse->getBody()->getContents());
            return [
                'access_token' => $responseBody->access_token,
                'refresh_token' => $responseBody->refresh_token,
                'access_token_time' => time()
            ];
        } else if ($tokenResponse->getStatusCode() == 400) {
            // Check the error in the response body
            $responseBody = json_decode($tokenResponse->getBody()->getContents());
            if (isset($responseBody->error)) {
                $error = $responseBody->error;
                throw new Exception('Token endpoint returned ' . $error);
            }
        } else {
            throw new Exception('Token endpoint returned ' . $tokenResponse->getStatusCode());
        }
    }

}