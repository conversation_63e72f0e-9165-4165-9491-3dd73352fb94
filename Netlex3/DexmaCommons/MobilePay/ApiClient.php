<?php
namespace MobilePay;

class ApiClient implements ApiInterface
{
	private $apiUrl;
	private $certificatePath;
	private $ch = FALSE;
	private $logPath;
	private $merchantID;
	private $testMode;
	private $verbose;

	private $lastParams;
	private $lastResponse;
	private $lastResult;
	private $lastResultIsOk = FALSE;

	public function __construct($merchantID, $apiUrl, $certificatePath = NULL, $logPath = NULL, $verbose = FALSE, $testMode = FALSE)
	{
		$merchantID = trim($merchantID);
		$apiUrl = filter_var(trim($apiUrl), FILTER_VALIDATE_URL, FILTER_FLAG_PATH_REQUIRED);

		if (!$merchantID || !$apiUrl) {
			throw new \Exception('Missing settings during initialization');
		} elseif (!function_exists('curl_version')) {
			throw new \Exception('cURL is required');
		}

		if ($certificatePath) {
			if (!is_readable($certificatePath)) {
				throw new \Exception('The certificate path is not readable');
			}
			$this->certificatePath = $certificatePath;
		}

		if ($logPath) {
			if (!is_writeable($logPath)) {
				throw new \Exception('The log path is not writeable');
			}
			$this->logPath = $logPath;
		}

		$this->MerchantID = $merchantID;
		$this->apiUrl = $apiUrl;
		$this->testMode = (bool) $testMode;
		$this->verbose = (bool) $verbose;
	}

	public function startBillingTransaction($productID, $productDescription = NULL, $MSISDN = NULL, $Operator = NULL, $getTC = NULL, $tokenID = NULL)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<XML
<Response>
	<TransactionID>RButz5aKDORzKakYjnXowze31dqqSPS6bJ11e443cbcbdf6679</TransactionID>
	<ResultCode>1000</ResultCode>
	<PromoID></PromoID>
	<TC></TC>
</Response>
XML;
			$xml = new \SimpleXMLElement($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'MerchantID' => $this->MerchantID,
				'ProductID' => $productID,
			);
			if (NULL !== $productDescription) {
				$params['productDescription'] = $productDescription;
			}
			if (NULL !== $MSISDN) {
				$params['MSISDN'] = $MSISDN;
			}
			if (NULL !== $Operator) {
				$params['Operator'] = $Operator;
			}
			if (NULL !== $getTC) {
				$params['getTC'] = $getTC;
			}
			if (NULL !== $tokenID) {
				$params['tokenID'] = $tokenID;
			}
			$this->lastParams = $params;

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'MPayStartBillingTransaction',
				CURLOPT_POSTFIELDS => http_build_query($params),
			));

			$xml = $this->execute($ch);
		}

		$result = new \stdClass();

		if ($xml) {
			$result->ResultCode = (string) $xml->ResultCode;
			if ($xml->TransactionID) {
				$result->TransactionID = (string) $xml->TransactionID;
			}
			if ($xml->IdentificationURL) {
				$result->IdentificationURL = (string) $xml->IdentificationURL;
			}
			if ($xml->PromoID) {
				$result->PromoID = (string) $xml->PromoID;
			}
			$this->setLastResultOk($result->ResultCode);
		}

		return $result;
	}

	public function sendCaringMessage($TransactionID, $ProductID, $MessageType, $MSISDN, $Operator, $MessageValue = NULL)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<XML
<Response>
	<ResultCode>1000</ResultCode>
	<Operator>$Operator</Operator>
</Response>
XML;
			$xml = new \SimpleXMLElement($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'TransactionID' => $TransactionID,
				'MerchantID' => $this->MerchantID,
				'ProductID' => $ProductID,
				'MessageType' => $MessageType,
				'MSISDN' => $MSISDN,
				'Operator' => $Operator
			);
			if (NULL !== $MessageValue) {
				$params['MessageValue'] = $MessageValue;
			}
			$this->lastParams = $params;

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'MPaySendCaringMessage',
				CURLOPT_POSTFIELDS => http_build_query($params),
			));

			$xml = $this->execute($ch);
		}

		$result = new \stdClass();

		if ($xml) {
			$result->ResultCode = (string) $xml->ResultCode;
			if ($xml->Operator) {
				$result->Operator = (string) $xml->Operator;
			}
			$this->setLastResultOk($result->ResultCode);
		}

		return $result;
	}

	public function notifyMsisdnAndOperator($TransactionID, $MSISDN, $Operator, $UserAgent = NULL, $GetTC = NULL)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<XML
<Response>
	<ResultCode>1000</ResultCode>
	<PromoID></PromoID>
	<TC></TC>
</Response>
XML;
			$xml = new \SimpleXMLElement($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'TransactionID' => $TransactionID,
				'MSISDN' => $MSISDN,
				'Operator' => $Operator,
			);
			if (NULL !== $UserAgent) {
				$params['UserAgent'] = $UserAgent;
			}
			if (NULL !== $GetTC) {
				$params['GetTC'] = $GetTC;
			}
			$this->lastParams = $params;

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'MPayNotifyMsisdnAndOperator',
				CURLOPT_POSTFIELDS => http_build_query($params),
			));

			$xml = $this->execute($ch);
		}

		$result = new \stdClass();

		if ($xml) {
			$result->ResultCode = (string) $xml->ResultCode;
			if ($xml->PromoID) {
				$result->PromoID = (string) $xml->PromoID;
			}
			if ($xml->GetTC) {
				$result->GetTC = (string) $xml->GetTC;
			}
			$this->setLastResultOk($result->ResultCode);
		}

		return $result;
	}

	public function completeBillingTransaction($TransactionID, $StatusCode, $GetMsisdnOrigin = NULL, $GetBilledAmount = NULL)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<XML
<Response>
	<ResultCode>1000</ResultCode>
	<MSISDN>+393347521133</MSISDN>
	<Operator>WIND</Operator>
	<msisdnOrigin>XXX</msisdnOrigin>
	<billedAmount>10</billedAmount>
	<PromoID></PromoID>
</Response>
XML;
			$xml = new \SimpleXMLElement($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'TransactionID' => $TransactionID,
				'StatusCode' => $StatusCode,
			);
			if (NULL !== $GetMsisdnOrigin) {
				$params['GetMsisdnOrigin'] = $GetMsisdnOrigin;
			}
			if (NULL !== $GetBilledAmount) {
				$params['GetBilledAmount'] = $GetBilledAmount;
			}
			$this->lastParams = $params;

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'MPayCompleteBillingTransaction',
				CURLOPT_POSTFIELDS => http_build_query($params),
			));

			$xml = $this->execute($ch);
		}

		$result = new \stdClass();

		if ($xml) {
			$result->ResultCode = (string) $xml->ResultCode;
			$result->MSISDN = (string) $xml->MSISDN;
			if ($xml->Operator) {
				$result->Operator = (string) $xml->Operator;
			}
			if ($xml->msisdnOrigin) {
				$result->msisdnOrigin = (string) $xml->msisdnOrigin;
			}
			if ($xml->billedAmount) {
				$result->billedAmount = (string) $xml->billedAmount;
			}
			if ($xml->PromoID) {
				$result->PromoID = (string) $xml->PromoID;
			}
			$this->setLastResultOk($result->ResultCode);
		}

		return $result;
	}

	public function subscriptionRenewal($TransactionID, $ProductID, $MSISDN, $GetBilledAmount = NULL)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<XML
<Response>
	<ResultCode>1000</ResultCode>
	<Operator>WIND</Operator>
	<PromoID></PromoID>
	<billedAmount>12</billedAmount>
</Response>
XML;
			$xml = new \SimpleXMLElement($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'TransactionID' => $TransactionID,
				'MerchantID' => $this->MerchantID,
				'ProductID' => $ProductID,
				'MSISDN' => $MSISDN,
			);
			if (NULL !== $GetBilledAmount) {
				$params['GetBilledAmount'] = $GetBilledAmount;
			}
			$this->lastParams = $params;

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'MPaySubscriptionRenewal',
				CURLOPT_POSTFIELDS => http_build_query($params),
			));

			$xml = $this->execute($ch);
		}

		$result = new \stdClass();

		if ($xml) {
			$result->ResultCode = (string) $xml->ResultCode;
			if ($xml->Operator) {
				$result->Operator = (string) $xml->Operator;
			}
			if ($xml->PromoID) {
				$result->PromoID = (string) $xml->PromoID;
			}
			if ($xml->billedAmount) {
				$result->billedAmount = (string) $xml->billedAmount;
			}
			$this->setLastResultOk($result->ResultCode);
		}

		return $result;
	}

	public function unsubscribeCustomer($TransactionID, $ProductID, $MSISDN)
	{
		if ($this->testMode) {
# TEST --------------------------------------------------
			$string = <<<XML
<Response>
	<ResultCode>1000</ResultCode>
	<Operator>WIND</Operator>
</Response>
XML;
			$xml = new \SimpleXMLElement($string);
# TEST --------------------------------------------------
		} else {
			$ch = $this->getConnection();
			$params = array(
				'TransactionID' => $TransactionID,
				'MerchantID' => $this->MerchantID,
				'ProductID' => $ProductID,
				'MSISDN' => $MSISDN,
			);
			$this->lastParams = $params;

			curl_setopt_array($ch, array(
				CURLOPT_URL => $this->apiUrl . 'MPayUnsubscribeCustomer',
				CURLOPT_POSTFIELDS => http_build_query($params),
			));

			$xml = $this->execute($ch);
		}

		$result = new \stdClass();

		if ($xml) {
			$result->ResultCode = (string) $xml->ResultCode;
			if ($xml->Operator) {
				$result->Operator = (string) $xml->Operator;
			}
			$this->setLastResultOk($result->ResultCode);
		}

		return $result;
	}

	public function lastResultIsOk()
	{
		return $this->lastResultIsOk;
	}

	public function getLastParams()
	{
		return $this->lastParams;
	}

	public function getLastInfo()
	{
		if (is_resource($this->ch)) {
			return curl_getinfo($this->ch);
		}
	}

	public function getLastResponse()
	{
		return $this->lastResponse;
	}

	public function isTemporaryError()
	{
		return !empty($this->lastResult) && '2' == $this->lastResult[0];
	}

	public function isPermamentError()
	{
		return !empty($this->lastResult) && '3' == $this->lastResult[0];
	}

	public function isCustomerInBLackList()
	{
		return !empty($this->lastResult) && in_array($this->lastResult, array('3201', '3202'));
	}

	public function hasInsufficientCredit()
	{
		return !empty($this->lastResult) && '3400' == $this->lastResult;
	}

	public function isThresholdReached()
	{
		return !empty($this->lastResult) && '3500' == $this->lastResult;
	}

	private function setLastResultOk($resultCode, Array $okResults = NULL)
	{
		$this->lastResult = $resultCode;
		$this->lastResultIsOk = !empty($resultCode) && (
			(!$okResults && '1' == $resultCode[0]) || ($okResults && in_array($resultCode, $okResults))
		);
	}

	private function getConnection()
	{
		if (FALSE !== $this->ch) {
			return $this->ch;
		}

		$ch = curl_init();
		$opt = array(
			CURLOPT_FAILONERROR => TRUE,
			CURLOPT_FOLLOWLOCATION => TRUE,
			CURLOPT_HEADER => FALSE,
			CURLOPT_POST => TRUE,
			CURLOPT_RETURNTRANSFER => TRUE,
			CURLOPT_SSL_VERIFYHOST => 2,
			CURLOPT_SSL_VERIFYPEER => TRUE,
			CURLOPT_VERBOSE => $this->verbose,
		);

		if ($this->certificatePath) {
			$opt[CURLOPT_CAINFO] = $this->certificatePath;
			$opt[CURLOPT_CAPATH] = dirname($this->certificatePath);
		}

		if ($this->logPath) {
			$opt[CURLOPT_STDERR] = fopen($this->logPath, 'a+');
		}

		curl_setopt_array($ch, $opt);
		$this->ch = $ch;

		return $this->ch;
	}

	private function execute(&$ch)
	{
		$this->lastResult = NULL;
		$this->lastResultIsOk = FALSE;
		$response = trim(curl_exec($ch));
		if ($curlError = curl_error($ch)) {
			throw new \Exception($curlError);
		}
		$this->lastResponse = $response;

		try {
			return new \SimpleXMLElement($response);
		} catch (Exception $e) {
			error_log($e->getMessage());
		}

		return NULL;
	}

	public function __destruct()
	{
		if (FALSE !== $this->ch) {
			curl_close($this->ch);
		}
	}
}