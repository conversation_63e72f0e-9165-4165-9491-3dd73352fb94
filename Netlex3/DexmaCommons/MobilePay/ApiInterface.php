<?php
namespace MobilePay;

interface ApiInterface
{
	public function startBillingTransaction($productID, $productDescription = NULL, $MSISDN = NULL, $Operator = NULL, $getTC = NULL, $tokenID = NULL);

	public function sendCaringMessage($TransactionID, $ProductID, $MessageType, $MSISDN, $Operator, $MessageValue = NULL);

	public function notifyMsisdnAndOperator($TransactionID, $MSISDN, $Operator, $UserAgent = NULL, $GetTC = NULL);

	public function completeBillingTransaction($TransactionID, $StatusCode, $GetMsisdnOrigin = NULL, $GetBilledAmount = NULL);

	public function subscriptionRenewal($TransactionID, $ProductID, $MSISDN, $GetBilledAmount = NULL);

	public function unsubscribeCustomer($TransactionID, $ProductID, $MSISDN);
}