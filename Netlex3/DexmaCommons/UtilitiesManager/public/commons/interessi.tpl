<script src="/public/UtilitiesManager/commons/js/interessi.js"></script>
<script src="/public/UtilitiesManager/js/utilities.js"></script>
<div class="row-fluid top-divider">
	{if $mode == 0}
	<div class="utilitiesExplanation">
		{t}Questa utilità calcola gli interessi legali in accordo con il tasso di interesse legale è fissato dal legislatore{/t}.
	</div>
	{else if $mode == 1}
	<div class="utilitiesExplanation">
           {t}Questa utilità calcola gli interessi con un tasso fisso specificato dall'utente{/t}.
       </div>
       {/if}

	<div class="utilitiesExplanation top-divider">
		{t}L'anatocismo è consentito dal codice civile nell'art. 1283 in casi ristretti, fatta salva la possibilità di ricorrervi quando lo prevedano gli usi normativi{/t}.
	</div>
	<div class="utilitiesExplanation">
		<p>{t}Impostando la capitalizzazione ogni 3, 6 o 12 mesi i periodi di decorrenza partono da date prefissate{/t}:</p>
		<ul>
			<li>{t}Capitalizzazione trimestrale: 1 gennaio, 1 aprile, 1 luglio, 1 ottobre{/t}</li>
			<li>{t}Capitalizzazione semestrale: 1 gennaio e 1 luglio{/t}</li>
			<li>{t}Capitalizzazione annuale: 1 gennaio{/t}</li>
		</ul>
	</div>
</div>
<form id="interessiForm" class="form-horizontal">
	<div id="amountContainer" class="control-group custom-control-group">
		<label class="control-label" for="amount">{t}Importo{/t} &euro;:</label>
		<div class="controls">
			<input id="amount" name="amount" type="text" value="" onkeydown="return isKeyboardNumber(event, 1)" onblur="validateAmount()"/>
			<span id="amountError" class="help-inline"></span>	
		</div>
	</div>
	{if $mode == 1}
		<div id="rateContainer" class="control-group custom-control-group">
			<label class="control-label" for="rate">{t}Tasso{/t}:</label>
			<div class="controls">
				<input id="rate" name="rate" type="text" value="" onkeydown="return isKeyboardNumber(event, 1)" onblur="validateRate()" />
				<span id="rateError" class="help-inline"></span>	
			</div>
		</div>
	{/if}
	<div id="startDateContainer" class="control-group custom-control-group">
		<label class="control-label" for="startDate">{t}Data inizio{/t}:</label>
		<div class="controls">
			<input id="startDate" name="startDate" type="text" class="jqueryCalendar" title="{t}Seleziona data{/t}" onblur="validateStartDate()" />
			<span id="startDateError" class="help-inline"></span>	
		</div>
	</div>
	<div id="endDateContainer" class="control-group custom-control-group">
		<label class="control-label" for="endDate">{t}Data fine{/t}:</label>
		<div class="controls">
			<input id="endDate" name="endDate" type="text" class="jqueryCalendar" title="{t}Seleziona data{/t}" onblur="validateEndDate()" />
			<span id="endDateError" class="help-inline"></span>	
		</div>
	</div>
	<div class="control-group">
		<div class="controls">
			<label class="radio">
				<input type="radio" name="capitalization" value="0" checked="checked" /> {t}Nessuna capitalizzazione{/t}
			</label>
			<label class="radio">
				<input type="radio" name="capitalization" value="1" /> {t}Trimestrale{/t} ({t}Ogni tre mesi l'interesse maturato si aggiunge al capitale{/t})
			</label>
			<label class="radio">
				<input type="radio" name="capitalization" value="2" /> {t}Semestrale{/t} ({t}Ogni sei mesi l'interesse maturato si aggiunge al capitale{/t})
			</label>
			<label class="radio">
				<input type="radio" name="capitalization" value="3" /> {t}Annuale{/t} ({t}Ogni dodici mesi l'interesse maturato si aggiunge al capitale{/t})
			</label>
		</div>
	</div>
	<div class="row-fluid">
		<div class="top-divider utilitiesMarginLeft">
			<button class="btn btn-large btn-primary" type="button" onclick="calc({$mode})">{t}Calcola{/t}</button>
			{if isset($archiveID)}
				<input type="hidden" id="interestToReturn">
				<input type="hidden" value="{$archiveID}" id="archiveID">
				<button onclick="backtoDocuments('update')" id="backToDocuments" type="button" class="btn btn-success btn-large" disabled>{t}Aggiorna{/t}</button>
				<button onclick="backtoDocuments()" type="button" class="btn btn-large">{t}Indietro{/t}</button>
			{/if}
		</div>
	</div>
</form>
<div id="result" class="row-fluid result utilitiesMarginLeft0">
	<div class="row-fluid heading utilitiesMarginLeft0">
		<div id="invoiceHeader" class="utilitiesMarginLeft">
			<h2>{t}Risultati{/t}</h2>
		</div>
	</div>
	<div class="top-divider utilitiesMarginLeft utilitiesMarginRight">
		<div id="results"></div>
	</div>
	<div class="row-fluid top-divider utilitiesMarginLeft0" style="padding-left: 20px">
		<div class="utilitiesMarginLeft0">
			<form id="resultsForm" name="resultsForm" method="post" autocomplete="off" class="utilitiesMarginBottom0" action="/{$controllerName}/saveresultsinteressi">
				<input id="filename" name="filename" type="hidden" value="{$fileName}" />
				<input id="title" name="title" type="hidden" value="{$title}" />
				<button id="printButton" class="btn btn-large btn-primary" type="submit">{t}Esporta in formato Word{/t}</button>
			</form>
		</div>
	</div>
</div>
