function validateRate(mode)
{
	if(mode == 1)
	{
		return isNumber('rate', 'Tasso non numerico');
	}
	else
	{
		return true;
	}
}
//Il tasso di interessi di mora/legali, espressi come frazione decimale.

// Per quanto riguarda il tasso di interessi (0.12), esso rappresenta il 12%.
// La percentuale è stata convertita in una frazione decimale dividendo per 100.
// Quindi, 0.12 corrisponde al 12% di interessi.
function getRatesArray(mode,rate) {
	var data = new Array();
	if (mode == 0) {
	    /** Table of legal interest rate TO BE checked with Government updates */
	    data[0] = new Array(new Date(1970,00,01,0,0,0).getTime(), new Date(1990,11,15,23,59,59).getTime(), 0.05);
	    data[1] = new Array(new Date(1990,11,16,0,0,0).getTime(), new Date(1990,11,31,23,59,59).getTime(), 0.1);
	    data[2] = new Array(new Date(1991,00,01,0,0,0).getTime(), new Date(1996,11,31,23,59,59).getTime(), 0.1);
	    data[3] = new Array(new Date(1997,00,01,0,0,0).getTime(), new Date(1998,11,31,23,59,59).getTime(), 0.05);
	    data[4] = new Array(new Date(1999,00,01,0,0,0).getTime(), new Date(2000,11,31,23,59,59).getTime(), 0.025);
	    data[5] = new Array(new Date(2001,00,01,0,0,0).getTime(), new Date(2001,11,31,23,59,59).getTime(), 0.035);
	    data[6] = new Array(new Date(2002,00,01,0,0,0).getTime(), new Date(2003,11,31,23,59,59).getTime(), 0.03);
	    data[7] = new Array(new Date(2004,00,01,0,0,0).getTime(), new Date(2007,11,31,23,59,59).getTime(), 0.025);
	    data[8] = new Array(new Date(2008,00,01,0,0,0).getTime(), new Date(2009,11,31,23,59,59).getTime(), 0.03);
	    data[9] = new Array(new Date(2010,00,01,0,0,0).getTime(), new Date(2010,11,31,23,59,59).getTime(), 0.01);
	    data[10] = new Array(new Date(2011,00,01,0,0,0).getTime(), new Date(2011,11,31,23,59,59).getTime(), 0.015);
	    data[11] = new Array(new Date(2012,00,01,0,0,0).getTime(), new Date(2013,11,31,23,59,59).getTime(), 0.025);
	    data[12] = new Array(new Date(2014,00,01,0,0,0).getTime(), new Date(2014,11,31,23,59,59).getTime(), 0.01);
	    data[13] = new Array(new Date(2015,00,01,0,0,0).getTime(), new Date(2015,11,31,23,59,59).getTime(),0.005);
	    data[14] = new Array(new Date(2016,00,01,0,0,0).getTime(), new Date(2016,11,31,23,59,59).getTime(),0.002);
	    data[15] = new Array(new Date(2017,00,01,0,0,0).getTime(), new Date(2017,11,31,23,59,59).getTime(),0.001);
	    data[16] = new Array(new Date(2018,00,01,0,0,0).getTime(), new Date(2018,11,31,23,59,59).getTime(),0.003);
		data[17] = new Array(new Date(2019,00,01,0,0,0).getTime(), new Date(2019,11,31,23,59,59).getTime(),0.008);
		data[18] = new Array(new Date(2020,00,01,0,0,0).getTime(), new Date(2020,11,31,23,59,59).getTime(),0.0005);
		data[19] = new Array(new Date(2021,00,01,0,0,0).getTime(), new Date(2021,11,31,23,59,59).getTime(),0.0001);
		data[20] = new Array(new Date(2022,00,01,0,0,0).getTime(), new Date(2022,11,31,23,59,59).getTime(),0.0125);
		data[21] = new Array(new Date(2023,00,01,0,0,0).getTime(), new Date(2023,11,31,23,59,59).getTime(),0.05);
		data[22] = new Array(new Date(2024,00,01,0,0,0).getTime(), new Date(2024,11,31,23,59,59).getTime(),0.025);
		data[23] = new Array(new Date(2025,00,01,0,0,0).getTime(), new Date(2025,11,31,23,59,59).getTime(),0.02);
		data[24] = new Array(new Date(2026,00,01,0,0,0).getTime(), new Date(2026,11,31,23,59,59).getTime(),0.02);
		data[25] = new Array(new Date(2027,00,01,0,0,0).getTime(), new Date(2027,11,31,23,59,59).getTime(),0.02);
	}
	else if (mode == 1) {
	    data[0] = new Array(new Date(1970,00,01,0,0,0).getTime(), new Date().getTime(), rate);
	}

	return data;
}

function getRatesArrayInteressiDiMora() {
	var data = new Array();
	data[0] = new Array(new Date(2002,06,01,0,0,0).getTime(), new Date(2002,11,31,23,59,59).getTime(), 0.1035);
	data[1] = new Array(new Date(2003,00,01,0,0,0).getTime(), new Date(2003,05,30,23,59,59).getTime(), 0.0985);
	data[2] = new Array(new Date(2003,06,01,0,0,0).getTime(), new Date(2003,11,31,23,59,59).getTime(), 0.091);
	data[3] = new Array(new Date(2004,00,01,0,0,0).getTime(), new Date(2004,05,30,23,59,59).getTime(), 0.0902);
	data[4] = new Array(new Date(2004,06,01,0,0,0).getTime(), new Date(2004,11,31,23,59,59).getTime(), 0.0901);
	data[5] = new Array(new Date(2005,00,01,0,0,0).getTime(), new Date(2005,05,30,23,59,59).getTime(), 0.0909);
	data[6] = new Array(new Date(2005,06,01,0,0,0).getTime(), new Date(2005,11,31,23,59,59).getTime(), 0.0905);
	data[7] = new Array(new Date(2006,00,01,0,0,0).getTime(), new Date(2006,05,30,23,59,59).getTime(), 0.0925);
	data[8] = new Array(new Date(2006,06,01,0,0,0).getTime(), new Date(2006,11,31,23,59,59).getTime(), 0.0983);
	data[9] = new Array(new Date(2007,00,01,0,0,0).getTime(), new Date(2007,05,30,23,59,59).getTime(), 0.1058);
	data[10] = new Array(new Date(2007,06,01,0,0,0).getTime(), new Date(2007,11,31,23,59,59).getTime(), 0.1107);
	data[11] = new Array(new Date(2008,00,01,0,0,0).getTime(), new Date(2008,05,30,23,59,59).getTime(), 0.112);
	data[12] = new Array(new Date(2008,06,01,0,0,0).getTime(), new Date(2008,11,31,23,59,59).getTime(), 0.111);
	data[13] = new Array(new Date(2009,00,01,0,0,0).getTime(), new Date(2009,05,30,23,59,59).getTime(), 0.095);
	data[14] = new Array(new Date(2009,06,01,0,0,0).getTime(), new Date(2009,11,31,23,59,59).getTime(), 0.08);
	data[15] = new Array(new Date(2010,00,01,0,0,0).getTime(), new Date(2010,05,30,23,59,59).getTime(), 0.08);
	data[16] = new Array(new Date(2010,06,01,0,0,0).getTime(), new Date(2010,11,31,23,59,59).getTime(), 0.08);
	data[17] = new Array(new Date(2011,00,01,0,0,0).getTime(), new Date(2011,05,30,23,59,59).getTime(), 0.08);
	data[18] = new Array(new Date(2011,06,01,0,0,0).getTime(), new Date(2011,11,31,23,59,59).getTime(), 0.0825);
	data[19] = new Array(new Date(2012,00,01,0,0,0).getTime(), new Date(2012,05,30,23,59,59).getTime(), 0.08);
	data[20] = new Array(new Date(2012,06,01,0,0,0).getTime(), new Date(2012,11,31,23,59,59).getTime(), 0.08);
	data[21] = new Array(new Date(2013,00,01,0,0,0).getTime(), new Date(2013,05,30,23,59,59).getTime(), 0.0875);
	data[22] = new Array(new Date(2013,06,01,0,0,0).getTime(), new Date(2013,11,31,23,59,59).getTime(), 0.0850);
	data[23] = new Array(new Date(2014,00,01,0,0,0).getTime(), new Date(2014,05,30,23,59,59).getTime(), 0.0825);
	data[24] = new Array(new Date(2014,06,01,0,0,0).getTime(), new Date(2014,11,31,23,59,59).getTime(), 0.0815);
	data[25] = new Array(new Date(2015,00,01,0,0,0).getTime(), new Date(2015,05,30,23,59,59).getTime(), 0.0805);
	data[26] = new Array(new Date(2015,06,01,0,0,0).getTime(), new Date(2015,11,31,23,59,59).getTime(), 0.0805);
	data[27] = new Array(new Date(2016,00,01,0,0,0).getTime(), new Date(2016,05,30,23,59,59).getTime(), 0.0805);
	data[28] = new Array(new Date(2016,06,01,0,0,0).getTime(), new Date(2016,11,31,23,59,59).getTime(), 0.08);
	data[29] = new Array(new Date(2017,00,01,0,0,0).getTime(), new Date(2017,05,30,23,59,59).getTime(), 0.08);
	data[30] = new Array(new Date(2017,06,01,0,0,0).getTime(), new Date(2017,11,31,23,59,59).getTime(), 0.08);
	data[31] = new Array(new Date(2018,00,01,0,0,0).getTime(), new Date(2018,05,30,23,59,59).getTime(), 0.08);
	data[32] = new Array(new Date(2018,06,01,0,0,0).getTime(), new Date(2018,11,31,23,59,59).getTime(), 0.08);
	data[33] = new Array(new Date(2019,00,01,0,0,0).getTime(), new Date(2019,05,30,23,59,59).getTime(), 0.08);
	data[34] = new Array(new Date(2019,06,01,0,0,0).getTime(), new Date(2019,11,31,23,59,59).getTime(), 0.08);
	data[35] = new Array(new Date(2020,00,01,0,0,0).getTime(), new Date(2020,05,30,23,59,59).getTime(), 0.08);
	data[36] = new Array(new Date(2020,06,01,0,0,0).getTime(), new Date(2020,11,31,23,59,59).getTime(), 0.08);
	data[37] = new Array(new Date(2021,00,01,0,0,0).getTime(), new Date(2021,05,30,23,59,59).getTime(), 0.08);
	data[38] = new Array(new Date(2021,06,01,0,0,0).getTime(), new Date(2021,11,31,23,59,59).getTime(), 0.08);
	data[39] = new Array(new Date(2022,00,01,0,0,0).getTime(), new Date(2022,05,30,23,59,59).getTime(), 0.08);
	data[40] = new Array(new Date(2022,06,01,0,0,0).getTime(), new Date(2022,11,31,23,59,59).getTime(), 0.08);
	data[41] = new Array(new Date(2023,00,01,0,0,0).getTime(), new Date(2023,05,30,23,59,59).getTime(), 0.105);
	data[42] = new Array(new Date(2023,06,01,0,0,0).getTime(), new Date(2023,11,31,23,59,59).getTime(), 0.12);
	data[43] = new Array(new Date(2024,00,01,0,0,0).getTime(), new Date(2024,05,30,23,59,59).getTime(), 0.125);
	data[44] = new Array(new Date(2024,06,01,0,0,0).getTime(), new Date(2024,11,31,23,59,59).getTime(), 0.1225);
	data[45] = new Array(new Date(2025,00,01,0,0,0).getTime(), new Date(2025,05,30,23,59,59).getTime(), 0.1115);
	data[46] = new Array(new Date(2025,06,01,0,0,0).getTime(), new Date(2025,11,31,23,59,59).getTime(), 0.1115);
	data[47] = new Array(new Date(2026,00,01,0,0,0).getTime(), new Date(2026,05,30,23,59,59).getTime(), 0.1115);
	data[48] = new Array(new Date(2026,06,01,0,0,0).getTime(), new Date(2026,11,31,23,59,59).getTime(), 0.1115);
	data[49] = new Array(new Date(2027,00,01,0,0,0).getTime(), new Date(2027,05,30,23,59,59).getTime(), 0.1115);
	data[50] = new Array(new Date(2027,06,01,0,0,0).getTime(), new Date(2027,11,31,23,59,59).getTime(), 0.1115);
	return data;
}

function calc(mode) 
{
	resetForm('resultsForm');
	if(validateAmount() && validateRate(mode) && validateStartDate() && validateEndDate())
	{
		/** Form params */
		var capitalization = $('input:radio[name=capitalization]:checked').val();
		var amount = $("#amount").val();
		if (amount == "") {
			amount = 0;
		}
		amount = parseFloat(amount);
		var rate = -1;
		if (mode == 1) {
			rate = parseFloat($("#rate").val()/100);
		}

		var sday = $('#startDate').datepicker('getDate').getDate();
		var smonth = $('#startDate').datepicker('getDate').getMonth()+1;
		var syear = $('#startDate').datepicker('getDate').getFullYear();
		var d1d = new Date(syear,smonth-1,sday,0,0,0);

		var eday = $('#endDate').datepicker('getDate').getDate();
		var emonth = $('#endDate').datepicker('getDate').getMonth()+1;
		var eyear = $('#endDate').datepicker('getDate').getFullYear();
		var d2d = new Date(eyear,emonth-1,eday,23,59,59);

	    /** Fake */
	   /* d1="1989-04-17 00:00:00";
	    d2="1991-01-15 23:59:59";
	    amount=parseFloat(50000);
	    capitalization = 1;*/

	    var oneDay = 86400000;

	    var calcArray=new Array();
	    var calcArrayCounter=0;
	    var counter=1;

	    var year = d1d.getFullYear();
	    var prevDate = null;

	    /** Create calculation array */
	    if (capitalization > 0) {
	        while(true) {
	            var dcd = new Date(year,counter-1,1,0,0,0);
	            dcd = new Date(dcd.getTime()-1000);

	            /** End of period */
	            if ((dcd.getTime()+1000) > d2d.getTime()) {
	                var diff = d2d.getTime()-dcd.getTime();
	                dcd = new Date((dcd.getTime()+diff));                 
	                var dataArray = new Array();
	                dataArray[0] = prevDate;
	                dataArray[1] = dcd;
	                calcArray[calcArrayCounter++] = dataArray;
	                break;
	            }

	            /** If capitalization date > start */
	            if (dcd.getTime()>d1d.getTime()) {
	                var dataArray = new Array();
	                if (prevDate == null) {
	                    dataArray[0] = d1d;
	                }
	                else {
	                    dataArray[0] = prevDate;
	                }
	                dataArray[1] = dcd;
	                calcArray[calcArrayCounter++] = dataArray;

	                var pyear = dcd.getFullYear();
	                var pmonth = dcd.getMonth()+1;
	                var pday = dcd.getDate();
	                prevDate = new Date(pyear,pmonth-1,pday,0,0,0);
	                prevDate = new Date(prevDate.getTime()+oneDay);
	            }

	            /** quarter */
	            if (capitalization == 1) {
	                counter=counter+3;
	                if (counter > 10) {
	                    year = year + 1;
	                    counter = 1;
	                }
	            }
	            /** six-month */
	            else if (capitalization == 2) {
	                counter=counter+6;
	                if (counter > 8) {
	                    year = year + 1;
	                    counter = 1;
	                }
	            }
	            /** Year */
	            else if (capitalization == 3) {
	                year = year + 1;
	                counter = 1;
	            }

	        }
	    }
	    else {
	        var tmpArray = new Array();
	        tmpArray[0] = d1d;
	        tmpArray[1] = d2d;
	        calcArray[0] = tmpArray;
	    }

	    var html = "<table id='no-more-tables' style='width:100%'>"+
	    	"<thead>"+
	            "<tr>"+
	                "<th style='text-align:left'>" + gettext("Dal") + "</th>"+
	                "<th style='text-align:left'>" + gettext("Al") + "</th>"+
	                "<th style='text-align:right'>" + gettext("Capitale") + "</th>"+
	                "<th style='text-align:right'>" + gettext("Tasso") + "</th>"+
	                "<th style='text-align:right'>" + gettext("Giorni") + "</th>"+
	                "<th style='text-align:right'>" + gettext("Interesse") + "</th>"+
	            "</tr>"+
	    	"</thead>";
	    /** Find rates and periods */
	    var rowCounter = 0;
	    var resultsForm = $('#resultsForm');
	    var superTotalInterest = 0;
	    var superTotalDays = 0;
	    var firstTime = true;
	    for(var i=0; i<calcArray.length; i++) {
	            var d1 = calcArray[i][0];
	            var d2 = calcArray[i][1];
	            var resultArray = findRates(d1, d2, oneDay, mode, rate);
	            var totalInterest = 0;
	            for(var j=0; j<resultArray.length; j++) {
	                var startDate = resultArray[j][0];
	                var stopDate = resultArray[j][1];
	                var diff = Math.round((stopDate.getTime()-startDate.getTime())/oneDay);
	                if (firstTime) {
	                    diff -= 1;
	                    firstTime = false;
	                }
	                superTotalDays += diff;
	                var rate = resultArray[j][2];
	                var interest = amount * rate * diff / 365;
	                interest = Math.round(interest*100)/100;
	                totalInterest += interest;

	                /** Print result */
	                var day = startDate.getDate();
	                if(day < 10)
	                	day = '0'+ day;
	                var month = startDate.getMonth()+1;
	                if(month < 10)
	                	month = '0'+ month;
	                var year = startDate.getFullYear();
	                var dStartDate = day + "/" + month + "/" +year;

	                var day = stopDate.getDate();
	                if(day < 10)
	                	day = '0'+ day;
	                var month = stopDate.getMonth()+1;
	                if(month < 10)
	                	month = '0'+ month;
	                var year = stopDate.getFullYear();
	                var dStopDate = day + "/" + month + "/" +year;

	                var capitale = number_format(amount.toFixed(2), 2, ',', '.');
	                var tasso = (Math.round(((rate*100)*100))/100).toFixed(2);
	                var interesse = number_format(interest.toFixed(2), 2, ',', '.');

	                html += "<tr>"+
	                            "<td data-title='Dal'>"+dStartDate+"</td>"+
	                            "<td data-title='Al'>"+dStopDate+"</td>"+
	                            "<td data-title='Capitale' style='text-align:right'>"+"&euro; " + capitale + "</td>"+
	                            "<td data-title='Tasso' style='text-align:right'>" + tasso + "%</td>"+
	                            "<td data-title='Giorni' style='text-align:right'>"+diff+"</td>"+
	                            "<td data-title='Interessi' style='text-align:right'>"+"&euro; " + interesse + "</td>"+
	                "</tr>";
	                
	                resultsForm
	                	.append('<input type="hidden" id="dal'+(rowCounter+1)+'" name="dal'+(rowCounter+1)+'" value="'+dStartDate+'" />')
	                	.append('<input type="hidden" id="al'+(rowCounter+1)+'" name="al'+(rowCounter+1)+'" value="'+dStopDate+'" />')
	                	.append('<input type="hidden" id="capitale'+(rowCounter+1)+'" name="capitale'+(rowCounter+1)+'" value="'+capitale+'" />')
	                	.append('<input type="hidden" id="tasso'+(rowCounter+1)+'" name="tasso'+(rowCounter+1)+'" value="'+tasso+'" />')
	                	.append('<input type="hidden" id="diff'+(rowCounter+1)+'" name="diff'+(rowCounter+1)+'" value="'+diff+'" />')
	                	.append('<input type="hidden" id="interesse'+(rowCounter+1)+'" name="interesse'+(rowCounter+1)+'" value="'+interesse+'" />');
	                rowCounter++;
	            }

	            if (capitalization > 0) {
	                amount = amount + totalInterest;
	            }
	            superTotalInterest += totalInterest;
	    }
	    html += "</table>";

	    resultsForm.append('<input type="hidden" name="totalRows" value="' + rowCounter + '" />');

	    var totalAmount;
	    if (capitalization == 0) {
	        totalAmount = amount + superTotalInterest;
	    }
	    else {
	        totalAmount = amount;
	    }
	    
	    var superTotalInterestFormatted = number_format((Math.round(superTotalInterest*100)/100).toFixed(2), 2, ',', '.');
	    var totalAmountFormatted = number_format((Math.round(totalAmount*100)/100).toFixed(2), 2, ',', '.');
	    
	    html += "<div id='totalContainer'>";
	    html += "<div class='row top-divider'>"+
	            "<div class='span3'>"+
	            "<span class='boldify'>" + gettext("Giorni totali") + ":</span>"+
	            "</div>"+
	            "<div class='span2 right'>"+
	            superTotalDays+
	            "</div></div>";
	    html += "<div class='row'>"+
	            "<div class='span3'>"+
	            "<span class='boldify'>"+ gettext("Totale interessi") + ":</span> "+
	            "</div>"+
	            "<div class='span2 right'>"+
	            "&euro; "+superTotalInterestFormatted+
	            "</div></div>";
	    html += "<div class='row'>"+
	            "<div class='span3'>"+
	            "<span class='boldify'>"+ gettext("Capitale") +  " + " +  gettext("Interessi") + ":</span>"+
	            "</div>"+
	            "<div class='span2 right'>"+
	            " &euro; "+totalAmountFormatted+
	            "</div></div></div>";
	    
	    resultsForm
	    	.append('<input type="hidden" id="giorniTot" name="giorniTot" value="' + superTotalDays + '" />')
	    	.append('<input type="hidden" id="interessiTot" name="interessiTot" value="' + superTotalInterestFormatted + '" />')
	    	.append('<input type="hidden" id="capitaleInteressi" name="capitaleInteressi" value="' + totalAmountFormatted + '" />');

	    
	    $("#results").html(html);
	    $("#result").show();
		$('#interestToReturn').val(superTotalInterestFormatted);
		$('#backToDocuments').prop('disabled', false);
	}
}

function findRates(d1,d2, oneDay, mode, rate) {
	var data = getRatesArray(mode, rate);

	if (mode == 55) { /** Calcolo interessi di mora */
		var data = getRatesArrayInteressiDiMora();
	}

    var oneDay = 86400000;

    var c1 = new Date(d1.getTime());
    var c2 = new Date(d2.getTime());

    var resultArray = new Array();
    var resultArrayIndex = 0;

    for(var i=0; i<data.length; i++) {
        if ((c1.getTime() >= data[i][0]) && (c1.getTime() <= data[i][1])) {
            if (c2.getTime() <= data[i][1]) {
                var tmpArray = new Array();
                tmpArray[0] = c1;
                tmpArray[1] = c2;
                tmpArray[2] = data[i][2];
                resultArray[resultArrayIndex++] = tmpArray;
                break;
            }
            else {
                var tmpArray = new Array();
                tmpArray[0] = c1;
                tmpArray[1] = new Date(data[i][1]);  
                tmpArray[2] = data[i][2];
                resultArray[resultArrayIndex++] = tmpArray;

                var cTmpDate = new Date(data[i][1]);
                var day = cTmpDate.getDate();
                var month = (cTmpDate.getMonth()+1);
                var year = cTmpDate.getFullYear();
                c1 = new Date(year,month-1,day,0,0,0);
                c1 = new Date(c1.getTime()+oneDay);
            }
        }
    }
    return resultArray;
}

function calcReturnResults($startDateField, $endDateField, $rateField, $amountField, $capitalizationField, mode) {
	var capitalization = $capitalizationField.val();
	var amount = parseFloat($amountField.val());
	var rate = -1;
	var result = {};

	if (mode == 1) {
		rate = parseFloat($rateField.val()/100);
	}

	var sday = $startDateField.datepicker('getDate').getDate();
	var smonth = $startDateField.datepicker('getDate').getMonth()+1;
	var syear = $startDateField.datepicker('getDate').getFullYear();
	var d1d = new Date(syear,smonth-1,sday,0,0,0);

	var eday = $endDateField.datepicker('getDate').getDate();
	var emonth = $endDateField.datepicker('getDate').getMonth()+1;
	var eyear = $endDateField.datepicker('getDate').getFullYear();
	var d2d = new Date(eyear,emonth-1,eday,23,59,59);

    var oneDay = 86400000;

    var calcArray = new Array();
    var calcArrayCounter = 0;
    var counter = 1;

    var year = d1d.getFullYear();
    var prevDate = null;

	/** Create calculation array */
    if (capitalization > 0) {
        while(true) {
            var dcd = new Date(year,counter-1,1,0,0,0);
            dcd = new Date(dcd.getTime()-1000);

            /** End of period */
            if ((dcd.getTime()+1000) > d2d.getTime()) {
                var diff = d2d.getTime()-dcd.getTime();
                dcd = new Date((dcd.getTime()+diff));                 
                var dataArray = new Array();
                dataArray[0] = prevDate;
                dataArray[1] = dcd;
                calcArray[calcArrayCounter++] = dataArray;
                break;
            }

            /** If capitalization date > start */
            if (dcd.getTime()>d1d.getTime()) {
                var dataArray = new Array();
                if (prevDate == null) {
                    dataArray[0] = d1d;
                }
                else {
                    dataArray[0] = prevDate;
                }
                dataArray[1] = dcd;
                calcArray[calcArrayCounter++] = dataArray;

                var pyear = dcd.getFullYear();
                var pmonth = dcd.getMonth()+1;
                var pday = dcd.getDate();
                prevDate = new Date(pyear,pmonth-1,pday,0,0,0);
                prevDate = new Date(prevDate.getTime()+oneDay);
            }

            /** quarter */
            if (capitalization == 1) {
                counter=counter+3;
                if (counter > 10) {
                    year = year + 1;
                    counter = 1;
                }
            }
            /** six-month */
            else if (capitalization == 2) {
                counter=counter+6;
                if (counter > 8) {
                    year = year + 1;
                    counter = 1;
                }
            }
            /** Year */
            else if (capitalization == 3) {
                year = year + 1;
                counter = 1;
            }

        }
    }
    else {
        var tmpArray = new Array();
        tmpArray[0] = d1d;
        tmpArray[1] = d2d;
        calcArray[0] = tmpArray;
    }

    var superTotalInterest = 0;
    var superTotalDays = 0;
    var firstTime = true;
    for(var i=0; i<calcArray.length; i++) {
        var d1 = calcArray[i][0];
        var d2 = calcArray[i][1];
        var resultArray = findRates(d1, d2, oneDay, mode, rate);
        var totalInterest = 0;
        for(var j=0; j<resultArray.length; j++) {
            var startDate = resultArray[j][0];
            var stopDate = resultArray[j][1];
            var diff = Math.round((stopDate.getTime()-startDate.getTime())/oneDay);
            if (firstTime) {
                diff -= 1;
                firstTime = false;
            }
            superTotalDays += diff;
            var rate = resultArray[j][2];
            var interest = amount * rate * diff / 365;
            interest = Math.round(interest*100)/100;
            totalInterest += interest;

            var capitale = number_format(amount.toFixed(2), 2, ',', '.');
            var tasso = (Math.round(((rate*100)*100))/100).toFixed(2);
            var interesse = number_format(interest.toFixed(2), 2, ',', '.');
        }

        if (capitalization > 0) {
            amount = amount + totalInterest;
        }
        superTotalInterest += totalInterest;
    }

    var totalAmount;
    if (capitalization == 0) {
        totalAmount = amount + superTotalInterest;
    }
    else {
        totalAmount = amount;
    }

    superTotalInterest = (Math.round(superTotalInterest*100)/100).toFixed(2);
    totalAmount = (Math.round(totalAmount*100)/100).toFixed(2);
    
    var superTotalInterestFormatted = number_format(superTotalInterest, 2, ',', '.');
    var totalAmountFormatted = number_format(totalAmount, 2, ',', '.');

    result = {'totalDays': superTotalDays, 'totalInterests': superTotalInterest, 'totalInterestsFormatted': superTotalInterestFormatted, 'totalAmount': totalAmount, 'totalAmountFormatted': totalAmountFormatted};

    return result;
}

function calcInteressiDiMora($startDateField, $endDateField, $amountField, $before2012Field, $agriproductsField, $agriproductsValueField) {
	var amount = parseFloat($amountField.val());
	var rate = -1;
	var mode = 55;
	var result = {};

	var sday = $startDateField.datepicker('getDate').getDate();
	var smonth = $startDateField.datepicker('getDate').getMonth()+1;
	var syear = $startDateField.datepicker('getDate').getFullYear();
	var d1d = new Date(syear,smonth-1,sday,0,0,0);

	var eday = $endDateField.datepicker('getDate').getDate();
	var emonth = $endDateField.datepicker('getDate').getMonth()+1;
	var eyear = $endDateField.datepicker('getDate').getFullYear();
	var d2d = new Date(eyear,emonth-1,eday,23,59,59);

    var oneDay = 86400000;

    var calcArray = new Array();
    var calcArrayCounter = 0;
    var counter = 1;

    var year = d1d.getFullYear();
    var prevDate = null;

	/** Create calculation array */
    var tmpArray = new Array();
    tmpArray[0] = d1d;
    tmpArray[1] = d2d;
    calcArray[0] = tmpArray;

    var superTotalInterest = 0;
    var superTotalDays = 0;
    var firstTime = true;
    for(var i=0; i<calcArray.length; i++) {
        var d1 = calcArray[i][0];
        var d2 = calcArray[i][1];
        var resultArray = findRates(d1, d2, oneDay, mode, rate);
        var totalInterest = 0;
        for(var j=0; j<resultArray.length; j++) {
            var startDate = resultArray[j][0];
            var stopDate = resultArray[j][1];
            var diff = Math.round((stopDate.getTime()-startDate.getTime())/oneDay);
            if (firstTime) {
                diff -= 1;
                firstTime = false;
            }
            superTotalDays += diff;
            var rate = resultArray[j][2];
            if ($before2012Field.prop('checked')) {
            	rate = 0.07;
            }
            
            if ($agriproductsField.prop('checked')) {
            	var upRate = $agriproductsValueField.val();
            	rate += upRate / 100;
            }

            var interest = amount * rate * diff / 365;

            interest = Math.round(interest*100)/100;
            totalInterest += interest;
        }

        superTotalInterest += totalInterest;
    }

    var totalAmount = amount + superTotalInterest;

    superTotalInterest = (Math.round(superTotalInterest*100)/100).toFixed(2);
    totalAmount = (Math.round(totalAmount*100)/100).toFixed(2);

    var superTotalInterestFormatted = number_format(superTotalInterest, 2, ',', '.');
    var totalAmountFormatted = number_format(totalAmount, 2, ',', '.');

    result = {'totalDays': superTotalDays, 'totalInterests': superTotalInterest, 'totalAmount': totalAmount, 'totalInterestsFormatted': superTotalInterestFormatted, 'totalAmountFormatted': totalAmountFormatted};

    return result;
}

function backtoDocuments(action = null) {
	if(action){
		localStorage.setItem('documentInterests', $('#interestToReturn').val());
	}
	document.location.href = "/archivecreditrecovery/creditrecovery?fileUniqueid=" + $('#archiveID').val();
}

$(document).ready(function() {
	createCalendar('startDate', '01/01/1970', true);
	$('#startDate').datepicker('setDate', '01/01/2020');
	var currentTime = new Date();

	createCalendar('endDate', null, true);

	$('#endDate').datepicker('setDate', currentTime);

	$('#startDate').change(function(){
		$('#endDate').datepicker('option', 'minDate', $('#startDate').datepicker('getDate'));
	})
	.change();

	$("#amount").focus();
	if(localStorage.getItem("documentData") != null){
		var documentData = JSON.parse(localStorage.getItem("documentData"));
		var calcData = JSON.parse(localStorage.getItem("calcData"));
		$("#amount").val(calcData.onorari);
		$("#startDate").val(calcData.date);
		$("#endDate").val(new Date().toLocaleDateString([], {month: '2-digit', day: '2-digit', year: 'numeric'}));
	}
});
