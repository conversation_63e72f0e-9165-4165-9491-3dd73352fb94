function validateAmount()
{
	var result = false;
	if(checkField('amount', gettext('Importo obbligatorio')))
	{
		result = isNumber('amount', gettext('Importo non numerico'));
	}
	return result;
}

function validateStartDate()
{
	return checkField('startDate', gettext('Data inizio obbligatoria'));
}

function validateEndDate()
{
	return checkField('endDate', gettext('Data fine obbligatoria'));
}

function resetForm(formName)
{
	var filename  = $('#filename');
	var title = $('#title');
	var printButton = $('#printButton');
	var resultsForm = $('#' + formName);
	resultsForm.empty();
	resultsForm.append(filename);
	resultsForm.append(title);
	resultsForm.append(printButton);
}
