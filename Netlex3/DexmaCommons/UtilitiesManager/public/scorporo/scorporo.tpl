<script src="/public/UtilitiesManager/scorporo/js/scorporo.js"></script>
<script src="/public/UtilitiesManager/js/utilities.js"></script>
<div class="row-fluid top-divider">
	<div class="utilitiesExplanation">
		{t}Questa utilità calcola l'importo imponibile da inserire in fattura a fronte di un importo specificato come{/t} "{t}Netto a pagare{/t}"
	</div>
</div>
<form id="scorporoForm" class="form-horizontal">
	<div id="amountContainer" class="control-group">
		<label class="control-label" for="amount">{t}Importo{/t} &euro;:</label>
		<div class="controls">
			<input id="amount" name="amount" type="text" value="" onkeydown="return isKeyboardNumber(event, 1)" onblur="validateAmount()"/>
			<span id="amountError" class="help-inline"></span>	
		</div>
	</div>
	<div id="speseEsentiContainer" class="control-group custom-control-group">
		<label class="control-label" for="speseEsenti">{t}Di cui spese esenti{/t} &euro;:</label>
		<div class="controls">
			<input id="speseEsenti" name="speseEsenti" type="text" value="" onkeydown="return isKeyboardNumber(event, 1)" onblur="validateSpeseEsenti()"/>
			<span id="speseEsentiError" class="help-inline"></span>	
		</div>
	</div>
	<div id="useSpeseGeneraliContainer" class="control-group custom-control-group">
		<label class="control-label" for="useSpeseGenerali">{t}Spese generali{/t}</label>
		<div class="controls">
			<input id="useSpeseGeneraliCheck" name="useSpeseGeneraliCheck" type="checkbox" checked="checked"/>
			<div class="input-append" style="margin-left:10px;">
				<input class="input-mini" id="useSpeseGenerali" name="useSpeseGenerali" type="text" onkeydown="return isKeyboardNumber(event, 1)" onblur="validateSpeseGenerali()" value="{$defaultValues.speseGenerali}"><span class="add-on">%</span>
			</div>
		</div>
	</div>
	<div id="useCassaAvvocatiContainer" class="control-group custom-control-group">
		<label class="control-label" for="useCassaAvvocati">{t}Cassa avvocati{/t}</label>
		<div class="controls">
			<input id="useCassaAvvocatiCheck" name="useCassaAvvocatiCheck" type="checkbox" checked="checked"/>
			<div class="input-append" style="margin-left:10px;">
				<input class="input-mini" id="useCassaAvvocati" name="useCassaAvvocati" type="text" onkeydown="return isKeyboardNumber(event, 1)" onblur="validateCassaAvvocati()" value="{$defaultValues.cassa}"><span class="add-on">%</span>
			</div>
		</div>
	</div>
	<div id="useRitenutaContainer" class="control-group custom-control-group">
		<label class="control-label" for="useRitenuta">{t}Ritenuta d'acconto{/t}</label>
		<div class="controls">
			<input id="useRitenutaCheck" name="useRitenutaCheck" type="checkbox" checked="checked"/>
			<div class="input-append" style="margin-left:10px">
				<input class="input-mini" id="useRitenuta" name="useRitenuta" type="text" onkeydown="return isKeyboardNumber(event, 1)" onblur="validateRitenuta()" value="{$defaultValues.ritenuta}"><span class="add-on">%</span>
			</div>
		</div>
	</div>
	<div id="useSplitPaymentContainer" class="control-group custom-control-group">
		<label class="control-label" for="useSplitPayment">{t}Split payment{/t}</label>
		<div class="controls"><input id="useSplitPaymentCheck" name="useSplitPaymentCheck" type="checkbox"></div>
	</div>
	<div id="regimeSemplificatoContainer" class="control-group custom-control-group-checkbox">
		<label class="control-label" for="regimeSemplificato">{t}Regime semplificato{/t}</label>
		<div class="controls">
			<input id="regimeSemplificato" name="regimeSemplificato" type="checkbox"/>
		</div>
	</div>
	<div class="row-fluid">
		<div class="top-divider utilitiesMarginLeft">
			<button class="btn btn-large btn-primary" type="button" onclick="calc()">{t}Scorpora Importi{/t}</button>
		</div>
	</div>
</form>
<div id="result" class="row-fluid result utilitiesMarginLeft0">
	<div class="row-fluid heading utilitiesMarginLeft0">
		<div id="invoiceHeader" class="utilitiesMarginLeft">
			<h2>{t}Dettaglio Fattura{/t}</h2>
		</div>
	</div>
	<div class="row-fluid top-divider">
		<div class="span4 boldify right">
			{t}Compensi professionali{/t}:
		</div>
		<div class="span2 right dottify">
			<div id="imponibile"></div>
		</div>
	</div>
	<div class="row-fluid">
		<div class="span4 boldify right">
			{t}Spese Generali{/t} (<span id="pSpeseGenerali"></span>%):
		</div>
		<div class="span2 right dottify">
			<div id="speseGenerali"></div>
		</div>
	</div>
	<div class="row-fluid">
		<div class="span4 boldify right">
			{t}Cassa Avvocati{/t} (<span id="pCassa"></span>%):
		</div>
		<div class="span2 right dottify">
			<div id="cassa"></div>
		</div>
	</div>
	<div class="row-fluid">
		<div class="span4 boldify right">
			{t}Totale imponibile{/t}:
		</div>
		<div class="span2 right dottify">
			<div id="totaleImponibile"></div>
		</div>
	</div>
	<div id="dIva" class="row-fluid top-divider">
		<div class="span4 boldify right">
			IVA (<span id="pIva"></span>%) {t}Su Imponibile{/t}:
		</div>
		<div class="span2 right dottify">
			<div id="iva"></div>
		</div>
	</div>
	<div class="row-fluid top-divider">
		<div class="span4 boldify right">
			{t}Spese esenti{/t} art. 10:
		</div>
		<div class="span2 right dottify">
			<div id="dSpeseEsenti"></div>
		</div>
	</div>
	<div class="row-fluid">
		<div class="span4 boldify right">
			{t}Totale Documento{/t}:
		</div>
		<div class="span2 right dottify">
			<div id="totale"></div>
		</div>
	</div>
	<div class="row-fluid top-divider">
		<div class="span4 boldify right">
			{t}Ritenuta d'acconto{/t} <span id="pRitenuta"></span>%:
		</div>
		<div class="span2 right dottify">
			<div id="ritenuta"></div>
		</div>
	</div>
	<div class="row-fluid top-divider">
		<div class="span4 boldify right">
			{t}Netto a pagare{/t}:
		</div>
		<div class="span2 right dottify">
			<div id="totaleFattura"></div>
		</div>
	</div>
	<div class="row-fluid top-divider utilitiesMarginLeft0" style="padding-left: 20px">
		<div class="utilitiesMarginLeft0">
			<form id="resultsForm" name="resultsForm" method="post" autocomplete="off" class="utilitiesMarginBottom0" action="/{$controllerName}/saveresultsscorporo">
				<input id="filename" name="filename" type="hidden" value="{$fileName}" />
				<input id="title" name="title" type="hidden" value="{$title}" />
				<button id="printButton" class="btn btn-large btn-primary" type="submit">{t}Esporta in formato Word{/t}</button>
			</form>
		</div>
	</div>
</div>
