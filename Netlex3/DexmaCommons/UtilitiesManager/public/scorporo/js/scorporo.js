var myResults = null;

$(document).ready(function() {
	$("#amount").focus();

});

function validateSpeseEsenti()
{
	return isNumber('speseEsenti', 'Spese esenti non numerico');
}

function validateSpeseGenerali() {
	var id = 'useSpeseGenerali';
	var	msg = 'Percentuale spese generali in formato non numerico';
	var	checked = $('#useSpeseGeneraliCheck').prop('checked');

	if (!checked) {
		$('#' + id + 'Container').removeClass('text-error');
		disableErrorClass(id);
		return true;
	}

	return checkPercentages(id, msg);
}

function validateCassaAvvocati() {
	var id = 'useCassaAvvocati';
	var	msg = 'Percentuale cassa avvocati in formato non numerico';
	var	checked = $('#useCassaAvvocatiCheck').prop('checked');

	if (!checked) {
		$('#' + id + 'Container').removeClass('text-error');
		disableErrorClass(id);
		return true;
	}

	return checkPercentages(id, msg);
}

function validateRitenuta() {
	var id = 'useRitenuta';
	var msg = 'Percentuale spese generali in formato non numerico';
	var checked = $('#useRitenuta');
	return checkPercentages(id, msg);
}

function checkPercentages(id, msg) {
	if (checkField(id, msg)) {
		if (!isNumber(id, msg)) {
			var container = $('#' + id + 'Container').addClass('text-error');
			$('#' + id).one('focus', function(){
				container.removeClass('text-error');
			});
			return false;
		}
		else {
			$('#' + id + 'Container').removeClass('text-error');
		}

		return true;
	}
}

/*
 * params ed il suo contenuto, speseEsenti e speseEscluse, sono facoltativi
 * Uso: calcolaScorporo(importo, speseEsenti, speseEscluse, {speseGenerali:x, cassa:y, ...})
 */
function calcolaScorporo(amount, speseEsenti, speseEscluse, params) {
	var defaults = {
		speseGenerali : 0.15,
		cassa : 0.04,
		iva : 0.22,
		ritenuta : 0.20
	};

	for (var i in params) {
		defaults[i] = params[i];
	}

	amount = parseFloat(amount) || 0.00;
	speseEsenti = parseFloat(speseEsenti) || 0.00;
	speseEscluse = parseFloat(speseEscluse) || 0.00;

	var pSpeseGenerali = parseFloat(defaults.speseGenerali) || 0.00;
	var pCassa = parseFloat(defaults.cassa) || 0.00;
	var pRitenuta = parseFloat(defaults.ritenuta) || 0.00;
	var pIva = parseFloat(defaults.iva) || 0.00;

	/** Program setup, nothing must be changed here ! */
	var cGenerale = 1+pSpeseGenerali;
	var cCassa = cGenerale*pCassa;
	var cImponibile = cGenerale+cCassa;
	var cIva = cImponibile * pIva;
	var cTotale = defaults.splitPayment? cGenerale + cCassa : cImponibile + cIva;
	var cRitenuta = cGenerale * pRitenuta;
	var cNetto = cTotale - cRitenuta;

	/** Calculations */
	amount = amount - speseEsenti - speseEscluse;

	var imponibile = parseFloat((amount / cNetto).toFixed(2));
	var speseGenerali = parseFloat((imponibile * pSpeseGenerali).toFixed(2));
	var cassa = parseFloat(((imponibile + speseGenerali) * pCassa).toFixed(2));
	var totaleImponibile = parseFloat((imponibile + speseGenerali + cassa).toFixed(2));
	var iva = parseFloat((totaleImponibile * pIva).toFixed(2));
	var totale = totaleImponibile + iva + speseEsenti + speseEscluse;
	var ritenuta = parseFloat(((imponibile + speseGenerali)*pRitenuta).toFixed(2));
	var totaleFattura = defaults.splitPayment? totale - ritenuta - iva : totale - ritenuta;

	// trick for round fee calculation
	const unwantedDiff = parseFloat(totaleFattura - (amount + speseEsenti + speseEscluse)).toFixed(2);

	return {
		imponibile: parseFloat(imponibile - unwantedDiff),
		speseGenerali: speseGenerali,
		speseEsenti: speseEsenti,
		speseEscluse: speseEscluse,
		cassa: cassa,
		totaleImponibile: parseFloat(totaleImponibile - unwantedDiff),
		iva: iva,
		totale: parseFloat(totale - unwantedDiff),
		ritenuta: ritenuta,
		totaleFattura: parseFloat(totaleFattura - unwantedDiff)
	}
}

function calc()
{
	var resultsForm = $('#resultsForm');
	resetForm('resultsForm');
	if(validateAmount() && validateSpeseEsenti() && validateSpeseGenerali() && validateRitenuta())
	{
		myResults = new Array();
		/** Parameters to be set up */
		var pSpeseGenerali = parseFloat($('#useSpeseGenerali').val()) / 100;
		var pCassa = 0.04;
		var pIva = 0.22;
		var pRitenuta = parseFloat($('#useRitenuta').val()) / 100;

		/** Data from page, nothing must be changed here ! */
		var amount = $("#amount").val();
		var speseEsenti = $("#speseEsenti").val();
		if (speseEsenti == "") {
			speseEsenti = 0;
		}
		speseEsenti = parseFloat(speseEsenti);
		var useSpeseGenerali = $("#useSpeseGeneraliCheck").is(':checked');
		var useCassaAvvocati = $("#useCassaAvvocatiCheck").is(':checked');
		var useRitenuta = $("#useRitenutaCheck").is(':checked');
		var regimeSemplificato = $("#regimeSemplificato").is(':checked');
		var splitPayment = $('#useSplitPaymentCheck').prop('checked');

		if (useSpeseGenerali == false) {
			pSpeseGenerali = 0;
		}
		
		if (useCassaAvvocati == false) {
			pCassa = 0;
		}

		if (useRitenuta == false) {
			pRitenuta = 0;
		}

		if (regimeSemplificato == true) {
			pIva = 0;
			$("#dIva").hide();
		}

		var result = calcolaScorporo(amount, speseEsenti, 0, {
			cassa: pCassa,
			speseGenerali: pSpeseGenerali,
			iva: pIva,
			ritenuta: pRitenuta,
			splitPayment: splitPayment,
		});

		$("#imponibile").html("&euro; "+number_format(result.imponibile.toFixed(2), 2, ',', '.'));
		$("#speseGenerali").html("&euro; "+number_format(result.speseGenerali.toFixed(2), 2, ',', '.'));
		$("#pSpeseGenerali").html(number_format((pSpeseGenerali*100).toFixed(2), 2, ',', '.'));
		$("#pCassa").html(pCassa*100);
		$("#cassa").html("&euro; "+number_format(result.cassa.toFixed(2), 2, ',', '.'));
		$("#totaleImponibile").html("&euro; "+number_format(result.totaleImponibile.toFixed(2), 2, ',', '.'));
		$("#pIva").html(pIva*100);
		$("#iva").html("&euro; "+number_format(result.iva.toFixed(2), 2, ',', '.'));
		$("#dSpeseEsenti").html("&euro; "+number_format(speseEsenti.toFixed(2), 2, ',', '.'));
		$("#totale").html("&euro; "+number_format(result.totale.toFixed(2), 2, ',', '.'));
		$("#pRitenuta").html(pRitenuta*100);
		$("#ritenuta").html("&euro; "+number_format(result.ritenuta.toFixed(2), 2, ',', '.'));
		$("#totaleFattura").html("&euro; "+number_format(result.totaleFattura.toFixed(2), 2, ',', '.'));

		$("#result").show();

		resultsForm
	    	.append('<input type="hidden" id="imponibile" name="imponibile" value="' + $("#imponibile").text() + '" />')
	    	.append('<input type="hidden" id ="speseGenerali" name="speseGenerali" value="' + $("#speseGenerali").text() + '" />')
	    	.append('<input type="hidden" id ="cassa" name="cassa" value="' + $("#cassa").text() + '" />')
	    	.append('<input type="hidden" id ="totaleImponibile" name="totaleImponibile" value="' + $("#totaleImponibile").text() + '" />')
	    	.append('<input type="hidden" id ="iva" name="iva" value="' + $("#iva").text() + '" />')
	    	.append('<input type="hidden" id ="speseEsenti" name="speseEsenti" value="' + $("#dSpeseEsenti").text() + '" />')
	    	.append('<input type="hidden" id ="totale" name="totale" value="' + $("#totale").text() + '" />')
	    	.append('<input type="hidden" id ="ritenuta" name="ritenuta" value="' + $("#ritenuta").text() + '" />')
	    	.append('<input type="hidden" id ="totaleFattura" name="totaleFattura" value="' + $("#totaleFattura").text() + '" />');
	}
}
