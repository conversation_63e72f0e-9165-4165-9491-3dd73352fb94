.right { text-align: right;}
.top-divider { margin-top: 20px; }
.result { 
	margin-top: 20px;
	padding-bottom: 20px;
	display: none; 
	border: 1px solid #ECEDF3;
	line-height: 25px;
	margin-left: 10px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
}

.boldify { font-weight: bold; }
.container {font-size: 14px;}
.breadcrumbs { margin-bottom: 0px; }

.control-group.custom-control-group { margin-top: 20px; }

.control-group.custom-control-group-checkbox 
{
	margin-bottom: 10px;
}
.custom-control-group .control-label, .custom-control-group-checkbox .control-label
{
	text-align: right;
}

.control-label {
	font-weight: bold;
}

.form-horizontal .control-label {
	width: 150px;
}

.form-horizontal { margin-top: 10px; }

.dottify { border-bottom: 2px dotted #ccc; }

.heading {
	background: #F2F2F2;
	color: #222;
	-webkit-border-top-left-radius: 4px;
	-webkit-border-top-right-radius: 4px;
	-moz-border-radius-topleft: 4px;
	-moz-border-radius-topright: 4px;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}

.utilitiesExplanation { margin-left: 20px; margin-bottom: 20px; }
.utilitiesMarginLeft { margin-left: 20px; }
.utilitiesMarginRight { margin-right: 20px; }
.utilitiesMarginLeft0 { margin-left: 0px; }
.utilitiesMarginBottom0 { margin-bottom: 0px; }