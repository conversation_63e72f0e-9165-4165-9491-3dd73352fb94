<?php
class UtilitiesManager_Exportword
{
	private function sendFile($tmpl, $filename)
	{
		require_once DEXMA_COMMONS . '/RTFManager/RTF.php';
		$tmpl = RTFManager_RTF::encodeSpecialChars($tmpl);
		RTFManager_RTF::send($tmpl, $filename);
	}

	public function saveResultsInteressi()
	{
		$content = '';
		$interessiFileTemplate = 'interessi.rtf';
		$result = $_REQUEST;

		$totalRows = $result['totalRows'];
		$filename = $result['filename'] . '.rtf';
		$title = $result['title'];
		$giorniTot = $result['giorniTot'];
		$interessiTot = $result['interessiTot'];
		$capitaleInteressi = $result['capitaleInteressi'];

		unset($result['totalRows']);
		unset($result['filename']);
		unset($result['title']);
		unset($result['giorniTot']);
		unset($result['interessiTot']);
		unset($result['capitaleInteressi']);

		$row = file_get_contents(DEXMA_COMMONS.'/public/UtilitiesManager/rtftemplates/row.rtf');
		for($i=1; $i<=$totalRows; $i++)
		{
			$rowCopy = $row;
			foreach ($result as $key => $value)
			{
				if($key == "dal$i")
				{
					$rowCopy = str_replace('<%dal%>', $value, $rowCopy);
					unset($result[$key]);
				}
				else if($key == "al$i")
				{
					$rowCopy = str_replace('<%al%>', $value, $rowCopy);
					unset($result[$key]);
				}
				else if($key == "capitale$i")
				{
					$rowCopy = str_replace('<%capitale%>', $value, $rowCopy);
					unset($result[$key]);
				}
				else if($key == "tasso$i")
				{
					$rowCopy = str_replace('<%tasso%>', $value, $rowCopy);
					unset($result[$key]);
				}
				else if($key == "diff$i")
				{
					$rowCopy = str_replace('<%giorni%>', $value, $rowCopy);
					unset($result[$key]);
				}
				else if($key == "interesse$i")
				{
					$rowCopy = str_replace('<%interesse%>', $value, $rowCopy);
					unset($result[$key]);
				}
			}
			$content .= $rowCopy;
		}
		$tmpl = file_get_contents(DEXMA_COMMONS.'/public/UtilitiesManager/rtftemplates/' . $interessiFileTemplate);
		$tmpl = str_replace('<%title%>', $title, $tmpl);
		$tmpl = str_replace('<%content%>', $content, $tmpl);
		$tmpl = str_replace('<%giorniTot%>', $giorniTot, $tmpl);
		$tmpl = str_replace('<%interessiTot%>', $interessiTot, $tmpl);
		$tmpl = str_replace('<%capitaleInteressi%>', $capitaleInteressi, $tmpl);

		$this->sendFile($tmpl, $filename);
	}

	public function saveResultsScorporo()
	{
		$result = $_REQUEST;
		$filename = $result['filename'].'.rtf';
		$title = $result['title'];
		unset($result['filename']);
		unset($result['title']);

		if ($tmpl = file_get_contents(DEXMA_COMMONS.'/public/UtilitiesManager/rtftemplates/' . $filename))
		{
			$tags = array();
			preg_match_all('/<%(.*?)%>/', $tmpl, $tags);
			$tags[0] = array_values(array_unique($tags[0]));
			$tags[1] = array_values(array_unique($tags[1]));
			for ($i = 0; $i < count($tags[0]); $i++)
			{
				$value = $tags[1][$i];
				$tagValue = $result["$value"];
				$tagValue = str_replace(chr(0xE2).chr(0x82).chr(0xAC), '', $tagValue);
				$tmpl = str_replace($tags[0][$i], $tagValue, $tmpl);
			}

			$this->sendFile($tmpl, $filename);
		}
	}
}