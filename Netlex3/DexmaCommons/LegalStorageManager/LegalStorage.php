<?php
class LegalStorageManager_LegalStorage {
// 	private static $LEGAL_STORAGE_DIR = "/home/<USER>/";
	public static $STORAGE = 1;
	public static $REPLACE = 2;
	public static $GET = 3;
	public static $DELETE = 4;
	public static $STORAGE_MESSAGE = "Documento in conservazione digitale";
	public static $REPLACE_MESSAGE = "Documento rettificato";
	public static $GET_MESSAGE = "Documento scaricato";
	public static $DELETE_MESSAGE = "Documento cancellato dalla conservazione digitale";
	public static $NO_STORED = "0";
	public static $STORED = "1";
	public static $REPLACED = "2";
	public static $DELETED = "3";
	private static $RESPONSE_PREFIX = "response";
	private static $RESPONSE_DELETE_PREFIX = "response_delete";
	private static $PARAM_FILE = "conserve.xml";
	private static $INDEX_FILE = "index.xml";
	/** DOCUMENTAL CLASSES **/
	private static $CREDIT_NOTE_DOCUMENT = "CREDIT_NOTE_DOCUMENT";
	private static $PDF_DOCUMENT = "PDF_DOCUMENT";
	private static $ELECTRONIC_BILL_DOCUMENT = "ELECTRONIC_BILL_DOCUMENT";
	/** DOCUMENTAL CLASSES **/
	private $applogger;
	private $customer;
	private $db;
	private $insertHistory;
	private $ldSessionId;
	private $userId;
	private $updateHistory;
	private $config;
	public static $COST_PER_MB = 0.12;
	public static $CREDIT_NOTE_SUFFIX = "CN";
	public static $PDF_DOCUMENT_SUFFIX = "D";
	public static $ELECTRONIC_BILL_SUFFIX = "EB";
	public function __construct($siteRoot, $customer, $db, $userId, $insertHistory, $updateHistory) {
		$today = date("Y_m");
		$apploggerDir = "$siteRoot/Software/data/logs/legalStorage/" . $customer["subdomain_name"];
		if (!file_exists($apploggerDir)) {
			$mkdirResult = mkdir($apploggerDir, 0755, true);
		}
		$mkdirResult = true;
		if (!$mkdirResult) {
			throw new Exception("Applogger directory not created");
		}
		$apploggerFile = "$apploggerDir/legalStorage_$today.log";
		$applogger = new Zend_Log(new Zend_Log_Writer_Stream($apploggerFile));
		$filter = new Zend_Log_Filter_Priority(7);
		$applogger->addFilter($filter);
		$this->applogger = $applogger;
		$this->customer = $customer;
		$this->db = $db;
		$this->ldSessionId = null;
		$this->insertHistory = $insertHistory;
		$this->userId = $userId;
		$this->updateHistory = $updateHistory;
		$this->config = parse_ini_file("config.ini");
	}
	private function checkResponse($response) {
		$isXML = true;
		try {
			libxml_use_internal_errors(true);
			$doc = simplexml_load_string($response);
			$xml = explode("\n", $response);
			if (!$doc) {
				$errors = libxml_get_errors();
				foreach ($errors as $error) {
					$isXML = false;
				}
				libxml_clear_errors();
			}
		} catch (Exception $e) {
			$isXML = false;
		}
		return $isXML;
	}
	private function createFileName($s3, $dir, $prefix) {
		$fileName = null;
		try {
			$ext = ".xml.p7m";
			$counter = 1;
			$fileName = $prefix . "_" . $counter . $ext;
			$counter++;
			while ($s3->doesObjectExist($this->config["awsBucket"], $dir . $fileName)) {
				$fileName = $prefix . "_" . $counter . $ext;
				$counter++;
			}
		} catch (Exception $e) {
			throw $e;
		}
		$this->applogger->info("CREATE RESPONSE FILE NAME " . $fileName);
		return $fileName;
	}
	private function createIndexFile() {
		$result = false;
		try {
			$this->applogger->info("LegalStorage.createIndexFile()");
			$doc = new DomDocument("1.0", "ISO-8859-1");
			$doc->version  = "1.0";
			$doc->encoding = "UTF-8";
			$root = $doc->createElement("legaldocIndex");
			$root->setAttribute("documentClass", "ae_gen");
			$root->setAttribute("label", "PDF");
			$description = $doc->createElement("field", "Netlexweb");
			$description->setAttribute("name", "denominazione_s");
			$root->appendChild($description);
			$date = $doc->createElement("field", date("d-m-Y H:i:s"));
			$date->setAttribute("name", "__data_documento_dt");
			$root->appendChild($date);
			$doc->appendChild($root);
// 			$result = $doc->save(self::$LEGAL_STORAGE_DIR . self::$INDEX_FILE);
			$result = $doc->saveXML($doc);
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
		return $result;
	}
	private function createIndexFileEB($item, $documentType) {
		$result = false;
		try {
			$this->applogger->info("LegalStorage.createIndexFileEB()");
			$doc = new DomDocument("1.0", "ISO-8859-1");
			$doc->version  = "1.0";
			$doc->encoding = "UTF-8";
			$root = $doc->createElement("legaldocIndex");
			$root->setAttribute("documentClass", "ae_fata");
			$root->setAttribute("label", "Fatture emesse");
			$dateArray = explode("-", substr($item["date"], 0, 10));
			$date = $doc->createElement("field", $dateArray[2] . "-" . $dateArray[1] . "-" . $dateArray[0]);
			$date->setAttribute("name", "__data_documento_dt");
			$root->appendChild($date);
			$beginDate = $doc->createElement("field", "01-01-" . $item["year"]);
			$beginDate->setAttribute("name", "__data_inizio_numerazione_dt");
			$root->appendChild($beginDate);
			$fiscalYear = $doc->createElement("field", $item["year"]);
			$fiscalYear->setAttribute("name", "__anno_fiscale_i");
			$root->appendChild($fiscalYear);
			if ($documentType == self::$ELECTRONIC_BILL_DOCUMENT) {
				$description = "Fattura elettronica " . $item["number"];
			} else if ($documentType == self::$CREDIT_NOTE_DOCUMENT) {
				$description = "Nota di credito " . $item["number"];
			}
			$series = $doc->createElement("field", $description);
			$series->setAttribute("name", "__serie_s");
			$root->appendChild($series);
			$documentNumber = $doc->createElement("field", $item["number"]);
			$documentNumber->setAttribute("name", "__numero_documento_l");
			$root->appendChild($documentNumber);
			$fiscalCode = $doc->createElement("field", $item["paFiscalCode"]);
			$fiscalCode->setAttribute("name", "codice_fiscale_s");
			$root->appendChild($fiscalCode);
			$vatNumber = $doc->createElement("field", $item["paFiscalCode"]);
			$vatNumber->setAttribute("name", "partita_iva_s");
			$root->appendChild($vatNumber);
			$name = $doc->createElement("field", $item["paName"]);
			$name->setAttribute("name", "denominazione_s");
			$root->appendChild($name);
			$totalAmount = $doc->createElement("field", $item["totalAmount"]);
			$totalAmount->setAttribute("name", "totale_importo_d");
			$root->appendChild($totalAmount);
			$doc->appendChild($root);
// 			$result = $doc->save(self::$LEGAL_STORAGE_DIR . $item["id"] . self::$INDEX_FILE);
			$result = $doc->saveXML($doc);
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
		return $result;
	}
	private function createParamFile($indexHash, $dataPath, $fileName, $cSPath, $documentType) {
		$result = false;
		try {
			$this->applogger->info("LegalStorage.createParametersFile()");
			$doc = new DomDocument("1.0", "ISO-8859-1");
			$doc->version  = "1.0";
			$doc->encoding = "UTF-8";
			$root = $doc->createElement("parameters");
			$root->appendChild($doc->createElement("policy_id", $this->config["policyId"]));
			if (!empty($indexHash) && !empty($dataPath) && !empty($fileName)) {
				$indexFile = $doc->createElement("index_file");
				$indexFile->appendChild($doc->createElement("index_name", self::$INDEX_FILE));
				$indexFile->appendChild($doc->createElement("index_hash", $indexHash));
				$indexFile->appendChild($doc->createElement("index_mimetype", "text/xml;1.0"));
				$root->appendChild($indexFile);
				$dataFile = $doc->createElement("data_file");
				/** **/
				$fileName = str_replace("(", "_", $fileName);
				$fileName = str_replace(")", "_", $fileName);
				/** **/
				$dataFile->appendChild($doc->createElement("data_name", $fileName));
				$dataHash = hash_file("sha256", $dataPath);
				$dataFile->appendChild($doc->createElement("data_hash", $dataHash));
				if ($documentType == self::$ELECTRONIC_BILL_DOCUMENT || $documentType == self::$CREDIT_NOTE_DOCUMENT) {
					$fileMime = "application/pkcs7;NA|text/xml;1.0";
				} else if ($documentType == self::$PDF_DOCUMENT) {
					$finfo = finfo_open(FILEINFO_MIME_TYPE);
					$fileMime = finfo_file($finfo, $dataPath);
					$fileMime .= ";NA";
					finfo_close($finfo);
				}
				$dataFile->appendChild($doc->createElement("data_mimetype", $fileMime));
// 				$finfo = finfo_open(FILEINFO_MIME_TYPE);
// 				$fileMime = finfo_file($finfo, $dataPath);
// 				finfo_close($finfo);
// 				$dataFile->appendChild($doc->createElement("data_mimetype", $fileMime . ";NA"));
				$root->appendChild($dataFile);
			}
			$root->appendChild($doc->createElement("path", $cSPath));
			$doc->appendChild($root);
// 			$result = $doc->save(self::$LEGAL_STORAGE_DIR . $indexHash .self::$PARAM_FILE);
			$result = $doc->saveXML($doc);
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
		return $result;
	}
	public function delete($s3, $item) {
		$result = array(
			"error" => true,
			"data" => array()
		);
		try {
			$this->applogger->info("LegalStorage.delete()");
			if (!empty($item["id"])) {
				$result = $this->login();
				if (!empty($this->ldSessionId)) {
					$result["error"] = true;
					$paramFile = $this->createParamFile(null, null, null, $item["legalStoragePath"], self::$PDF_DOCUMENT);
					$tmpParamFileDir = "/tmp/" . $this->db->getUid() . $this->db->getUid();
					$tmpParamFile = tempnam($tmpParamFileDir, self::$PARAM_FILE);
					$handleParamFile = fopen($tmpParamFile, "w");
					fwrite($handleParamFile, $paramFile);
					fclose($handleParamFile);
					$filePath = $this->getResponsePath($item["id"] . $item["suffix"]);
					$serviceURL = $this->config["serviceUrl"] . $this->config["bucketId"] . "/document/" . $this->getToken($s3, $item["id"], $filePath, self::$RESPONSE_PREFIX);
					$curl = curl_init($serviceURL);
					curl_setopt($curl, CURLOPT_HTTPHEADER, array("ldsessionid: " . $this->ldSessionId, "Content-Type: multipart/form-data"));
					$postData = array(
						"PARAMFILE" => new CURLFile($tmpParamFile, "text/xml", self::$PARAM_FILE)
					);
					curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
					curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
					curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);
					$response = curl_exec($curl);
// 					$this->applogger->debug("RESPONSE " . $response);
					$iH = $this->insertHistory;
					$hUid = $iH($this->db, $this->applogger, $item["id"], $this->userId, self::$DELETE);
					if (!empty($response)) {
						if ($this->checkResponse($response)) {
							$response = $this->parseResponse($response);
							$result["data"] = $response;
						} else {
							$fileName = self::$RESPONSE_DELETE_PREFIX . ".xml.p7m";
							$s3->saveStringAsFile($this->config["awsBucket"], $filePath . $fileName, $response);
							$result["error"] = false;
							$result["data"] = array("code" => "OK");
							$uH = $this->updateHistory;
							$uH($this->db, $this->applogger, $hUid, $fileName, $this->getToken($s3, $item["id"], $filePath, self::$RESPONSE_DELETE_PREFIX), 0);
						}
					}
					unlink($tmpParamFile);
					curl_close($curl);
					$this->logout();
				}
			}
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
		return $result;
	}
	public function get($s3, $iId, $suffix) {
		try {
			$this->applogger->info("LegalStorage.get()");
			if (!empty($iId)) {
				$filePath = $this->getResponsePath($iId . $suffix);
				$token = $this->getToken($s3, $iId, $filePath, self::$RESPONSE_PREFIX);
				$this->login();
				if (!empty($this->ldSessionId)) {
					$serviceURL = $this->config["serviceUrl"] . $this->config["bucketId"] . "/document/" . $token;
					$curl = curl_init($serviceURL);
					curl_setopt($curl, CURLOPT_HTTPHEADER, array("ldsessionid: " . $this->ldSessionId,
					"Accept: application/x-zip-compressed"));
					curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
					$response = curl_exec($curl);
// 					$this->applogger->debug("RESPONSE " . $response);
					$iH = $this->insertHistory;
					$hUid = $iH($this->db, $this->applogger, $iId, $this->userId, self::$GET);
					curl_close($curl);
					$this->logout();
					if (!empty($response)) {
						if ($this->checkResponse($response)) {
							$response = $this->parseResponse($response);
						} else {
							$uH = $this->updateHistory;
							$uH($this->db, $this->applogger, $hUid, null, null, 0);
							$finfo = new finfo(FILEINFO_MIME_TYPE);
							$mime = $finfo->buffer($response);
							header("Cache-Control: no-cache");
							header_remove("Pragma");
							header("Content-Description: File Transfer");
							header("Content-Type: application/zip");
							header("Content-Disposition: attachment; filename=Documento_in_conservazione.zip");
							header("Content-Transfer-Encoding: binary");
							header("Content-Length: " . strlen($response));
							echo $response;
						}
					}
				}
			}
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
	}
	private function getCSPath($uUid) {
		$path = null;
		try {
			$path = "/" . $this->customer["uniqueid"] . "/" . $uUid;
			$this->applogger->info("PATH DI CONSERVAZIONE " . $path);
		} catch (Exception $e) {
			throw $e;
		}
		return $path;
	}
	private function getLastFileName($s3, $dir, $prefix) {
		$lastFileName = null;
		try {
			$ext = ".xml.p7m";
			if ($prefix == self::$RESPONSE_DELETE_PREFIX) {
				$lastFileName = self::$RESPONSE_DELETE_PREFIX . $ext;
			} else {
				$counter = 1;
				$fileName = $prefix . "_" . $counter . $ext;
				$lastFileName = $fileName;
				$counter++;
				while ($s3->doesObjectExist($this->config["awsBucket"], $dir . $fileName)) {
					$lastFileName = $fileName;
					$fileName = $prefix . "_" . $counter . $ext;
					$counter++;
				}
			}
		} catch (Exception $e) {
			throw $e;
		}
		$this->applogger->info("GET RESPONSE FILE NAME " . $lastFileName);
		return $lastFileName;
	}
	private function getResponsePath($iId) {
		$path = null;
		try {
			$path = $this->customer["subdomain_name"] . "/" . $iId . "/";
		} catch (Exception $e) {
			throw $e;
		}
		return $path;
	}
	private function getToken($s3, $iId, $filePath, $prefix) {
		$token = null;
		try {
			$lastFileName = $this->getLastFileName($s3, $filePath, $prefix);
			$this->applogger->info("LETTURA FILE " . $filePath . $lastFileName);
			$file = $s3->getBody($this->config["awsBucket"], $filePath . $lastFileName);
			$tmpFileDir = "/tmp/" . $this->db->getUid() . $this->db->getUid();
			$tmpFile = tempnam($tmpFileDir, $iId);
			$handle = fopen($tmpFile, "w");
			fwrite($handle, $file);
			fclose($handle);
			$response = file_get_contents($tmpFile);
			$begin = '<additionalInfo key="token">';
			$beginIndex = strpos($response, $begin);
			$beginIndex = $beginIndex + strlen($begin);
			$endIndex = strpos($response, "</additionalInfo>");
			$token = substr($response, $beginIndex, $endIndex - $beginIndex);
			$this->applogger->info("TOKEN " . $token);
			unlink($tmpFile);
		} catch (Exception $e) {
			if (!empty($tmpFile)) {
				unlink($tmpFile);
			}
			throw $e;
		}
		return $token;
	}
	private function login() {
		$result = array(
			"error" => true,
			"data" => array()
		);
		try {
			$this->ldSessionId = null;
			$this->applogger->info("LegalStorage.login()");
			$serviceURL = $this->config["serviceUrl"] . "session";
			$curl = curl_init($serviceURL);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array("Content-Type: application/x-www-form-urlencoded"));
			$postData = array("userid" => $this->config["username"], "password" => $this->config["password"]);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_POST, true);
			curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($postData));
			$response = curl_exec($curl);
			$this->applogger->info("RESPONSE " . $response);
			if (!empty($response)) {
				$response = $this->parseResponse($response);
				if ($response["code"] == "OK") {
					$result["error"] = false;
					$this->ldSessionId = $response["LDSessionId"];
				}
				$result["data"] = $response;
			}
			curl_close($curl);
			$this->applogger->info("LdSessionId " . $this->ldSessionId);
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
		return $result;
	}
	private function logout() {
		try {
			$this->applogger->info("LegalStorage.logout()");
			$serviceURL = $this->config["serviceUrl"] . "session";
			$curl = curl_init($serviceURL);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array("ldsessionid: " . $this->ldSessionId, "Content-Type: application/x-www-form-urlencoded"));
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
			$response = curl_exec($curl);
			$this->applogger->info("RESPONSE " . $response);
			$this->ldSessionId = null;
			curl_close($curl);
			$this->applogger->info("LdSessionId " . $this->ldSessionId);
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
	}
	private function parseResponse($response) {
		$result = array();
		try {
			if (!empty($response)) {
				$doc = new DOMDocument;
				$doc->loadXML($response);
				$root = $doc->documentElement;
				if (!empty($root)) {
					foreach($root->childNodes as $child) {
						$result[$child->nodeName] = $child->nodeValue;
					}
				}
			}
		} catch (Exception $e) {
			throw $e;
		}
		return $result;
	}
	public function replace($s3, $item, $logout) {
		return $this->storageCORE($s3, $item, $logout, false, self::$PDF_DOCUMENT, null);
	}
	public function storage($s3, $item, $logout, $uUid) {
		return $this->storageCORE($s3, $item, $logout, true, self::$PDF_DOCUMENT, $uUid);
	}
	public function storageEB($s3, $item, $logout, $uUid) {
		return $this->storageCORE($s3, $item, $logout, true, self::$ELECTRONIC_BILL_DOCUMENT, $uUid);
	}
	public function storageCN($s3, $item, $logout, $uUid) {
		return $this->storageCORE($s3, $item, $logout, true, self::$CREDIT_NOTE_DOCUMENT, $uUid);
	}
	private function storageCORE($s3, $item, $logout, $storage, $documentType, $uUid) {
		$result = array(
			"error" => true,
			"data" => array()
		);
		try {
			if ($storage) {
				$this->applogger->info("LegalStorage.storage()");
			} else {
				$this->applogger->info("LegalStorage.replace)");
			}
			if (!empty($item)) {
				$result = $this->login();
				if (!empty($this->ldSessionId)) {
					$result["error"] = true;
					if ($documentType == self::$ELECTRONIC_BILL_DOCUMENT || $documentType == self::$CREDIT_NOTE_DOCUMENT) {
						$indexFile = $this->createIndexFileEB($item, $documentType);
					} else if ($documentType == self::$PDF_DOCUMENT) {
						$indexFile = $this->createIndexFile();
					}
					if (!empty($indexFile)) {
						$tmpIndexFileDir = "/tmp/" . $this->db->getUid() . $this->db->getUid();
						$tmpIndexFile = tempnam($tmpIndexFileDir, self::$INDEX_FILE);
						$handleIndexFile = fopen($tmpIndexFile, "w");
						fwrite($handleIndexFile, $indexFile);
						fclose($handleIndexFile);
						$indexHash = hash("sha256", $indexFile);
						if ($storage) {
							$cSPath = $this->getCSPath($uUid);
						} else {
							$cSPath = $item["legalStoragePath"];
						}
						$paramFile = $this->createParamFile($indexHash, $item["directory"], $item["nomefile"], $cSPath, $documentType);
						$tmpParamFileDir = "/tmp/" . $this->db->getUid() . $this->db->getUid();
						$tmpParamFile = tempnam($tmpParamFileDir, self::$PARAM_FILE);
						$handleParamFile = fopen($tmpParamFile, "w");
						fwrite($handleParamFile, $paramFile);
						fclose($handleParamFile);
						$serviceURL = $this->config["serviceUrl"] . $this->config["bucketId"] . "/document";
						$filePath = $this->getResponsePath($item["id"] . $item["suffix"]);
						if (!$storage) {
							$token = $this->getToken($s3, $item["id"], $filePath, self::$RESPONSE_PREFIX);
							$serviceURL .= "/" . $token;
						}
						$documentDIR = $item["directory"];
						$curl = curl_init($serviceURL);
						curl_setopt($curl, CURLOPT_HTTPHEADER, array("ldsessionid: " . $this->ldSessionId, "Content-Type: multipart/form-data"));
						$finfo = finfo_open(FILEINFO_MIME_TYPE);
						$docMime = finfo_file($finfo, $documentDIR);
						finfo_close($finfo);
						$postData = array(
							"PARAMFILE" => new CURLFile($tmpParamFile, "text/xml", self::$PARAM_FILE),
							"INDEXFILE" => new CURLFile($tmpIndexFile, "text/xml", self::$INDEX_FILE),
							"DATAFILE" => new CURLFile($documentDIR, $docMime, $item["nomefile"])
						);
						curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
						if ($storage) {
							curl_setopt($curl, CURLOPT_POST, true);
						} else {
							curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
						}
						curl_setopt($curl, CURLOPT_POSTFIELDS, $postData);
						$response = curl_exec($curl);
//						$this->applogger->debug("RESPONSE " . $response);
						$action = self::$STORAGE;
						if (!$storage) {
							$action = self::$REPLACE;
						}
						$iH = $this->insertHistory;
						$hUid = $iH($this->db, $this->applogger, $item["id"], $this->userId, $action);
						if (!empty($response)) {
							if ($this->checkResponse($response)) {
								$response = $this->parseResponse($response);
								$result["data"] = $response;
							} else {
								$fileName = $this->createFileName($s3, $filePath, self::$RESPONSE_PREFIX);
								$s3->saveStringAsFile($this->config["awsBucket"], $filePath . $fileName, $response);
								$result["error"] = false;
								$result["data"] = array("code" => "OK");
								$uH = $this->updateHistory;
								$uH($this->db, $this->applogger, $hUid, $fileName, $this->getToken($s3, $item["id"], $filePath, self::$RESPONSE_PREFIX), 0);
 								$itemUpdate = array("legal_storage" => self::$STORED);
 								if ($storage) {
 									$itemUpdate["legal_storage_path"] = $cSPath;
 								}
								$this->db->update(
									$item["tableName"],
 									$itemUpdate,
 									array("id = ?" => $item["id"])
 								);
							}
						}
						unlink($tmpIndexFile);
						unlink($tmpParamFile);
						curl_close($curl);
					}
					if ($logout) {
						$this->logout();
					}
				}
			}
			$this->applogger->info("END");
		} catch (Exception $e) {
			throw $e;
		}
		return $result;
	}
}
?>