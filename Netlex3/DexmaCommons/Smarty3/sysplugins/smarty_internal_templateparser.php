<?php

/**
 * Smarty Internal Plugin Templateparser
 * This is the template parser.
 * It is generated from the internal.templateparser.y file
 *
 * @package    Smarty
 * @subpackage Compiler
 * <AUTHOR> Tews
 */
class TP_yyToken implements ArrayAccess
{
    public $string = '';
    public $metadata = array();

    public function __construct($s, $m = array())
    {
        if ($s instanceof TP_yyToken) {
            $this->string = $s->string;
            $this->metadata = $s->metadata;
        } else {
            $this->string = (string) $s;
            if ($m instanceof TP_yyToken) {
                $this->metadata = $m->metadata;
            } elseif (is_array($m)) {
                $this->metadata = $m;
            }
        }
    }

    public function __toString()
    {
        return $this->_string;
    }

    public function offsetExists($offset)
    {
        return isset($this->metadata[$offset]);
    }

    public function offsetGet($offset)
    {
        return $this->metadata[$offset];
    }

    public function offsetSet($offset, $value)
    {
        if ($offset === null) {
            if (isset($value[0])) {
                $x = ($value instanceof TP_yyToken) ?
                    $value->metadata : $value;
                $this->metadata = array_merge($this->metadata, $x);

                return;
            }
            $offset = count($this->metadata);
        }
        if ($value === null) {
            return;
        }
        if ($value instanceof TP_yyToken) {
            if ($value->metadata) {
                $this->metadata[$offset] = $value->metadata;
            }
        } elseif ($value) {
            $this->metadata[$offset] = $value;
        }
    }

    public function offsetUnset($offset)
    {
        unset($this->metadata[$offset]);
    }
}

class TP_yyStackEntry
{
    public $stateno;       /* The state-number */
    public $major;         /* The major token value.  This is the code
                     ** number for the token at this stack level */
    public $minor; /* The user-supplied minor token value.  This
                     ** is the value of the token  */
}

;

#line 13 "smarty_internal_templateparser.y"
class Smarty_Internal_Templateparser#line 80 "smarty_internal_templateparser.php"
{
    #line 15 "smarty_internal_templateparser.y"

    const Err1 = "Security error: Call to private object member not allowed";
    const Err2 = "Security error: Call to dynamic object member not allowed";
    const Err3 = "PHP in template not allowed. Use SmartyBC to enable it";
    // states whether the parse was successful or not
    public $successful = true;
    public $retvalue = 0;
    public static $prefix_number = 0;
    private $_string;
    public $yymajor;
    public $last_index;
    public $last_variable;
    public $root_buffer;
    public $current_buffer;
    private $lex;
    private $internalError = false;
    private $strip = false;

    function __construct($lex, $compiler)
    {
        $this->lex = $lex;
        $this->compiler = $compiler;
        $this->smarty = $this->compiler->smarty;
        $this->template = $this->compiler->template;
        $this->compiler->has_variable_string = false;
        $this->compiler->prefix_code = array();
        $this->block_nesting_level = 0;
        if ($this->security = isset($this->smarty->security_policy)) {
            $this->php_handling = $this->smarty->security_policy->php_handling;
        } else {
            $this->php_handling = $this->smarty->php_handling;
        }
        $this->is_xml = false;
        $this->asp_tags = (ini_get('asp_tags') != '0');
        $this->current_buffer = $this->root_buffer = new _smarty_template_buffer($this);
    }

    public function compileVariable($variable)
    {
        if (strpos($variable, '(') == 0) {
            // not a variable variable
            $var = trim($variable, '\'');
            $this->compiler->tag_nocache = $this->compiler->tag_nocache | $this->template->getVariable($var, null, true, false)->nocache;
            $this->template->properties['variables'][$var] = $this->compiler->tag_nocache | $this->compiler->nocache;
        }
        //       return '(isset($_smarty_tpl->tpl_vars['. $variable .'])?$_smarty_tpl->tpl_vars['. $variable .']->value:$_smarty_tpl->getVariable('. $variable .')->value)';
        return '$_smarty_tpl->tpl_vars[' . $variable . ']->value';
    }

    #line 130 "smarty_internal_templateparser.php"

    const TP_VERT = 1;
    const TP_COLON = 2;
    const TP_RDEL = 3;
    const TP_COMMENT = 4;
    const TP_PHPSTARTTAG = 5;
    const TP_PHPENDTAG = 6;
    const TP_PHPENDSCRIPT = 7;
    const TP_ASPSTARTTAG = 8;
    const TP_ASPENDTAG = 9;
    const TP_XMLTAG = 10;
    const TP_TEXT = 11;
    const TP_STRIPON = 12;
    const TP_STRIPOFF = 13;
    const TP_BLOCKSOURCE = 14;
    const TP_LITERALSTART = 15;
    const TP_LITERALEND = 16;
    const TP_LITERAL = 17;
    const TP_LDEL = 18;
    const TP_DOLLAR = 19;
    const TP_ID = 20;
    const TP_EQUAL = 21;
    const TP_PTR = 22;
    const TP_LDELIF = 23;
    const TP_LDELFOR = 24;
    const TP_SEMICOLON = 25;
    const TP_INCDEC = 26;
    const TP_TO = 27;
    const TP_STEP = 28;
    const TP_LDELFOREACH = 29;
    const TP_SPACE = 30;
    const TP_AS = 31;
    const TP_APTR = 32;
    const TP_LDELSETFILTER = 33;
    const TP_SMARTYBLOCKCHILDPARENT = 34;
    const TP_LDELSLASH = 35;
    const TP_ATTR = 36;
    const TP_INTEGER = 37;
    const TP_COMMA = 38;
    const TP_OPENP = 39;
    const TP_CLOSEP = 40;
    const TP_MATH = 41;
    const TP_UNIMATH = 42;
    const TP_ANDSYM = 43;
    const TP_ISIN = 44;
    const TP_ISDIVBY = 45;
    const TP_ISNOTDIVBY = 46;
    const TP_ISEVEN = 47;
    const TP_ISNOTEVEN = 48;
    const TP_ISEVENBY = 49;
    const TP_ISNOTEVENBY = 50;
    const TP_ISODD = 51;
    const TP_ISNOTODD = 52;
    const TP_ISODDBY = 53;
    const TP_ISNOTODDBY = 54;
    const TP_INSTANCEOF = 55;
    const TP_QMARK = 56;
    const TP_NOT = 57;
    const TP_TYPECAST = 58;
    const TP_HEX = 59;
    const TP_DOT = 60;
    const TP_SINGLEQUOTESTRING = 61;
    const TP_DOUBLECOLON = 62;
    const TP_AT = 63;
    const TP_HATCH = 64;
    const TP_OPENB = 65;
    const TP_CLOSEB = 66;
    const TP_EQUALS = 67;
    const TP_NOTEQUALS = 68;
    const TP_GREATERTHAN = 69;
    const TP_LESSTHAN = 70;
    const TP_GREATEREQUAL = 71;
    const TP_LESSEQUAL = 72;
    const TP_IDENTITY = 73;
    const TP_NONEIDENTITY = 74;
    const TP_MOD = 75;
    const TP_LAND = 76;
    const TP_LOR = 77;
    const TP_LXOR = 78;
    const TP_QUOTE = 79;
    const TP_BACKTICK = 80;
    const TP_DOLLARID = 81;
    const YY_NO_ACTION = 560;
    const YY_ACCEPT_ACTION = 559;
    const YY_ERROR_ACTION = 558;

    const YY_SZ_ACTTAB = 2541;
    static public $yy_action = array(
        /*     0 */
        225, 35, 312, 333, 198, 272, 273, 275, 283, 292,
        /*    10 */
        293, 294, 295, 287, 288, 267, 190, 43, 19, 8,
        /*    20 */
        204, 14, 212, 299, 2, 108, 225, 9, 424, 41,
        /*    30 */
        139, 208, 225, 41, 250, 32, 228, 13, 159, 32,
        /*    40 */
        51, 52, 50, 44, 11, 12, 298, 300, 21, 23,
        /*    50 */
        303, 302, 25, 17, 225, 424, 234, 225, 225, 381,
        /*    60 */
        421, 424, 45, 38, 145, 357, 313, 323, 322, 324,
        /*    70 */
        325, 326, 320, 315, 314, 316, 317, 319, 127, 41,
        /*    80 */
        46, 42, 338, 41, 168, 32, 41, 421, 14, 32,
        /*    90 */
        299, 34, 32, 421, 51, 52, 50, 44, 11, 12,
        /*   100 */
        298, 300, 21, 23, 303, 302, 25, 17, 225, 104,
        /*   110 */
        185, 46, 46, 559, 95, 279, 242, 271, 3, 321,
        /*   120 */
        313, 323, 322, 324, 325, 326, 320, 315, 314, 316,
        /*   130 */
        317, 319, 14, 241, 299, 34, 208, 41, 225, 14,
        /*   140 */
        418, 299, 28, 32, 179, 7, 356, 285, 51, 52,
        /*   150 */
        50, 44, 11, 12, 298, 300, 21, 23, 303, 302,
        /*   160 */
        25, 17, 225, 225, 274, 424, 240, 41, 134, 190,
        /*   170 */
        332, 343, 340, 32, 313, 323, 322, 324, 325, 326,
        /*   180 */
        320, 315, 314, 316, 317, 319, 37, 122, 182, 31,
        /*   190 */
        202, 225, 424, 352, 225, 14, 7, 299, 424, 225,
        /*   200 */
        150, 386, 51, 52, 50, 44, 11, 12, 298, 300,
        /*   210 */
        21, 23, 303, 302, 25, 17, 225, 46, 349, 134,
        /*   220 */
        41, 278, 242, 271, 7, 29, 32, 341, 313, 323,
        /*   230 */
        322, 324, 325, 326, 320, 315, 314, 316, 317, 319,
        /*   240 */
        226, 227, 197, 304, 103, 184, 232, 134, 46, 14,
        /*   250 */
        35, 299, 265, 46, 321, 18, 51, 52, 50, 44,
        /*   260 */
        11, 12, 298, 300, 21, 23, 303, 302, 25, 17,
        /*   270 */
        225, 208, 264, 259, 258, 218, 6, 109, 345, 35,
        /*   280 */
        193, 263, 313, 323, 322, 324, 325, 326, 320, 315,
        /*   290 */
        314, 316, 317, 319, 241, 192, 304, 107, 102, 175,
        /*   300 */
        269, 201, 191, 304, 203, 142, 253, 208, 321, 335,
        /*   310 */
        51, 52, 50, 44, 11, 12, 298, 300, 21, 23,
        /*   320 */
        303, 302, 25, 17, 225, 208, 268, 196, 208, 305,
        /*   330 */
        485, 208, 230, 200, 308, 485, 313, 323, 322, 324,
        /*   340 */
        325, 326, 320, 315, 314, 316, 317, 319, 30, 159,
        /*   350 */
        105, 20, 233, 161, 186, 305, 301, 274, 256, 247,
        /*   360 */
        208, 225, 321, 36, 51, 52, 50, 44, 11, 12,
        /*   370 */
        298, 300, 21, 23, 303, 302, 25, 17, 225, 204,
        /*   380 */
        207, 14, 254, 299, 131, 259, 249, 225, 158, 127,
        /*   390 */
        313, 323, 322, 324, 325, 326, 320, 315, 314, 316,
        /*   400 */
        317, 319, 237, 262, 130, 225, 45, 183, 187, 14,
        /*   410 */
        169, 223, 301, 342, 274, 334, 321, 321, 51, 52,
        /*   420 */
        50, 44, 11, 12, 298, 300, 21, 23, 303, 302,
        /*   430 */
        25, 17, 225, 204, 204, 14, 6, 244, 141, 259,
        /*   440 */
        291, 46, 98, 194, 313, 323, 322, 324, 325, 326,
        /*   450 */
        320, 315, 314, 316, 317, 319, 132, 262, 227, 166,
        /*   460 */
        163, 189, 14, 284, 231, 26, 237, 27, 321, 321,
        /*   470 */
        321, 32, 51, 52, 50, 44, 11, 12, 298, 300,
        /*   480 */
        21, 23, 303, 302, 25, 17, 225, 204, 14, 276,
        /*   490 */
        245, 206, 22, 4, 144, 328, 150, 120, 313, 323,
        /*   500 */
        322, 324, 325, 326, 320, 315, 314, 316, 317, 319,
        /*   510 */
        195, 348, 262, 176, 171, 39, 30, 199, 143, 180,
        /*   520 */
        148, 281, 321, 260, 159, 230, 51, 52, 50, 44,
        /*   530 */
        11, 12, 298, 300, 21, 23, 303, 302, 25, 17,
        /*   540 */
        225, 296, 205, 129, 346, 289, 124, 329, 125, 339,
        /*   550 */
        97, 119, 313, 323, 322, 324, 325, 326, 320, 315,
        /*   560 */
        314, 316, 317, 319, 127, 262, 262, 239, 277, 282,
        /*   570 */
        307, 94, 266, 351, 269, 170, 159, 181, 172, 106,
        /*   580 */
        51, 52, 50, 44, 11, 12, 298, 300, 21, 23,
        /*   590 */
        303, 302, 25, 17, 225, 330, 270, 301, 15, 115,
        /*   600 */
        331, 310, 326, 255, 140, 126, 313, 323, 322, 324,
        /*   610 */
        325, 326, 320, 315, 314, 316, 317, 319, 301, 326,
        /*   620 */
        262, 326, 326, 326, 326, 326, 326, 326, 326, 326,
        /*   630 */
        326, 326, 326, 243, 51, 52, 50, 44, 11, 12,
        /*   640 */
        298, 300, 21, 23, 303, 302, 25, 17, 225, 40,
        /*   650 */
        326, 326, 326, 326, 326, 326, 326, 113, 99, 100,
        /*   660 */
        313, 323, 322, 324, 325, 326, 320, 315, 314, 316,
        /*   670 */
        317, 319, 262, 262, 262, 326, 326, 326, 326, 326,
        /*   680 */
        326, 326, 326, 326, 326, 326, 326, 326, 51, 52,
        /*   690 */
        50, 44, 11, 12, 298, 300, 21, 23, 303, 302,
        /*   700 */
        25, 17, 225, 326, 326, 326, 326, 326, 326, 326,
        /*   710 */
        326, 114, 326, 326, 313, 323, 322, 324, 325, 326,
        /*   720 */
        320, 315, 314, 316, 317, 319, 262, 326, 326, 326,
        /*   730 */
        326, 326, 326, 326, 326, 326, 326, 326, 326, 326,
        /*   740 */
        326, 286, 51, 52, 50, 44, 11, 12, 298, 300,
        /*   750 */
        21, 23, 303, 302, 25, 17, 225, 326, 326, 326,
        /*   760 */
        326, 326, 326, 326, 326, 326, 326, 326, 313, 323,
        /*   770 */
        322, 324, 325, 326, 320, 315, 314, 316, 317, 319,
        /*   780 */
        326, 326, 326, 326, 326, 326, 326, 326, 326, 326,
        /*   790 */
        326, 326, 326, 326, 326, 326, 51, 52, 50, 44,
        /*   800 */
        11, 12, 298, 300, 21, 23, 303, 302, 25, 17,
        /*   810 */
        326, 326, 326, 326, 326, 326, 326, 326, 326, 326,
        /*   820 */
        159, 326, 313, 323, 322, 324, 325, 326, 320, 315,
        /*   830 */
        314, 316, 317, 319, 326, 326, 326, 326, 51, 52,
        /*   840 */
        50, 44, 11, 12, 298, 300, 21, 23, 303, 302,
        /*   850 */
        25, 17, 326, 326, 326, 326, 326, 326, 212, 326,
        /*   860 */
        326, 326, 326, 9, 313, 323, 322, 324, 325, 326,
        /*   870 */
        320, 315, 314, 316, 317, 319, 326, 326, 326, 326,
        /*   880 */
        326, 326, 326, 8, 138, 211, 326, 326, 2, 108,
        /*   890 */
        326, 235, 326, 326, 139, 157, 165, 326, 250, 128,
        /*   900 */
        228, 326, 246, 326, 24, 321, 326, 48, 261, 326,
        /*   910 */
        326, 251, 336, 353, 326, 311, 326, 301, 174, 173,
        /*   920 */
        326, 326, 49, 47, 280, 238, 297, 321, 321, 105,
        /*   930 */
        1, 337, 326, 147, 326, 326, 326, 326, 326, 301,
        /*   940 */
        301, 8, 123, 92, 96, 257, 2, 108, 326, 311,
        /*   950 */
        326, 326, 139, 326, 326, 235, 250, 309, 228, 146,
        /*   960 */
        246, 326, 24, 128, 162, 48, 326, 326, 326, 326,
        /*   970 */
        235, 326, 350, 321, 155, 251, 336, 353, 128, 311,
        /*   980 */
        49, 47, 280, 238, 297, 301, 326, 105, 1, 326,
        /*   990 */
        251, 336, 353, 326, 311, 14, 326, 299, 326, 8,
        /*  1000 */
        138, 224, 96, 326, 2, 108, 326, 41, 326, 252,
        /*  1010 */
        139, 235, 326, 32, 250, 153, 228, 326, 246, 128,
        /*  1020 */
        24, 326, 326, 48, 326, 326, 326, 326, 326, 326,
        /*  1030 */
        326, 251, 336, 353, 326, 311, 326, 326, 49, 47,
        /*  1040 */
        280, 238, 297, 326, 326, 105, 1, 326, 326, 326,
        /*  1050 */
        326, 326, 14, 326, 299, 326, 326, 8, 142, 224,
        /*  1060 */
        96, 326, 2, 108, 41, 235, 248, 326, 139, 154,
        /*  1070 */
        32, 235, 250, 128, 228, 156, 246, 326, 33, 128,
        /*  1080 */
        326, 48, 326, 326, 326, 251, 336, 353, 326, 311,
        /*  1090 */
        326, 251, 336, 353, 326, 311, 49, 47, 280, 238,
        /*  1100 */
        297, 326, 326, 105, 1, 326, 326, 326, 326, 326,
        /*  1110 */
        326, 326, 326, 326, 326, 8, 138, 213, 96, 326,
        /*  1120 */
        2, 108, 326, 326, 326, 326, 139, 235, 326, 326,
        /*  1130 */
        250, 149, 228, 326, 246, 128, 24, 326, 326, 48,
        /*  1140 */
        326, 326, 326, 326, 326, 326, 326, 251, 336, 353,
        /*  1150 */
        326, 311, 326, 326, 49, 47, 280, 238, 297, 326,
        /*  1160 */
        326, 105, 1, 326, 326, 326, 326, 326, 326, 326,
        /*  1170 */
        326, 326, 326, 8, 138, 210, 96, 326, 2, 108,
        /*  1180 */
        326, 326, 326, 326, 139, 235, 326, 326, 250, 151,
        /*  1190 */
        228, 326, 219, 128, 24, 326, 326, 48, 326, 326,
        /*  1200 */
        326, 326, 326, 326, 326, 251, 336, 353, 326, 311,
        /*  1210 */
        326, 326, 49, 47, 280, 238, 297, 326, 326, 105,
        /*  1220 */
        1, 326, 326, 326, 326, 326, 326, 326, 326, 326,
        /*  1230 */
        326, 8, 136, 224, 96, 326, 2, 108, 326, 326,
        /*  1240 */
        326, 326, 139, 235, 326, 326, 250, 152, 228, 326,
        /*  1250 */
        246, 128, 24, 326, 326, 48, 326, 326, 326, 326,
        /*  1260 */
        326, 326, 326, 251, 336, 353, 326, 311, 326, 326,
        /*  1270 */
        49, 47, 280, 238, 297, 326, 326, 105, 1, 326,
        /*  1280 */
        225, 326, 391, 326, 422, 326, 326, 326, 326, 8,
        /*  1290 */
        135, 224, 96, 326, 2, 108, 326, 326, 326, 326,
        /*  1300 */
        139, 236, 229, 326, 250, 326, 228, 326, 246, 41,
        /*  1310 */
        5, 422, 326, 48, 326, 32, 326, 422, 7, 326,
        /*  1320 */
        7, 326, 326, 326, 198, 178, 326, 326, 49, 47,
        /*  1330 */
        280, 238, 297, 326, 321, 105, 1, 43, 19, 326,
        /*  1340 */
        326, 134, 326, 134, 326, 326, 326, 8, 138, 209,
        /*  1350 */
        96, 208, 2, 108, 326, 326, 326, 326, 139, 326,
        /*  1360 */
        326, 326, 250, 326, 228, 326, 246, 326, 24, 198,
        /*  1370 */
        164, 48, 326, 326, 326, 326, 326, 326, 326, 321,
        /*  1380 */
        326, 326, 43, 19, 326, 326, 49, 47, 280, 238,
        /*  1390 */
        297, 326, 326, 105, 1, 326, 208, 326, 326, 326,
        /*  1400 */
        326, 326, 326, 326, 326, 8, 142, 224, 96, 326,
        /*  1410 */
        2, 108, 326, 326, 326, 326, 139, 326, 326, 326,
        /*  1420 */
        250, 326, 228, 326, 246, 326, 33, 198, 177, 48,
        /*  1430 */
        326, 326, 326, 326, 326, 326, 326, 321, 326, 326,
        /*  1440 */
        43, 19, 326, 326, 49, 47, 280, 238, 297, 326,
        /*  1450 */
        326, 105, 326, 326, 208, 326, 326, 326, 326, 326,
        /*  1460 */
        326, 326, 326, 8, 142, 222, 96, 326, 2, 108,
        /*  1470 */
        326, 326, 326, 326, 139, 326, 326, 326, 250, 326,
        /*  1480 */
        228, 326, 246, 326, 33, 326, 467, 48, 326, 326,
        /*  1490 */
        326, 326, 326, 326, 326, 326, 326, 326, 326, 326,
        /*  1500 */
        326, 326, 49, 47, 280, 238, 297, 326, 467, 105,
        /*  1510 */
        467, 467, 326, 467, 467, 326, 326, 326, 326, 467,
        /*  1520 */
        326, 467, 7, 467, 96, 326, 326, 326, 326, 326,
        /*  1530 */
        326, 326, 326, 326, 326, 235, 326, 326, 467, 121,
        /*  1540 */
        326, 326, 86, 128, 326, 134, 326, 326, 326, 467,
        /*  1550 */
        326, 290, 318, 326, 326, 251, 336, 353, 326, 311,
        /*  1560 */
        326, 326, 326, 467, 326, 326, 326, 235, 326, 216,
        /*  1570 */
        354, 133, 326, 326, 68, 117, 249, 326, 326, 326,
        /*  1580 */
        326, 326, 326, 290, 318, 326, 326, 251, 336, 353,
        /*  1590 */
        235, 311, 326, 326, 133, 326, 326, 76, 128, 326,
        /*  1600 */
        326, 326, 326, 326, 326, 326, 290, 318, 326, 235,
        /*  1610 */
        251, 336, 353, 133, 311, 326, 76, 128, 326, 326,
        /*  1620 */
        326, 221, 326, 326, 326, 290, 318, 326, 326, 251,
        /*  1630 */
        336, 353, 326, 311, 326, 326, 326, 326, 235, 326,
        /*  1640 */
        215, 326, 121, 326, 326, 86, 128, 326, 326, 326,
        /*  1650 */
        326, 326, 326, 326, 290, 318, 326, 326, 251, 336,
        /*  1660 */
        353, 235, 311, 326, 326, 133, 326, 326, 59, 117,
        /*  1670 */
        137, 326, 326, 355, 326, 326, 326, 290, 318, 326,
        /*  1680 */
        326, 251, 336, 353, 235, 311, 326, 326, 133, 326,
        /*  1690 */
        326, 76, 128, 326, 326, 326, 326, 326, 326, 326,
        /*  1700 */
        290, 318, 326, 235, 251, 336, 353, 110, 311, 326,
        /*  1710 */
        69, 128, 326, 326, 326, 217, 326, 326, 326, 290,
        /*  1720 */
        318, 326, 326, 251, 336, 353, 326, 311, 326, 235,
        /*  1730 */
        101, 160, 326, 133, 326, 326, 57, 128, 326, 326,
        /*  1740 */
        321, 326, 326, 43, 19, 290, 318, 326, 235, 251,
        /*  1750 */
        336, 353, 133, 311, 326, 73, 128, 208, 326, 326,
        /*  1760 */
        326, 326, 326, 326, 290, 318, 326, 326, 251, 336,
        /*  1770 */
        353, 235, 311, 326, 326, 133, 326, 326, 78, 128,
        /*  1780 */
        326, 326, 326, 326, 326, 326, 326, 290, 318, 326,
        /*  1790 */
        235, 251, 336, 353, 133, 311, 326, 70, 128, 326,
        /*  1800 */
        326, 326, 326, 326, 326, 326, 290, 318, 326, 326,
        /*  1810 */
        251, 336, 353, 326, 311, 326, 235, 198, 167, 326,
        /*  1820 */
        133, 326, 326, 64, 128, 326, 326, 321, 326, 326,
        /*  1830 */
        43, 19, 290, 318, 326, 235, 251, 336, 353, 133,
        /*  1840 */
        311, 326, 67, 128, 208, 326, 326, 326, 326, 326,
        /*  1850 */
        326, 290, 318, 326, 326, 251, 336, 353, 235, 311,
        /*  1860 */
        326, 326, 133, 326, 326, 88, 128, 326, 326, 326,
        /*  1870 */
        326, 326, 326, 326, 290, 318, 326, 235, 251, 336,
        /*  1880 */
        353, 133, 311, 326, 82, 128, 326, 326, 326, 326,
        /*  1890 */
        326, 326, 326, 290, 318, 326, 326, 251, 336, 353,
        /*  1900 */
        326, 311, 326, 235, 198, 188, 326, 133, 326, 326,
        /*  1910 */
        72, 128, 326, 326, 321, 326, 326, 43, 19, 290,
        /*  1920 */
        318, 326, 235, 251, 336, 353, 93, 311, 326, 55,
        /*  1930 */
        116, 208, 326, 326, 326, 326, 326, 326, 290, 318,
        /*  1940 */
        326, 326, 251, 336, 353, 235, 311, 326, 326, 112,
        /*  1950 */
        326, 326, 87, 128, 326, 326, 326, 326, 326, 326,
        /*  1960 */
        326, 290, 318, 326, 235, 251, 336, 353, 133, 311,
        /*  1970 */
        326, 77, 128, 326, 326, 326, 326, 326, 326, 326,
        /*  1980 */
        290, 318, 326, 326, 251, 336, 353, 326, 311, 326,
        /*  1990 */
        235, 326, 326, 326, 133, 326, 326, 89, 128, 326,
        /*  2000 */
        326, 326, 326, 326, 326, 326, 290, 318, 326, 235,
        /*  2010 */
        251, 336, 353, 133, 311, 326, 75, 128, 326, 326,
        /*  2020 */
        326, 326, 326, 326, 326, 290, 318, 326, 326, 251,
        /*  2030 */
        336, 353, 235, 311, 326, 326, 133, 326, 326, 91,
        /*  2040 */
        128, 326, 326, 326, 326, 326, 326, 326, 290, 318,
        /*  2050 */
        326, 235, 251, 336, 353, 133, 311, 326, 62, 128,
        /*  2060 */
        326, 326, 326, 326, 326, 326, 326, 290, 318, 326,
        /*  2070 */
        326, 251, 336, 353, 326, 311, 326, 235, 326, 326,
        /*  2080 */
        326, 93, 326, 326, 53, 116, 326, 326, 326, 326,
        /*  2090 */
        326, 326, 326, 290, 318, 326, 235, 220, 336, 353,
        /*  2100 */
        133, 311, 326, 63, 128, 326, 326, 326, 326, 326,
        /*  2110 */
        326, 326, 290, 318, 326, 326, 251, 336, 353, 235,
        /*  2120 */
        311, 326, 326, 133, 326, 326, 61, 128, 326, 326,
        /*  2130 */
        326, 326, 326, 326, 326, 290, 318, 326, 235, 214,
        /*  2140 */
        336, 353, 133, 311, 326, 71, 128, 326, 326, 326,
        /*  2150 */
        326, 326, 326, 326, 290, 318, 326, 326, 251, 336,
        /*  2160 */
        353, 326, 311, 326, 235, 326, 326, 326, 133, 326,
        /*  2170 */
        326, 85, 128, 326, 326, 326, 326, 326, 326, 326,
        /*  2180 */
        290, 318, 326, 235, 251, 336, 353, 133, 311, 326,
        /*  2190 */
        65, 128, 326, 326, 326, 326, 326, 326, 326, 290,
        /*  2200 */
        318, 326, 326, 251, 336, 353, 235, 311, 326, 326,
        /*  2210 */
        111, 326, 326, 74, 128, 326, 326, 326, 326, 326,
        /*  2220 */
        326, 326, 290, 318, 326, 235, 251, 336, 353, 118,
        /*  2230 */
        311, 326, 58, 128, 326, 326, 326, 326, 326, 326,
        /*  2240 */
        326, 290, 318, 326, 326, 251, 336, 353, 326, 311,
        /*  2250 */
        326, 235, 326, 326, 326, 133, 326, 326, 56, 128,
        /*  2260 */
        326, 326, 326, 326, 326, 326, 326, 290, 318, 326,
        /*  2270 */
        235, 251, 336, 353, 133, 311, 326, 79, 128, 326,
        /*  2280 */
        326, 326, 326, 326, 326, 326, 290, 318, 326, 326,
        /*  2290 */
        251, 336, 353, 235, 311, 326, 326, 133, 326, 326,
        /*  2300 */
        80, 128, 326, 326, 326, 326, 326, 326, 326, 290,
        /*  2310 */
        318, 326, 235, 251, 336, 353, 133, 311, 326, 84,
        /*  2320 */
        128, 326, 326, 326, 326, 326, 326, 326, 290, 318,
        /*  2330 */
        326, 326, 251, 336, 353, 326, 311, 326, 235, 326,
        /*  2340 */
        326, 326, 133, 326, 326, 66, 128, 326, 326, 326,
        /*  2350 */
        326, 326, 326, 326, 290, 318, 326, 344, 251, 336,
        /*  2360 */
        353, 326, 311, 326, 10, 326, 326, 326, 326, 2,
        /*  2370 */
        108, 344, 326, 326, 326, 139, 326, 326, 10, 250,
        /*  2380 */
        326, 228, 326, 2, 108, 326, 326, 326, 326, 139,
        /*  2390 */
        326, 326, 326, 250, 326, 228, 235, 326, 326, 326,
        /*  2400 */
        133, 326, 326, 60, 128, 326, 326, 326, 326, 326,
        /*  2410 */
        326, 326, 290, 318, 326, 326, 251, 336, 353, 326,
        /*  2420 */
        311, 326, 326, 326, 326, 306, 16, 347, 326, 235,
        /*  2430 */
        326, 326, 326, 133, 326, 326, 81, 128, 326, 327,
        /*  2440 */
        16, 347, 326, 326, 326, 290, 318, 326, 326, 251,
        /*  2450 */
        336, 353, 326, 311, 326, 326, 235, 326, 326, 326,
        /*  2460 */
        133, 326, 326, 68, 128, 326, 326, 326, 326, 326,
        /*  2470 */
        326, 326, 290, 318, 326, 326, 251, 336, 353, 326,
        /*  2480 */
        311, 326, 235, 326, 326, 326, 133, 326, 326, 54,
        /*  2490 */
        128, 326, 326, 326, 326, 326, 326, 326, 290, 318,
        /*  2500 */
        326, 326, 251, 336, 353, 235, 311, 326, 326, 133,
        /*  2510 */
        326, 326, 83, 128, 326, 326, 235, 326, 326, 326,
        /*  2520 */
        133, 290, 318, 90, 128, 251, 336, 353, 326, 311,
        /*  2530 */
        326, 326, 290, 318, 326, 326, 251, 336, 353, 326,
        /*  2540 */
        311,
    );
    static public $yy_lookahead = array(
        /*     0 */
        1, 38, 3, 40, 91, 4, 5, 6, 7, 8,
        /*    10 */
        9, 10, 11, 12, 13, 14, 15, 104, 105, 18,
        /*    20 */
        118, 18, 60, 20, 23, 24, 1, 65, 3, 30,
        /*    30 */
        29, 118, 1, 30, 33, 36, 35, 21, 22, 36,
        /*    40 */
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50,
        /*    50 */
        51, 52, 53, 54, 1, 30, 31, 1, 1, 3,
        /*    60 */
        3, 36, 2, 18, 19, 20, 67, 68, 69, 70,
        /*    70 */
        71, 72, 73, 74, 75, 76, 77, 78, 62, 30,
        /*    80 */
        55, 28, 37, 30, 111, 36, 30, 30, 18, 36,
        /*    90 */
        20, 21, 36, 36, 41, 42, 43, 44, 45, 46,
        /*   100 */
        47, 48, 49, 50, 51, 52, 53, 54, 1, 91,
        /*   110 */
        92, 55, 55, 83, 84, 85, 86, 87, 38, 101,
        /*   120 */
        67, 68, 69, 70, 71, 72, 73, 74, 75, 76,
        /*   130 */
        77, 78, 18, 63, 20, 21, 118, 30, 1, 18,
        /*   140 */
        3, 20, 21, 36, 111, 39, 66, 26, 41, 42,
        /*   150 */
        43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
        /*   160 */
        53, 54, 1, 1, 26, 3, 60, 30, 62, 15,
        /*   170 */
        16, 17, 66, 36, 67, 68, 69, 70, 71, 72,
        /*   180 */
        73, 74, 75, 76, 77, 78, 18, 19, 20, 32,
        /*   190 */
        100, 1, 30, 109, 1, 18, 39, 20, 36, 1,
        /*   200 */
        116, 3, 41, 42, 43, 44, 45, 46, 47, 48,
        /*   210 */
        49, 50, 51, 52, 53, 54, 1, 55, 80, 62,
        /*   220 */
        30, 85, 86, 87, 39, 32, 36, 66, 67, 68,
        /*   230 */
        69, 70, 71, 72, 73, 74, 75, 76, 77, 78,
        /*   240 */
        63, 86, 114, 115, 91, 92, 31, 62, 55, 18,
        /*   250 */
        38, 20, 40, 55, 101, 18, 41, 42, 43, 44,
        /*   260 */
        45, 46, 47, 48, 49, 50, 51, 52, 53, 54,
        /*   270 */
        1, 118, 3, 94, 95, 96, 39, 122, 123, 38,
        /*   280 */
        91, 40, 67, 68, 69, 70, 71, 72, 73, 74,
        /*   290 */
        75, 76, 77, 78, 63, 114, 115, 100, 91, 92,
        /*   300 */
        112, 91, 114, 115, 91, 19, 20, 118, 101, 20,
        /*   310 */
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50,
        /*   320 */
        51, 52, 53, 54, 1, 118, 3, 100, 118, 119,
        /*   330 */
        60, 118, 2, 91, 3, 65, 67, 68, 69, 70,
        /*   340 */
        71, 72, 73, 74, 75, 76, 77, 78, 21, 22,
        /*   350 */
        64, 21, 63, 92, 111, 119, 113, 26, 19, 20,
        /*   360 */
        118, 1, 101, 21, 41, 42, 43, 44, 45, 46,
        /*   370 */
        47, 48, 49, 50, 51, 52, 53, 54, 1, 118,
        /*   380 */
        3, 18, 22, 20, 19, 94, 95, 1, 98, 62,
        /*   390 */
        67, 68, 69, 70, 71, 72, 73, 74, 75, 76,
        /*   400 */
        77, 78, 60, 113, 39, 1, 2, 92, 92, 18,
        /*   410 */
        111, 20, 113, 87, 26, 89, 101, 101, 41, 42,
        /*   420 */
        43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
        /*   430 */
        53, 54, 1, 118, 118, 18, 39, 20, 19, 94,
        /*   440 */
        95, 55, 98, 25, 67, 68, 69, 70, 71, 72,
        /*   450 */
        73, 74, 75, 76, 77, 78, 38, 113, 86, 92,
        /*   460 */
        92, 92, 18, 40, 20, 30, 60, 2, 101, 101,
        /*   470 */
        101, 36, 41, 42, 43, 44, 45, 46, 47, 48,
        /*   480 */
        49, 50, 51, 52, 53, 54, 1, 118, 18, 109,
        /*   490 */
        20, 20, 56, 39, 19, 123, 116, 98, 67, 68,
        /*   500 */
        69, 70, 71, 72, 73, 74, 75, 76, 77, 78,
        /*   510 */
        25, 80, 113, 92, 64, 27, 21, 20, 19, 64,
        /*   520 */
        20, 3, 101, 20, 22, 2, 41, 42, 43, 44,
        /*   530 */
        45, 46, 47, 48, 49, 50, 51, 52, 53, 54,
        /*   540 */
        1, 20, 3, 19, 3, 20, 20, 20, 19, 66,
        /*   550 */
        98, 98, 67, 68, 69, 70, 71, 72, 73, 74,
        /*   560 */
        75, 76, 77, 78, 62, 113, 113, 20, 37, 37,
        /*   570 */
        3, 20, 20, 101, 112, 111, 22, 111, 111, 111,
        /*   580 */
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50,
        /*   590 */
        51, 52, 53, 54, 1, 116, 30, 113, 97, 88,
        /*   600 */
        16, 115, 124, 97, 99, 98, 67, 68, 69, 70,
        /*   610 */
        71, 72, 73, 74, 75, 76, 77, 78, 113, 124,
        /*   620 */
        113, 124, 124, 124, 124, 124, 124, 124, 124, 124,
        /*   630 */
        124, 124, 124, 40, 41, 42, 43, 44, 45, 46,
        /*   640 */
        47, 48, 49, 50, 51, 52, 53, 54, 1, 2,
        /*   650 */
        124, 124, 124, 124, 124, 124, 124, 98, 98, 98,
        /*   660 */
        67, 68, 69, 70, 71, 72, 73, 74, 75, 76,
        /*   670 */
        77, 78, 113, 113, 113, 124, 124, 124, 124, 124,
        /*   680 */
        124, 124, 124, 124, 124, 124, 124, 124, 41, 42,
        /*   690 */
        43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
        /*   700 */
        53, 54, 1, 124, 124, 124, 124, 124, 124, 124,
        /*   710 */
        124, 98, 124, 124, 67, 68, 69, 70, 71, 72,
        /*   720 */
        73, 74, 75, 76, 77, 78, 113, 124, 124, 124,
        /*   730 */
        124, 124, 124, 124, 124, 124, 124, 124, 124, 124,
        /*   740 */
        124, 40, 41, 42, 43, 44, 45, 46, 47, 48,
        /*   750 */
        49, 50, 51, 52, 53, 54, 1, 124, 124, 124,
        /*   760 */
        124, 124, 124, 124, 124, 124, 124, 124, 67, 68,
        /*   770 */
        69, 70, 71, 72, 73, 74, 75, 76, 77, 78,
        /*   780 */
        124, 124, 124, 124, 124, 124, 124, 124, 124, 124,
        /*   790 */
        124, 124, 124, 124, 124, 124, 41, 42, 43, 44,
        /*   800 */
        45, 46, 47, 48, 49, 50, 51, 52, 53, 54,
        /*   810 */
        124, 124, 124, 124, 124, 124, 124, 124, 124, 124,
        /*   820 */
        22, 124, 67, 68, 69, 70, 71, 72, 73, 74,
        /*   830 */
        75, 76, 77, 78, 124, 124, 124, 124, 41, 42,
        /*   840 */
        43, 44, 45, 46, 47, 48, 49, 50, 51, 52,
        /*   850 */
        53, 54, 124, 124, 124, 124, 124, 124, 60, 124,
        /*   860 */
        124, 124, 124, 65, 67, 68, 69, 70, 71, 72,
        /*   870 */
        73, 74, 75, 76, 77, 78, 124, 124, 124, 124,
        /*   880 */
        124, 124, 124, 18, 19, 20, 124, 124, 23, 24,
        /*   890 */
        124, 86, 124, 124, 29, 90, 92, 124, 33, 94,
        /*   900 */
        35, 124, 37, 124, 39, 101, 124, 42, 103, 124,
        /*   910 */
        124, 106, 107, 108, 124, 110, 124, 113, 92, 92,
        /*   920 */
        124, 124, 57, 58, 59, 60, 61, 101, 101, 64,
        /*   930 */
        65, 66, 124, 94, 124, 124, 124, 124, 124, 113,
        /*   940 */
        113, 18, 19, 20, 79, 106, 23, 24, 124, 110,
        /*   950 */
        124, 124, 29, 124, 124, 86, 33, 34, 35, 90,
        /*   960 */
        37, 124, 39, 94, 92, 42, 124, 124, 124, 124,
        /*   970 */
        86, 124, 103, 101, 90, 106, 107, 108, 94, 110,
        /*   980 */
        57, 58, 59, 60, 61, 113, 124, 64, 65, 124,
        /*   990 */
        106, 107, 108, 124, 110, 18, 124, 20, 124, 18,
        /*  1000 */
        19, 20, 79, 124, 23, 24, 124, 30, 124, 32,
        /*  1010 */
        29, 86, 124, 36, 33, 90, 35, 124, 37, 94,
        /*  1020 */
        39, 124, 124, 42, 124, 124, 124, 124, 124, 124,
        /*  1030 */
        124, 106, 107, 108, 124, 110, 124, 124, 57, 58,
        /*  1040 */
        59, 60, 61, 124, 124, 64, 65, 124, 124, 124,
        /*  1050 */
        124, 124, 18, 124, 20, 124, 124, 18, 19, 20,
        /*  1060 */
        79, 124, 23, 24, 30, 86, 32, 124, 29, 90,
        /*  1070 */
        36, 86, 33, 94, 35, 90, 37, 124, 39, 94,
        /*  1080 */
        124, 42, 124, 124, 124, 106, 107, 108, 124, 110,
        /*  1090 */
        124, 106, 107, 108, 124, 110, 57, 58, 59, 60,
        /*  1100 */
        61, 124, 124, 64, 65, 124, 124, 124, 124, 124,
        /*  1110 */
        124, 124, 124, 124, 124, 18, 19, 20, 79, 124,
        /*  1120 */
        23, 24, 124, 124, 124, 124, 29, 86, 124, 124,
        /*  1130 */
        33, 90, 35, 124, 37, 94, 39, 124, 124, 42,
        /*  1140 */
        124, 124, 124, 124, 124, 124, 124, 106, 107, 108,
        /*  1150 */
        124, 110, 124, 124, 57, 58, 59, 60, 61, 124,
        /*  1160 */
        124, 64, 65, 124, 124, 124, 124, 124, 124, 124,
        /*  1170 */
        124, 124, 124, 18, 19, 20, 79, 124, 23, 24,
        /*  1180 */
        124, 124, 124, 124, 29, 86, 124, 124, 33, 90,
        /*  1190 */
        35, 124, 37, 94, 39, 124, 124, 42, 124, 124,
        /*  1200 */
        124, 124, 124, 124, 124, 106, 107, 108, 124, 110,
        /*  1210 */
        124, 124, 57, 58, 59, 60, 61, 124, 124, 64,
        /*  1220 */
        65, 124, 124, 124, 124, 124, 124, 124, 124, 124,
        /*  1230 */
        124, 18, 19, 20, 79, 124, 23, 24, 124, 124,
        /*  1240 */
        124, 124, 29, 86, 124, 124, 33, 90, 35, 124,
        /*  1250 */
        37, 94, 39, 124, 124, 42, 124, 124, 124, 124,
        /*  1260 */
        124, 124, 124, 106, 107, 108, 124, 110, 124, 124,
        /*  1270 */
        57, 58, 59, 60, 61, 124, 124, 64, 65, 124,
        /*  1280 */
        1, 124, 3, 124, 3, 124, 124, 124, 124, 18,
        /*  1290 */
        19, 20, 79, 124, 23, 24, 124, 124, 124, 124,
        /*  1300 */
        29, 22, 21, 124, 33, 124, 35, 124, 37, 30,
        /*  1310 */
        39, 30, 124, 42, 124, 36, 124, 36, 39, 124,
        /*  1320 */
        39, 124, 124, 124, 91, 92, 124, 124, 57, 58,
        /*  1330 */
        59, 60, 61, 124, 101, 64, 65, 104, 105, 124,
        /*  1340 */
        124, 62, 124, 62, 124, 124, 124, 18, 19, 20,
        /*  1350 */
        79, 118, 23, 24, 124, 124, 124, 124, 29, 124,
        /*  1360 */
        124, 124, 33, 124, 35, 124, 37, 124, 39, 91,
        /*  1370 */
        92, 42, 124, 124, 124, 124, 124, 124, 124, 101,
        /*  1380 */
        124, 124, 104, 105, 124, 124, 57, 58, 59, 60,
        /*  1390 */
        61, 124, 124, 64, 65, 124, 118, 124, 124, 124,
        /*  1400 */
        124, 124, 124, 124, 124, 18, 19, 20, 79, 124,
        /*  1410 */
        23, 24, 124, 124, 124, 124, 29, 124, 124, 124,
        /*  1420 */
        33, 124, 35, 124, 37, 124, 39, 91, 92, 42,
        /*  1430 */
        124, 124, 124, 124, 124, 124, 124, 101, 124, 124,
        /*  1440 */
        104, 105, 124, 124, 57, 58, 59, 60, 61, 124,
        /*  1450 */
        124, 64, 124, 124, 118, 124, 124, 124, 124, 124,
        /*  1460 */
        124, 124, 124, 18, 19, 20, 79, 124, 23, 24,
        /*  1470 */
        124, 124, 124, 124, 29, 124, 124, 124, 33, 124,
        /*  1480 */
        35, 124, 37, 124, 39, 124, 3, 42, 124, 124,
        /*  1490 */
        124, 124, 124, 124, 124, 124, 124, 124, 124, 124,
        /*  1500 */
        124, 124, 57, 58, 59, 60, 61, 124, 25, 64,
        /*  1510 */
        27, 28, 124, 30, 31, 124, 124, 124, 124, 36,
        /*  1520 */
        124, 38, 39, 40, 79, 124, 124, 124, 124, 124,
        /*  1530 */
        124, 124, 124, 124, 124, 86, 124, 124, 55, 90,
        /*  1540 */
        124, 124, 93, 94, 124, 62, 124, 124, 124, 66,
        /*  1550 */
        124, 102, 103, 124, 124, 106, 107, 108, 124, 110,
        /*  1560 */
        124, 124, 124, 80, 124, 124, 124, 86, 124, 120,
        /*  1570 */
        121, 90, 124, 124, 93, 94, 95, 124, 124, 124,
        /*  1580 */
        124, 124, 124, 102, 103, 124, 124, 106, 107, 108,
        /*  1590 */
        86, 110, 124, 124, 90, 124, 124, 93, 94, 124,
        /*  1600 */
        124, 124, 124, 124, 124, 124, 102, 103, 124, 86,
        /*  1610 */
        106, 107, 108, 90, 110, 124, 93, 94, 124, 124,
        /*  1620 */
        124, 117, 124, 124, 124, 102, 103, 124, 124, 106,
        /*  1630 */
        107, 108, 124, 110, 124, 124, 124, 124, 86, 124,
        /*  1640 */
        117, 124, 90, 124, 124, 93, 94, 124, 124, 124,
        /*  1650 */
        124, 124, 124, 124, 102, 103, 124, 124, 106, 107,
        /*  1660 */
        108, 86, 110, 124, 124, 90, 124, 124, 93, 94,
        /*  1670 */
        95, 124, 124, 121, 124, 124, 124, 102, 103, 124,
        /*  1680 */
        124, 106, 107, 108, 86, 110, 124, 124, 90, 124,
        /*  1690 */
        124, 93, 94, 124, 124, 124, 124, 124, 124, 124,
        /*  1700 */
        102, 103, 124, 86, 106, 107, 108, 90, 110, 124,
        /*  1710 */
        93, 94, 124, 124, 124, 117, 124, 124, 124, 102,
        /*  1720 */
        103, 124, 124, 106, 107, 108, 124, 110, 124, 86,
        /*  1730 */
        91, 92, 124, 90, 124, 124, 93, 94, 124, 124,
        /*  1740 */
        101, 124, 124, 104, 105, 102, 103, 124, 86, 106,
        /*  1750 */
        107, 108, 90, 110, 124, 93, 94, 118, 124, 124,
        /*  1760 */
        124, 124, 124, 124, 102, 103, 124, 124, 106, 107,
        /*  1770 */
        108, 86, 110, 124, 124, 90, 124, 124, 93, 94,
        /*  1780 */
        124, 124, 124, 124, 124, 124, 124, 102, 103, 124,
        /*  1790 */
        86, 106, 107, 108, 90, 110, 124, 93, 94, 124,
        /*  1800 */
        124, 124, 124, 124, 124, 124, 102, 103, 124, 124,
        /*  1810 */
        106, 107, 108, 124, 110, 124, 86, 91, 92, 124,
        /*  1820 */
        90, 124, 124, 93, 94, 124, 124, 101, 124, 124,
        /*  1830 */
        104, 105, 102, 103, 124, 86, 106, 107, 108, 90,
        /*  1840 */
        110, 124, 93, 94, 118, 124, 124, 124, 124, 124,
        /*  1850 */
        124, 102, 103, 124, 124, 106, 107, 108, 86, 110,
        /*  1860 */
        124, 124, 90, 124, 124, 93, 94, 124, 124, 124,
        /*  1870 */
        124, 124, 124, 124, 102, 103, 124, 86, 106, 107,
        /*  1880 */
        108, 90, 110, 124, 93, 94, 124, 124, 124, 124,
        /*  1890 */
        124, 124, 124, 102, 103, 124, 124, 106, 107, 108,
        /*  1900 */
        124, 110, 124, 86, 91, 92, 124, 90, 124, 124,
        /*  1910 */
        93, 94, 124, 124, 101, 124, 124, 104, 105, 102,
        /*  1920 */
        103, 124, 86, 106, 107, 108, 90, 110, 124, 93,
        /*  1930 */
        94, 118, 124, 124, 124, 124, 124, 124, 102, 103,
        /*  1940 */
        124, 124, 106, 107, 108, 86, 110, 124, 124, 90,
        /*  1950 */
        124, 124, 93, 94, 124, 124, 124, 124, 124, 124,
        /*  1960 */
        124, 102, 103, 124, 86, 106, 107, 108, 90, 110,
        /*  1970 */
        124, 93, 94, 124, 124, 124, 124, 124, 124, 124,
        /*  1980 */
        102, 103, 124, 124, 106, 107, 108, 124, 110, 124,
        /*  1990 */
        86, 124, 124, 124, 90, 124, 124, 93, 94, 124,
        /*  2000 */
        124, 124, 124, 124, 124, 124, 102, 103, 124, 86,
        /*  2010 */
        106, 107, 108, 90, 110, 124, 93, 94, 124, 124,
        /*  2020 */
        124, 124, 124, 124, 124, 102, 103, 124, 124, 106,
        /*  2030 */
        107, 108, 86, 110, 124, 124, 90, 124, 124, 93,
        /*  2040 */
        94, 124, 124, 124, 124, 124, 124, 124, 102, 103,
        /*  2050 */
        124, 86, 106, 107, 108, 90, 110, 124, 93, 94,
        /*  2060 */
        124, 124, 124, 124, 124, 124, 124, 102, 103, 124,
        /*  2070 */
        124, 106, 107, 108, 124, 110, 124, 86, 124, 124,
        /*  2080 */
        124, 90, 124, 124, 93, 94, 124, 124, 124, 124,
        /*  2090 */
        124, 124, 124, 102, 103, 124, 86, 106, 107, 108,
        /*  2100 */
        90, 110, 124, 93, 94, 124, 124, 124, 124, 124,
        /*  2110 */
        124, 124, 102, 103, 124, 124, 106, 107, 108, 86,
        /*  2120 */
        110, 124, 124, 90, 124, 124, 93, 94, 124, 124,
        /*  2130 */
        124, 124, 124, 124, 124, 102, 103, 124, 86, 106,
        /*  2140 */
        107, 108, 90, 110, 124, 93, 94, 124, 124, 124,
        /*  2150 */
        124, 124, 124, 124, 102, 103, 124, 124, 106, 107,
        /*  2160 */
        108, 124, 110, 124, 86, 124, 124, 124, 90, 124,
        /*  2170 */
        124, 93, 94, 124, 124, 124, 124, 124, 124, 124,
        /*  2180 */
        102, 103, 124, 86, 106, 107, 108, 90, 110, 124,
        /*  2190 */
        93, 94, 124, 124, 124, 124, 124, 124, 124, 102,
        /*  2200 */
        103, 124, 124, 106, 107, 108, 86, 110, 124, 124,
        /*  2210 */
        90, 124, 124, 93, 94, 124, 124, 124, 124, 124,
        /*  2220 */
        124, 124, 102, 103, 124, 86, 106, 107, 108, 90,
        /*  2230 */
        110, 124, 93, 94, 124, 124, 124, 124, 124, 124,
        /*  2240 */
        124, 102, 103, 124, 124, 106, 107, 108, 124, 110,
        /*  2250 */
        124, 86, 124, 124, 124, 90, 124, 124, 93, 94,
        /*  2260 */
        124, 124, 124, 124, 124, 124, 124, 102, 103, 124,
        /*  2270 */
        86, 106, 107, 108, 90, 110, 124, 93, 94, 124,
        /*  2280 */
        124, 124, 124, 124, 124, 124, 102, 103, 124, 124,
        /*  2290 */
        106, 107, 108, 86, 110, 124, 124, 90, 124, 124,
        /*  2300 */
        93, 94, 124, 124, 124, 124, 124, 124, 124, 102,
        /*  2310 */
        103, 124, 86, 106, 107, 108, 90, 110, 124, 93,
        /*  2320 */
        94, 124, 124, 124, 124, 124, 124, 124, 102, 103,
        /*  2330 */
        124, 124, 106, 107, 108, 124, 110, 124, 86, 124,
        /*  2340 */
        124, 124, 90, 124, 124, 93, 94, 124, 124, 124,
        /*  2350 */
        124, 124, 124, 124, 102, 103, 124, 11, 106, 107,
        /*  2360 */
        108, 124, 110, 124, 18, 124, 124, 124, 124, 23,
        /*  2370 */
        24, 11, 124, 124, 124, 29, 124, 124, 18, 33,
        /*  2380 */
        124, 35, 124, 23, 24, 124, 124, 124, 124, 29,
        /*  2390 */
        124, 124, 124, 33, 124, 35, 86, 124, 124, 124,
        /*  2400 */
        90, 124, 124, 93, 94, 124, 124, 124, 124, 124,
        /*  2410 */
        124, 124, 102, 103, 124, 124, 106, 107, 108, 124,
        /*  2420 */
        110, 124, 124, 124, 124, 79, 80, 81, 124, 86,
        /*  2430 */
        124, 124, 124, 90, 124, 124, 93, 94, 124, 79,
        /*  2440 */
        80, 81, 124, 124, 124, 102, 103, 124, 124, 106,
        /*  2450 */
        107, 108, 124, 110, 124, 124, 86, 124, 124, 124,
        /*  2460 */
        90, 124, 124, 93, 94, 124, 124, 124, 124, 124,
        /*  2470 */
        124, 124, 102, 103, 124, 124, 106, 107, 108, 124,
        /*  2480 */
        110, 124, 86, 124, 124, 124, 90, 124, 124, 93,
        /*  2490 */
        94, 124, 124, 124, 124, 124, 124, 124, 102, 103,
        /*  2500 */
        124, 124, 106, 107, 108, 86, 110, 124, 124, 90,
        /*  2510 */
        124, 124, 93, 94, 124, 124, 86, 124, 124, 124,
        /*  2520 */
        90, 102, 103, 93, 94, 106, 107, 108, 124, 110,
        /*  2530 */
        124, 124, 102, 103, 124, 124, 106, 107, 108, 124,
        /*  2540 */
        110,
    );
    const YY_SHIFT_USE_DFLT = - 39;
    const YY_SHIFT_MAX = 259;
    static public $yy_shift_ofst = array(
        /*     0 */
        1, 1097, 1271, 1097, 981, 1271, 981, 981, 923, 865,
        /*    10 */
        923, 981, 981, 981, 981, 981, 981, 981, 981, 981,
        /*    20 */
        981, 981, 1213, 981, 981, 981, 1155, 1329, 981, 981,
        /*    30 */
        981, 981, 981, 981, 981, 981, 981, 981, 981, 981,
        /*    40 */
        981, 1155, 981, 981, 1039, 1039, 1445, 1387, 1387, 1387,
        /*    50 */
        1387, 1387, 1387, - 1, 53, 107, 107, 107, 107, 107,
        /*    60 */
        539, 431, 701, 161, 377, 269, 647, 485, 593, 215,
        /*    70 */
        323, 755, 755, 755, 755, 755, 755, 755, 755, 755,
        /*    80 */
        755, 755, 755, 755, 755, 755, 755, 755, 755, 755,
        /*    90 */
        797, 797, 1279, 56, 190, 1, 2346, 977, 1034, 3,
        /*   100 */
        3, 137, 137, 190, 190, 286, 798, 404, 365, 2360,
        /*   110 */
        25, 162, 57, 121, 70, 154, 16, 327, 198, 231,
        /*   120 */
        114, 193, 417, 391, 360, 363, 363, 339, 502, 363,
        /*   130 */
        365, 363, 365, 386, 339, 444, 470, 49, 444, 435,
        /*   140 */
        49, 363, 363, 363, 363, 363, 31, 554, 31, 31,
        /*   150 */
        554, 31, 31, 31, 31, 31, 31, - 39, 177, 168,
        /*   160 */
        49, 49, 49, 49, 49, 49, 49, 49, - 38, - 38,
        /*   170 */
        - 38, 270, - 38, 49, 49, 49, 49, 49, 49, - 38,
        /*   180 */
        270, - 38, 237, 49, 49, 49, - 38, 49, 49, 49,
        /*   190 */
        584, 554, 554, 31, 566, 566, 60, 554, 31, 454,
        /*   200 */
        31, 31, 60, 31, - 39, - 39, - 39, - 39, - 39, 1483,
        /*   210 */
        1281, 106, 45, 157, 138, - 37, 80, 241, 418, 342,
        /*   220 */
        331, 212, 185, 330, 185, 289, 552, 541, 526, 525,
        /*   230 */
        521, 523, 524, 527, 529, 567, 551, 532, 531, 483,
        /*   240 */
        547, 503, 518, 436, 454, 465, 406, 397, 419, 423,
        /*   250 */
        471, 388, 475, 455, 500, 499, 497, 450, 488, 495,
    );
    const YY_REDUCE_USE_DFLT = - 99;
    const YY_REDUCE_MAX = 208;
    static public $yy_reduce_ofst = array(
        /*     0 */
        30, 1449, 1575, 1552, 1523, 1481, 1504, 1598, 1836, 2010,
        /*    10 */
        1991, 1791, 1685, 1643, 1704, 1749, 2033, 2078, 2310, 2419,
        /*    20 */
        2139, 2207, 2252, 2343, 2370, 1772, 1617, 1946, 1904, 2184,
        /*    30 */
        1923, 1878, 1859, 1965, 2052, 1662, 1817, 1730, 2097, 2396,
        /*    40 */
        2430, 2120, 2165, 2226, 869, 805, 985, 925, 884, 979,
        /*    50 */
        1099, 1041, 1157, 1639, 1233, 1639, 1336, 1278, 1726, 1813,
        /*    60 */
        - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87,
        /*    70 */
        - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87,
        /*    80 */
        - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87, - 87,
        /*    90 */
        - 87, - 87, 18, 207, 153, 136, 155, 827, 872, 804,
        /*   100 */
        826, 261, 316, 369, 315, 839, 188, 210, 179, 372,
        /*   110 */
        189, 189, 189, 505, 299, 326, 128, 128, 189, 299,
        /*   120 */
        299, 189, 507, 453, 242, 344, 243, 84, 128, 452,
        /*   130 */
        291, 399, 345, 189, 380, 613, 453, 421, 453, 368,
        /*   140 */
        367, 560, 453, 559, 561, 290, 189, 128, 213, 189,
        /*   150 */
        181, 189, 189, 189, 189, 189, 189, 189, 484, 479,
        /*   160 */
        472, 472, 472, 472, 472, 472, 472, 472, 462, 462,
        /*   170 */
        462, 464, 462, 472, 472, 472, 472, 472, 472, 462,
        /*   180 */
        467, 462, 466, 472, 472, 472, 462, 472, 472, 472,
        /*   190 */
        511, 486, 486, - 98, 501, 506, 236, 486, - 98, 468,
        /*   200 */
        - 98, - 98, 236, - 98, 227, - 27, 197, 33, 90,
    );
    static public $yyExpectedTokens = array(
        /* 0 */
        array(4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 23, 24, 29, 33, 35,),
        /* 1 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 2 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 3 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 4 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 5 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 6 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 7 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 8 */
        array(18, 19, 20, 23, 24, 29, 33, 34, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 9 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 66, 79,),
        /* 10 */
        array(18, 19, 20, 23, 24, 29, 33, 34, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 11 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 12 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 13 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 14 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 15 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 16 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 17 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 18 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 19 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 20 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 21 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 22 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 23 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 24 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 25 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 26 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 27 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 28 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 29 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 30 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 31 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 32 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 33 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 34 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 35 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 36 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 37 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 38 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 39 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 40 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 41 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 42 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 43 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 44 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 45 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 65, 79,),
        /* 46 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 79,),
        /* 47 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 79,),
        /* 48 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 79,),
        /* 49 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 79,),
        /* 50 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 79,),
        /* 51 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 79,),
        /* 52 */
        array(18, 19, 20, 23, 24, 29, 33, 35, 37, 39, 42, 57, 58, 59, 60, 61, 64, 79,),
        /* 53 */
        array(1, 3, 30, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 54 */
        array(1, 28, 30, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 55 */
        array(1, 30, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 56 */
        array(1, 30, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 57 */
        array(1, 30, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 58 */
        array(1, 30, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 59 */
        array(1, 30, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 60 */
        array(1, 3, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 61 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80,),
        /* 62 */
        array(1, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 63 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 64 */
        array(1, 3, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 65 */
        array(1, 3, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 66 */
        array(1, 2, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 67 */
        array(1, 25, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 68 */
        array(1, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 69 */
        array(1, 31, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 70 */
        array(1, 3, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 71 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 72 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 73 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 74 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 75 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 76 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 77 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 78 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 79 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 80 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 81 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 82 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 83 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 84 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 85 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 86 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 87 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 88 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 89 */
        array(1, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 90 */
        array(41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 91 */
        array(41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78,),
        /* 92 */
        array(1, 3, 22, 30, 36, 39, 62,),
        /* 93 */
        array(1, 3, 30, 36, 55,),
        /* 94 */
        array(1, 30, 36,),
        /* 95 */
        array(4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 23, 24, 29, 33, 35,),
        /* 96 */
        array(11, 18, 23, 24, 29, 33, 35, 79, 80, 81,),
        /* 97 */
        array(18, 20, 30, 32, 36,),
        /* 98 */
        array(18, 20, 30, 32, 36,),
        /* 99 */
        array(18, 20, 30, 36,),
        /* 100 */
        array(18, 20, 30, 36,),
        /* 101 */
        array(1, 3, 30, 36,),
        /* 102 */
        array(1, 3, 30, 36,),
        /* 103 */
        array(1, 30, 36,),
        /* 104 */
        array(1, 30, 36,),
        /* 105 */
        array(19, 20, 64,),
        /* 106 */
        array(22, 60, 65,),
        /* 107 */
        array(1, 2,),
        /* 108 */
        array(19, 39,),
        /* 109 */
        array(11, 18, 23, 24, 29, 33, 35, 79, 80, 81,),
        /* 110 */
        array(1, 3, 30, 31, 36, 55,),
        /* 111 */
        array(1, 3, 30, 36, 55,),
        /* 112 */
        array(1, 3, 30, 36, 55,),
        /* 113 */
        array(18, 20, 21, 26,),
        /* 114 */
        array(18, 20, 21, 63,),
        /* 115 */
        array(15, 16, 17,),
        /* 116 */
        array(21, 22, 62,),
        /* 117 */
        array(21, 22, 62,),
        /* 118 */
        array(1, 3, 55,),
        /* 119 */
        array(18, 20, 63,),
        /* 120 */
        array(18, 20, 21,),
        /* 121 */
        array(1, 32, 55,),
        /* 122 */
        array(18, 20,),
        /* 123 */
        array(18, 20,),
        /* 124 */
        array(1, 22,),
        /* 125 */
        array(18, 20,),
        /* 126 */
        array(18, 20,),
        /* 127 */
        array(19, 20,),
        /* 128 */
        array(22, 62,),
        /* 129 */
        array(18, 20,),
        /* 130 */
        array(19, 39,),
        /* 131 */
        array(18, 20,),
        /* 132 */
        array(19, 39,),
        /* 133 */
        array(1, 55,),
        /* 134 */
        array(19, 20,),
        /* 135 */
        array(18, 20,),
        /* 136 */
        array(18, 20,),
        /* 137 */
        array(30, 36,),
        /* 138 */
        array(18, 20,),
        /* 139 */
        array(30, 36,),
        /* 140 */
        array(30, 36,),
        /* 141 */
        array(18, 20,),
        /* 142 */
        array(18, 20,),
        /* 143 */
        array(18, 20,),
        /* 144 */
        array(18, 20,),
        /* 145 */
        array(18, 20,),
        /* 146 */
        array(1,),
        /* 147 */
        array(22,),
        /* 148 */
        array(1,),
        /* 149 */
        array(1,),
        /* 150 */
        array(22,),
        /* 151 */
        array(1,),
        /* 152 */
        array(1,),
        /* 153 */
        array(1,),
        /* 154 */
        array(1,),
        /* 155 */
        array(1,),
        /* 156 */
        array(1,),
        /* 157 */
        array(),
        /* 158 */
        array(18, 20, 63,),
        /* 159 */
        array(18, 19, 20,),
        /* 160 */
        array(30, 36,),
        /* 161 */
        array(30, 36,),
        /* 162 */
        array(30, 36,),
        /* 163 */
        array(30, 36,),
        /* 164 */
        array(30, 36,),
        /* 165 */
        array(30, 36,),
        /* 166 */
        array(30, 36,),
        /* 167 */
        array(30, 36,),
        /* 168 */
        array(60, 65,),
        /* 169 */
        array(60, 65,),
        /* 170 */
        array(60, 65,),
        /* 171 */
        array(60, 65,),
        /* 172 */
        array(60, 65,),
        /* 173 */
        array(30, 36,),
        /* 174 */
        array(30, 36,),
        /* 175 */
        array(30, 36,),
        /* 176 */
        array(30, 36,),
        /* 177 */
        array(30, 36,),
        /* 178 */
        array(30, 36,),
        /* 179 */
        array(60, 65,),
        /* 180 */
        array(60, 65,),
        /* 181 */
        array(60, 65,),
        /* 182 */
        array(18, 39,),
        /* 183 */
        array(30, 36,),
        /* 184 */
        array(30, 36,),
        /* 185 */
        array(30, 36,),
        /* 186 */
        array(60, 65,),
        /* 187 */
        array(30, 36,),
        /* 188 */
        array(30, 36,),
        /* 189 */
        array(30, 36,),
        /* 190 */
        array(16,),
        /* 191 */
        array(22,),
        /* 192 */
        array(22,),
        /* 193 */
        array(1,),
        /* 194 */
        array(30,),
        /* 195 */
        array(30,),
        /* 196 */
        array(2,),
        /* 197 */
        array(22,),
        /* 198 */
        array(1,),
        /* 199 */
        array(39,),
        /* 200 */
        array(1,),
        /* 201 */
        array(1,),
        /* 202 */
        array(2,),
        /* 203 */
        array(1,),
        /* 204 */
        array(),
        /* 205 */
        array(),
        /* 206 */
        array(),
        /* 207 */
        array(),
        /* 208 */
        array(),
        /* 209 */
        array(3, 25, 27, 28, 30, 31, 36, 38, 39, 40, 55, 62, 66, 80,),
        /* 210 */
        array(3, 21, 30, 36, 39, 62,),
        /* 211 */
        array(39, 60, 62, 66,),
        /* 212 */
        array(18, 19, 20, 37,),
        /* 213 */
        array(32, 39, 62,),
        /* 214 */
        array(26, 80,),
        /* 215 */
        array(38, 40,),
        /* 216 */
        array(38, 66,),
        /* 217 */
        array(38, 40,),
        /* 218 */
        array(25, 38,),
        /* 219 */
        array(21, 60,),
        /* 220 */
        array(3, 26,),
        /* 221 */
        array(38, 40,),
        /* 222 */
        array(39, 62,),
        /* 223 */
        array(2, 21,),
        /* 224 */
        array(39, 62,),
        /* 225 */
        array(20, 63,),
        /* 226 */
        array(20,),
        /* 227 */
        array(3,),
        /* 228 */
        array(20,),
        /* 229 */
        array(20,),
        /* 230 */
        array(20,),
        /* 231 */
        array(2,),
        /* 232 */
        array(19,),
        /* 233 */
        array(20,),
        /* 234 */
        array(19,),
        /* 235 */
        array(3,),
        /* 236 */
        array(20,),
        /* 237 */
        array(37,),
        /* 238 */
        array(37,),
        /* 239 */
        array(66,),
        /* 240 */
        array(20,),
        /* 241 */
        array(20,),
        /* 242 */
        array(3,),
        /* 243 */
        array(56,),
        /* 244 */
        array(39,),
        /* 245 */
        array(2,),
        /* 246 */
        array(60,),
        /* 247 */
        array(39,),
        /* 248 */
        array(19,),
        /* 249 */
        array(40,),
        /* 250 */
        array(20,),
        /* 251 */
        array(26,),
        /* 252 */
        array(19,),
        /* 253 */
        array(64,),
        /* 254 */
        array(20,),
        /* 255 */
        array(19,),
        /* 256 */
        array(20,),
        /* 257 */
        array(64,),
        /* 258 */
        array(27,),
        /* 259 */
        array(21,),
        /* 260 */
        array(),
        /* 261 */
        array(),
        /* 262 */
        array(),
        /* 263 */
        array(),
        /* 264 */
        array(),
        /* 265 */
        array(),
        /* 266 */
        array(),
        /* 267 */
        array(),
        /* 268 */
        array(),
        /* 269 */
        array(),
        /* 270 */
        array(),
        /* 271 */
        array(),
        /* 272 */
        array(),
        /* 273 */
        array(),
        /* 274 */
        array(),
        /* 275 */
        array(),
        /* 276 */
        array(),
        /* 277 */
        array(),
        /* 278 */
        array(),
        /* 279 */
        array(),
        /* 280 */
        array(),
        /* 281 */
        array(),
        /* 282 */
        array(),
        /* 283 */
        array(),
        /* 284 */
        array(),
        /* 285 */
        array(),
        /* 286 */
        array(),
        /* 287 */
        array(),
        /* 288 */
        array(),
        /* 289 */
        array(),
        /* 290 */
        array(),
        /* 291 */
        array(),
        /* 292 */
        array(),
        /* 293 */
        array(),
        /* 294 */
        array(),
        /* 295 */
        array(),
        /* 296 */
        array(),
        /* 297 */
        array(),
        /* 298 */
        array(),
        /* 299 */
        array(),
        /* 300 */
        array(),
        /* 301 */
        array(),
        /* 302 */
        array(),
        /* 303 */
        array(),
        /* 304 */
        array(),
        /* 305 */
        array(),
        /* 306 */
        array(),
        /* 307 */
        array(),
        /* 308 */
        array(),
        /* 309 */
        array(),
        /* 310 */
        array(),
        /* 311 */
        array(),
        /* 312 */
        array(),
        /* 313 */
        array(),
        /* 314 */
        array(),
        /* 315 */
        array(),
        /* 316 */
        array(),
        /* 317 */
        array(),
        /* 318 */
        array(),
        /* 319 */
        array(),
        /* 320 */
        array(),
        /* 321 */
        array(),
        /* 322 */
        array(),
        /* 323 */
        array(),
        /* 324 */
        array(),
        /* 325 */
        array(),
        /* 326 */
        array(),
        /* 327 */
        array(),
        /* 328 */
        array(),
        /* 329 */
        array(),
        /* 330 */
        array(),
        /* 331 */
        array(),
        /* 332 */
        array(),
        /* 333 */
        array(),
        /* 334 */
        array(),
        /* 335 */
        array(),
        /* 336 */
        array(),
        /* 337 */
        array(),
        /* 338 */
        array(),
        /* 339 */
        array(),
        /* 340 */
        array(),
        /* 341 */
        array(),
        /* 342 */
        array(),
        /* 343 */
        array(),
        /* 344 */
        array(),
        /* 345 */
        array(),
        /* 346 */
        array(),
        /* 347 */
        array(),
        /* 348 */
        array(),
        /* 349 */
        array(),
        /* 350 */
        array(),
        /* 351 */
        array(),
        /* 352 */
        array(),
        /* 353 */
        array(),
        /* 354 */
        array(),
        /* 355 */
        array(),
        /* 356 */
        array(),
        /* 357 */
        array(),
    );
    static public $yy_default = array(
        /*     0 */
        361, 541, 558, 558, 512, 558, 512, 512, 558, 558,
        /*    10 */
        558, 558, 558, 558, 558, 558, 558, 558, 558, 558,
        /*    20 */
        558, 558, 558, 558, 558, 558, 558, 558, 558, 558,
        /*    30 */
        558, 558, 558, 558, 558, 558, 558, 558, 558, 558,
        /*    40 */
        558, 558, 558, 558, 558, 558, 558, 558, 558, 558,
        /*    50 */
        558, 558, 558, 558, 418, 418, 418, 418, 387, 395,
        /*    60 */
        558, 558, 558, 558, 558, 558, 558, 558, 558, 423,
        /*    70 */
        558, 428, 425, 510, 423, 429, 511, 543, 444, 542,
        /*    80 */
        447, 448, 443, 442, 439, 452, 544, 420, 451, 400,
        /*    90 */
        456, 455, 467, 431, 418, 358, 558, 418, 418, 418,
        /*   100 */
        418, 438, 475, 418, 418, 558, 524, 409, 558, 558,
        /*   110 */
        431, 431, 431, 558, 485, 558, 476, 476, 431, 485,
        /*   120 */
        485, 431, 558, 558, 412, 558, 485, 558, 476, 558,
        /*   130 */
        558, 558, 558, 431, 558, 558, 558, 397, 558, 418,
        /*   140 */
        418, 558, 558, 558, 558, 558, 441, 476, 414, 434,
        /*   150 */
        521, 436, 435, 460, 459, 458, 454, 519, 486, 558,
        /*   160 */
        385, 384, 405, 404, 389, 406, 399, 388, 505, 483,
        /*   170 */
        482, 481, 480, 407, 408, 383, 398, 403, 402, 504,
        /*   180 */
        479, 502, 485, 393, 392, 390, 503, 382, 396, 394,
        /*   190 */
        378, 525, 522, 475, 557, 557, 513, 499, 438, 485,
        /*   200 */
        413, 410, 514, 415, 518, 485, 518, 485, 518, 433,
        /*   210 */
        467, 467, 558, 467, 457, 558, 558, 558, 558, 463,
        /*   220 */
        457, 558, 453, 497, 467, 558, 558, 558, 558, 558,
        /*   230 */
        558, 497, 558, 558, 558, 558, 558, 465, 558, 558,
        /*   240 */
        558, 558, 558, 469, 497, 497, 463, 523, 558, 558,
        /*   250 */
        558, 457, 558, 558, 558, 558, 558, 558, 426, 558,
        /*   260 */
        477, 520, 495, 507, 490, 508, 487, 374, 498, 484,
        /*   270 */
        556, 364, 363, 365, 461, 366, 472, 466, 360, 359,
        /*   280 */
        462, 362, 464, 367, 430, 401, 469, 372, 373, 419,
        /*   290 */
        432, 427, 368, 369, 370, 371, 433, 470, 445, 497,
        /*   300 */
        446, 496, 450, 449, 500, 517, 545, 474, 552, 411,
        /*   310 */
        501, 478, 553, 526, 534, 533, 535, 536, 437, 537,
        /*   320 */
        532, 417, 528, 527, 529, 530, 531, 546, 547, 515,
        /*   330 */
        506, 375, 376, 509, 377, 516, 468, 494, 489, 492,
        /*   340 */
        491, 493, 379, 380, 555, 548, 554, 551, 550, 549,
        /*   350 */
        440, 416, 473, 471, 539, 540, 538, 488,
    );
    const YYNOCODE = 125;
    const YYSTACKDEPTH = 500;
    const YYNSTATE = 358;
    const YYNRULE = 200;
    const YYERRORSYMBOL = 82;
    const YYERRSYMDT = 'yy0';
    const YYFALLBACK = 0;
    public static $yyFallback = array();

    public function Trace($TraceFILE, $zTracePrompt)
    {
        if (!$TraceFILE) {
            $zTracePrompt = 0;
        } elseif (!$zTracePrompt) {
            $TraceFILE = 0;
        }
        $this->yyTraceFILE = $TraceFILE;
        $this->yyTracePrompt = $zTracePrompt;
    }

    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }

    public $yyTraceFILE;
    public $yyTracePrompt;
    public $yyidx;                    /* Index of top element in stack */
    public $yyerrcnt;                 /* Shifts left before out of the error */
    public $yystack = array();  /* The parser's stack */

    public $yyTokenName = array(
        '$', 'VERT', 'COLON', 'RDEL',
        'COMMENT', 'PHPSTARTTAG', 'PHPENDTAG', 'PHPENDSCRIPT',
        'ASPSTARTTAG', 'ASPENDTAG', 'XMLTAG', 'TEXT',
        'STRIPON', 'STRIPOFF', 'BLOCKSOURCE', 'LITERALSTART',
        'LITERALEND', 'LITERAL', 'LDEL', 'DOLLAR',
        'ID', 'EQUAL', 'PTR', 'LDELIF',
        'LDELFOR', 'SEMICOLON', 'INCDEC', 'TO',
        'STEP', 'LDELFOREACH', 'SPACE', 'AS',
        'APTR', 'LDELSETFILTER', 'SMARTYBLOCKCHILDPARENT', 'LDELSLASH',
        'ATTR', 'INTEGER', 'COMMA', 'OPENP',
        'CLOSEP', 'MATH', 'UNIMATH', 'ANDSYM',
        'ISIN', 'ISDIVBY', 'ISNOTDIVBY', 'ISEVEN',
        'ISNOTEVEN', 'ISEVENBY', 'ISNOTEVENBY', 'ISODD',
        'ISNOTODD', 'ISODDBY', 'ISNOTODDBY', 'INSTANCEOF',
        'QMARK', 'NOT', 'TYPECAST', 'HEX',
        'DOT', 'SINGLEQUOTESTRING', 'DOUBLECOLON', 'AT',
        'HATCH', 'OPENB', 'CLOSEB', 'EQUALS',
        'NOTEQUALS', 'GREATERTHAN', 'LESSTHAN', 'GREATEREQUAL',
        'LESSEQUAL', 'IDENTITY', 'NONEIDENTITY', 'MOD',
        'LAND', 'LOR', 'LXOR', 'QUOTE',
        'BACKTICK', 'DOLLARID', 'error', 'start',
        'template', 'template_element', 'smartytag', 'literal',
        'literal_elements', 'literal_element', 'value', 'modifierlist',
        'attributes', 'expr', 'varindexed', 'statement',
        'statements', 'optspace', 'varvar', 'foraction',
        'modparameters', 'attribute', 'ternary', 'array',
        'ifcond', 'lop', 'variable', 'function',
        'doublequoted_with_quotes', 'static_class_access', 'object', 'arrayindex',
        'indexdef', 'varvarele', 'objectchain', 'objectelement',
        'method', 'params', 'modifier', 'modparameter',
        'arrayelements', 'arrayelement', 'doublequoted', 'doublequotedcontent',
    );

    public static $yyRuleName = array(
        /*   0 */
        "start ::= template",
        /*   1 */
        "template ::= template_element",
        /*   2 */
        "template ::= template template_element",
        /*   3 */
        "template ::=",
        /*   4 */
        "template_element ::= smartytag RDEL",
        /*   5 */
        "template_element ::= COMMENT",
        /*   6 */
        "template_element ::= literal",
        /*   7 */
        "template_element ::= PHPSTARTTAG",
        /*   8 */
        "template_element ::= PHPENDTAG",
        /*   9 */
        "template_element ::= PHPENDSCRIPT",
        /*  10 */
        "template_element ::= ASPSTARTTAG",
        /*  11 */
        "template_element ::= ASPENDTAG",
        /*  12 */
        "template_element ::= XMLTAG",
        /*  13 */
        "template_element ::= TEXT",
        /*  14 */
        "template_element ::= STRIPON",
        /*  15 */
        "template_element ::= STRIPOFF",
        /*  16 */
        "template_element ::= BLOCKSOURCE",
        /*  17 */
        "literal ::= LITERALSTART LITERALEND",
        /*  18 */
        "literal ::= LITERALSTART literal_elements LITERALEND",
        /*  19 */
        "literal_elements ::= literal_elements literal_element",
        /*  20 */
        "literal_elements ::=",
        /*  21 */
        "literal_element ::= literal",
        /*  22 */
        "literal_element ::= LITERAL",
        /*  23 */
        "smartytag ::= LDEL value",
        /*  24 */
        "smartytag ::= LDEL value modifierlist attributes",
        /*  25 */
        "smartytag ::= LDEL value attributes",
        /*  26 */
        "smartytag ::= LDEL expr modifierlist attributes",
        /*  27 */
        "smartytag ::= LDEL expr attributes",
        /*  28 */
        "smartytag ::= LDEL DOLLAR ID EQUAL value",
        /*  29 */
        "smartytag ::= LDEL DOLLAR ID EQUAL expr",
        /*  30 */
        "smartytag ::= LDEL DOLLAR ID EQUAL expr attributes",
        /*  31 */
        "smartytag ::= LDEL varindexed EQUAL expr attributes",
        /*  32 */
        "smartytag ::= LDEL ID attributes",
        /*  33 */
        "smartytag ::= LDEL ID",
        /*  34 */
        "smartytag ::= LDEL ID PTR ID attributes",
        /*  35 */
        "smartytag ::= LDEL ID modifierlist attributes",
        /*  36 */
        "smartytag ::= LDEL ID PTR ID modifierlist attributes",
        /*  37 */
        "smartytag ::= LDELIF expr",
        /*  38 */
        "smartytag ::= LDELIF expr attributes",
        /*  39 */
        "smartytag ::= LDELIF statement",
        /*  40 */
        "smartytag ::= LDELIF statement attributes",
        /*  41 */
        "smartytag ::= LDELFOR statements SEMICOLON optspace expr SEMICOLON optspace DOLLAR varvar foraction attributes",
        /*  42 */
        "foraction ::= EQUAL expr",
        /*  43 */
        "foraction ::= INCDEC",
        /*  44 */
        "smartytag ::= LDELFOR statement TO expr attributes",
        /*  45 */
        "smartytag ::= LDELFOR statement TO expr STEP expr attributes",
        /*  46 */
        "smartytag ::= LDELFOREACH attributes",
        /*  47 */
        "smartytag ::= LDELFOREACH SPACE value AS DOLLAR varvar attributes",
        /*  48 */
        "smartytag ::= LDELFOREACH SPACE value AS DOLLAR varvar APTR DOLLAR varvar attributes",
        /*  49 */
        "smartytag ::= LDELFOREACH SPACE expr AS DOLLAR varvar attributes",
        /*  50 */
        "smartytag ::= LDELFOREACH SPACE expr AS DOLLAR varvar APTR DOLLAR varvar attributes",
        /*  51 */
        "smartytag ::= LDELSETFILTER ID modparameters",
        /*  52 */
        "smartytag ::= LDELSETFILTER ID modparameters modifierlist",
        /*  53 */
        "smartytag ::= LDEL SMARTYBLOCKCHILDPARENT",
        /*  54 */
        "smartytag ::= LDELSLASH ID",
        /*  55 */
        "smartytag ::= LDELSLASH ID modifierlist",
        /*  56 */
        "smartytag ::= LDELSLASH ID PTR ID",
        /*  57 */
        "smartytag ::= LDELSLASH ID PTR ID modifierlist",
        /*  58 */
        "attributes ::= attributes attribute",
        /*  59 */
        "attributes ::= attribute",
        /*  60 */
        "attributes ::=",
        /*  61 */
        "attribute ::= SPACE ID EQUAL ID",
        /*  62 */
        "attribute ::= ATTR expr",
        /*  63 */
        "attribute ::= ATTR value",
        /*  64 */
        "attribute ::= SPACE ID",
        /*  65 */
        "attribute ::= SPACE expr",
        /*  66 */
        "attribute ::= SPACE value",
        /*  67 */
        "attribute ::= SPACE INTEGER EQUAL expr",
        /*  68 */
        "statements ::= statement",
        /*  69 */
        "statements ::= statements COMMA statement",
        /*  70 */
        "statement ::= DOLLAR varvar EQUAL expr",
        /*  71 */
        "statement ::= varindexed EQUAL expr",
        /*  72 */
        "statement ::= OPENP statement CLOSEP",
        /*  73 */
        "expr ::= value",
        /*  74 */
        "expr ::= ternary",
        /*  75 */
        "expr ::= DOLLAR ID COLON ID",
        /*  76 */
        "expr ::= expr MATH value",
        /*  77 */
        "expr ::= expr UNIMATH value",
        /*  78 */
        "expr ::= expr ANDSYM value",
        /*  79 */
        "expr ::= array",
        /*  80 */
        "expr ::= expr modifierlist",
        /*  81 */
        "expr ::= expr ifcond expr",
        /*  82 */
        "expr ::= expr ISIN array",
        /*  83 */
        "expr ::= expr ISIN value",
        /*  84 */
        "expr ::= expr lop expr",
        /*  85 */
        "expr ::= expr ISDIVBY expr",
        /*  86 */
        "expr ::= expr ISNOTDIVBY expr",
        /*  87 */
        "expr ::= expr ISEVEN",
        /*  88 */
        "expr ::= expr ISNOTEVEN",
        /*  89 */
        "expr ::= expr ISEVENBY expr",
        /*  90 */
        "expr ::= expr ISNOTEVENBY expr",
        /*  91 */
        "expr ::= expr ISODD",
        /*  92 */
        "expr ::= expr ISNOTODD",
        /*  93 */
        "expr ::= expr ISODDBY expr",
        /*  94 */
        "expr ::= expr ISNOTODDBY expr",
        /*  95 */
        "expr ::= value INSTANCEOF ID",
        /*  96 */
        "expr ::= value INSTANCEOF value",
        /*  97 */
        "ternary ::= OPENP expr CLOSEP QMARK DOLLAR ID COLON expr",
        /*  98 */
        "ternary ::= OPENP expr CLOSEP QMARK expr COLON expr",
        /*  99 */
        "value ::= variable",
        /* 100 */
        "value ::= UNIMATH value",
        /* 101 */
        "value ::= NOT value",
        /* 102 */
        "value ::= TYPECAST value",
        /* 103 */
        "value ::= variable INCDEC",
        /* 104 */
        "value ::= HEX",
        /* 105 */
        "value ::= INTEGER",
        /* 106 */
        "value ::= INTEGER DOT INTEGER",
        /* 107 */
        "value ::= INTEGER DOT",
        /* 108 */
        "value ::= DOT INTEGER",
        /* 109 */
        "value ::= ID",
        /* 110 */
        "value ::= function",
        /* 111 */
        "value ::= OPENP expr CLOSEP",
        /* 112 */
        "value ::= SINGLEQUOTESTRING",
        /* 113 */
        "value ::= doublequoted_with_quotes",
        /* 114 */
        "value ::= ID DOUBLECOLON static_class_access",
        /* 115 */
        "value ::= varindexed DOUBLECOLON static_class_access",
        /* 116 */
        "value ::= smartytag RDEL",
        /* 117 */
        "value ::= value modifierlist",
        /* 118 */
        "variable ::= varindexed",
        /* 119 */
        "variable ::= DOLLAR varvar AT ID",
        /* 120 */
        "variable ::= object",
        /* 121 */
        "variable ::= HATCH ID HATCH",
        /* 122 */
        "variable ::= HATCH ID HATCH arrayindex",
        /* 123 */
        "variable ::= HATCH variable HATCH",
        /* 124 */
        "variable ::= HATCH variable HATCH arrayindex",
        /* 125 */
        "varindexed ::= DOLLAR varvar arrayindex",
        /* 126 */
        "arrayindex ::= arrayindex indexdef",
        /* 127 */
        "arrayindex ::=",
        /* 128 */
        "indexdef ::= DOT DOLLAR varvar",
        /* 129 */
        "indexdef ::= DOT DOLLAR varvar AT ID",
        /* 130 */
        "indexdef ::= DOT ID",
        /* 131 */
        "indexdef ::= DOT INTEGER",
        /* 132 */
        "indexdef ::= DOT LDEL expr RDEL",
        /* 133 */
        "indexdef ::= OPENB ID CLOSEB",
        /* 134 */
        "indexdef ::= OPENB ID DOT ID CLOSEB",
        /* 135 */
        "indexdef ::= OPENB expr CLOSEB",
        /* 136 */
        "indexdef ::= OPENB CLOSEB",
        /* 137 */
        "varvar ::= varvarele",
        /* 138 */
        "varvar ::= varvar varvarele",
        /* 139 */
        "varvarele ::= ID",
        /* 140 */
        "varvarele ::= LDEL expr RDEL",
        /* 141 */
        "object ::= varindexed objectchain",
        /* 142 */
        "objectchain ::= objectelement",
        /* 143 */
        "objectchain ::= objectchain objectelement",
        /* 144 */
        "objectelement ::= PTR ID arrayindex",
        /* 145 */
        "objectelement ::= PTR DOLLAR varvar arrayindex",
        /* 146 */
        "objectelement ::= PTR LDEL expr RDEL arrayindex",
        /* 147 */
        "objectelement ::= PTR ID LDEL expr RDEL arrayindex",
        /* 148 */
        "objectelement ::= PTR method",
        /* 149 */
        "function ::= ID OPENP params CLOSEP",
        /* 150 */
        "method ::= ID OPENP params CLOSEP",
        /* 151 */
        "method ::= DOLLAR ID OPENP params CLOSEP",
        /* 152 */
        "params ::= params COMMA expr",
        /* 153 */
        "params ::= expr",
        /* 154 */
        "params ::=",
        /* 155 */
        "modifierlist ::= modifierlist modifier modparameters",
        /* 156 */
        "modifierlist ::= modifier modparameters",
        /* 157 */
        "modifier ::= VERT AT ID",
        /* 158 */
        "modifier ::= VERT ID",
        /* 159 */
        "modparameters ::= modparameters modparameter",
        /* 160 */
        "modparameters ::=",
        /* 161 */
        "modparameter ::= COLON value",
        /* 162 */
        "modparameter ::= COLON array",
        /* 163 */
        "static_class_access ::= method",
        /* 164 */
        "static_class_access ::= method objectchain",
        /* 165 */
        "static_class_access ::= ID",
        /* 166 */
        "static_class_access ::= DOLLAR ID arrayindex",
        /* 167 */
        "static_class_access ::= DOLLAR ID arrayindex objectchain",
        /* 168 */
        "ifcond ::= EQUALS",
        /* 169 */
        "ifcond ::= NOTEQUALS",
        /* 170 */
        "ifcond ::= GREATERTHAN",
        /* 171 */
        "ifcond ::= LESSTHAN",
        /* 172 */
        "ifcond ::= GREATEREQUAL",
        /* 173 */
        "ifcond ::= LESSEQUAL",
        /* 174 */
        "ifcond ::= IDENTITY",
        /* 175 */
        "ifcond ::= NONEIDENTITY",
        /* 176 */
        "ifcond ::= MOD",
        /* 177 */
        "lop ::= LAND",
        /* 178 */
        "lop ::= LOR",
        /* 179 */
        "lop ::= LXOR",
        /* 180 */
        "array ::= OPENB arrayelements CLOSEB",
        /* 181 */
        "arrayelements ::= arrayelement",
        /* 182 */
        "arrayelements ::= arrayelements COMMA arrayelement",
        /* 183 */
        "arrayelements ::=",
        /* 184 */
        "arrayelement ::= value APTR expr",
        /* 185 */
        "arrayelement ::= ID APTR expr",
        /* 186 */
        "arrayelement ::= expr",
        /* 187 */
        "doublequoted_with_quotes ::= QUOTE QUOTE",
        /* 188 */
        "doublequoted_with_quotes ::= QUOTE doublequoted QUOTE",
        /* 189 */
        "doublequoted ::= doublequoted doublequotedcontent",
        /* 190 */
        "doublequoted ::= doublequotedcontent",
        /* 191 */
        "doublequotedcontent ::= BACKTICK variable BACKTICK",
        /* 192 */
        "doublequotedcontent ::= BACKTICK expr BACKTICK",
        /* 193 */
        "doublequotedcontent ::= DOLLARID",
        /* 194 */
        "doublequotedcontent ::= LDEL variable RDEL",
        /* 195 */
        "doublequotedcontent ::= LDEL expr RDEL",
        /* 196 */
        "doublequotedcontent ::= smartytag RDEL",
        /* 197 */
        "doublequotedcontent ::= TEXT",
        /* 198 */
        "optspace ::= SPACE",
        /* 199 */
        "optspace ::=",
    );

    public function tokenName($tokenType)
    {
        if ($tokenType === 0) {
            return 'End of Input';
        }
        if ($tokenType > 0 && $tokenType < count($this->yyTokenName)) {
            return $this->yyTokenName[$tokenType];
        } else {
            return "Unknown";
        }
    }

    public static function yy_destructor($yymajor, $yypminor)
    {
        switch ($yymajor) {
            default:
                break;   /* If no destructor action specified: do nothing */
        }
    }

    public function yy_pop_parser_stack()
    {
        if (!count($this->yystack)) {
            return;
        }
        $yytos = array_pop($this->yystack);
        if ($this->yyTraceFILE && $this->yyidx >= 0) {
            fwrite($this->yyTraceFILE,
                   $this->yyTracePrompt . 'Popping ' . $this->yyTokenName[$yytos->major] .
                   "\n");
        }
        $yymajor = $yytos->major;
        self::yy_destructor($yymajor, $yytos->minor);
        $this->yyidx --;

        return $yymajor;
    }

    public function __destruct()
    {
        while ($this->yystack !== Array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    public function yy_get_expected_tokens($token)
    {
        $state = $this->yystack[$this->yyidx]->stateno;
        $expected = self::$yyExpectedTokens[$state];
        if (in_array($token, self::$yyExpectedTokens[$state], true)) {
            return $expected;
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done ++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return array_unique($expected);
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno]['rhs'];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[$this->yyidx]->stateno,
                        self::$yyRuleInfo[$yyruleno]['lhs']);
                    if (isset(self::$yyExpectedTokens[$nextstate])) {
                        $expected = array_merge($expected, self::$yyExpectedTokens[$nextstate]);
                        if (in_array($token,
                                     self::$yyExpectedTokens[$nextstate], true)) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;

                            return array_unique($expected);
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx ++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno]['lhs'];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return array_unique($expected);
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return $expected;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;

        return array_unique($expected);
    }

    public function yy_is_expected_token($token)
    {
        if ($token === 0) {
            return true; // 0 is not part of this
        }
        $state = $this->yystack[$this->yyidx]->stateno;
        if (in_array($token, self::$yyExpectedTokens[$state], true)) {
            return true;
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done ++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return true;
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno]['rhs'];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[$this->yyidx]->stateno,
                        self::$yyRuleInfo[$yyruleno]['lhs']);
                    if (isset(self::$yyExpectedTokens[$nextstate]) &&
                        in_array($token, self::$yyExpectedTokens[$nextstate], true)
                    ) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;

                        return true;
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx ++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno]['lhs'];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        if (!$token) {
                            // end of input: this is valid
                            return true;
                        }
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return false;
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return true;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;

        return true;
    }

    public function yy_find_shift_action($iLookAhead)
    {
        $stateno = $this->yystack[$this->yyidx]->stateno;

        /* if ($this->yyidx < 0) return self::YY_NO_ACTION;  */
        if (!isset(self::$yy_shift_ofst[$stateno])) {
            // no shift actions
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_shift_ofst[$stateno];
        if ($i === self::YY_SHIFT_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
            self::$yy_lookahead[$i] != $iLookAhead
        ) {
            if (count(self::$yyFallback) && $iLookAhead < count(self::$yyFallback)
                && ($iFallback = self::$yyFallback[$iLookAhead]) != 0
            ) {
                if ($this->yyTraceFILE) {
                    fwrite($this->yyTraceFILE, $this->yyTracePrompt . "FALLBACK " .
                        $this->yyTokenName[$iLookAhead] . " => " .
                        $this->yyTokenName[$iFallback] . "\n");
                }

                return $this->yy_find_shift_action($iFallback);
            }

            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_find_reduce_action($stateno, $iLookAhead)
    {
        /* $stateno = $this->yystack[$this->yyidx]->stateno; */

        if (!isset(self::$yy_reduce_ofst[$stateno])) {
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_reduce_ofst[$stateno];
        if ($i == self::YY_REDUCE_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
            self::$yy_lookahead[$i] != $iLookAhead
        ) {
            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_shift($yyNewState, $yyMajor, $yypMinor)
    {
        $this->yyidx ++;
        if ($this->yyidx >= self::YYSTACKDEPTH) {
            $this->yyidx --;
            if ($this->yyTraceFILE) {
                fprintf($this->yyTraceFILE, "%sStack Overflow!\n", $this->yyTracePrompt);
            }
            while ($this->yyidx >= 0) {
                $this->yy_pop_parser_stack();
            }
            #line 82 "smarty_internal_templateparser.y"

            $this->internalError = true;
            $this->compiler->trigger_template_error("Stack overflow in template parser");
            #line 1721 "smarty_internal_templateparser.php"

            return;
        }
        $yytos = new TP_yyStackEntry;
        $yytos->stateno = $yyNewState;
        $yytos->major = $yyMajor;
        $yytos->minor = $yypMinor;
        array_push($this->yystack, $yytos);
        if ($this->yyTraceFILE && $this->yyidx > 0) {
            fprintf($this->yyTraceFILE, "%sShift %d\n", $this->yyTracePrompt,
                    $yyNewState);
            fprintf($this->yyTraceFILE, "%sStack:", $this->yyTracePrompt);
            for ($i = 1; $i <= $this->yyidx; $i ++) {
                fprintf($this->yyTraceFILE, " %s",
                        $this->yyTokenName[$this->yystack[$i]->major]);
            }
            fwrite($this->yyTraceFILE, "\n");
        }
    }

    public static $yyRuleInfo = array(
        array('lhs' => 83, 'rhs' => 1),
        array('lhs' => 84, 'rhs' => 1),
        array('lhs' => 84, 'rhs' => 2),
        array('lhs' => 84, 'rhs' => 0),
        array('lhs' => 85, 'rhs' => 2),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 85, 'rhs' => 1),
        array('lhs' => 87, 'rhs' => 2),
        array('lhs' => 87, 'rhs' => 3),
        array('lhs' => 88, 'rhs' => 2),
        array('lhs' => 88, 'rhs' => 0),
        array('lhs' => 89, 'rhs' => 1),
        array('lhs' => 89, 'rhs' => 1),
        array('lhs' => 86, 'rhs' => 2),
        array('lhs' => 86, 'rhs' => 4),
        array('lhs' => 86, 'rhs' => 3),
        array('lhs' => 86, 'rhs' => 4),
        array('lhs' => 86, 'rhs' => 3),
        array('lhs' => 86, 'rhs' => 5),
        array('lhs' => 86, 'rhs' => 5),
        array('lhs' => 86, 'rhs' => 6),
        array('lhs' => 86, 'rhs' => 5),
        array('lhs' => 86, 'rhs' => 3),
        array('lhs' => 86, 'rhs' => 2),
        array('lhs' => 86, 'rhs' => 5),
        array('lhs' => 86, 'rhs' => 4),
        array('lhs' => 86, 'rhs' => 6),
        array('lhs' => 86, 'rhs' => 2),
        array('lhs' => 86, 'rhs' => 3),
        array('lhs' => 86, 'rhs' => 2),
        array('lhs' => 86, 'rhs' => 3),
        array('lhs' => 86, 'rhs' => 11),
        array('lhs' => 99, 'rhs' => 2),
        array('lhs' => 99, 'rhs' => 1),
        array('lhs' => 86, 'rhs' => 5),
        array('lhs' => 86, 'rhs' => 7),
        array('lhs' => 86, 'rhs' => 2),
        array('lhs' => 86, 'rhs' => 7),
        array('lhs' => 86, 'rhs' => 10),
        array('lhs' => 86, 'rhs' => 7),
        array('lhs' => 86, 'rhs' => 10),
        array('lhs' => 86, 'rhs' => 3),
        array('lhs' => 86, 'rhs' => 4),
        array('lhs' => 86, 'rhs' => 2),
        array('lhs' => 86, 'rhs' => 2),
        array('lhs' => 86, 'rhs' => 3),
        array('lhs' => 86, 'rhs' => 4),
        array('lhs' => 86, 'rhs' => 5),
        array('lhs' => 92, 'rhs' => 2),
        array('lhs' => 92, 'rhs' => 1),
        array('lhs' => 92, 'rhs' => 0),
        array('lhs' => 101, 'rhs' => 4),
        array('lhs' => 101, 'rhs' => 2),
        array('lhs' => 101, 'rhs' => 2),
        array('lhs' => 101, 'rhs' => 2),
        array('lhs' => 101, 'rhs' => 2),
        array('lhs' => 101, 'rhs' => 2),
        array('lhs' => 101, 'rhs' => 4),
        array('lhs' => 96, 'rhs' => 1),
        array('lhs' => 96, 'rhs' => 3),
        array('lhs' => 95, 'rhs' => 4),
        array('lhs' => 95, 'rhs' => 3),
        array('lhs' => 95, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 1),
        array('lhs' => 93, 'rhs' => 1),
        array('lhs' => 93, 'rhs' => 4),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 1),
        array('lhs' => 93, 'rhs' => 2),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 2),
        array('lhs' => 93, 'rhs' => 2),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 2),
        array('lhs' => 93, 'rhs' => 2),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 93, 'rhs' => 3),
        array('lhs' => 102, 'rhs' => 8),
        array('lhs' => 102, 'rhs' => 7),
        array('lhs' => 90, 'rhs' => 1),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 90, 'rhs' => 1),
        array('lhs' => 90, 'rhs' => 1),
        array('lhs' => 90, 'rhs' => 3),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 90, 'rhs' => 1),
        array('lhs' => 90, 'rhs' => 1),
        array('lhs' => 90, 'rhs' => 3),
        array('lhs' => 90, 'rhs' => 1),
        array('lhs' => 90, 'rhs' => 1),
        array('lhs' => 90, 'rhs' => 3),
        array('lhs' => 90, 'rhs' => 3),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 90, 'rhs' => 2),
        array('lhs' => 106, 'rhs' => 1),
        array('lhs' => 106, 'rhs' => 4),
        array('lhs' => 106, 'rhs' => 1),
        array('lhs' => 106, 'rhs' => 3),
        array('lhs' => 106, 'rhs' => 4),
        array('lhs' => 106, 'rhs' => 3),
        array('lhs' => 106, 'rhs' => 4),
        array('lhs' => 94, 'rhs' => 3),
        array('lhs' => 111, 'rhs' => 2),
        array('lhs' => 111, 'rhs' => 0),
        array('lhs' => 112, 'rhs' => 3),
        array('lhs' => 112, 'rhs' => 5),
        array('lhs' => 112, 'rhs' => 2),
        array('lhs' => 112, 'rhs' => 2),
        array('lhs' => 112, 'rhs' => 4),
        array('lhs' => 112, 'rhs' => 3),
        array('lhs' => 112, 'rhs' => 5),
        array('lhs' => 112, 'rhs' => 3),
        array('lhs' => 112, 'rhs' => 2),
        array('lhs' => 98, 'rhs' => 1),
        array('lhs' => 98, 'rhs' => 2),
        array('lhs' => 113, 'rhs' => 1),
        array('lhs' => 113, 'rhs' => 3),
        array('lhs' => 110, 'rhs' => 2),
        array('lhs' => 114, 'rhs' => 1),
        array('lhs' => 114, 'rhs' => 2),
        array('lhs' => 115, 'rhs' => 3),
        array('lhs' => 115, 'rhs' => 4),
        array('lhs' => 115, 'rhs' => 5),
        array('lhs' => 115, 'rhs' => 6),
        array('lhs' => 115, 'rhs' => 2),
        array('lhs' => 107, 'rhs' => 4),
        array('lhs' => 116, 'rhs' => 4),
        array('lhs' => 116, 'rhs' => 5),
        array('lhs' => 117, 'rhs' => 3),
        array('lhs' => 117, 'rhs' => 1),
        array('lhs' => 117, 'rhs' => 0),
        array('lhs' => 91, 'rhs' => 3),
        array('lhs' => 91, 'rhs' => 2),
        array('lhs' => 118, 'rhs' => 3),
        array('lhs' => 118, 'rhs' => 2),
        array('lhs' => 100, 'rhs' => 2),
        array('lhs' => 100, 'rhs' => 0),
        array('lhs' => 119, 'rhs' => 2),
        array('lhs' => 119, 'rhs' => 2),
        array('lhs' => 109, 'rhs' => 1),
        array('lhs' => 109, 'rhs' => 2),
        array('lhs' => 109, 'rhs' => 1),
        array('lhs' => 109, 'rhs' => 3),
        array('lhs' => 109, 'rhs' => 4),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 104, 'rhs' => 1),
        array('lhs' => 105, 'rhs' => 1),
        array('lhs' => 105, 'rhs' => 1),
        array('lhs' => 105, 'rhs' => 1),
        array('lhs' => 103, 'rhs' => 3),
        array('lhs' => 120, 'rhs' => 1),
        array('lhs' => 120, 'rhs' => 3),
        array('lhs' => 120, 'rhs' => 0),
        array('lhs' => 121, 'rhs' => 3),
        array('lhs' => 121, 'rhs' => 3),
        array('lhs' => 121, 'rhs' => 1),
        array('lhs' => 108, 'rhs' => 2),
        array('lhs' => 108, 'rhs' => 3),
        array('lhs' => 122, 'rhs' => 2),
        array('lhs' => 122, 'rhs' => 1),
        array('lhs' => 123, 'rhs' => 3),
        array('lhs' => 123, 'rhs' => 3),
        array('lhs' => 123, 'rhs' => 1),
        array('lhs' => 123, 'rhs' => 3),
        array('lhs' => 123, 'rhs' => 3),
        array('lhs' => 123, 'rhs' => 2),
        array('lhs' => 123, 'rhs' => 1),
        array('lhs' => 97, 'rhs' => 1),
        array('lhs' => 97, 'rhs' => 0),
    );

    public static $yyReduceMap = array(
        0   => 0,
        1   => 1,
        2   => 2,
        4   => 4,
        5   => 5,
        6   => 6,
        7   => 7,
        8   => 8,
        9   => 9,
        10  => 10,
        11  => 11,
        12  => 12,
        13  => 13,
        14  => 14,
        15  => 15,
        16  => 16,
        17  => 17,
        20  => 17,
        199 => 17,
        18  => 18,
        72  => 18,
        19  => 19,
        100 => 19,
        102 => 19,
        103 => 19,
        126 => 19,
        164 => 19,
        21  => 21,
        22  => 21,
        43  => 21,
        65  => 21,
        66  => 21,
        73  => 21,
        74  => 21,
        79  => 21,
        99  => 21,
        104 => 21,
        105 => 21,
        110 => 21,
        112 => 21,
        113 => 21,
        120 => 21,
        137 => 21,
        163 => 21,
        165 => 21,
        181 => 21,
        186 => 21,
        198 => 21,
        23  => 23,
        24  => 24,
        25  => 25,
        27  => 25,
        26  => 26,
        28  => 28,
        29  => 28,
        30  => 30,
        31  => 31,
        32  => 32,
        33  => 33,
        34  => 34,
        35  => 35,
        36  => 36,
        37  => 37,
        38  => 38,
        40  => 38,
        39  => 39,
        41  => 41,
        42  => 42,
        44  => 44,
        45  => 45,
        46  => 46,
        47  => 47,
        49  => 47,
        48  => 48,
        50  => 48,
        51  => 51,
        52  => 52,
        53  => 53,
        54  => 54,
        55  => 55,
        56  => 56,
        57  => 57,
        58  => 58,
        59  => 59,
        68  => 59,
        153 => 59,
        157 => 59,
        161 => 59,
        162 => 59,
        60  => 60,
        154 => 60,
        160 => 60,
        61  => 61,
        62  => 62,
        63  => 62,
        64  => 64,
        67  => 67,
        69  => 69,
        70  => 70,
        71  => 70,
        75  => 75,
        76  => 76,
        77  => 76,
        78  => 76,
        80  => 80,
        117 => 80,
        81  => 81,
        84  => 81,
        95  => 81,
        82  => 82,
        83  => 83,
        85  => 85,
        86  => 86,
        87  => 87,
        92  => 87,
        88  => 88,
        91  => 88,
        89  => 89,
        94  => 89,
        90  => 90,
        93  => 90,
        96  => 96,
        97  => 97,
        98  => 98,
        101 => 101,
        106 => 106,
        107 => 107,
        108 => 108,
        109 => 109,
        111 => 111,
        114 => 114,
        115 => 115,
        116 => 116,
        118 => 118,
        119 => 119,
        121 => 121,
        122 => 122,
        123 => 123,
        124 => 124,
        125 => 125,
        127 => 127,
        183 => 127,
        128 => 128,
        129 => 129,
        130 => 130,
        131 => 131,
        132 => 132,
        135 => 132,
        133 => 133,
        134 => 134,
        136 => 136,
        138 => 138,
        139 => 139,
        140 => 140,
        141 => 141,
        142 => 142,
        143 => 143,
        144 => 144,
        145 => 145,
        146 => 146,
        147 => 147,
        148 => 148,
        149 => 149,
        150 => 150,
        151 => 151,
        152 => 152,
        155 => 155,
        156 => 156,
        158 => 158,
        159 => 159,
        166 => 166,
        167 => 167,
        168 => 168,
        169 => 169,
        170 => 170,
        171 => 171,
        172 => 172,
        173 => 173,
        174 => 174,
        175 => 175,
        176 => 176,
        177 => 177,
        178 => 178,
        179 => 179,
        180 => 180,
        182 => 182,
        184 => 184,
        185 => 185,
        187 => 187,
        188 => 188,
        189 => 189,
        190 => 190,
        191 => 191,
        192 => 191,
        194 => 191,
        193 => 193,
        195 => 195,
        196 => 196,
        197 => 197,
    );

    #line 93 "smarty_internal_templateparser.y"
    function yy_r0()
    {
        $this->_retvalue = $this->root_buffer->to_smarty_php();
    }
    #line 2150 "smarty_internal_templateparser.php"
    #line 101 "smarty_internal_templateparser.y"
    function yy_r1()
    {
        if ($this->yystack[$this->yyidx + 0]->minor != null) {
            $this->current_buffer->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        }
    }
    #line 2157 "smarty_internal_templateparser.php"
    #line 108 "smarty_internal_templateparser.y"
    function yy_r2()
    {
        if ($this->yystack[$this->yyidx + 0]->minor != null) {
            // because of possible code injection
            $this->current_buffer->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        }
    }
    #line 2165 "smarty_internal_templateparser.php"
    #line 122 "smarty_internal_templateparser.y"
    function yy_r4()
    {
        if ($this->compiler->has_code) {
            $tmp = '';
            foreach ($this->compiler->prefix_code as $code) {
                $tmp .= $code;
            }
            $this->compiler->prefix_code = array();
            $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode($tmp . $this->yystack[$this->yyidx + - 1]->minor, true));
        } else {
            $this->_retvalue = null;
        }
        $this->compiler->has_variable_string = false;
        $this->block_nesting_level = count($this->compiler->_tag_stack);
    }
    #line 2177 "smarty_internal_templateparser.php"
    #line 134 "smarty_internal_templateparser.y"
    function yy_r5()
    {
        $this->_retvalue = null;
    }
    #line 2182 "smarty_internal_templateparser.php"
    #line 139 "smarty_internal_templateparser.y"
    function yy_r6()
    {
        $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2187 "smarty_internal_templateparser.php"
    #line 144 "smarty_internal_templateparser.y"
    function yy_r7()
    {
        if (strpos($this->yystack[$this->yyidx + 0]->minor, '<s') === 0) {
            $this->lex->is_phpScript = true;
        }
        if ($this->php_handling == Smarty::PHP_PASSTHRU) {
            if ($this->lex->is_phpScript) {
                $s = addcslashes($this->yystack[$this->yyidx + 0]->minor, "'");
                $this->_retvalue = new _smarty_text($this, $s);
            } else {
                $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
            }
        } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
            $this->_retvalue = new _smarty_text($this, htmlspecialchars($this->yystack[$this->yyidx + 0]->minor, ENT_QUOTES));
        } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
            if (!($this->smarty instanceof SmartyBC)) {
                $this->compiler->trigger_template_error(self::Err3);
            }
            $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode('<?php ', true));
        } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
            $this->_retvalue = null;
        }
    }
    #line 2211 "smarty_internal_templateparser.php"
    #line 168 "smarty_internal_templateparser.y"
    function yy_r8()
    {
        if ($this->is_xml) {
            $this->compiler->tag_nocache = true;
            $this->is_xml = false;
            $save = $this->template->has_nocache_code;
            $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode("<?php echo '?>';?>\n", $this->compiler, true));
            $this->template->has_nocache_code = $save;
        } elseif ($this->php_handling == Smarty::PHP_PASSTHRU) {
            $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
        } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
            $this->_retvalue = new _smarty_text($this, htmlspecialchars('?>', ENT_QUOTES));
        } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
            $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode('?>', true));
        } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
            $this->_retvalue = null;
        }
    }
    #line 2230 "smarty_internal_templateparser.php"
    #line 186 "smarty_internal_templateparser.y"
    function yy_r9()
    {
        if (!$this->lex->is_phpScript) {
            $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
        } else {
            $this->lex->is_phpScript = false;
            if ($this->php_handling == Smarty::PHP_PASSTHRU) {
                $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
            } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
                $this->_retvalue = new _smarty_text($this, htmlspecialchars($this->yystack[$this->yyidx + 0]->minor, ENT_QUOTES));
            } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
                $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode('?>', true));
            } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
                $this->_retvalue = null;
            }
        }
    }
    #line 2248 "smarty_internal_templateparser.php"
    #line 204 "smarty_internal_templateparser.y"
    function yy_r10()
    {
        if ($this->php_handling == Smarty::PHP_PASSTHRU) {
            $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
        } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
            $this->_retvalue = new _smarty_text($this, htmlspecialchars($this->yystack[$this->yyidx + 0]->minor, ENT_QUOTES));
        } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
            if ($this->asp_tags) {
                if (!($this->smarty instanceof SmartyBC)) {
                    $this->compiler->trigger_template_error(self::Err3);
                }
                $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode('<%', true));
            } else {
                $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
            }
        } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
            if ($this->asp_tags) {
                $this->_retvalue = null;
            } else {
                $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
            }
        }
    }
    #line 2272 "smarty_internal_templateparser.php"
    #line 228 "smarty_internal_templateparser.y"
    function yy_r11()
    {
        if ($this->php_handling == Smarty::PHP_PASSTHRU) {
            $this->_retvalue = new _smarty_text($this, st);
        } elseif ($this->php_handling == Smarty::PHP_QUOTE) {
            $this->_retvalue = new _smarty_text($this, htmlspecialchars('%>', ENT_QUOTES));
        } elseif ($this->php_handling == Smarty::PHP_ALLOW) {
            if ($this->asp_tags) {
                $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode('%>', true));
            } else {
                $this->_retvalue = new _smarty_text($this, st);
            }
        } elseif ($this->php_handling == Smarty::PHP_REMOVE) {
            if ($this->asp_tags) {
                $this->_retvalue = null;
            } else {
                $this->_retvalue = new _smarty_text($this, st);
            }
        }
    }
    #line 2293 "smarty_internal_templateparser.php"
    #line 250 "smarty_internal_templateparser.y"
    function yy_r12()
    {
        $this->compiler->tag_nocache = true;
        $this->is_xml = true;
        $save = $this->template->has_nocache_code;
        $this->_retvalue = new _smarty_tag($this, $this->compiler->processNocacheCode("<?php echo '<?xml';?>", $this->compiler, true));
        $this->template->has_nocache_code = $save;
    }
    #line 2302 "smarty_internal_templateparser.php"
    #line 259 "smarty_internal_templateparser.y"
    function yy_r13()
    {
        if ($this->strip) {
            $this->_retvalue = new _smarty_text($this, preg_replace('![\t ]*[\r\n]+[\t ]*!', '', $this->yystack[$this->yyidx + 0]->minor));
        } else {
            $this->_retvalue = new _smarty_text($this, $this->yystack[$this->yyidx + 0]->minor);
        }
    }
    #line 2311 "smarty_internal_templateparser.php"
    #line 268 "smarty_internal_templateparser.y"
    function yy_r14()
    {
        $this->strip = true;
    }
    #line 2316 "smarty_internal_templateparser.php"
    #line 272 "smarty_internal_templateparser.y"
    function yy_r15()
    {
        $this->strip = false;
    }
    #line 2321 "smarty_internal_templateparser.php"
    #line 276 "smarty_internal_templateparser.y"
    function yy_r16()
    {
        if ($this->strip) {
            SMARTY_INTERNAL_COMPILE_BLOCK::blockSource($this->compiler, preg_replace('![\t ]*[\r\n]+[\t ]*!', '', $this->yystack[$this->yyidx + 0]->minor));
        } else {
            SMARTY_INTERNAL_COMPILE_BLOCK::blockSource($this->compiler, $this->yystack[$this->yyidx + 0]->minor);
        }
    }
    #line 2330 "smarty_internal_templateparser.php"
    #line 285 "smarty_internal_templateparser.y"
    function yy_r17()
    {
        $this->_retvalue = '';
    }
    #line 2335 "smarty_internal_templateparser.php"
    #line 289 "smarty_internal_templateparser.y"
    function yy_r18()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }
    #line 2340 "smarty_internal_templateparser.php"
    #line 293 "smarty_internal_templateparser.y"
    function yy_r19()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2345 "smarty_internal_templateparser.php"
    #line 301 "smarty_internal_templateparser.y"
    function yy_r21()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2350 "smarty_internal_templateparser.php"
    #line 314 "smarty_internal_templateparser.y"
    function yy_r23()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2355 "smarty_internal_templateparser.php"
    #line 318 "smarty_internal_templateparser.y"
    function yy_r24()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value' => $this->yystack[$this->yyidx + - 2]->minor, 'modifierlist' => $this->yystack[$this->yyidx + - 1]->minor));
    }
    #line 2360 "smarty_internal_templateparser.php"
    #line 322 "smarty_internal_templateparser.y"
    function yy_r25()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value' => $this->yystack[$this->yyidx + - 1]->minor));
    }
    #line 2365 "smarty_internal_templateparser.php"
    #line 326 "smarty_internal_templateparser.y"
    function yy_r26()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value' => $this->yystack[$this->yyidx + - 2]->minor, 'modifierlist' => $this->yystack[$this->yyidx + - 1]->minor));
    }
    #line 2370 "smarty_internal_templateparser.php"
    #line 339 "smarty_internal_templateparser.y"
    function yy_r28()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array(array('value' => $this->yystack[$this->yyidx + 0]->minor), array('var' => "'" . $this->yystack[$this->yyidx + - 2]->minor . "'")));
    }
    #line 2375 "smarty_internal_templateparser.php"
    #line 347 "smarty_internal_templateparser.y"
    function yy_r30()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array_merge(array(array('value' => $this->yystack[$this->yyidx + - 1]->minor), array('var' => "'" . $this->yystack[$this->yyidx + - 3]->minor . "'")), $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2380 "smarty_internal_templateparser.php"
    #line 351 "smarty_internal_templateparser.y"
    function yy_r31()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array_merge(array(array('value' => $this->yystack[$this->yyidx + - 1]->minor), array('var' => $this->yystack[$this->yyidx + - 3]->minor['var'])), $this->yystack[$this->yyidx + 0]->minor), array('smarty_internal_index' => $this->yystack[$this->yyidx + - 3]->minor['smarty_internal_index']));
    }
    #line 2385 "smarty_internal_templateparser.php"
    #line 356 "smarty_internal_templateparser.y"
    function yy_r32()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2390 "smarty_internal_templateparser.php"
    #line 360 "smarty_internal_templateparser.y"
    function yy_r33()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor, array());
    }
    #line 2395 "smarty_internal_templateparser.php"
    #line 365 "smarty_internal_templateparser.y"
    function yy_r34()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 3]->minor, $this->yystack[$this->yyidx + 0]->minor, array('object_method' => $this->yystack[$this->yyidx + - 1]->minor));
    }
    #line 2400 "smarty_internal_templateparser.php"
    #line 370 "smarty_internal_templateparser.y"
    function yy_r35()
    {
        $this->_retvalue = '<?php ob_start();?>' . $this->compiler->compileTag($this->yystack[$this->yyidx + - 2]->minor, $this->yystack[$this->yyidx + 0]->minor) . '<?php echo ';
        $this->_retvalue .= $this->compiler->compileTag('private_modifier', array(), array('modifierlist' => $this->yystack[$this->yyidx + - 1]->minor, 'value' => 'ob_get_clean()')) . '?>';
    }
    #line 2406 "smarty_internal_templateparser.php"
    #line 376 "smarty_internal_templateparser.y"
    function yy_r36()
    {
        $this->_retvalue = '<?php ob_start();?>' . $this->compiler->compileTag($this->yystack[$this->yyidx + - 4]->minor, $this->yystack[$this->yyidx + 0]->minor, array('object_method' => $this->yystack[$this->yyidx + - 2]->minor)) . '<?php echo ';
        $this->_retvalue .= $this->compiler->compileTag('private_modifier', array(), array('modifierlist' => $this->yystack[$this->yyidx + - 1]->minor, 'value' => 'ob_get_clean()')) . '?>';
    }
    #line 2412 "smarty_internal_templateparser.php"
    #line 382 "smarty_internal_templateparser.y"
    function yy_r37()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 1]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, array(), array('if condition' => $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2418 "smarty_internal_templateparser.php"
    #line 387 "smarty_internal_templateparser.y"
    function yy_r38()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 2]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, $this->yystack[$this->yyidx + 0]->minor, array('if condition' => $this->yystack[$this->yyidx + - 1]->minor));
    }
    #line 2424 "smarty_internal_templateparser.php"
    #line 392 "smarty_internal_templateparser.y"
    function yy_r39()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 1]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, array(), array('if condition' => $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2430 "smarty_internal_templateparser.php"
    #line 403 "smarty_internal_templateparser.y"
    function yy_r41()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 9]->minor), array('ifexp' => $this->yystack[$this->yyidx + - 6]->minor), array('var' => $this->yystack[$this->yyidx + - 2]->minor), array('step' => $this->yystack[$this->yyidx + - 1]->minor))), 1);
    }
    #line 2435 "smarty_internal_templateparser.php"
    #line 407 "smarty_internal_templateparser.y"
    function yy_r42()
    {
        $this->_retvalue = '=' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2440 "smarty_internal_templateparser.php"
    #line 415 "smarty_internal_templateparser.y"
    function yy_r44()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 3]->minor), array('to' => $this->yystack[$this->yyidx + - 1]->minor))), 0);
    }
    #line 2445 "smarty_internal_templateparser.php"
    #line 419 "smarty_internal_templateparser.y"
    function yy_r45()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 5]->minor), array('to' => $this->yystack[$this->yyidx + - 3]->minor), array('step' => $this->yystack[$this->yyidx + - 1]->minor))), 0);
    }
    #line 2450 "smarty_internal_templateparser.php"
    #line 424 "smarty_internal_templateparser.y"
    function yy_r46()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2455 "smarty_internal_templateparser.php"
    #line 429 "smarty_internal_templateparser.y"
    function yy_r47()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('from' => $this->yystack[$this->yyidx + - 4]->minor), array('item' => $this->yystack[$this->yyidx + - 1]->minor))));
    }
    #line 2460 "smarty_internal_templateparser.php"
    #line 433 "smarty_internal_templateparser.y"
    function yy_r48()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('from' => $this->yystack[$this->yyidx + - 7]->minor), array('item' => $this->yystack[$this->yyidx + - 1]->minor), array('key' => $this->yystack[$this->yyidx + - 4]->minor))));
    }
    #line 2465 "smarty_internal_templateparser.php"
    #line 446 "smarty_internal_templateparser.y"
    function yy_r51()
    {
        $this->_retvalue = $this->compiler->compileTag('setfilter', array(), array('modifier_list' => array(array_merge(array($this->yystack[$this->yyidx + - 1]->minor), $this->yystack[$this->yyidx + 0]->minor))));
    }
    #line 2470 "smarty_internal_templateparser.php"
    #line 450 "smarty_internal_templateparser.y"
    function yy_r52()
    {
        $this->_retvalue = $this->compiler->compileTag('setfilter', array(), array('modifier_list' => array_merge(array(array_merge(array($this->yystack[$this->yyidx + - 2]->minor), $this->yystack[$this->yyidx + - 1]->minor)), $this->yystack[$this->yyidx + 0]->minor)));
    }
    #line 2475 "smarty_internal_templateparser.php"
    #line 455 "smarty_internal_templateparser.y"
    function yy_r53()
    {
        $j = strrpos($this->yystack[$this->yyidx + 0]->minor, '.');
        if ($this->yystack[$this->yyidx + 0]->minor[$j + 1] == 'c') {
            // {$smarty.block.child}
            $this->_retvalue = SMARTY_INTERNAL_COMPILE_BLOCK::compileChildBlock($this->compiler);
        } else {
            // {$smarty.block.parent}
            $this->_retvalue = SMARTY_INTERNAL_COMPILE_BLOCK::compileParentBlock($this->compiler);
        }
    }
    #line 2487 "smarty_internal_templateparser.php"
    #line 468 "smarty_internal_templateparser.y"
    function yy_r54()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor . 'close', array());
    }
    #line 2492 "smarty_internal_templateparser.php"
    #line 472 "smarty_internal_templateparser.y"
    function yy_r55()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 1]->minor . 'close', array(), array('modifier_list' => $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2497 "smarty_internal_templateparser.php"
    #line 477 "smarty_internal_templateparser.y"
    function yy_r56()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 2]->minor . 'close', array(), array('object_method' => $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2502 "smarty_internal_templateparser.php"
    #line 481 "smarty_internal_templateparser.y"
    function yy_r57()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 3]->minor . 'close', array(), array('object_method' => $this->yystack[$this->yyidx + - 1]->minor, 'modifier_list' => $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2507 "smarty_internal_templateparser.php"
    #line 489 "smarty_internal_templateparser.y"
    function yy_r58()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
        $this->_retvalue[] = $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2513 "smarty_internal_templateparser.php"
    #line 495 "smarty_internal_templateparser.y"
    function yy_r59()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2518 "smarty_internal_templateparser.php"
    #line 500 "smarty_internal_templateparser.y"
    function yy_r60()
    {
        $this->_retvalue = array();
    }
    #line 2523 "smarty_internal_templateparser.php"
    #line 505 "smarty_internal_templateparser.y"
    function yy_r61()
    {
        if (preg_match('~^true$~i', $this->yystack[$this->yyidx + 0]->minor)) {
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => 'true');
        } elseif (preg_match('~^false$~i', $this->yystack[$this->yyidx + 0]->minor)) {
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => 'false');
        } elseif (preg_match('~^null$~i', $this->yystack[$this->yyidx + 0]->minor)) {
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => 'null');
        } else {
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => "'" . $this->yystack[$this->yyidx + 0]->minor . "'");
        }
    }
    #line 2536 "smarty_internal_templateparser.php"
    #line 517 "smarty_internal_templateparser.y"
    function yy_r62()
    {
        $this->_retvalue = array(trim($this->yystack[$this->yyidx + - 1]->minor, " =\n\r\t") => $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2541 "smarty_internal_templateparser.php"
    #line 525 "smarty_internal_templateparser.y"
    function yy_r64()
    {
        $this->_retvalue = "'" . $this->yystack[$this->yyidx + 0]->minor . "'";
    }
    #line 2546 "smarty_internal_templateparser.php"
    #line 537 "smarty_internal_templateparser.y"
    function yy_r67()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2551 "smarty_internal_templateparser.php"
    #line 550 "smarty_internal_templateparser.y"
    function yy_r69()
    {
        $this->yystack[$this->yyidx + - 2]->minor[] = $this->yystack[$this->yyidx + 0]->minor;
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor;
    }
    #line 2557 "smarty_internal_templateparser.php"
    #line 555 "smarty_internal_templateparser.y"
    function yy_r70()
    {
        $this->_retvalue = array('var' => $this->yystack[$this->yyidx + - 2]->minor, 'value' => $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2562 "smarty_internal_templateparser.php"
    #line 583 "smarty_internal_templateparser.y"
    function yy_r75()
    {
        $this->_retvalue = '$_smarty_tpl->getStreamVariable(\'' . $this->yystack[$this->yyidx + - 2]->minor . '://' . $this->yystack[$this->yyidx + 0]->minor . '\')';
    }
    #line 2567 "smarty_internal_templateparser.php"
    #line 588 "smarty_internal_templateparser.y"
    function yy_r76()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . trim($this->yystack[$this->yyidx + - 1]->minor) . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2572 "smarty_internal_templateparser.php"
    #line 607 "smarty_internal_templateparser.y"
    function yy_r80()
    {
        $this->_retvalue = $this->compiler->compileTag('private_modifier', array(), array('value' => $this->yystack[$this->yyidx + - 1]->minor, 'modifierlist' => $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2577 "smarty_internal_templateparser.php"
    #line 613 "smarty_internal_templateparser.y"
    function yy_r81()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2582 "smarty_internal_templateparser.php"
    #line 617 "smarty_internal_templateparser.y"
    function yy_r82()
    {
        $this->_retvalue = 'in_array(' . $this->yystack[$this->yyidx + - 2]->minor . ',' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }
    #line 2587 "smarty_internal_templateparser.php"
    #line 621 "smarty_internal_templateparser.y"
    function yy_r83()
    {
        $this->_retvalue = 'in_array(' . $this->yystack[$this->yyidx + - 2]->minor . ',(array)' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }
    #line 2592 "smarty_internal_templateparser.php"
    #line 629 "smarty_internal_templateparser.y"
    function yy_r85()
    {
        $this->_retvalue = '!(' . $this->yystack[$this->yyidx + - 2]->minor . ' % ' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }
    #line 2597 "smarty_internal_templateparser.php"
    #line 633 "smarty_internal_templateparser.y"
    function yy_r86()
    {
        $this->_retvalue = '(' . $this->yystack[$this->yyidx + - 2]->minor . ' % ' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }
    #line 2602 "smarty_internal_templateparser.php"
    #line 637 "smarty_internal_templateparser.y"
    function yy_r87()
    {
        $this->_retvalue = '!(1 & ' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }
    #line 2607 "smarty_internal_templateparser.php"
    #line 641 "smarty_internal_templateparser.y"
    function yy_r88()
    {
        $this->_retvalue = '(1 & ' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }
    #line 2612 "smarty_internal_templateparser.php"
    #line 645 "smarty_internal_templateparser.y"
    function yy_r89()
    {
        $this->_retvalue = '!(1 & ' . $this->yystack[$this->yyidx + - 2]->minor . ' / ' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }
    #line 2617 "smarty_internal_templateparser.php"
    #line 649 "smarty_internal_templateparser.y"
    function yy_r90()
    {
        $this->_retvalue = '(1 & ' . $this->yystack[$this->yyidx + - 2]->minor . ' / ' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }
    #line 2622 "smarty_internal_templateparser.php"
    #line 673 "smarty_internal_templateparser.y"
    function yy_r96()
    {
        self::$prefix_number ++;
        $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . '=' . $this->yystack[$this->yyidx + 0]->minor . ';?>';
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + - 1]->minor . '$_tmp' . self::$prefix_number;
    }
    #line 2629 "smarty_internal_templateparser.php"
    #line 682 "smarty_internal_templateparser.y"
    function yy_r97()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 6]->minor . ' ? ' . $this->compileVariable("'" . $this->yystack[$this->yyidx + - 2]->minor . "'") . ' : ' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2634 "smarty_internal_templateparser.php"
    #line 686 "smarty_internal_templateparser.y"
    function yy_r98()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 5]->minor . ' ? ' . $this->yystack[$this->yyidx + - 2]->minor . ' : ' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2639 "smarty_internal_templateparser.php"
    #line 701 "smarty_internal_templateparser.y"
    function yy_r101()
    {
        $this->_retvalue = '!' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2644 "smarty_internal_templateparser.php"
    #line 722 "smarty_internal_templateparser.y"
    function yy_r106()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '.' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2649 "smarty_internal_templateparser.php"
    #line 726 "smarty_internal_templateparser.y"
    function yy_r107()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . '.';
    }
    #line 2654 "smarty_internal_templateparser.php"
    #line 730 "smarty_internal_templateparser.y"
    function yy_r108()
    {
        $this->_retvalue = '.' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2659 "smarty_internal_templateparser.php"
    #line 735 "smarty_internal_templateparser.y"
    function yy_r109()
    {
        if (preg_match('~^true$~i', $this->yystack[$this->yyidx + 0]->minor)) {
            $this->_retvalue = 'true';
        } elseif (preg_match('~^false$~i', $this->yystack[$this->yyidx + 0]->minor)) {
            $this->_retvalue = 'false';
        } elseif (preg_match('~^null$~i', $this->yystack[$this->yyidx + 0]->minor)) {
            $this->_retvalue = 'null';
        } else {
            $this->_retvalue = "'" . $this->yystack[$this->yyidx + 0]->minor . "'";
        }
    }
    #line 2672 "smarty_internal_templateparser.php"
    #line 753 "smarty_internal_templateparser.y"
    function yy_r111()
    {
        $this->_retvalue = "(" . $this->yystack[$this->yyidx + - 1]->minor . ")";
    }
    #line 2677 "smarty_internal_templateparser.php"
    #line 768 "smarty_internal_templateparser.y"
    function yy_r114()
    {
        if (!$this->security || isset($this->smarty->registered_classes[$this->yystack[$this->yyidx + - 2]->minor]) || $this->smarty->security_policy->isTrustedStaticClass($this->yystack[$this->yyidx + - 2]->minor, $this->compiler)) {
            if (isset($this->smarty->registered_classes[$this->yystack[$this->yyidx + - 2]->minor])) {
                $this->_retvalue = $this->smarty->registered_classes[$this->yystack[$this->yyidx + - 2]->minor] . '::' . $this->yystack[$this->yyidx + 0]->minor;
            } else {
                $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '::' . $this->yystack[$this->yyidx + 0]->minor;
            }
        } else {
            $this->compiler->trigger_template_error("static class '" . $this->yystack[$this->yyidx + - 2]->minor . "' is undefined or not allowed by security setting");
        }
    }
    #line 2690 "smarty_internal_templateparser.php"
    #line 780 "smarty_internal_templateparser.y"
    function yy_r115()
    {
        if ($this->yystack[$this->yyidx + - 2]->minor['var'] == '\'smarty\'') {
            $this->_retvalue = $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + - 2]->minor['smarty_internal_index']) . '::' . $this->yystack[$this->yyidx + 0]->minor;
        } else {
            $this->_retvalue = $this->compileVariable($this->yystack[$this->yyidx + - 2]->minor['var']) . $this->yystack[$this->yyidx + - 2]->minor['smarty_internal_index'] . '::' . $this->yystack[$this->yyidx + 0]->minor;
        }
    }
    #line 2699 "smarty_internal_templateparser.php"
    #line 789 "smarty_internal_templateparser.y"
    function yy_r116()
    {
        self::$prefix_number ++;
        $this->compiler->prefix_code[] = '<?php ob_start();?>' . $this->yystack[$this->yyidx + - 1]->minor . '<?php $_tmp' . self::$prefix_number . '=ob_get_clean();?>';
        $this->_retvalue = '$_tmp' . self::$prefix_number;
    }
    #line 2706 "smarty_internal_templateparser.php"
    #line 804 "smarty_internal_templateparser.y"
    function yy_r118()
    {
        if ($this->yystack[$this->yyidx + 0]->minor['var'] == '\'smarty\'') {
            $smarty_var = $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index']);
            $this->_retvalue = $smarty_var;
        } else {
            // used for array reset,next,prev,end,current 
            $this->last_variable = $this->yystack[$this->yyidx + 0]->minor['var'];
            $this->last_index = $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
            $this->_retvalue = $this->compileVariable($this->yystack[$this->yyidx + 0]->minor['var']) . $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
        }
    }
    #line 2719 "smarty_internal_templateparser.php"
    #line 817 "smarty_internal_templateparser.y"
    function yy_r119()
    {
        $this->_retvalue = '$_smarty_tpl->tpl_vars[' . $this->yystack[$this->yyidx + - 2]->minor . ']->' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2724 "smarty_internal_templateparser.php"
    #line 827 "smarty_internal_templateparser.y"
    function yy_r121()
    {
        $this->_retvalue = '$_smarty_tpl->getConfigVariable(\'' . $this->yystack[$this->yyidx + - 1]->minor . '\')';
    }
    #line 2729 "smarty_internal_templateparser.php"
    #line 831 "smarty_internal_templateparser.y"
    function yy_r122()
    {
        $this->_retvalue = '(is_array($tmp = $_smarty_tpl->getConfigVariable(\'' . $this->yystack[$this->yyidx + - 2]->minor . '\')) ? $tmp' . $this->yystack[$this->yyidx + 0]->minor . ' :null)';
    }
    #line 2734 "smarty_internal_templateparser.php"
    #line 835 "smarty_internal_templateparser.y"
    function yy_r123()
    {
        $this->_retvalue = '$_smarty_tpl->getConfigVariable(' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }
    #line 2739 "smarty_internal_templateparser.php"
    #line 839 "smarty_internal_templateparser.y"
    function yy_r124()
    {
        $this->_retvalue = '(is_array($tmp = $_smarty_tpl->getConfigVariable(' . $this->yystack[$this->yyidx + - 2]->minor . ')) ? $tmp' . $this->yystack[$this->yyidx + 0]->minor . ' : null)';
    }
    #line 2744 "smarty_internal_templateparser.php"
    #line 843 "smarty_internal_templateparser.y"
    function yy_r125()
    {
        $this->_retvalue = array('var' => $this->yystack[$this->yyidx + - 1]->minor, 'smarty_internal_index' => $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2749 "smarty_internal_templateparser.php"
    #line 856 "smarty_internal_templateparser.y"
    function yy_r127()
    {
        return;
    }
    #line 2754 "smarty_internal_templateparser.php"
    #line 862 "smarty_internal_templateparser.y"
    function yy_r128()
    {
        $this->_retvalue = '[' . $this->compileVariable($this->yystack[$this->yyidx + 0]->minor) . ']';
    }
    #line 2759 "smarty_internal_templateparser.php"
    #line 866 "smarty_internal_templateparser.y"
    function yy_r129()
    {
        $this->_retvalue = '[' . $this->compileVariable($this->yystack[$this->yyidx + - 2]->minor) . '->' . $this->yystack[$this->yyidx + 0]->minor . ']';
    }
    #line 2764 "smarty_internal_templateparser.php"
    #line 870 "smarty_internal_templateparser.y"
    function yy_r130()
    {
        $this->_retvalue = "['" . $this->yystack[$this->yyidx + 0]->minor . "']";
    }
    #line 2769 "smarty_internal_templateparser.php"
    #line 874 "smarty_internal_templateparser.y"
    function yy_r131()
    {
        $this->_retvalue = "[" . $this->yystack[$this->yyidx + 0]->minor . "]";
    }
    #line 2774 "smarty_internal_templateparser.php"
    #line 878 "smarty_internal_templateparser.y"
    function yy_r132()
    {
        $this->_retvalue = "[" . $this->yystack[$this->yyidx + - 1]->minor . "]";
    }
    #line 2779 "smarty_internal_templateparser.php"
    #line 883 "smarty_internal_templateparser.y"
    function yy_r133()
    {
        $this->_retvalue = '[' . $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' . $this->yystack[$this->yyidx + - 1]->minor . '\'][\'index\']') . ']';
    }
    #line 2784 "smarty_internal_templateparser.php"
    #line 887 "smarty_internal_templateparser.y"
    function yy_r134()
    {
        $this->_retvalue = '[' . $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' . $this->yystack[$this->yyidx + - 3]->minor . '\'][\'' . $this->yystack[$this->yyidx + - 1]->minor . '\']') . ']';
    }
    #line 2789 "smarty_internal_templateparser.php"
    #line 897 "smarty_internal_templateparser.y"
    function yy_r136()
    {
        $this->_retvalue = '[]';
    }
    #line 2794 "smarty_internal_templateparser.php"
    #line 910 "smarty_internal_templateparser.y"
    function yy_r138()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . '.' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2799 "smarty_internal_templateparser.php"
    #line 915 "smarty_internal_templateparser.y"
    function yy_r139()
    {
        $this->_retvalue = '\'' . $this->yystack[$this->yyidx + 0]->minor . '\'';
    }
    #line 2804 "smarty_internal_templateparser.php"
    #line 920 "smarty_internal_templateparser.y"
    function yy_r140()
    {
        $this->_retvalue = '(' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }
    #line 2809 "smarty_internal_templateparser.php"
    #line 927 "smarty_internal_templateparser.y"
    function yy_r141()
    {
        if ($this->yystack[$this->yyidx + - 1]->minor['var'] == '\'smarty\'') {
            $this->_retvalue = $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + - 1]->minor['smarty_internal_index']) . $this->yystack[$this->yyidx + 0]->minor;
        } else {
            $this->_retvalue = $this->compileVariable($this->yystack[$this->yyidx + - 1]->minor['var']) . $this->yystack[$this->yyidx + - 1]->minor['smarty_internal_index'] . $this->yystack[$this->yyidx + 0]->minor;
        }
    }
    #line 2818 "smarty_internal_templateparser.php"
    #line 936 "smarty_internal_templateparser.y"
    function yy_r142()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2823 "smarty_internal_templateparser.php"
    #line 941 "smarty_internal_templateparser.y"
    function yy_r143()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2828 "smarty_internal_templateparser.php"
    #line 946 "smarty_internal_templateparser.y"
    function yy_r144()
    {
        if ($this->security && substr($this->yystack[$this->yyidx + - 1]->minor, 0, 1) == '_') {
            $this->compiler->trigger_template_error(self::Err1);
        }
        $this->_retvalue = '->' . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2836 "smarty_internal_templateparser.php"
    #line 953 "smarty_internal_templateparser.y"
    function yy_r145()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{' . $this->compileVariable($this->yystack[$this->yyidx + - 1]->minor) . $this->yystack[$this->yyidx + 0]->minor . '}';
    }
    #line 2844 "smarty_internal_templateparser.php"
    #line 960 "smarty_internal_templateparser.y"
    function yy_r146()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{' . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + 0]->minor . '}';
    }
    #line 2852 "smarty_internal_templateparser.php"
    #line 967 "smarty_internal_templateparser.y"
    function yy_r147()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{\'' . $this->yystack[$this->yyidx + - 4]->minor . '\'.' . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + 0]->minor . '}';
    }
    #line 2860 "smarty_internal_templateparser.php"
    #line 975 "smarty_internal_templateparser.y"
    function yy_r148()
    {
        $this->_retvalue = '->' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2865 "smarty_internal_templateparser.php"
    #line 983 "smarty_internal_templateparser.y"
    function yy_r149()
    {
        if (!$this->security || $this->smarty->security_policy->isTrustedPhpFunction($this->yystack[$this->yyidx + - 3]->minor, $this->compiler)) {
            if (strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'isset') === 0 || strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'empty') === 0 || strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'array') === 0 || is_callable($this->yystack[$this->yyidx + - 3]->minor)) {
                $func_name = strtolower($this->yystack[$this->yyidx + - 3]->minor);
                if ($func_name == 'isset') {
                    if (count($this->yystack[$this->yyidx + - 1]->minor) == 0) {
                        $this->compiler->trigger_template_error('Illegal number of paramer in "isset()"');
                    }
                    $par = implode(',', $this->yystack[$this->yyidx + - 1]->minor);
                    if (strncasecmp($par, '$_smarty_tpl->getConfigVariable', strlen('$_smarty_tpl->getConfigVariable')) === 0) {
                        self::$prefix_number ++;
                        $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . '=' . str_replace(')', ', false)', $par) . ';?>';
                        $isset_par = '$_tmp' . self::$prefix_number;
                    } else {
                        $isset_par = str_replace("')->value", "',null,true,false)->value", $par);
                    }
                    $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . $isset_par . ")";
                } elseif (in_array($func_name, array('empty', 'reset', 'current', 'end', 'prev', 'next'))) {
                    if (count($this->yystack[$this->yyidx + - 1]->minor) != 1) {
                        $this->compiler->trigger_template_error('Illegal number of paramer in "empty()"');
                    }
                    if ($func_name == 'empty') {
                        $this->_retvalue = $func_name . '(' . str_replace("')->value", "',null,true,false)->value", $this->yystack[$this->yyidx + - 1]->minor[0]) . ')';
                    } else {
                        $this->_retvalue = $func_name . '(' . $this->yystack[$this->yyidx + - 1]->minor[0] . ')';
                    }
                } else {
                    $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ")";
                }
            } else {
                $this->compiler->trigger_template_error("unknown function \"" . $this->yystack[$this->yyidx + - 3]->minor . "\"");
            }
        }
    }
    #line 2901 "smarty_internal_templateparser.php"
    #line 1021 "smarty_internal_templateparser.y"
    function yy_r150()
    {
        if ($this->security && substr($this->yystack[$this->yyidx + - 3]->minor, 0, 1) == '_') {
            $this->compiler->trigger_template_error(self::Err1);
        }
        $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ")";
    }
    #line 2909 "smarty_internal_templateparser.php"
    #line 1028 "smarty_internal_templateparser.y"
    function yy_r151()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        self::$prefix_number ++;
        $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . '=' . $this->compileVariable("'" . $this->yystack[$this->yyidx + - 3]->minor . "'") . ';?>';
        $this->_retvalue = '$_tmp' . self::$prefix_number . '(' . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ')';
    }
    #line 2919 "smarty_internal_templateparser.php"
    #line 1039 "smarty_internal_templateparser.y"
    function yy_r152()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 2]->minor, array($this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2924 "smarty_internal_templateparser.php"
    #line 1056 "smarty_internal_templateparser.y"
    function yy_r155()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 2]->minor, array(array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor)));
    }
    #line 2929 "smarty_internal_templateparser.php"
    #line 1060 "smarty_internal_templateparser.y"
    function yy_r156()
    {
        $this->_retvalue = array(array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor));
    }
    #line 2934 "smarty_internal_templateparser.php"
    #line 1068 "smarty_internal_templateparser.y"
    function yy_r158()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2939 "smarty_internal_templateparser.php"
    #line 1076 "smarty_internal_templateparser.y"
    function yy_r159()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 2944 "smarty_internal_templateparser.php"
    #line 1110 "smarty_internal_templateparser.y"
    function yy_r166()
    {
        $this->_retvalue = '$' . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2949 "smarty_internal_templateparser.php"
    #line 1115 "smarty_internal_templateparser.y"
    function yy_r167()
    {
        $this->_retvalue = '$' . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 2954 "smarty_internal_templateparser.php"
    #line 1121 "smarty_internal_templateparser.y"
    function yy_r168()
    {
        $this->_retvalue = '==';
    }
    #line 2959 "smarty_internal_templateparser.php"
    #line 1125 "smarty_internal_templateparser.y"
    function yy_r169()
    {
        $this->_retvalue = '!=';
    }
    #line 2964 "smarty_internal_templateparser.php"
    #line 1129 "smarty_internal_templateparser.y"
    function yy_r170()
    {
        $this->_retvalue = '>';
    }
    #line 2969 "smarty_internal_templateparser.php"
    #line 1133 "smarty_internal_templateparser.y"
    function yy_r171()
    {
        $this->_retvalue = '<';
    }
    #line 2974 "smarty_internal_templateparser.php"
    #line 1137 "smarty_internal_templateparser.y"
    function yy_r172()
    {
        $this->_retvalue = '>=';
    }
    #line 2979 "smarty_internal_templateparser.php"
    #line 1141 "smarty_internal_templateparser.y"
    function yy_r173()
    {
        $this->_retvalue = '<=';
    }
    #line 2984 "smarty_internal_templateparser.php"
    #line 1145 "smarty_internal_templateparser.y"
    function yy_r174()
    {
        $this->_retvalue = '===';
    }
    #line 2989 "smarty_internal_templateparser.php"
    #line 1149 "smarty_internal_templateparser.y"
    function yy_r175()
    {
        $this->_retvalue = '!==';
    }
    #line 2994 "smarty_internal_templateparser.php"
    #line 1153 "smarty_internal_templateparser.y"
    function yy_r176()
    {
        $this->_retvalue = '%';
    }
    #line 2999 "smarty_internal_templateparser.php"
    #line 1157 "smarty_internal_templateparser.y"
    function yy_r177()
    {
        $this->_retvalue = '&&';
    }
    #line 3004 "smarty_internal_templateparser.php"
    #line 1161 "smarty_internal_templateparser.y"
    function yy_r178()
    {
        $this->_retvalue = '||';
    }
    #line 3009 "smarty_internal_templateparser.php"
    #line 1165 "smarty_internal_templateparser.y"
    function yy_r179()
    {
        $this->_retvalue = ' XOR ';
    }
    #line 3014 "smarty_internal_templateparser.php"
    #line 1172 "smarty_internal_templateparser.y"
    function yy_r180()
    {
        $this->_retvalue = 'array(' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }
    #line 3019 "smarty_internal_templateparser.php"
    #line 1180 "smarty_internal_templateparser.y"
    function yy_r182()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . ',' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 3024 "smarty_internal_templateparser.php"
    #line 1188 "smarty_internal_templateparser.y"
    function yy_r184()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '=>' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 3029 "smarty_internal_templateparser.php"
    #line 1192 "smarty_internal_templateparser.y"
    function yy_r185()
    {
        $this->_retvalue = '\'' . $this->yystack[$this->yyidx + - 2]->minor . '\'=>' . $this->yystack[$this->yyidx + 0]->minor;
    }
    #line 3034 "smarty_internal_templateparser.php"
    #line 1204 "smarty_internal_templateparser.y"
    function yy_r187()
    {
        $this->_retvalue = "''";
    }
    #line 3039 "smarty_internal_templateparser.php"
    #line 1208 "smarty_internal_templateparser.y"
    function yy_r188()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor->to_smarty_php();
    }
    #line 3044 "smarty_internal_templateparser.php"
    #line 1213 "smarty_internal_templateparser.y"
    function yy_r189()
    {
        $this->yystack[$this->yyidx + - 1]->minor->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }
    #line 3050 "smarty_internal_templateparser.php"
    #line 1218 "smarty_internal_templateparser.y"
    function yy_r190()
    {
        $this->_retvalue = new _smarty_doublequoted($this, $this->yystack[$this->yyidx + 0]->minor);
    }
    #line 3055 "smarty_internal_templateparser.php"
    #line 1222 "smarty_internal_templateparser.y"
    function yy_r191()
    {
        $this->_retvalue = new _smarty_code($this, '(string)' . $this->yystack[$this->yyidx + - 1]->minor);
    }
    #line 3060 "smarty_internal_templateparser.php"
    #line 1230 "smarty_internal_templateparser.y"
    function yy_r193()
    {
        $this->_retvalue = new _smarty_code($this, '(string)$_smarty_tpl->tpl_vars[\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\']->value');
    }
    #line 3065 "smarty_internal_templateparser.php"
    #line 1238 "smarty_internal_templateparser.y"
    function yy_r195()
    {
        $this->_retvalue = new _smarty_code($this, '(string)(' . $this->yystack[$this->yyidx + - 1]->minor . ')');
    }
    #line 3070 "smarty_internal_templateparser.php"
    #line 1242 "smarty_internal_templateparser.y"
    function yy_r196()
    {
        $this->_retvalue = new _smarty_tag($this, $this->yystack[$this->yyidx + - 1]->minor);
    }
    #line 3075 "smarty_internal_templateparser.php"
    #line 1246 "smarty_internal_templateparser.y"
    function yy_r197()
    {
        $this->_retvalue = new _smarty_dq_content($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 3080 "smarty_internal_templateparser.php"

    private $_retvalue;

    public function yy_reduce($yyruleno)
    {
        $yymsp = $this->yystack[$this->yyidx];
        if ($this->yyTraceFILE && $yyruleno >= 0
            && $yyruleno < count(self::$yyRuleName)
        ) {
            fprintf($this->yyTraceFILE, "%sReduce (%d) [%s].\n",
                    $this->yyTracePrompt, $yyruleno,
                    self::$yyRuleName[$yyruleno]);
        }

        $this->_retvalue = $yy_lefthand_side = null;
        if (array_key_exists($yyruleno, self::$yyReduceMap)) {
            // call the action
            $this->_retvalue = null;
            $this->{'yy_r' . self::$yyReduceMap[$yyruleno]}();
            $yy_lefthand_side = $this->_retvalue;
        }
        $yygoto = self::$yyRuleInfo[$yyruleno]['lhs'];
        $yysize = self::$yyRuleInfo[$yyruleno]['rhs'];
        $this->yyidx -= $yysize;
        for ($i = $yysize; $i; $i --) {
            // pop all of the right-hand side parameters
            array_pop($this->yystack);
        }
        $yyact = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, $yygoto);
        if ($yyact < self::YYNSTATE) {
            if (!$this->yyTraceFILE && $yysize) {
                $this->yyidx ++;
                $x = new TP_yyStackEntry;
                $x->stateno = $yyact;
                $x->major = $yygoto;
                $x->minor = $yy_lefthand_side;
                $this->yystack[$this->yyidx] = $x;
            } else {
                $this->yy_shift($yyact, $yygoto, $yy_lefthand_side);
            }
        } elseif ($yyact == self::YYNSTATE + self::YYNRULE + 1) {
            $this->yy_accept();
        }
    }

    public function yy_parse_failed()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sFail!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
    }

    public function yy_syntax_error($yymajor, $TOKEN)
    {
        #line 75 "smarty_internal_templateparser.y"

        $this->internalError = true;
        $this->yymajor = $yymajor;
        $this->compiler->trigger_template_error();
        #line 3142 "smarty_internal_templateparser.php"
    }

    public function yy_accept()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sAccept!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $stack = $this->yy_pop_parser_stack();
        }
        #line 66 "smarty_internal_templateparser.y"

        $this->successful = !$this->internalError;
        $this->internalError = false;
        $this->retvalue = $this->_retvalue;
        //echo $this->retvalue."\n\n";

        #line 3160 "smarty_internal_templateparser.php"
    }

    public function doParse($yymajor, $yytokenvalue)
    {
        $yyerrorhit = 0;   /* True if yymajor has invoked an error */

        if ($this->yyidx === null || $this->yyidx < 0) {
            $this->yyidx = 0;
            $this->yyerrcnt = - 1;
            $x = new TP_yyStackEntry;
            $x->stateno = 0;
            $x->major = 0;
            $this->yystack = array();
            array_push($this->yystack, $x);
        }
        $yyendofinput = ($yymajor == 0);

        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sInput %s\n",
                    $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
        }

        do {
            $yyact = $this->yy_find_shift_action($yymajor);
            if ($yymajor < self::YYERRORSYMBOL &&
                !$this->yy_is_expected_token($yymajor)
            ) {
                // force a syntax error
                $yyact = self::YY_ERROR_ACTION;
            }
            if ($yyact < self::YYNSTATE) {
                $this->yy_shift($yyact, $yymajor, $yytokenvalue);
                $this->yyerrcnt --;
                if ($yyendofinput && $this->yyidx >= 0) {
                    $yymajor = 0;
                } else {
                    $yymajor = self::YYNOCODE;
                }
            } elseif ($yyact < self::YYNSTATE + self::YYNRULE) {
                $this->yy_reduce($yyact - self::YYNSTATE);
            } elseif ($yyact == self::YY_ERROR_ACTION) {
                if ($this->yyTraceFILE) {
                    fprintf($this->yyTraceFILE, "%sSyntax Error!\n",
                            $this->yyTracePrompt);
                }
                if (self::YYERRORSYMBOL) {
                    if ($this->yyerrcnt < 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $yymx = $this->yystack[$this->yyidx]->major;
                    if ($yymx == self::YYERRORSYMBOL || $yyerrorhit) {
                        if ($this->yyTraceFILE) {
                            fprintf($this->yyTraceFILE, "%sDiscard input token %s\n",
                                    $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
                        }
                        $this->yy_destructor($yymajor, $yytokenvalue);
                        $yymajor = self::YYNOCODE;
                    } else {
                        while ($this->yyidx >= 0 &&
                            $yymx != self::YYERRORSYMBOL &&
                            ($yyact = $this->yy_find_shift_action(self::YYERRORSYMBOL)) >= self::YYNSTATE
                        ) {
                            $this->yy_pop_parser_stack();
                        }
                        if ($this->yyidx < 0 || $yymajor == 0) {
                            $this->yy_destructor($yymajor, $yytokenvalue);
                            $this->yy_parse_failed();
                            $yymajor = self::YYNOCODE;
                        } elseif ($yymx != self::YYERRORSYMBOL) {
                            $u2 = 0;
                            $this->yy_shift($yyact, self::YYERRORSYMBOL, $u2);
                        }
                    }
                    $this->yyerrcnt = 3;
                    $yyerrorhit = 1;
                } else {
                    if ($this->yyerrcnt <= 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $this->yyerrcnt = 3;
                    $this->yy_destructor($yymajor, $yytokenvalue);
                    if ($yyendofinput) {
                        $this->yy_parse_failed();
                    }
                    $yymajor = self::YYNOCODE;
                }
            } else {
                $this->yy_accept();
                $yymajor = self::YYNOCODE;
            }
        } while ($yymajor != self::YYNOCODE && $this->yyidx >= 0);
    }
}
