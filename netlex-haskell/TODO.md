# Netlex3 to Haskell/IHP Migration TODO

This comprehensive migration plan covers the complete port of the Netlex3 PHP application to a modern Haskell/IHP web application.

## 🎯 Project Overview

**Goal**: Replace the existing PHP Netlex3 application with a type-safe, performant Haskell/IHP application while maintaining all existing functionality.

**Key Benefits**:
- Type safety and compile-time error checking
- Better performance and memory management
- Modern web framework with built-in security
- Improved maintainability and code quality
- PostgreSQL with UUID primary keys for better scalability

---

## 📊 Database Schema Migration

### 1.1 Schema Analysis and Documentation
- [ ] **Analyze existing MySQL schema structure**
  - [ ] Document all tables from provisioning.sql (users, customers, products, configurations)
  - [ ] Document client database tables (anagrafiche, archivio, documento, item, utente)
  - [ ] Map relationships between tables and foreign key constraints
  - [ ] Identify indexes and performance optimizations
  - [ ] Document data types and their PostgreSQL equivalents

### 1.2 PostgreSQL Schema Design
- [ ] **Design new PostgreSQL schema with improvements**
  - [ ] Replace integer IDs with UUIDs for better distributed system support
  - [ ] Add proper foreign key constraints with CASCADE options
  - [ ] Design optimized indexes for query performance
  - [ ] Add database-level constraints for data integrity
  - [ ] Plan for multi-tenancy with organization-based data separation

### 1.3 Core Tables Implementation
- [ ] **Users and Authentication Tables**
  - [ ] `users` table with UUID, email, password_hash, profile info
  - [ ] `user_sessions` table for session management
  - [ ] `user_organizations` table for multi-tenant access
  - [ ] `api_keys` table for API authentication

- [ ] **Business Entity Tables**
  - [ ] `organizations` table (replaces customers/clients)
  - [ ] `projects` table (replaces archivio/pratica)
  - [ ] `registries` table (replaces anagrafiche)
  - [ ] `documents` table with proper file management
  - [ ] `timesheet_entries` table for time tracking
  - [ ] `billing_items` table for invoicing
  - [ ] `deadlines` table for deadline management
  - [ ] `appointments` table for calendar/agenda

### 1.4 Data Migration Scripts
- [ ] **Create migration utilities**
  - [ ] Write Haskell scripts to connect to existing MySQL database
  - [ ] Create data transformation functions (INT to UUID, date formats)
  - [ ] Handle password migration (MD5/SHA256 to bcrypt)
  - [ ] Migrate file attachments and document references
  - [ ] Create data validation and integrity checks

---

## 🔐 Authentication System Migration

### 2.1 IHP Authentication Setup
- [ ] **Configure IHP built-in authentication**
  - [ ] Set up IHP.AuthSupport.Authentication
  - [ ] Configure session management with secure cookies
  - [ ] Implement password hashing with bcrypt
  - [ ] Add email verification system

### 2.2 Password Migration Strategy
- [ ] **Handle existing password formats**
  - [ ] Support legacy MD5 password verification during transition
  - [ ] Support SHA256 password verification
  - [ ] Implement gradual migration to bcrypt on user login
  - [ ] Add password strength requirements for new passwords

### 2.3 Role-Based Access Control
- [ ] **Implement authorization system**
  - [ ] Create Role and Permission data types
  - [ ] Implement role hierarchy (guest → user → admin → superadmin)
  - [ ] Create authorization middleware for controllers
  - [ ] Port existing ACL rules from auth.ini
  - [ ] Add organization-level permissions

### 2.4 API Authentication
- [ ] **JWT Token System**
  - [ ] Implement JWT token generation and validation
  - [ ] Add API key authentication for external integrations
  - [ ] Create token refresh mechanism
  - [ ] Add rate limiting and security headers

---

## 🏗️ Core Business Logic Migration

### 3.1 Registry/Anagrafiche Domain
- [ ] **Contact Management System**
  - [ ] Create Registry data types and validation
  - [ ] Implement contact information management (phone, email, addresses)
  - [ ] Add search and filtering capabilities
  - [ ] Port relationship management between registries
  - [ ] Implement import/export functionality

### 3.2 Archive/Pratica Domain (Case Management)
- [ ] **Project/Case Management**
  - [ ] Create Archive/Project data types
  - [ ] Implement case lifecycle management (open, active, closed)
  - [ ] Add case categorization and tagging
  - [ ] Port workflow automation system
  - [ ] Implement case assignment and collaboration

### 3.3 Document Management System
- [ ] **File and Document Handling**
  - [ ] Create Document data types with metadata
  - [ ] Implement file upload and storage (local/cloud)
  - [ ] Add document versioning and history
  - [ ] Create document relationships (main, attachment, other)
  - [ ] Implement document search and indexing
  - [ ] Add document permissions and access control

### 3.4 Timesheet and Billing System
- [ ] **Time Tracking**
  - [ ] Create TimesheetEntry data types
  - [ ] Implement time entry creation and editing
  - [ ] Add time validation and approval workflow
  - [ ] Create reporting and analytics
  - [ ] Implement billing rate management

- [ ] **Billing and Invoicing**
  - [ ] Create BillingItem data types
  - [ ] Implement invoice generation
  - [ ] Add tax calculations and compliance
  - [ ] Create payment tracking
  - [ ] Implement financial reporting

### 3.5 Calendar and Deadline Management
- [ ] **Appointment System**
  - [ ] Create Appointment data types
  - [ ] Implement calendar view and scheduling
  - [ ] Add recurring appointments
  - [ ] Create notification system
  - [ ] Implement calendar sharing and permissions

- [ ] **Deadline Tracking**
  - [ ] Create Deadline data types
  - [ ] Implement deadline notifications
  - [ ] Add escalation workflows
  - [ ] Create deadline reporting and analytics

### 3.6 Workflow Engine
- [ ] **Business Process Automation**
  - [ ] Design workflow component system
  - [ ] Implement workflow triggers and actions
  - [ ] Create workflow designer interface
  - [ ] Add workflow execution engine
  - [ ] Implement workflow monitoring and logging

---

## 🌐 API Endpoints Migration

### 4.1 REST API Structure
- [ ] **API Architecture Design**
  - [ ] Design RESTful API structure with proper HTTP methods
  - [ ] Implement JSON serialization for all data types
  - [ ] Add proper error handling and status codes
  - [ ] Create API versioning strategy (v1, v2)
  - [ ] Add API documentation with OpenAPI/Swagger

### 4.2 Core API Endpoints
- [ ] **Authentication APIs**
  - [ ] POST /api/v2/auth/login
  - [ ] POST /api/v2/auth/logout
  - [ ] POST /api/v2/auth/refresh
  - [ ] GET /api/v2/auth/profile

- [ ] **Registry APIs**
  - [ ] GET /api/v2/registries (with pagination and search)
  - [ ] POST /api/v2/registries
  - [ ] GET /api/v2/registries/:id
  - [ ] PUT /api/v2/registries/:id
  - [ ] DELETE /api/v2/registries/:id

- [ ] **Project/Archive APIs**
  - [ ] GET /api/v2/projects
  - [ ] POST /api/v2/projects
  - [ ] GET /api/v2/projects/:id
  - [ ] PUT /api/v2/projects/:id
  - [ ] DELETE /api/v2/projects/:id

- [ ] **Document APIs**
  - [ ] GET /api/v2/documents
  - [ ] POST /api/v2/documents (with file upload)
  - [ ] GET /api/v2/documents/:id
  - [ ] PUT /api/v2/documents/:id
  - [ ] DELETE /api/v2/documents/:id
  - [ ] GET /api/v2/documents/:id/download

- [ ] **Timesheet APIs**
  - [ ] GET /api/v2/timesheet
  - [ ] POST /api/v2/timesheet
  - [ ] PUT /api/v2/timesheet/:id
  - [ ] DELETE /api/v2/timesheet/:id
  - [ ] GET /api/v2/timesheet/reports

### 4.3 Mobile API Support
- [ ] **Mobile App Integration**
  - [ ] Implement mobile-specific endpoints
  - [ ] Add offline synchronization support
  - [ ] Create mobile authentication flow
  - [ ] Implement push notifications
  - [ ] Add mobile-optimized data formats

---

## 🎨 Frontend Views Migration

### 5.1 HSX Template System
- [ ] **Convert PHP templates to HSX**
  - [ ] Set up IHP.ViewSupport and HSX syntax
  - [ ] Create base layout templates
  - [ ] Implement responsive design with Bootstrap/Tailwind
  - [ ] Add proper form handling and validation
  - [ ] Implement CSRF protection

### 5.2 Core Application Views
- [ ] **Authentication Views**
  - [ ] Login page with form validation
  - [ ] Registration page
  - [ ] Password reset flow
  - [ ] User profile management

- [ ] **Dashboard and Navigation**
  - [ ] Main dashboard with widgets
  - [ ] Navigation menu with role-based visibility
  - [ ] Search functionality
  - [ ] Notification center

- [ ] **Business Entity Views**
  - [ ] Registry list and detail views
  - [ ] Project/Archive management interface
  - [ ] Document browser and viewer
  - [ ] Timesheet entry interface
  - [ ] Calendar and appointment views
  - [ ] Deadline management interface

### 5.3 JavaScript Integration
- [ ] **Client-side Functionality**
  - [ ] Integrate modern JavaScript framework (Alpine.js/HTMX)
  - [ ] Add real-time updates with WebSockets
  - [ ] Implement file drag-and-drop upload
  - [ ] Add client-side form validation
  - [ ] Create interactive calendar components

---

## ⚙️ Configuration and Environment Setup

### 6.1 Development Environment
- [ ] **Local Development Setup**
  - [ ] Create comprehensive README with setup instructions
  - [ ] Set up PostgreSQL database with sample data
  - [ ] Configure environment variables (.env file)
  - [ ] Add development scripts (start, test, migrate)
  - [ ] Set up hot reloading for development

### 6.2 Configuration Management
- [ ] **Application Configuration**
  - [ ] Port configuration from config.ini and config_secrets.ini
  - [ ] Implement environment-specific configurations
  - [ ] Add database connection pooling
  - [ ] Configure email settings (SMTP)
  - [ ] Set up file storage configuration (local/S3)
  - [ ] Add logging and monitoring configuration

### 6.3 Security Configuration
- [ ] **Security Hardening**
  - [ ] Configure HTTPS and SSL certificates
  - [ ] Add security headers (CSP, HSTS, etc.)
  - [ ] Implement rate limiting
  - [ ] Add input validation and sanitization
  - [ ] Configure CORS for API endpoints

---

## 🧪 Testing and Quality Assurance

### 7.1 Test Infrastructure
- [ ] **Testing Framework Setup**
  - [ ] Set up Hspec for unit testing
  - [ ] Configure QuickCheck for property testing
  - [ ] Add integration testing with test database
  - [ ] Set up test coverage reporting
  - [ ] Create test data factories and fixtures

### 7.2 Unit Tests
- [ ] **Core Logic Testing**
  - [ ] Test all data validation functions
  - [ ] Test business logic and calculations
  - [ ] Test authentication and authorization
  - [ ] Test API serialization/deserialization
  - [ ] Test database operations and queries

### 7.3 Integration Tests
- [ ] **End-to-End Testing**
  - [ ] Test complete user workflows
  - [ ] Test API endpoints with real HTTP requests
  - [ ] Test database migrations and data integrity
  - [ ] Test file upload and document management
  - [ ] Test email notifications and external integrations

### 7.4 Performance Testing
- [ ] **Load and Performance Testing**
  - [ ] Benchmark database queries and optimize
  - [ ] Test API response times under load
  - [ ] Profile memory usage and optimize
  - [ ] Test concurrent user scenarios
  - [ ] Optimize asset loading and caching

---

## 🚀 Deployment and DevOps

### 8.1 Containerization
- [ ] **Docker Setup**
  - [ ] Create optimized Dockerfile for Haskell application
  - [ ] Set up docker-compose for local development
  - [ ] Configure PostgreSQL container with initialization
  - [ ] Add Redis container for caching and sessions
  - [ ] Set up reverse proxy (nginx) configuration

### 8.2 CI/CD Pipeline
- [ ] **Continuous Integration**
  - [ ] Set up GitHub Actions or GitLab CI
  - [ ] Add automated testing on pull requests
  - [ ] Configure code quality checks (hlint, stylish-haskell)
  - [ ] Add security scanning and dependency checks
  - [ ] Set up automated deployment to staging

### 8.3 Production Deployment
- [ ] **Production Infrastructure**
  - [ ] Set up production server environment
  - [ ] Configure database backups and monitoring
  - [ ] Set up application monitoring and logging
  - [ ] Configure SSL certificates and domain setup
  - [ ] Add health checks and uptime monitoring

### 8.4 Migration Strategy
- [ ] **Go-Live Planning**
  - [ ] Create data migration runbook
  - [ ] Plan phased rollout strategy
  - [ ] Set up rollback procedures
  - [ ] Create user training materials
  - [ ] Plan maintenance windows and communication

---

## 📋 Additional Considerations

### 9.1 Data Migration and Validation
- [ ] **Data Integrity**
  - [ ] Create comprehensive data validation scripts
  - [ ] Test migration with production data subset
  - [ ] Verify all relationships and constraints
  - [ ] Validate business logic with migrated data
  - [ ] Create data reconciliation reports

### 9.2 Performance Optimization
- [ ] **System Performance**
  - [ ] Optimize database queries and indexes
  - [ ] Implement caching strategy (Redis/Memcached)
  - [ ] Add CDN for static assets
  - [ ] Optimize image and file handling
  - [ ] Implement database connection pooling

### 9.3 Monitoring and Observability
- [ ] **System Monitoring**
  - [ ] Set up application performance monitoring (APM)
  - [ ] Add structured logging with proper log levels
  - [ ] Create business metrics and dashboards
  - [ ] Set up alerting for critical issues
  - [ ] Implement error tracking and reporting

### 9.4 Documentation
- [ ] **Technical Documentation**
  - [ ] Create API documentation
  - [ ] Document database schema and relationships
  - [ ] Write deployment and maintenance guides
  - [ ] Create user manuals and training materials
  - [ ] Document migration process and lessons learned

---

## 🎯 Success Criteria

- [ ] **Functional Parity**: All existing features work as expected
- [ ] **Performance**: Response times equal or better than PHP version
- [ ] **Security**: Modern security practices implemented
- [ ] **Reliability**: 99.9% uptime with proper error handling
- [ ] **Maintainability**: Clean, well-documented, type-safe code
- [ ] **User Experience**: Improved UI/UX with modern web standards

---

## 📅 Estimated Timeline

- **Phase 1 (Database & Auth)**: 4-6 weeks
- **Phase 2 (Core Business Logic)**: 8-10 weeks
- **Phase 3 (API & Frontend)**: 6-8 weeks
- **Phase 4 (Testing & Deployment)**: 4-6 weeks
- **Total Estimated Time**: 22-30 weeks

---

## 📊 Key PHP → Haskell Mappings

### Database Tables
| PHP/MySQL Table | Haskell/PostgreSQL Table | Notes |
|------------------|---------------------------|-------|
| `utente` | `users` | User authentication and profiles |
| `anagrafiche` | `registries` | Contact/client management |
| `archivio` | `projects` | Case/project management |
| `documento` | `documents` | Document management |
| `item` | `billing_items` | Billing and timesheet items |
| `customers` | `organizations` | Multi-tenant organizations |

### Authentication
| PHP Component | Haskell Component | Notes |
|---------------|-------------------|-------|
| Zend_Auth | IHP.AuthSupport | Built-in authentication |
| MD5/SHA256 passwords | bcrypt | Secure password hashing |
| Zend_Acl | Custom RBAC | Role-based access control |
| Sessions | IHP Sessions | Secure session management |

### Business Logic
| PHP Domain | Haskell Module | Notes |
|------------|----------------|-------|
| AnagraficaService | Registry.Service | Contact management |
| ArchiveService | Project.Service | Case management |
| TimesheetService | Timesheet.Service | Time tracking |
| DocumentService | Document.Service | File management |

---

## 🔧 Development Tools and Dependencies

### Haskell Dependencies (Cabal)
```haskell
-- Core IHP framework
ihp
ihp-hsx

-- Database
postgresql-simple
resource-pool

-- Authentication & Security
bcrypt
jwt
wai-cors

-- JSON & API
aeson
servant
servant-server

-- Testing
hspec
quickcheck
hspec-wai

-- Utilities
time
uuid
text
bytestring
```

### System Dependencies
- PostgreSQL 13+
- Redis (for caching and sessions)
- ImageMagick (for image processing)
- wkhtmltopdf (for PDF generation)
- Node.js (for asset compilation)

---

*This comprehensive TODO list should be updated regularly as tasks are completed and new requirements are discovered during the migration process. Each major section can be tackled incrementally, allowing for iterative development and testing.*
